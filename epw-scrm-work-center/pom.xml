<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>epw-scrm</artifactId>
        <groupId>com.cenker.scrm</groupId>
        <version>2.6.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>企业微信服务</description>
    <artifactId>epw-scrm-work-center</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>epw-scrm-work-common</module>
        <module>epw-scrm-work-cp</module>
        <module>epw-scrm-work-tp</module>
        <module>epw-scrm-work-api</module>
        <module>epw-scrm-work-service</module>
        <module>epw-scrm-work-mp</module>
    </modules>


    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-general</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-remote</artifactId>
        </dependency>

        <!-- 第三方企微依赖包 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
    </dependencies>
</project>