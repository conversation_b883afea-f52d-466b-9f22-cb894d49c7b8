package com.cenker.scrm.config;

import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.cp.config.impl.WxCpTpRedissonConfigImpl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpServiceImpl;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @Date 2022/12/19
 * @Description 代开发自建应用配置注入开发参数
 */
@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "wechat.app")
public class AppTpStoreConfig {

    private static String corpId;
    private static String token;
    private static String encodingAesKey;
    /**
     * 模板id
     */
    private static String suiteId;
    /**
     * 模板秘钥
     */
    private static String secret;

    private final StringRedisTemplate stringRedisTemplate;

    @Bean("appCpTpRedissonConfigImpl")
    public WxCpTpRedissonConfigImpl wxCpTpConfigStorage() {
        RedisTemplateWxRedisOps wxTpRedisOps = new RedisTemplateWxRedisOps(stringRedisTemplate);
        return WxCpTpRedissonConfigImpl.builder().
                wxRedisOps(wxTpRedisOps).aesKey(encodingAesKey).token(token).
                suiteId(suiteId).suiteSecret(secret).corpId(corpId).build();
    }

    @Bean("appCpTpService")
    public WxCpTpService wxCpTpService() {
        return new WxCpTpServiceImpl(wxCpTpConfigStorage());
    }

    public void setCorpId(String corpId) {
        AppTpStoreConfig.corpId = corpId;
    }

    public void setToken(String token) {
        AppTpStoreConfig.token = token;
    }

    public void setEncodingAesKey(String encodingAesKey) {
        AppTpStoreConfig.encodingAesKey = encodingAesKey;
    }

    public void setSuiteId(String suiteId) {
        AppTpStoreConfig.suiteId = suiteId;
    }

    public void setSecret(String secret) {
        AppTpStoreConfig.secret = secret;
    }

    public static String getCorpId() {
        return corpId;
    }

    public static String getToken() {
        return token;
    }

    public static String getEncodingAesKey() {
        return encodingAesKey;
    }

    public static String getSuiteId() {
        return suiteId;
    }

    public static String getSecret() {
        return secret;
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }
}
