package com.cenker.scrm.handler.message;

import com.cenker.scrm.constants.WorkCommonConstant;
import com.cenker.scrm.handler.message.cp.AbstractMessageCpHandler;
import com.cenker.scrm.service.ICorpCustomerCallbackService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @Date 2023/5/17
 * @Description 客户联系-回调事件路由处理
 */
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnBean(name = {"privateCpStoreConfig"})
public class CorpCustomerContactHandler extends AbstractMessageCpHandler {
    /**
     * 事件类型路由处理
     */
    private Map<String, BiConsumer<WxCpXmlMessage, Map<String, Object>>> routers = Maps.newHashMap();

    private final ICorpCustomerCallbackService corpCustomerContactEventHandler;

    @PostConstruct
    @Override
    public void initial() {
        log.info("【客户联系】事件类型路由处理初始化：CorpCustomerContactHandler");
        // 客户联系人事件处理
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.ADD_EXTERNAL_CONTACT,
                corpCustomerContactEventHandler::handlerAddExternalContact);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.ADD_HALF_EXTERNAL_CONTACT,
                corpCustomerContactEventHandler::handlerAddHalfExternalContact);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.DEL_EXTERNAL_CONTACT,
                corpCustomerContactEventHandler::handlerDelExternalContact);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.DEL_FOLLOW_USER,
                corpCustomerContactEventHandler::handlerDelFollowUser);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.EDIT_EXTERNAL_CONTACT,
                corpCustomerContactEventHandler::handlerEditExternalContact);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalContactChangeType.TRANSFER_FAIL,
                corpCustomerContactEventHandler::handlerTransferFail);
        // 客户群
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CHAT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalChatChangeType.CREATE,
                corpCustomerContactEventHandler::handlerCreateChat);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CHAT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalChatChangeType.UPDATE,
                corpCustomerContactEventHandler::handlerUpdateChat);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_CHAT + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalChatChangeType.DISMISS,
                corpCustomerContactEventHandler::handlerDismissChat);
        // 企业标签事件处理
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_TAG + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalTagChangeType.CREATE,
                corpCustomerContactEventHandler::handlerCreateTag);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_TAG + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalTagChangeType.UPDATE,
                corpCustomerContactEventHandler::handlerUpdateTag);
        routers.put(WxCpConsts.EventType.CHANGE_EXTERNAL_TAG + WorkCommonConstant.SEPARATOR_2 + WxCpConsts.ExternalTagChangeType.DELETE,
                corpCustomerContactEventHandler::handlerDeleteTag);
    }

    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map, WxCpService wxCpService, WxSessionManager wxSessionManager) {
        // 执行一些必要操作
        log.info("【客户联系回调】回调事件，来到了客户联系事件处理CorpCustomerContactHandler");
        log.info("【客户联系回调】回调事件，事件内容：{}", wxCpXmlMessage);
        // 填充数据
        super.next(map, wxCpService, wxSessionManager);
        // 继续路由到指定处理器
        routers.getOrDefault(wxCpXmlMessage.getEvent() + WorkCommonConstant.SEPARATOR_2 + wxCpXmlMessage.getChangeType(), this::defaultFn).accept(wxCpXmlMessage, map);
        log.info("【客户联系回调】处理事件完成");
        return null;
    }
}
