package com.cenker.scrm.service;

import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/12
 * @Description 会话存档接口
 */
public interface IChatArchiveCallbackService extends IEventHandlerService {
    /**
     * 会话存档回调事件,当微信客户、接待人员发消息或有行为动作时，企业微信后台会将事件的回调数据包发送到企业指定URL
     *
     * @param wxMessage 解析消息
     * @param map            接口对象、自定义参数
     */
    void handlerAuditNotifyMsgOrEvent(WxCpXmlMessage wxMessage, Map<String, Object> map);
    /**
     * 会话存档回调事件,当微信客户、接待人员发消息或有行为动作时，企业微信后台会将事件的回调数据包发送到企业指定URL
     *
     * @param wxMessage 解析消息
     * @param map            接口对象、自定义参数
     */
    void handlerAuditApprovedMsgOrEvent(WxCpXmlMessage wxMessage, Map<String, Object> map);

}
