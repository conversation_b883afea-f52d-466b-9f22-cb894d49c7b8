package com.cenker.scrm.enums;

/**
 * 企微应用Id枚举
 */
public enum WorkAgentIdEnum {
    /**
     * 应用Id枚举
     */
    BOOK(1000000,"通讯录", "Key值：CorpId +#+ book，例如：wx123#book"),
    CUSTOMER(10000001,"客户联系", "Key值：CorpId +#+ cutomer，例如：wx123#cutomer"),
    WECHATKF(10000002,"微信客服", "Key值：CorpId +#+ wechatkf，例如：wx123#wechatkf"),
    REDPACKET(10000003, "企业红包", "Key值：CorpId +#+ redpacket，例如：wx123#redpacket"),
    CHATARCHIVE(10000004, "会话存档", "Key值：CorpId +#+ redpacket，例如：wx123#redpacket");

    private Integer id;

    private String name;

    private String desc;

    private WorkAgentIdEnum(Integer id, String name, String desc){
        this.id = id;
        this.name = name;
        this.desc = desc;
    }


    public Integer getId(){
        return this.id;
    }
    public String getName(){
        return this.name;
    }
}
