package com.cenker.scrm.handler.message.cp;

import com.cenker.scrm.constants.EventConstants;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.message.WxCpMessageHandler;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/16
 * @Description 抽象消息处理-企业回调
 */
@Slf4j
public abstract class AbstractMessageCpHandler implements WxCpMessageHandler {

    /**
     * 初始化具体事件路由器(可以不实现)
     */
    public void initial(){}

    /**
     * 填充数据到下一个路由器
     */
    protected void next(Map<String, Object> map, WxCpService wxCpService, WxSessionManager wxSessionManager){
        map.put(EventConstants.WX_CP_SERVICE,wxCpService);
        map.put(EventConstants.WX_SESSION_MANAGER,wxSessionManager);
    }

    /**
     * 回调事件路由无法匹配
     * @param wxCpXmlMessage 解析数据
     * @param map next()填充的数据及原本自定义数据
     */
    protected void defaultFn(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {
        log.info("【回调事件】找不到事件实现函数[{}]:{}", wxCpXmlMessage.toString(), map.toString());
    }
}
