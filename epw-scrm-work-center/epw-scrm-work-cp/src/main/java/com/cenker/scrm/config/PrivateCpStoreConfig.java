package com.cenker.scrm.config;


import com.cenker.scrm.constants.WorkCommonConstant;
import com.cenker.scrm.handler.message.*;
import com.cenker.scrm.handler.message.cp.CallbackMessageCpHandler;
import com.cenker.scrm.handler.message.cp.LogMessageCpHandler;
import com.cenker.scrm.util.UrlReplaceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2022/12/21
 * @Description 内部自研 注入该类即启动对应的接口类
 */
@Component("privateCpStoreConfig")
@Slf4j
@RequiredArgsConstructor
@Configurable
public class PrivateCpStoreConfig {
    private static final String QYAPI_URL = "https://qyapi.weixin.qq.com";
    private final RedissonClient redissonClient;
    private final LogMessageCpHandler logMessageCpHandler;
    private final CallbackMessageCpHandler callbackMessageCpHandler;

    private final CorpBookContactHandler corpBookContactHandler;
    private final CorpCustomerContactHandler corpCustomerContactHandler;
    private final AgentMessageHandler agentMessageHandler;
    private final KfAccountMessageHandler kfAccountMessageHandler;
    private final ChatArchiveMessageHandler chatArchiveMessageHandler;


    /**
     * 企业微信 service
     * 存放通讯录、客户联系、主自建应用、微信客服、企业红包
     */
    private final Map<String, WxCpServiceImpl> wxCpServiceMap = new ConcurrentHashMap<>(64);

    /**
     * 自建应用（除主自建应用外的其他自建应用） service
     */
    private final Map<String, WxCpServiceImpl> wxCpAgentServiceMap = new ConcurrentHashMap<>(64);

    /**
     * 事件回调路由
     */
    private final Map<String, WxCpMessageRouter> routers = new ConcurrentHashMap<>(64);


    /**
     * 初始化企业内部自研接口信息
     *
     * @param corpId  企业id
     * @param token   回调token
     * @param aesKey  回调aesKey
     * @param secret  应用密钥
     * @param agentId 应用id
     */
    private WxCpServiceImpl initWxCepService(String corpId, String token,
                                             String aesKey, String secret, int agentId) {
        // 域名处理
        String qyapi = UrlReplaceUtils.urlReplace(QYAPI_URL);

        WxCpRedissonConfigImpl config = new WxCpRedissonConfigImpl(redissonClient, "private:");
        config.setAesKey(aesKey);
        config.setToken(token);
        config.setCorpSecret(secret);
        config.setCorpId(corpId);
        config.setAgentId(agentId);
        config.setBaseApiUrl(qyapi);
        WxCpServiceImpl cpService = new WxCpServiceImpl();
        cpService.setWxCpConfigStorage(config);
        try {
            if (StringUtils.isEmpty(cpService.getAccessToken())) {
                cpService.getAccessToken(true);
            }
            return cpService;
        } catch (WxErrorException e) {
            log.error("【企业内部自研初始化】刷新token失败: corpId={}, agentId={}", corpId, agentId);
            log.error("【企业内部自研初始化】刷新token失败:", e);
            return cpService;
        }
    }

    /**
     * 构建企业内部-自研接口信息
     *
     * @param corpId   企业id
     * @param token    回调token
     * @param aesKey   回调aesKey
     * @param secret   应用密钥
     * @param agentKey 应用id枚举
     */
    public void buildWxCpService(String corpId, String token, String aesKey,
                                 String secret, Integer agentId, String agentKey) {
        log.info("注入服务：corpId={}, agentId={}, agentKey={}", corpId, agentId, agentKey);
        WxCpServiceImpl cpService = initWxCepService(corpId, token, aesKey, secret, agentId);
        wxCpServiceMap.put(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey, cpService);
    }


    /**
     * 构建企业内部-自研路由信息
     *
     * @param corpId   企业id
     * @param agentKey 应用标识
     */
    public void buildWxCpRoute(String corpId, String agentKey) {
        log.info("注入路由：corpId={}, agentKey={}", corpId, agentKey);
        WxCpServiceImpl cpService = getWxCpService(corpId, agentKey);
        routers.put(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey, this.newRouter(cpService));
    }


    public void refreshWxService(String corpId, String token, String aesKey,
                                 String secret, Integer agentId, String agentKey) {
        buildWxCpService(corpId, token, aesKey, secret, agentId, agentKey);
    }

    /**
     * 获取内部自研路由对象
     *
     * @param corpId   企业id
     * @param agentKey 企业微信固定应用
     * @return 内部自研路由对象
     */
    public WxCpMessageRouter getWxCpMessageRouterByCorpId(String corpId, String agentKey) {
        return routers.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey);
    }

    public WxCpMessageRouter getWxCpMessageRouterByCorpId(String corpId, Integer agentId) {
        return routers.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentId);
    }

    /**
     * 获取内部自研对象
     *
     * @param corpId   企业id
     * @param agentKey 企业微信固定应用
     * @return 内部自研接口对象
     */
    public WxCpServiceImpl getWxCpServiceByCorpId(String corpId, String agentKey) {
        return wxCpServiceMap.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey);
    }

    /**
     * 获取内部自研路由对象
     *
     * @param corpId   企业id
     * @param agentKey 企业微信固定应用
     * @return 内部自研路由对象
     */
    public WxCpMessageRouter getWxCpRouteByCorpId(String corpId, String agentKey) {
        return routers.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey);
    }

    /**
     * 判断是否存在主自建应用
     *
     * @param corpId
     * @param agentKey
     * @return
     */
    public boolean isCpService(String corpId, String agentKey) {
        return wxCpServiceMap.containsKey(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey);
    }

    /**
     * 初始化路由处理器
     * WxCpMessageRouter选用理由：路由处理、拦截器、异步配置
     *
     * @param wxCpService
     * @return
     */
    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        WxCpMessageRouter wxCpMessageRouter = new WxCpMessageRouter(wxCpService);
        // 统一日志处理
        wxCpMessageRouter.rule().handler(logMessageCpHandler).next()
                // 客户联系变更事件-客户、客户群、企业标签 统一到一个事件处理器
                .rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT)
                .handler(corpCustomerContactHandler).end()
                .rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.CHANGE_EXTERNAL_CHAT)
                .handler(corpCustomerContactHandler).end()
                .rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.CHANGE_EXTERNAL_TAG)
                .handler(corpCustomerContactHandler).end()
                // 自建应用成员关注及取消关注事件
                .rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.SUBSCRIBE)
                .handler(agentMessageHandler).end()
                .rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.UNSUBSCRIBE)
                .handler(agentMessageHandler).end()
                // 通讯录变更事件-成员/部门/标签
                .rule().msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.CHANGE_CONTACT)
                .handler(corpBookContactHandler).end()
                // 微信客服回调事件
                .rule().msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.KF_MSG_OR_EVENT)
                .handler(kfAccountMessageHandler).end()
                // 会话存档回调事件
                .rule().msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.MSGAUDIT_NOTIFY)
                .handler(chatArchiveMessageHandler).end()
                // 统一事件处理（兜底规则）
                .rule().async(false).handler(callbackMessageCpHandler).end();
        // 设置回调处理器
        return wxCpMessageRouter;
    }


    /** 以下为自建应用获取方式，对外部平台或客户提供应用服务 */

    /**
     * 构建企业内部自研接口信息
     *
     * @param corpId  企业id
     * @param token   回调token
     * @param aesKey  回调aesKey
     * @param secret  应用密钥
     * @param agentId 应用id
     */
    public void buildWxCpAgentService(String corpId, String token, String aesKey,
                                      String secret, int agentId) {
        log.info("【企业自研】非主自建应用注入服务：corpId={}, agentId={}", corpId, agentId);
        WxCpServiceImpl cpService = initWxCepService(corpId, token, aesKey, secret, agentId);
        String key = corpId + WorkCommonConstant.SEPARATOR_1 + agentId;
        wxCpAgentServiceMap.put(key, cpService);
    }


    public void buildWxCpAgentMessageRouter(String corpId, int agentId) {
        log.info("【企业自研】非主自建应用注入路由：corpId={}, agentId={}", corpId, agentId);
        WxCpServiceImpl cpService = getWxCpService(corpId, agentId);
        // 初始化路由
        routers.put(corpId + WorkCommonConstant.SEPARATOR_1 + agentId, this.newRouter(cpService));
    }

    public void refreshWxAgentService(String corpId, String token, String aesKey,
                                      String secret, int agentId) {
        buildWxCpAgentService(corpId, token, aesKey, secret, agentId);
    }

    /**
     * 获取内部自研对象-非主自建应用
     *
     * @param corpId  企业id
     * @param agentId 应用id
     * @return 内部自研接口对象
     */
    public WxCpServiceImpl getWxCpService(String corpId, Integer agentId) {
        return wxCpAgentServiceMap.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentId);
    }

    /**
     * 获取内部自研对象
     *
     * @param corpId   企业id
     * @param agentKey 应用标识
     * @return 内部自研接口对象
     */
    public WxCpServiceImpl getWxCpService(String corpId, String agentKey) {
        return wxCpServiceMap.get(corpId + WorkCommonConstant.SEPARATOR_1 + agentKey);
    }
}
