package com.cenker.scrm.handler.event.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cenker.scrm.handler.event.base.CorpBookCallbackServiceImpl;
import com.cenker.scrm.constants.EventConstants;
import com.cenker.scrm.pojo.entity.wechat.TbWxContactRel;
import com.cenker.scrm.pojo.entity.wechat.TbWxDepartment;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.service.contact.ITbWxContactRelWorkService;
import com.cenker.scrm.service.contact.ITbWxDepartmentWorkService;
import com.cenker.scrm.service.contact.ITbWxUserWorkService;
import com.cenker.scrm.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/19
 * @Description 通讯录事件处理器特殊处理方法
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CorpBookCallbackHandler extends CorpBookCallbackServiceImpl {

    private final ITbWxUserWorkService tbWxUserService;
    private final ITbWxContactRelWorkService tbWxContactRelService;
    private final ITbWxDepartmentWorkService tbWxDepartmentService;


    @Override
    public void handlerCreateUser(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】员工新增事件开始处理啦");
        WxCpService wxCpService = (WxCpService) map.get(EventConstants.WX_CP_SERVICE);
        String corpId = wxMessage.getToUserName();
        log.info("【通讯录回调】添加企业ID{}的成员", corpId);
        try {
            // 添加成员
            String userId = wxMessage.getUserId();
            log.info("【通讯录回调】获取到成员ID为：{}", userId);

            // 获取成员详情
            WxCpUser wxCpUser = wxCpService.getUserService().getById(userId);
            log.info("【通讯录回调】根据成员ID{}调用企业微信接口获取到结果为：{}", userId, JSONObject.toJSONString(wxCpUser));

            // 保存成员信息
            tbWxUserService.addWxUser(wxCpUser, corpId);
        } catch (Exception ex) {
            log.error("【通讯录回调】添加成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("【通讯录回调】添加企业ID{}的成员结束", corpId);
    }

    @Override
    public void handlerUpdateUser(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】员工更新事件开始处理啦");
        String corpId = wxMessage.getToUserName();
        WxCpService wxCpService = (WxCpService) map.get(EventConstants.WX_CP_SERVICE);
        log.info("【通讯录回调】修改企业ID{}的成员", corpId);
        try {
            String newUserId = wxMessage.getNewUserId();
            String userId = wxMessage.getUserId();
            if (StringUtils.isBlank(newUserId)) {
                // 修改成员
                log.info("【通讯录回调】获取到成员ID为：{}", userId);

                // 获取成员详情
                WxCpUser wxCpUser = wxCpService.getUserService().getById(userId);
                log.info("【通讯录回调】根据成员ID{}调用企业微信接口获取到结果为：{}", userId, JSONObject.toJSONString(wxCpUser));

                // 修改成员信息
                tbWxUserService.addWxUser(wxCpUser, corpId);
            } else {
                // 修改员工活码
                log.info("【通讯录回调】修改成员UserID同步修改员工活码");
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("corp_id", corpId);
                queryWrapper.eq("user_id", userId);
                 List<TbWxContactRel> tbWxContactRelList = tbWxContactRelService.list(queryWrapper);
                 if (null != tbWxContactRelList && tbWxContactRelList.size() > 0) {
                     for (TbWxContactRel tbWxContactRel : tbWxContactRelList) {
                         tbWxContactRel.setUserId(newUserId);
                         tbWxContactRelService.update(tbWxContactRel, queryWrapper);
                     }
                 }
                log.info("【通讯录回调】修改成员UserID同步修改员工活码结束");

                log.info("【通讯录回调】修改成员UserID同步员工登录账号");
                // 修改登录账号
            /*   SysUser sysUser = sysUserFeign.selectUserByCorpInfo(corpId, userId);
                if (null != sysUser) {
                    SysUser su = new SysUser();
                    su.setCorpUserId(newUserId);
                    su.setCorpId(corpId);
                    su.setUserId(sysUser.getUserId());
                    sysUserFeign.updateCorpUserId(su);
                }*/
                log.info("【通讯录回调】修改成员UserID同步员工登录账号结束");

                log.info("【通讯录回调】修改成员UserID同步新增员工信息");
                // 新增成员
                // 获取成员详情
                WxCpUser wxCpUser = wxCpService.getUserService().getById(newUserId);
                log.info("【通讯录回调】根据成员ID{}调用企业微信接口获取到结果为：{}", newUserId, JSONObject.toJSONString(wxCpUser));

                // 保存成员信息
                tbWxUserService.addWxUser(wxCpUser, corpId);
                log.info("【通讯录回调】修改成员UserID同步新增员工信息结束");

                // 废除老成员
                log.info("【通讯录回调】修改成员UserID同步废除原有员工信息");
                TbWxUser tbWxUser = new TbWxUser();
                tbWxUser.setDelFlag("2");
                QueryWrapper<TbWxUser> userQueryWrapper = new QueryWrapper<>();
                userQueryWrapper.eq("userid", userId);
                tbWxUserService.update(tbWxUser, userQueryWrapper);
                log.info("【通讯录回调】修改成员UserID同步废除原有员工信息结束");

                log.info("【通讯录回调】修改成员UserID同步建立新旧UserId关系");
                // 新旧UserId建立关联关系表
//                TbWxUserRel tbWxUserRel = new TbWxUserRel();
//                tbWxUserRel.setCreateTime(new Date());
//                tbWxUserRel.setNewUserId(newUserId);
//                tbWxUserRel.setOldUserId(userId);
//                tbWxUserRel.setCorpId(corpId);
//                tbWxUserRelService.save(tbWxUserRel);
                log.info("【通讯录回调】修改成员UserID同步建立新旧UserId关系结束");
            }
        } catch (Exception ex) {
            log.error("【通讯录回调】修改成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("【通讯录回调】修改企业ID{}的成员结束", corpId);

    }

    @Override
    public void handlerDeleteUser(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】员工删除事件开始处理啦");
        String corpId = wxMessage.getToUserName();
        log.info("通讯录回调：删除企业ID{}的成员", corpId);
        try {
            // 删除成员
            String userId = wxMessage.getUserId();
            log.info("通讯录回调：获取到成员ID为：{}", userId);

            tbWxUserService.delWxUser(corpId, userId);

            log.info("通讯录回调：禁用企业Id{}的员工ID{}开始", corpId, userId);
            // 禁用员工账号 报错暂时不同
           /*    SysUser sysUser = sysUserFeign.selectUserByCorpInfo(corpId, userId);
                if (null != sysUser) {
                    sysUser.setStatus("1");
                    int count = sysUserFeign.updateUser(sysUser);
                }*/
            log.info("通讯录回调：禁用企业Id{}的员工ID{}结束", corpId, userId);
        } catch (Exception ex) {
            log.error("通讯录回调：删除成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("通讯录回调：删除企业ID{}的成员结束", corpId);

    }

    @Override
    public void handlerCreateDepart(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】部门新增事件开始处理啦");
        String corpId = wxMessage.getToUserName();
        log.info("通讯录回调：添加企业ID{}的部门信息", corpId);
        try {
            // 添加部门
            log.info("通讯录回调：添加企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
            TbWxDepartment tbWxDepartment = new TbWxDepartment();
            tbWxDepartment.setId(wxMessage.getId() + "");
            tbWxDepartment.setCorpId(corpId);
            tbWxDepartment.setName(wxMessage.getName());
            tbWxDepartment.setParentId(wxMessage.getParentId());
            tbWxDepartment.setDelFlag("1");
            tbWxDepartmentService.save(tbWxDepartment);
            log.info("通讯录回调：添加企业Id{}的部门信息结束", corpId);
        } catch (Exception ex) {
            log.error("通讯录回调：添加企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("通讯录回调：添加企业ID{}的部门信息结束", corpId);
    }

    @Override
    public void handlerUpdateDepart(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】部门更新事件开始处理啦");
        String corpId = wxMessage.getToUserName();
        log.info("通讯录回调：修改企业ID{}的部门", corpId);
        try {
            // 修改部门
            log.info("通讯录回调：修改企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
            TbWxDepartment tbWxDepartment = new TbWxDepartment();
            tbWxDepartment.setId(wxMessage.getId() + "");
            tbWxDepartment.setCorpId(corpId);
            if (StringUtils.isNotEmpty(wxMessage.getName())) {
                tbWxDepartment.setName(wxMessage.getName());
            }
            if (wxMessage.getParentId() != null) {
                tbWxDepartment.setParentId(wxMessage.getParentId());
            }

            QueryWrapper<TbWxDepartment> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", wxMessage.getId());
            queryWrapper.eq("corp_id", corpId);
            tbWxDepartmentService.update(tbWxDepartment, queryWrapper);
            log.info("通讯录回调：修改企业Id{}的部门信息结束", corpId);
        } catch (Exception ex) {
            log.error("通讯录回调：修改企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("通讯录回调：修改企业ID{}的部门结束", corpId);

    }

    @Override
    public void handlerDeleteDepart(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【通讯录回调】部门删除事件开始处理啦");
        String corpId = wxMessage.getToUserName();
        log.info("通讯录回调：删除企业ID{}的部门", corpId);
        try {
            log.info("通讯录回调：删除企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
            // 删除部门
            TbWxDepartment tbWxDepartment = new TbWxDepartment();
            tbWxDepartment.setId(wxMessage.getId() + "");
            tbWxDepartment.setCorpId(corpId);
            tbWxDepartment.setDelFlag("2");

            QueryWrapper<TbWxDepartment> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", wxMessage.getId());
            queryWrapper.eq("corp_id", corpId);
            tbWxDepartmentService.update(tbWxDepartment, queryWrapper);
            log.info("通讯录回调：删除企业Id{}的部门信息结束", corpId);
        } catch (Exception ex) {
            log.error("通讯录回调：删除企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
        }
        log.info("通讯录回调：删除企业ID{}的部门结束", corpId);

    }
}
