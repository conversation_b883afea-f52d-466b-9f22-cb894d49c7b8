package com.cenker.scrm.handler.event.impl;

import com.cenker.scrm.handler.event.base.KfAccountCallbackServiceImpl;
import com.cenker.scrm.service.contact.ITbWxKfProcessMsgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/12
 * @Description 微信客服处理器特殊处理方法
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class KfAccountCallbackHandler extends KfAccountCallbackServiceImpl {

    private final ITbWxKfProcessMsgService tbWxKfProcessMsgService;

    @Override
    public void handlerKfMsgOrEvent(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【微信客服回调】客服回调事件开始处理啦" + wxMessage);
        // 处理回调信息, 刷新 redis
        tbWxKfProcessMsgService.ProcessWxMessage(wxMessage);
    }

}
