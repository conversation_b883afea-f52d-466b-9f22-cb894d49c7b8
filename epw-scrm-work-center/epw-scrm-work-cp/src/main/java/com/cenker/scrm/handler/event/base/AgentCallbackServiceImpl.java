package com.cenker.scrm.handler.event.base;

import com.cenker.scrm.handler.message.adapter.AgentEventAdapter;
import com.cenker.scrm.service.IAgentCallbackService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/12
 * @Description 应用事件处理器
 */
@Slf4j
public class AgentCallbackServiceImpl extends AgentEventAdapter implements IAgentCallbackService {
    @Override
    public void handlerSubscribe(WxCpXmlMessage wxMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerUnsubscribe(WxCpXmlMessage wxMessage, Map<String, Object> map) {

    }
}
