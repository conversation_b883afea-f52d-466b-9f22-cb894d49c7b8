package com.cenker.scrm.handler.event.impl;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.handler.event.base.ChatArchiveCallbackService;
import com.cenker.scrm.handler.event.base.KfAccountCallbackServiceImpl;
import com.cenker.scrm.service.contact.ITbWxKfProcessMsgService;
import com.cenker.scrm.service.external.ITbWxExtFollowUserWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/12
 * @Description 会话存档事件处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChatArchiveCallbackHandler extends ChatArchiveCallbackService {

    private final MqSendMessageManager mqSendMessageManager;
    private final ITbWxExtFollowUserWorkService tbWxExtFollowUserService;

    @Override
    public void handlerAuditNotifyMsgOrEvent(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【会话存档回调】产生会话回调事件开始处理啦" + wxMessage);
        mqSendMessageManager.sendPullChatArchiveMessage(wxMessage);
    }

    @Override
    public void handlerAuditApprovedMsgOrEvent(WxCpXmlMessage wxMessage, Map<String, Object> map) {
        log.info("【会话存档回调】客户同意进行聊天内容存档事件开始处理啦" + wxMessage);
        if (StrUtil.equals(WxCpConsts.MsgAuditChangeType.MSG_AUDIT_APPROVED, wxMessage.getChangeType())) {
            tbWxExtFollowUserService.handlerAuditApprovedMsgEvent(wxMessage);
        }
    }


}
