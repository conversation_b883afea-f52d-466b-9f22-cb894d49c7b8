package com.cenker.scrm.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2022/12/21
 * @Description 代自建 注入该类即启动对应的接口类
 */
@Slf4j
@RequiredArgsConstructor
public class AppCpStoreConfig{
    private final RedissonClient redissonClient;

    /**
     * 代自建应用service
     */
    private final Map<String, WxCpServiceImpl> wxCpAppServiceMap = new ConcurrentHashMap<>(64);

    /**
     * 构建代自建应用信息
     * @param corpId 企业id
     * @param token 回调token
     * @param aesKey 回调aesKey
     * @param secret 应用密钥
     * @param agentId 应用id
     * @return 接口调用对象
     */
    public WxCpServiceImpl buildWxCpService(String corpId, String token, String aesKey, String secret, int agentId) {
        WxCpServiceImpl wxTpCpService = wxCpAppServiceMap.get(corpId);
        if (wxTpCpService != null) {
            return wxTpCpService;
        }
        WxCpRedissonConfigImpl config = new WxCpRedissonConfigImpl(redissonClient, "app:");
        config.setCorpId(corpId);
        config.setCorpSecret(secret);
        config.setAesKey(aesKey);
        config.setToken(token);
        config.setAgentId(agentId);
        var cpService = new WxCpServiceImpl();
        cpService.setWxCpConfigStorage(config);
        try {
            if (StringUtils.isEmpty(cpService.getAccessToken())) {
                cpService.getAccessToken(true);
            }
        } catch (WxErrorException e) {
            log.error("【代自建应用初始化】刷新token失败：{}", corpId);
        }
        synchronized (this.wxCpAppServiceMap) {
            wxCpAppServiceMap.put(corpId, cpService);
        }
        return cpService;
    }

    public void refreshWxService(String corpId, String token, String aesKey, String secret, int agentId) {
        buildWxCpService(corpId, token, aesKey, secret, agentId);
    }

    /**
     * 通过corpId 获取 WxCpService
     */
    public WxCpServiceImpl getWxCpServiceByCorpId(String corpId) {
        return wxCpAppServiceMap.get(corpId);
    }

    // 当前工程注入，现在由其他工程启动注入
/*    public void afterPropertiesSet() throws Exception {
        // 找到符合的代自建应用创建
        buildWxCpService("wpRRa3DQAAJ8BeOWaACPICdAXHhRzMSg", "jnTVMGkEyCzCdxILJL","EzohjQgztqpHHCAkiaBaobhhML4BHQGAUApCODoo8Bz", "B9JRhRPxf7ZZhPliY4toOJWNPPgzb4oCgChZ21r8GoY", 1000003);
    }*/

}
