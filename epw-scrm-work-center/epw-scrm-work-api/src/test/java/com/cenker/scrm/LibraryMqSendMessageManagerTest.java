//package com.cenker.scrm;
//
//import com.cenker.WorkApiApplication;
//import com.cenker.scrm.example.LibraryRabbitMQExample;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = WorkApiApplication.class)
//public class LibraryMqSendMessageManagerTest {
//    @Autowired
//    private LibraryRabbitMQExample example;
//    @Test
//    public void sendReplyMessage() {
//        example.sendSuccessReplyExample();
//    }
//
//    @Test
//    public void sendNotificationExample() {
//        example.sendNotificationExample();
//    }
//}