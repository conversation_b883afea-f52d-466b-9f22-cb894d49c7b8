package com.cenker.scrm.config.init;

import com.cenker.scrm.config.WxMpStoreConfig;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.common.redis.WxRedisOps;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @Date 2021/11/22
 * @Description 微信公众号主体初始化
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(WxMpStoreConfig.class)
public class WxMpConfiguration {
    private final StringRedisTemplate redisTemplate;
    private final WxMpStoreConfig wxMpStoreConfig;

    @Bean
    public WxMpService wxMpService() {
        WxMpService service = new WxMpServiceImpl();
        WxRedisOps redisOps = new RedisTemplateWxRedisOps(redisTemplate);
        WxMpRedisConfigImpl wxMpRedisConfig = new WxMpRedisConfigImpl(redisOps,"mp");
        wxMpRedisConfig.setAppId(wxMpStoreConfig.getAppId());
        wxMpRedisConfig.setSecret(wxMpStoreConfig.getAppSecret());
        service.setWxMpConfigStorage(wxMpRedisConfig);
        return service;
    }
}
