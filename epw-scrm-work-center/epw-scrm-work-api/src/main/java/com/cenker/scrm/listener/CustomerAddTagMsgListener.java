package com.cenker.scrm.listener;

import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.biz.CustomerSyncTagManager;
import com.cenker.scrm.biz.CustomerTagMessageManager;
import com.cenker.scrm.constants.RabbitMqQueuesConstant;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerSyncTagMsgDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @Date 2023/6/8
 * @Description 监听为客户添加企业标签的MQ消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerAddTagMsgListener {
    private final CustomerTagMessageManager manager;
    private final CustomerSyncTagManager syncTagManager;

    @RabbitListener(queues = RabbitMqQueuesConstant.CUSTOMER_ADD_TAG, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void onAddMessage(Message message) {
        log.info("【RabbitMQ】接收到为客户添加企业标签的MQ消息!");
        long start = System.currentTimeMillis();
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【RabbitMQ】为客户添加企业标签的MQ消息体:{}", messageBody);
        CustomerAddTagMsgDto msgContent = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), CustomerAddTagMsgDto.class);
        manager.handlerCustomerAddTagMessage(msgContent);
        long time = (System.currentTimeMillis() - start);
        log.info("【RabbitMQ】处理为客户添加标签MQ消息完成[message:{},time={}ms]", messageBody, time);
    }

    @RabbitListener(queues = RabbitMqQueuesConstant.CUSTOMER_DEL_TAG, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void onRemoveMessage(Message message) {
        log.info("【RabbitMQ】接收到为客户删除企业标签的MQ消息!");
        long start = System.currentTimeMillis();
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【RabbitMQ】为客户删除企业标签的MQ消息体:{}", messageBody);
        CustomerRemoveTagMsgDto msgContent = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), CustomerRemoveTagMsgDto.class);
        manager.handlerCustomerRemoveTagMessage(msgContent);
        long time = (System.currentTimeMillis() - start);
        log.info("【RabbitMQ】处理为客户删除标签MQ消息完成[message:{},time={}ms]", messageBody, time);
    }

    @RabbitListener(queues = RabbitMqQueuesConstant.SYS_MSG_SYSN_TAG, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void onSyncMessage(Message message) {
        log.info("【RabbitMQ】接收到同步企业标签至企微的MQ消息!");
        long start = System.currentTimeMillis();
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【RabbitMQ】客户同步企业标签至企微的MQ消息体:{}", messageBody);
        CustomerSyncTagMsgDto msgContent = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), CustomerSyncTagMsgDto.class);
        syncTagManager.syncTag(msgContent);
        long time = (System.currentTimeMillis() - start);
        log.info("【RabbitMQ】处理客户同步企业标签至企微MQ消息完成[message:{},time={}ms]", messageBody, time);
    }
}
