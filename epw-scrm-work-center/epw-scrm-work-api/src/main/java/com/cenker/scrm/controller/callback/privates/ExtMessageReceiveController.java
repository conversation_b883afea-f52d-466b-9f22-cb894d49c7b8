package com.cenker.scrm.controller.callback.privates;

import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.service.IWorkCorpCpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.util.crypto.WxCpCryptUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/5/10
 * @Description 内部自研-客户联系密钥回调 当为自研模式时会加载此类
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@ConditionalOnBean(name = {"privateCpStoreConfig"})
@RequestMapping("/customer/callback")
public class ExtMessageReceiveController {

    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;

    @GetMapping
    public String checkAgentReceive(@RequestParam("echostr") String echostr,
                                    @RequestParam("msg_signature") String signature,
                                    @RequestParam("timestamp") String timestamp,
                                    @RequestParam("nonce") String nonce) {
        log.info("【客户联系应用】接收到来自微信服务器的接收事件认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            log.error("【客户联系应用】接收事件认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}], 请求参数非法，请核实!",
                    signature, timestamp, nonce, echostr);
            throw new IllegalArgumentException("【客户联系应用】请求参数非法，请核实!");
        }
        try{
            WxCpService wxCpService = workCorpCpService.getWxCpCustomerServiceByCorpId(CorpInfoProperties.getCorpId());
            if (wxCpService.checkSignature(signature, timestamp, nonce, echostr)) {
                WxCpCryptUtil cryptUtil = new WxCpCryptUtil(wxCpService.getWxCpConfigStorage());
                return cryptUtil.decrypt(echostr);
            }
        }catch (Exception e){
            log.error("【客户联系应用】接收事件认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}], 处理异常",
                    signature, timestamp, nonce, echostr);
            log.error("【客户联系应用】接收事件认证消息处理失败，失败原因：", e);
        }
        return DefaultConstants.ILLEGALITY_REQUEST;
    }
}
