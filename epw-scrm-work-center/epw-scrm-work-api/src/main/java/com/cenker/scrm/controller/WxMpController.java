package com.cenker.scrm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cenker.scrm.base.ExceptionCodeEnum;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.config.WxMpStoreConfig;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.efunds.WechatEngineClient;
import com.cenker.scrm.efunds.config.WechatEngineProperties;
import com.cenker.scrm.efunds.model.WxMpTokenGetReq;
import com.cenker.scrm.efunds.model.WxMpTokenGetRsp;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.entity.system.SysAppConfig;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.service.external.ITbWxExtCustomerWorkService;
import com.cenker.scrm.service.mp.IMpWxUserWorkService;
import com.cenker.scrm.work.model.WxMpPublishArticles;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.common.redis.WxRedisOps;
import me.chanjar.weixin.mp.api.WxMpFreePublishService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishArticles;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishItem;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishList;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RequestMapping("/service/mp")
@RestController
@AllArgsConstructor
@Slf4j
public class WxMpController {

    private final WxMpStoreConfig wxMpStoreConfig;
    private final WxMpService wxMpService;
    private final IMpWxUserWorkService mpWxUserService;
    private final ITbWxExtCustomerWorkService tbWxExtCustomerWorkService;
    private final WechatEngineProperties wechatEngineProperties;
    private final StringRedisTemplate redisTemplate;

    /**
     * 添加微信公众号配置
     *
     * @param appId  公众号appId
     * @param secret 公众号密钥
     * @return 操作结果
     */
    @RequestMapping("/addWxMpConfigStorage")
    public RemoteResult<Boolean> addWxMpConfigStorage(@RequestParam("appId") String appId,
                                                      @RequestParam("secret") String secret,
                                                      @RequestParam("isUpdate") Boolean isUpdate) {
        try {
            // 输入校验
            if (StrUtil.isBlank(appId)) {
                return RemoteResult.error(ExceptionCodeEnum.PARAM_ERROR.getCode(), "参数appId不能为空");
            }
            if (StrUtil.isBlank(secret)) {
                return RemoteResult.error(ExceptionCodeEnum.PARAM_ERROR.getCode(), "参数secret不能为空");
            }

            log.info("【微信公众号】开始添加微信公众号配置，appId={}", appId);

            // 如果是默认配置，则直接返回true
            if (appId.equals(wxMpStoreConfig.getAppId())) {
                return RemoteResult.data(true);
            }

            // 检查是否存在重复配置
            boolean isExist = wxMpService.switchover(appId);
            if (isExist && !isUpdate) {
                return RemoteResult.error(ExceptionCodeEnum.DATA_DUPLICATE.getCode(), "该公众号配置已存在");
            }

            // 配置存储
            WxRedisOps redisOps = new RedisTemplateWxRedisOps(redisTemplate);
            WxMpRedisConfigImpl wxMpRedisConfig = new WxMpRedisConfigImpl(redisOps, CacheKeyConstants.WX_MP_PREFIX);
            wxMpRedisConfig.setAppId(appId);
            wxMpRedisConfig.setSecret(secret);

            // 将配置添加到服务
            wxMpService.addConfigStorage(appId, wxMpRedisConfig);

            log.info("【微信公众号】成功添加微信公众号配置，appId={}", appId);
            return RemoteResult.data(true);
        } catch (Exception e) {
            log.error("【微信公众号】添加微信公众号配置失败，appId={}, 错误信息:{}", appId, e.getMessage(), e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_SYS.getCode(), "系统异常，请稍后再试");
        }
    }

    /**
     * 移除公众号配置
     *
     * @param appId
     * @return
     */
    @RequestMapping("/removeWxMpConfigStorage")
    public RemoteResult<Boolean> removeWxMpConfigStorage(@RequestParam("appId") String appId) {
        if (StrUtil.isBlank(appId)) {
            return RemoteResult.error(ExceptionCodeEnum.PARAM_ERROR.getCode(), "参数appId不能为空");
        }

        if (appId.equals(wxMpStoreConfig.getAppId())) {
            return RemoteResult.data(true);
        }

        wxMpService.removeConfigStorage(appId);
        return RemoteResult.data(true);
    }

    /**
     * 测试公众号配置信息是否正确
     *
     * @param appId 公众号AppId
     * @param serviceAccount 服务号账号（可选）
     * @return
     */
    @RequestMapping("/testConnection")
    public RemoteResult<Boolean> testConnection(@RequestParam("appId") String appId,
                                                @RequestParam("appSecret") String appSecret,
                                                @RequestParam(value = "serviceAccount", required = false) String serviceAccount) {
        log.info("【微信公众号】测试公众号配置信息是否正确, appId={}, serviceAccount={}", appId, serviceAccount);

        try {
            WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
            wxMpConfigStorage.setAppId(appId);
            wxMpConfigStorage.setSecret(appSecret);

            WxMpService mpService = new WxMpServiceImpl();
            mpService.setWxMpConfigStorage(wxMpConfigStorage);

            this.switchover(mpService, appId, serviceAccount);

            mpService.getCallbackIP();
            log.info("微信公众号配置信息验证成功: appId={}, serviceAccount={}", appId,serviceAccount);
            return RemoteResult.data(true);
        } catch (Exception e) {
            log.error("微信公众号配置信息验证失败: appId={}, serviceAccount={}", appId,serviceAccount, e);
            return RemoteResult.data(false);
        }
    }

    /**
     * 获取微信公众号已发布文章列表
     * @param appId 微信公众号AppId
     * @param serviceAccount 服务账号（可选）
     * @param offset 偏移量
     * @param count 获取数量
     * @return 已发布文章列表
     */
    @RequestMapping("/getWxMpPublishArticles")
    public RemoteResult<List<WxMpPublishArticles>> getPublishArticles(@RequestParam("appId") String appId,
                                                                      @RequestParam(value = "serviceAccount", required = false) String serviceAccount,
                                                                      @RequestParam("offset") Integer offset,
                                                                      @RequestParam("count") Integer count) {
        // 参数校验
        if (offset == null || offset < 0) {
            return RemoteResult.error(ExceptionCodeEnum.PARAM_ERROR.getCode(), "offset必须为非负整数");
        }
        if (count == null || count <= 0) {
            log.warn("【微信公众号】获取微信公众号已发布文章列表失败, count参数非法: {}", count);
            return RemoteResult.error(ExceptionCodeEnum.PARAM_ERROR.getCode(), "count必须为正整数");
        }

        log.info("【微信公众号】开始获取微信公众号已发布文章列表, appId={}, serviceAccount={}", appId, serviceAccount);

        try {
            this.switchover(this.wxMpService, appId, serviceAccount);

            WxMpFreePublishService wxMpFreePublishService = wxMpService.getFreePublishService();
            WxMpFreePublishList wxMpFreePublishList = wxMpFreePublishService.getPublicationRecords(offset, count);

            if (Objects.isNull(wxMpFreePublishList) || CollectionUtil.isEmpty(wxMpFreePublishList.getItems())) {
                log.info("【微信公众号】未找到任何已发布文章, appId={}, serviceAccount={}", appId, serviceAccount);
                return RemoteResult.data(new ArrayList<>());
            }

            List<WxMpPublishArticles> publishArticles = convertToPublishArticles(wxMpFreePublishList.getItems());
            log.info("【微信公众号】成功获取微信公众号已发布文章{}条, appId={}, serviceAccount={}", publishArticles.size(), appId, serviceAccount);
            return RemoteResult.data(publishArticles);
        } catch (WxErrorException e) {
            log.error("【微信公众号】获取微信公众号已发布文章列表失败, appId={}, serviceAccount={}", appId, serviceAccount, e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_WX.getCode(), "微信接口调用失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("【微信公众号】获取微信公众号已发布文章列表失败, appId={}, serviceAccount={}", appId, serviceAccount, e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_SYS.getCode(), e.getMessage());
        }
    }

    /**
     * 获取微信用户信息（通过公众号授权，调用公众号API接口）
     *
     * @param code
     * @param state
     * @return
     */
    @RequestMapping("/getWxUserInfo")
    public RemoteResult<MpWxUser> getWxUserInfo(@RequestParam("code") String code, @RequestParam(value = "state", required = false) String state) {
        log.info("【微信授权】获取微信用户信息, code={}, state={}", code, state);

        try {
            //从公众号的API接口获取access_token
            WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service()
                    .getAccessToken(wxMpStoreConfig.getAppId(), wxMpStoreConfig.getAppSecret(), code);

            //从公众号的API接口获取用户信息
            WxOAuth2UserInfo userInfo = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);
            log.info("【微信授权】微信返回用户信息原始报文：{}", userInfo);

            MpWxUser wxUser = mpWxUserService.saveOrUpdateWxUser(userInfo, "");
            log.info("【微信授权】成功获取微信用户信息，微信昵称：{}", wxUser.getNickName());

            // 3.1.4 统一微信昵称/头像
            tbWxExtCustomerWorkService.updateTbWxExtCustomer(wxUser.getUnionId(), wxUser.getNickName(), wxUser.getHeadImgUrl());

            return RemoteResult.data(wxUser);
        }  catch (Exception e) {
            log.error("【微信授权】获取微信用户信息失败！", e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_SYS.getCode(), e.getMessage());
        }
    }
    
    @RequestMapping("/getWxUserInfoByUnionId")
    public RemoteResult<MpWxUser> getWxUserInfoByUnionId(@RequestParam("unionId") String unionId) {
        log.info("【微信授权】获取微信用户信息, userName={}", unionId);

        try {

            MpWxUser wxUser = mpWxUserService.lambdaQuery().eq(MpWxUser::getUnionId, unionId).last("limit 1").one();
            log.info("【微信授权】成功获取微信用户信息，微信昵称：{}", wxUser.getNickName());

            return RemoteResult.data(wxUser);
        }  catch (Exception e) {
            log.error("【微信授权】获取微信用户信息失败！", e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_SYS.getCode(), e.getMessage());
        }
    }


    /**
     * 获取微信公众号access_token
     * 若启用了易方达微信接入层获取 access_token，则必须通过该方法获取 access_token
     *
     * @param appId 公众号AppId
     * @param serviceAccount 服务号账号（可选）
     * @return 切换配置结果
     */
    private boolean switchover(WxMpService mpService, String appId, String serviceAccount) {
        boolean switchResult = mpService.switchover(appId);
        if (!switchResult) {
            log.error("【微信公众号】切换公众号配置失败，appId={}", appId);
            throw new RuntimeException("切换公众号配置失败");
        }

        if (Objects.isNull(wechatEngineProperties)
                || !wechatEngineProperties.isEnableAccess()
                || StrUtil.isBlank(wechatEngineProperties.getAccessUrl())
                || StrUtil.isBlank(serviceAccount)) {
            return true;
        }

        String baseUrl = wechatEngineProperties.getAccessUrl();
        String wechatKey = wechatEngineProperties.getWechatKey();
        WxMpTokenGetReq wxMpTokenGetReq = new WxMpTokenGetReq(baseUrl, serviceAccount, wechatKey);
        WxMpTokenGetRsp wxMpTokenGetRsp = WechatEngineClient.getWxMpToken(wxMpTokenGetReq);
        if (Objects.isNull(wxMpTokenGetRsp) || !wxMpTokenGetRsp.isSuccess()) {
            log.error("【微信公众号】从易方达微信接入层接口获取微信公众号access_token失败！响应信息：{}", JSON.toJSONString(wxMpTokenGetRsp));
            throw new RuntimeException("【微信公众号】获取微信公众号access_token失败！");
        }

        WxMpConfigStorage wxMpConfigStorage = mpService.getWxMpConfigStorage();
        wxMpConfigStorage.updateAccessToken(wxMpTokenGetRsp.getToken(), wxMpTokenGetRsp.getExpiresIn());
        return true;
    }

    /**
     * 将WxMpFreePublishItem列表转换为WxMpPublishArticles列表
     * @param items WxMpFreePublishItem列表
     * @return 转换后的WxMpPublishArticles列表
     */
    private List<WxMpPublishArticles> convertToPublishArticles(List<WxMpFreePublishItem> items) {
        List<WxMpPublishArticles> publishArticles = new ArrayList<>();
        for (WxMpFreePublishItem item : items) {
            WxMpPublishArticles publishArticle = new WxMpPublishArticles();
            publishArticle.setArticleId(item.getArticleId());
            publishArticle.setUpdateTime(item.getUpdateTime());

            List<WxMpPublishArticles.NewsItem> newsItems = BeanUtil.copyToList(item.getContent().getNewsItem(), WxMpPublishArticles.NewsItem.class);
            publishArticle.setNewItems(newsItems);
            publishArticles.add(publishArticle);
        }
        return publishArticles;
    }

}
