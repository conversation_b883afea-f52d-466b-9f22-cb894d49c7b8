package com.cenker.scrm.client.oauth.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.login.H5LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.OAUTH_SERVICE,path = "/oauth")
public interface OauthFeign {
    /**
     * 获取token授权
     * @param loginUser
     * @return
     */
    @RequestMapping(value = "/getToken")
    String getToken(@RequestBody H5LoginUser loginUser);
}
