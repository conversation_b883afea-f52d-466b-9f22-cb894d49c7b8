package com.cenker.scrm.controller;

import com.cenker.scrm.base.ExceptionCodeEnum;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.biz.CustomerSyncTagManager;
import com.cenker.scrm.biz.CustomerTagMessageManager;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerSyncTagMsgDto;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.work.model.WxCpUserDetailVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx/user")
public class WxUserController {

    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    private final RedisCache redisCache;
    private final CustomerTagMessageManager customerTagMessageManager;
    private final CustomerSyncTagManager customerSyncTagManager;

    @GetMapping("/getUserInfo")
    public RemoteResult<WxCpUserDetailVo> getUserInfo(@RequestParam("code") String code,
                                                      @RequestParam("agentId") Integer agentId,
                                                      @RequestParam("corpId") String corpId) {
        log.info("开始获取企微登录用户信息，入参：code：{}，agentId：{}，corpId：{}", code, agentId, corpId);
        try {
            WxCpUserDetailVo userDetailVo = redisCache.getCacheObject(CacheKeyConstants.WECHAT_CODE_USER_INFO + code);
            if (userDetailVo!= null) {
                log.info("企微登录用户信息已缓存，直接返回缓存数据：{}", userDetailVo);
                return RemoteResult.data(userDetailVo);
            }

            userDetailVo = new WxCpUserDetailVo();
            userDetailVo.setAgentId(agentId);
            userDetailVo.setCorpId(corpId);
            // 获取企微接口对象
            WxCpService wxCpService = workCorpCpService.getWxCpAgentServiceByCorpId(corpId);
            WxCpOauth2UserInfo userInfo = wxCpService.getOauth2Service().getUserInfo(agentId, code);

            if (Objects.isNull(userInfo)) {
                log.error("获取企微用户信息失败!");
                return RemoteResult.error(ExceptionCodeEnum.ERROR_WX.getCode(), "获取企微用户信息失败！");
            }

            log.debug("获取企微用户信息成功，userInfo：{}", userInfo);
            BeanUtils.copyProperties(userInfo, userDetailVo);

            WxCpUserDetail userDetail = wxCpService.getOauth2Service().getUserDetail(userInfo.getUserTicket());

            if (userDetail != null) {
                log.debug("获取企微用户敏感信息成功，userDetail：{}", userDetail);
                BeanUtils.copyProperties(userDetail, userDetailVo);
            }

            redisCache.setCacheObject(CacheKeyConstants.WECHAT_CODE_USER_INFO + code, userDetailVo, 1, TimeUnit.DAYS);

            return RemoteResult.data(userDetailVo);
        } catch (Exception e) {
            log.error("获取企微用户信息失败!", e);
            return RemoteResult.error(ExceptionCodeEnum.ERROR_WX.getCode(), e.getMessage());
        }
    }

    @PostMapping("/markTag")
    public RemoteResult<List<String>> markTag(@RequestBody CustomerAddTagMsgDto msgContent) {
        List<String> successExtUserIdList = customerTagMessageManager.handlerCustomerAddTagMessage(msgContent);
        return RemoteResult.data(successExtUserIdList);
    }

    @PostMapping("/removeTag")
    public RemoteResult<List<String>> removeTag(@RequestBody CustomerRemoveTagMsgDto msgContent) {
        List<String> successExtUserIdList = customerTagMessageManager.handlerCustomerRemoveTagMessage(msgContent);
        return RemoteResult.data(successExtUserIdList);
    }

    @PostMapping("/syncTag")
    public RemoteResult<List<String>> syncTag(@RequestBody CustomerSyncTagMsgDto msgContent) {
        List<String> successExtUserIdList = customerSyncTagManager.syncTag(msgContent);
        return RemoteResult.data(successExtUserIdList);
    }
}
