
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.bu.BuLibraryMaterialMsgWorkMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.BuLibraryMaterialMsg">
        <id column="msg_id" property="msgId" jdbcType="VARCHAR"/>
        <result column="msg_type" property="msgType" jdbcType="VARCHAR"/>
        <result column="msg_version" property="msgVersion" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="material_name" property="materialName" jdbcType="VARCHAR"/>
        <result column="material_id" property="materialId" jdbcType="VARCHAR"/>
        <result column="material_desc" property="materialDesc" jdbcType="VARCHAR"/>
        <result column="last_modify_time" property="lastModifyTime" jdbcType="TIMESTAMP"/>
        <result column="storage_path" property="storagePath" jdbcType="VARCHAR"/>
        <result column="material_version" property="materialVersion" jdbcType="VARCHAR"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="file_type" property="fileType" jdbcType="VARCHAR"/>
        <result column="uploader" property="uploader" jdbcType="VARCHAR"/>
        <result column="owner" property="owner" jdbcType="VARCHAR"/>
        <result column="tag_list" property="tagList" jdbcType="VARCHAR"/>
        <result column="meta_data_list" property="metaDataList" jdbcType="VARCHAR"/>
        <result column="publish_module_name" property="publishModuleName" jdbcType="VARCHAR"/>
        <result column="publish_rule" property="publishRule" jdbcType="VARCHAR"/>
        <result column="outer_preview_url" property="outerPreviewUrl" jdbcType="VARCHAR"/>
        <result column="outer_cdn_url" property="outerCdnUrl" jdbcType="VARCHAR"/>
        <result column="outer_short_url" property="outerShortUrl" jdbcType="VARCHAR"/>
        <result column="exist_publish_record" property="existPublishRecord" jdbcType="VARCHAR"/>
        <result column="receive_msg_type" property="receiveMsgType" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        msg_id, msg_type, msg_version, create_time, material_name, material_id, material_desc,
        last_modify_time, storage_path, material_version, department, file_type, uploader,
        owner, tag_list, meta_data_list, publish_module_name, publish_rule, outer_preview_url,
        outer_cdn_url, outer_short_url, exist_publish_record, receive_msg_type
    </sql>
</mapper>