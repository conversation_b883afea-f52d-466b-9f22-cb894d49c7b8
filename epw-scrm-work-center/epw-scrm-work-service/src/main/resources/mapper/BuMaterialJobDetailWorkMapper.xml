<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.bu.BuMaterialJobDetailWorkMapper">

    <select id="selectExistsBySourceId" resultType="com.cenker.scrm.pojo.vo.radar.LibraryMaterialVO">
        select
        bj.id jobId,
        bj.job_name jobName,
        bj.create_by createBy,
        bjd.id detailId,
        bjd.source_id sourceId,
        bjd.material_id materialId,
        twri.title materialName
        from bu_material_job_detail bjd
        left join bu_material_job bj on bjd.job_id = bj.id
        left join tb_wx_radar_interact twri on bjd.material_id = twri.id and twri.del_flag = 0
        where bjd.source_id = #{materialId}

    </select>
</mapper>