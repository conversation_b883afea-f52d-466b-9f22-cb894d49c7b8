<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.external.TbWxExtFollowUserWorkMapper">
    <select id="queryUserIdByCorpIdAndExtUserId" resultType="CustomerFollowVO">
        select user_id userId,external_user_id extUserId,id from tb_wx_ext_follow_user where corp_id = #{corpId}
        -- and status = '0'
        and (external_user_id) in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getFrients" resultType="java.lang.Integer">
        select count(1)
        from tb_wx_ext_follow_user t
        left join tb_wx_user u on t.user_id = u.userid
        where t.external_user_id = #{externalUserId}
        <if test="staffId!= null and staffId!= ''">
            and t.user_id = #{staffId}
        </if>
        and t.status = #{status}
        and u.del_flag = '1'
    </select>
</mapper>