package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.tag.TbWxCorpTagWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.service.tag.ITbWxCorpTagWorkService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/1
 * @Description 企业微信标签Service业务层处理
 */
@Service
@Slf4j
public class TbWxCorpTagWorkServiceImpl extends ServiceImpl<TbWxCorpTagWorkMapper, TbWxCorpTag> implements ITbWxCorpTagWorkService {
    @Override
    public void syncUpdateCorpTag(String corpId, WxCpUserExternalTagGroupList list) {
        if (ObjectUtil.isNull(list)) {
            return;
        }
        List<TbWxCorpTag> tagList = Lists.newArrayList();
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtil.isNotEmpty(tagGroupList)) {
            WxCpUserExternalTagGroupList.TagGroup tagGroup = tagGroupList.get(0);
            if (CollectionUtil.isEmpty(tagGroup.getTag())) {
                return;
            }
            for (WxCpUserExternalTagGroupList.TagGroup.Tag tag1 : tagGroup.getTag()) {
                TbWxCorpTag tag = new TbWxCorpTag();
                tag.setStatus(UserStatus.OK.getCode());
                tag.setCreateBy(Constants.DEFAULT_USER);
                tag.setOrder(tag1.getOrder().intValue());
                tag.setCreateTime(new Date(tag1.getCreateTime() * 1000));
                tag.setName(tag1.getName());
                tag.setCorpId(corpId);
                tag.setGroupId(tagGroup.getGroupId());
                tag.setTagId(tag1.getId());
                if (tag1.getDeleted() != null && tag1.getDeleted()) {
                    tag.setStatus(UserStatus.DELETED.getCode());
                }
                tagList.add(tag);
            }
        }
        if (tagList.size() > 0) {
            LambdaUpdateWrapper<TbWxCorpTag> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TbWxCorpTag::getCorpId,corpId);
            // 一般一个事件只会有一个标签更新
            for (TbWxCorpTag tbWxCorpTag : tagList) {
                wrapper.eq(TbWxCorpTag::getGroupId,tbWxCorpTag.getGroupId());
                wrapper.eq(TbWxCorpTag::getTagId,tbWxCorpTag.getTagId());
                update(tbWxCorpTag,wrapper);
            }
        }
    }

    @Override
    public List<String> selectTbWxCorpTagGroupIdList(String corpId, String[] tagIds) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("corpId", corpId);
        param.put("tagIds", tagIds);
        return baseMapper.selectTbWxCorpTagGroupIdList(param);
    }

    @Override
    public List<String> selectTbWxCorpTagNameList(String corpId, String[] tagIds) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("corpId", corpId);
        param.put("tagIds", tagIds);
        return baseMapper.selectTbWxCorpTagNameList(param);
    }
}
