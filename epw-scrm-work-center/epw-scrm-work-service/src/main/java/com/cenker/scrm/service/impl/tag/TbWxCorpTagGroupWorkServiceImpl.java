package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.tag.TbWxCorpTagGroupWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupWorkService;
import com.cenker.scrm.service.tag.ITbWxCorpTagWorkService;
import com.cenker.scrm.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/1
 * @Description 企业客户标签组Service业务层处理
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxCorpTagGroupWorkServiceImpl extends ServiceImpl<TbWxCorpTagGroupWorkMapper, TbWxCorpTagGroup> implements ITbWxCorpTagGroupWorkService {
    private final ITbWxCorpTagWorkService tbWxCorpTagService;

    @Override
    @RedisLockAspect(key = CacheKeyConstants.SYNC_ADD_CORP_TAG_GROUP, value = "#corpId")
    public void syncAddCorpTagGroup(String corpId, WxCpUserExternalTagGroupList list) {
        if (ObjectUtil.isNull(list)) {
            return;
        }
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtils.isNotEmpty(tagGroupList)) {
            List<TbWxCorpTagGroup> groupList = Lists.newArrayList();
            List<TbWxCorpTag> tagList = Lists.newArrayList();
            for (WxCpUserExternalTagGroupList.TagGroup tagGroup : tagGroupList) {
                TbWxCorpTagGroup group = new TbWxCorpTagGroup();
                group.setCorpId(corpId);
                group.setStatus(UserStatus.OK.getCode());
                group.setType("CUSTOMER");
                group.setSynStatus("Y");
                group.setOrder(tagGroup.getOrder().intValue());
                group.setGroupId(tagGroup.getGroupId());
                group.setGroupName(tagGroup.getGroupName());
                TbWxCorpTagGroup one = getOne(new LambdaQueryWrapper<TbWxCorpTagGroup>()
                        .eq(TbWxCorpTagGroup::getGroupId, tagGroup.getGroupId())
                        .eq(TbWxCorpTagGroup::getCorpId, corpId)
                        .eq(TbWxCorpTagGroup::getStatus, StatusConstants.DEL_FLAG_FALSE)
                        .last("limit 1")
                );
                if (ObjectUtil.isNull(one)) {
                    group.setCreateBy(Constants.DEFAULT_USER);
                    groupList.add(group);
                }
                if (CollectionUtils.isNotEmpty(tagGroup.getTag())) {
                    for (WxCpUserExternalTagGroupList.TagGroup.Tag tag1 : tagGroup.getTag()) {
                        TbWxCorpTag tag = new TbWxCorpTag();
                        tag.setOrder(tag1.getOrder().intValue());
                        tag.setStatus(UserStatus.OK.getCode());
                        tag.setCreateTime(new Date(tag1.getCreateTime() * 1000));
                        tag.setName(tag1.getName());
                        tag.setGroupId(tagGroup.getGroupId());
                        tag.setCorpId(corpId);
                        tag.setTagId(tag1.getId());
                        TbWxCorpTag one1 = tbWxCorpTagService.getOne(new LambdaQueryWrapper<TbWxCorpTag>()
                                .eq(TbWxCorpTag::getTagId, tag1.getId())
                                .eq(TbWxCorpTag::getCorpId, corpId)
                                .eq(TbWxCorpTag::getStatus, StatusConstants.DEL_FLAG_FALSE)
                                .last("limit 1")
                        );
                        if (ObjectUtil.isNull(one1)) {
                            tag.setCreateBy(Constants.DEFAULT_USER);
                            tagList.add(tag);
                        }
                    }
                }
            }
            if (groupList.size() > 0) {
                saveBatch(groupList);
            }
            if (tagList.size() > 0) {
                tbWxCorpTagService.saveBatch(tagList);
            }
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncUpdateCorpTagGroup(String corpId, WxCpUserExternalTagGroupList list) {
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtils.isNotEmpty(tagGroupList)) {
            List<TbWxCorpTagGroup> groupExistList = Lists.newArrayList();
            for (WxCpUserExternalTagGroupList.TagGroup tagGroup : tagGroupList) {
                TbWxCorpTagGroup group = new TbWxCorpTagGroup();
                group.setCorpId(corpId);
                group.setOrder(tagGroup.getOrder().intValue());
                group.setGroupId(tagGroup.getGroupId());
                group.setGroupName(tagGroup.getGroupName());
                // 如果是存在的标签组 则进行更新
                group.setGroupName(tagGroup.getGroupName());
                group.setOrder(tagGroup.getOrder().intValue());
                group.setUpdateTime(DateUtils.getNowDate());
                groupExistList.add(group);
            }
            if (groupExistList.size() > 0) {
                LambdaUpdateWrapper<TbWxCorpTagGroup> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(TbWxCorpTagGroup::getCorpId, corpId);
                // 一般一个事件只会有一个标签组更新
                for (TbWxCorpTagGroup tbWxCorpTagGroup : groupExistList) {
                    wrapper.eq(TbWxCorpTagGroup::getGroupId, tbWxCorpTagGroup.getGroupId());
                    update(tbWxCorpTagGroup, wrapper);
                }
            }
        }
    }

    @Override
    public String selectTbWxCorpTagGroupNameByTagId(String tagId) {
        return baseMapper.selectTbWxCorpTagGroupNameByTagId(tagId);
    }

}
