package com.cenker.scrm.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.config.LibraryRabbitMQConfig;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.constants.LibraryRabbitMqQueuesConstant;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.AppTypeEnum;
import com.cenker.scrm.enums.MaterialInfoAcquisitionMethod;
import com.cenker.scrm.enums.SysNoticeEnum;
import com.cenker.scrm.manager.LibraryRabbitMessageProducer;
import com.cenker.scrm.pojo.dto.system.SysNoticeMsgDto;
import com.cenker.scrm.pojo.entity.system.BuMaterialJob;
import com.cenker.scrm.pojo.entity.system.BuMaterialJobDetail;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.LibraryMaterialVO;
import com.cenker.scrm.pojo.vo.radar.MaterialCancelNoticeVO;
import com.cenker.scrm.pojo.vo.radar.MaterialNoticeVO;
import com.cenker.scrm.pojo.vo.radar.MaterialResponseVO;
import com.cenker.scrm.service.bu.IBuMaterialJobDetailWorkService;
import com.cenker.scrm.service.bu.IBuMaterialJobWorkService;
import com.cenker.scrm.service.bu.ITbWxRadarContentWorkService;
import com.cenker.scrm.service.bu.ITbWxRadarInteractWorkService;
import com.cenker.scrm.service.sys.ISysUserWorkService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.messagebuilder.NewsBuilder;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Library RabbitMQ 通知消息消费者
 * 手动确认模式，消费通知消息队列的消息，成功消费时调用生产者投递应答消息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LibraryNotificationConsumer {

    private final LibraryRabbitMessageProducer libraryRabbitMessageProducer;
    private final LibraryRabbitMQConfig libraryRabbitMQConfig;
    private final IBuMaterialJobWorkService buMaterialJobWorkService;
    private final IBuMaterialJobDetailWorkService buMaterialJobDetailWorkService;
    private final ITbWxRadarInteractWorkService tbWxRadarInteractWorkService;
    private final ITbWxRadarContentWorkService tbWxRadarContentWorkService;
    private final ISysUserWorkService sysUserService;
    private final RedisCache redisCache;
    private final MqSendMessageManager mqSendMessageManager;

    /**
     * 消费通知消息队列的消息
     * 使用手动确认模式，成功处理后发送应答消息并确认消息
     * 
     * @param message 接收到的消息
     * @param deliveryTag 消息投递标签
     * @param channel RabbitMQ 通道
     */
    @RabbitListener(
            queues = "#{libraryRabbitMQConfig.getNotificationQueueName()}", 
            containerFactory = LibraryRabbitMqQueuesConstant.LIBRARY_MQ_FACTORY_NAME_MANUAL_ACK
    )
    public void onNotificationMessage(Message message, 
                                    @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, 
                                    Channel channel) throws IOException {

        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        String messageId = message.getMessageProperties().getMessageId();
        
        log.info("【Library RabbitMQ】接收到通知消息，消息ID：{}, 消息内容：{}", messageId, messageBody);
        
        try {
            // 处理通知消息的业务逻辑
            Result processResult = Result.error(500, "未知错误");
            if (messageBody != null && messageBody.contains("existPublishRecord")) {
                // 取消发布消息
                MaterialCancelNoticeVO msgContent = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), MaterialCancelNoticeVO.class);
                processCancelNotificationMessage(msgContent, messageId);
            } else {
                MaterialNoticeVO msgContent = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), MaterialNoticeVO.class);
                processNotificationMessage(msgContent, messageId);
            }
            if (processResult.isSuccess()) {
                // 业务处理成功，发送应答消息
                String replyMessage = buildReplyMessage(messageBody, messageId, true);
                libraryRabbitMessageProducer.sendReplyMessage(replyMessage, libraryRabbitMQConfig.getReplyRoutingKey());
                
                // 手动确认消息
                channel.basicAck(deliveryTag, false);
                log.info("【Library RabbitMQ】通知消息处理成功并已确认，消息ID：{}", messageId);
                
            } else if (400 == processResult.getCode()) {
                // 拒绝消息，丢弃
                channel.basicReject(deliveryTag, false);
                log.warn("【Library RabbitMQ】通知消息类型不支持生成智能物料，已拒绝，消息ID：{}", messageId);
            } else {
                // 业务处理失败
                // 否定确认消息，重新入队
                channel.basicNack(deliveryTag, false, true);
                log.warn("【Library RabbitMQ】通知消息未匹配到任务，进行否定确认，消息ID：{}", messageId);
            }
            
        } catch (Exception e) {
            log.error("【Library RabbitMQ】处理通知消息异常，消息ID：{}, 异常信息：{}", messageId, e.getMessage(), e);
            
            try {
                // 发送异常应答消息
                // 否定确认消息，重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (Exception replyException) {
                log.error("【Library RabbitMQ】发送异常应答消息失败，消息ID：{}, 异常信息：{}", 
                        messageId, replyException.getMessage(), replyException);
                // 否定确认消息，重新入队
                channel.basicNack(deliveryTag, false, true);
            }
        }
    }
    /**
     * 处理取消发布通知消息的业务逻辑
     *
     * @param msgContent 消息内容
     * @param messageId 消息ID
     * @return 处理结果，true表示成功，false表示失败
     */
    private Result processCancelNotificationMessage(MaterialCancelNoticeVO msgContent, String messageId) {
        log.info("【Library RabbitMQ】开始处理取消发布通知消息业务逻辑，消息ID：{}", messageId);
        try {
            if ("1".equals(msgContent.getExistPublishRecord())) {
                // 取消发布
                // 1、查询智能物料
                List<LibraryMaterialVO> list = buMaterialJobDetailWorkService.selectExistsBySourceId(msgContent.getMaterialId());
                if (!CollectionUtils.isEmpty(list)) {
                    Map<String, List<LibraryMaterialVO>> jobMap = list.stream().collect(Collectors.groupingBy(LibraryMaterialVO::getJobId));
                    StringBuilder sb = new StringBuilder();
                    sb.append("检测到物料库内容变更，已自动删除关联物料【任务名称】：每日财经早报  【删除物料】：3月15日市场分析（等3篇）");
                    for (String jobId : jobMap.keySet()) {
                        List<LibraryMaterialVO> libraryMaterialVOS = jobMap.get(jobId);
                        int index = 0;
                        SysNoticeMsgDto sysNoticeMsgDto = new SysNoticeMsgDto();
                        // 2、删除智能物料
                        for (LibraryMaterialVO jobDetail : libraryMaterialVOS) {
                            if (index == 0) {
                                sysNoticeMsgDto.setNoticeUser(jobDetail.getCreateBy());
                                sb.append("检测到物料库内容变更，已自动删除关联物料【任务名称】：").append(jobDetail.getJobName()).append("  【删除物料】：");
                                index++;
                            }
                            sb.append(jobDetail.getMaterialName());
                            sb.append("、");
                            // 2.1、删除智能物料
                            tbWxRadarContentWorkService.removeById(jobDetail.getMaterialId());
                            // 2.2、删除智能物料交互记录
                            tbWxRadarInteractWorkService.removeById(jobDetail.getMaterialId());
                        }
                        if (sb.lastIndexOf("、") == sb.length() - 1) {
                            sb.deleteCharAt(sb.length() - 1);
                        }
                        sb.append("（等" + libraryMaterialVOS.size() + "篇）");
                        // 3、发送通知消息
                        sysNoticeMsgDto.setNoticeTitle("物料取消发布成功应答");
                        sysNoticeMsgDto.setCorpId(CorpInfoProperties.getCorpId());
                        sysNoticeMsgDto.setNoticeContent(sb.toString());
                        sysNoticeMsgDto.setAgentMsgContent(sb.toString());
                        sysNoticeMsgDto.setNoticeMode(SysNoticeEnum.NOTICE_MODE_ADMIN.getCode()+"");
                        mqSendMessageManager.sendSysMsg(sysNoticeMsgDto);
                    }
                }
            }

            log.info("【Library RabbitMQ】取消发布通知消息业务逻辑处理完成，消息ID：{}", messageId);
            return Result.success();

        } catch (Exception e) {
            log.error("【Library RabbitMQ】处理取消发布通知消息业务逻辑异常，消息ID：{}, 异常信息：{}",
                    messageId, e.getMessage(), e);
            return Result.error(500, "处理取消发布通知消息业务逻辑异常：" + e.getMessage());
        }
    }

    /**
     * 处理发布通知消息的业务逻辑
     * 
     * @param messageBody 消息内容
     * @param messageId 消息ID
     * @return 处理结果，true表示成功，false表示失败
     */
    private Result processNotificationMessage(MaterialNoticeVO messageBody, String messageId) {
        log.info("【Library RabbitMQ】开始处理通知消息业务逻辑，消息ID：{}", messageId);
        
        try {
            // 例如：解析消息内容，调用相关服务处理业务
            /**
             * 1、判断是否图片类型       - file_type=003
             *  1.1、若不是，则发布Result.error(400, "非图片类型")
             *  1.2、若是的话，则调用buMaterialJobWorkService查询数据中物料库类型且是启用的定时任务 jobList
             *  若出现如下3种情况，则返回Result.error(300, "未匹配，重新发布")
             *  1）jobList为空
             *  2）jobList中的任务匹配 消息中的tag_list和keyword不匹配
             *      匹配规则：- tag_list 有多个标签，以半角逗号分隔
             *         - keyword 有多个标签，以半角逗号分隔
             *         - tag_list匹配到 keyword 配置的标签中的任意一个，则匹配成功
             *  3）jobList中存在匹配的任务，但是调用buMaterialJobDetailWorkService查询到该消息已经生成了智能物料，故不生成
             *     - 关联关系：bu_material_job_detail表的source_id 保存 “物料发布通知”中的material_id
             * 2、若jobList中的任务匹配成功，需要生成智能物料，则将消息中的数据转换
             * 3、调用buMaterialJobDetailWorkService保存物料库生成智能物料数据记录
             * 4、调用tbWxRadarInteractWorkService保存智能物料记录
             * 5、调用tbWxRadarContentWorkService保存智能物料内容记录
             * 6、调用企微方法，发送企微通知，发送的智能物料消息需要带上智能物料的链接、封面、卡片等
             * 7、返回Result.success()
             **/

            // 1、判断是否图片类型
            if (!"003".equals(messageBody.getFileType())) {
                return Result.error(400, "非图片类型");
            }

            // 1.2、查询物料库类型且是启用的定时任务 jobList
            List<BuMaterialJob> jobList = buMaterialJobWorkService.lambdaQuery()
                    .eq(BuMaterialJob::getJobStatus, 1)
                    .eq(BuMaterialJob::getAppType, AppTypeEnum.MATERIAL_LIBRARY.name())
                    .list();
            if (CollectionUtils.isEmpty(jobList)) {
                return Result.error(300, "未匹配，重新发布");
            }

            // 检查是否有匹配的任务
            Map<BuMaterialJob, BuMaterialJobDetail> jobDetailMap = getBuMaterialJobBuMaterialJobDetailMap(messageBody, jobList);

            if (jobDetailMap.isEmpty()) {
                return Result.error(300, "未匹配，重新发布");
            }

            // 根据 jobDetailMap 生成对应的智能物料
            for (BuMaterialJob job : jobDetailMap.keySet()) {
                makeMaterial(job, jobDetailMap.get(job));
            }

            
            log.info("【Library RabbitMQ】通知消息业务逻辑处理完成，消息ID：{}", messageId);
            return Result.success();
            
        } catch (Exception e) {
            log.error("【Library RabbitMQ】处理通知消息业务逻辑异常，消息ID：{}, 异常信息：{}", 
                    messageId, e.getMessage(), e);
            return Result.error(500, "处理通知消息业务逻辑异常：" + e.getMessage());
        }
    }

    /**
     * 智能物料生成逻辑
     * 读取物料库图片，生成智能物料
     * @param materialJob
     */
    private void makeMaterial(BuMaterialJob materialJob, BuMaterialJobDetail materialDetail) {
        // 一篇文章需要生成的智能物料数量
        Integer materialNumber = materialJob.getMaterialNumber();
        // 计划生成智能物料数量
        int targetMaterialNumber = materialNumber;
        log.info("【物料库 MQ 处理】任务【{}】开始生成智能物料，生成物料数量：{}", materialJob.getJobName(), targetMaterialNumber);

        List<TbWxRadarContent> materialContentList = new ArrayList<>();

        for (int j = 0; j < materialNumber; j++) {
            boolean isSameName = StrUtil.isNotBlank(materialJob.getNamingRule());
            String serialNumber = generateSerialNumber(isSameName,1, materialNumber, 0, j);

            // 构建智能物料信息，并保存到数据库
            TbWxRadarInteract material = buildMaterialInfo(materialJob, materialDetail.getTitle(), serialNumber);
            tbWxRadarInteractWorkService.save(material);

            // 构建智能物料卡片信息，并保存到数据库
            TbWxRadarContent content = buildMaterialContent(material.getId(), materialJob, materialDetail);
            tbWxRadarContentWorkService.save(content);
            materialContentList.add(content);

            // 保存来源物料信息到数据库
            BuMaterialJobDetail jobDetail = BeanUtil.copyProperties(materialDetail, BuMaterialJobDetail.class);
            jobDetail.setMaterialId(material.getId());
            buMaterialJobDetailWorkService.save(jobDetail);

            log.info("【物料库 MQ 处理】任务【{}】生成智能物料：{}", material.getTitle());
        }

        sendMsg(materialJob, materialContentList);
        log.info("【物料库 MQ 处理】任务【{}】生成智能物料成功！", materialJob.getJobName());
    }

    /**
     * 制作成功，发送应用消息通知相关人员
     * 应用消息以图片卡片方式展示，一次应用消息通知支持最多 8 个图文消息，如果生成物料超过 8 条，则拆封成多条应用消息通知。
     * 首个图文消息固定展示通知内容：智能物料【物料标题】已通过任务【任务名称】自动创建成功！
     * 其他图文消息展示每条生成的物料信息：物料标题、封面图等
     *
     * @param materialJob 物料任务信息
     * @param materialContentList 生成的智能物料内容列表
     */
    private void sendMsg(BuMaterialJob materialJob, List<TbWxRadarContent> materialContentList) {
        // 参数校验
        if (materialJob == null) {
            log.warn("【物料库 MQ 处理】发送通知失败：物料任务信息为空");
            return;
        }

        if (CollectionUtil.isEmpty(materialContentList)) {
            log.info("【物料库 MQ 处理】任务【{}】未生成智能物料，跳过发送通知", materialJob.getJobName());
            return;
        }

        if (materialJob.getMaterialScope() == 1 && StrUtil.isBlank(materialJob.getNoticeUser())) {
            log.info("【物料库 MQ 处理】任务【{}】未配置通知用户，跳过发送通知", materialJob.getJobName());
            return;
        }

        // 个人物料，通知用户为创建人
        if (materialJob.getMaterialScope() == 2) {
            SysUser user = sysUserService.selectUserById(Long.valueOf(materialJob.getCreateBy()));
            if (user == null || StrUtil.isBlank(user.getCorpUserId())) {
                log.warn("【物料库 MQ 处理】任务【{}】通知用户不存在，跳过发送通知", materialJob.getJobName());
                return;
            }
            materialJob.setNoticeUser(user.getCorpUserId());
        }

        try {
            log.info("【物料库 MQ 处理】开始发送任务【{}】制作智能物料成功通知，物料数量：{}",
                    materialJob.getJobName(), materialContentList.size());

            // 处理通知用户，将逗号分隔的用户ID转换为竖线分隔的格式
            String noticeUser = StrUtil.join("|", StrUtil.split(materialJob.getNoticeUser(), StrUtil.COMMA));

            // 创建通知文章（标题文章）
            NewArticle noticeArticle = createNoticeArticle(materialJob);

            // 企业微信限制每条图文消息最多包含8个图文，首条固定为通知内容，所以每批次最多可以包含7个物料
            // 需要将物料列表分批发送
            int batchSize = 7; // 每批次最多包含7个物料（加上1个通知文章，共8个）
            int totalBatches = NumberUtil.ceilDiv(materialContentList.size(), batchSize); // 向上取整计算批次数

            log.info("【物料库 MQ 处理】任务【{}】需要分{}批次发送通知", materialJob.getJobName(), totalBatches);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                // 计算当前批次的起始和结束索引
                int fromIndex = batchIndex * batchSize;
                int toIndex = Math.min(fromIndex + batchSize, materialContentList.size());

                // 获取当前批次的物料列表
                List<TbWxRadarContent> batchMaterials = materialContentList.subList(fromIndex, toIndex);

                // 创建消息对象
                WxCpMessage wxCpMessage = new WxCpMessage();
                wxCpMessage.setMsgType(WxCpConsts.AppChatMsgType.NEWS);
                wxCpMessage.setToUser(noticeUser);

                // 构建图文消息
                NewsBuilder newsBuilder = new NewsBuilder();

                // 添加首条通知图文（每批次都需要添加）
                newsBuilder.addArticle(noticeArticle);

                // 添加当前批次的物料文章
                for (TbWxRadarContent materialContent : batchMaterials) {
                    if (materialContent != null && StrUtil.isNotBlank(materialContent.getId())) {
                        NewArticle article = createMaterialArticle(materialContent);
                        newsBuilder.addArticle(article);
                    }
                }

                // 设置图文消息内容并发送
                wxCpMessage.setArticles(newsBuilder.build().getArticles());
                mqSendMessageManager.sendAgentMessage(wxCpMessage);

                log.info("【物料库 MQ 处理】任务【{}】批次{}/{}发送成功，包含{}个物料", materialJob.getJobName(), batchIndex + 1, totalBatches, batchMaterials.size());
            }

            log.info("【物料库 MQ 处理】任务【{}】制作智能物料成功通知发送完成，共发送{}批次，{}个物料", materialJob.getJobName(), totalBatches, materialContentList.size());
        } catch (Exception e) {
            log.error("【物料库 MQ 处理】任务【{}】发送通知失败：{}", materialJob.getJobName(), e.getMessage(), e);
        }
    }

    /**
     * 创建物料文章通知
     *
     * @param materialContent 物料内容
     * @return 物料图文对象
     */
    private NewArticle createMaterialArticle(TbWxRadarContent materialContent) {
        NewArticle article = new NewArticle();

        // 设置文章基本信息
        article.setTitle(materialContent.getTitle());
        article.setDescription(materialContent.getDigest());

        // 构建物料详情页URL
        String contentId = materialContent.getId();
        String url = RadarConfig.getContentPage() + "?id=" + contentId;
        article.setUrl(url);

        // 设置封面图
        article.setPicUrl(materialContent.getCover());

        return article;
    }

    /**
     * 创建首条通知
     *
     * @param materialJob 物料任务信息
     * @return 通知图文对象
     */
    private NewArticle createNoticeArticle(BuMaterialJob materialJob) {
        NewArticle noticeArticle = new NewArticle();
        String msgContent = String.format("物料库 MQ 处理任务【%s】成功创建智能物料！", materialJob.getJobName());
        noticeArticle.setTitle(msgContent);
        noticeArticle.setDescription(msgContent);

        // 获取默认图片URL
        String defaultPicUrl = redisCache.getCacheObject(DefaultConstants.DEFAULT_MSG_PIC);
        noticeArticle.setPicUrl(defaultPicUrl);

        return noticeArticle;
    }

    /**
     * 获取摘要
     * @param digest
     * @return
     */
    private static String getDigest(String digest) {
        return StrUtil.isBlank(StrUtil.trim(digest)) ? "点击查看" : digest;
    }

    /**
     * 拼装智能物料卡片信息
     * 若来源为自动获取，则直接取文章标题、摘要、封面图地址、文章链接等信息；
     * 若来源为手动输入，则取任务配置中的标题、摘要、封面图地址、文章链接等信息；
     *
     * @param radarId
     * @param materialJob
     * @param materialDetail
     * @return
     */
    private TbWxRadarContent buildMaterialContent(String radarId, BuMaterialJob materialJob, BuMaterialJobDetail materialDetail) {
        TbWxRadarContent content = new TbWxRadarContent();
        content.setRadarId(radarId);
        content.setAuthor(materialDetail.getAuthor());
        content.setContent(materialDetail.getContent());
        content.setTips(redisCache.getCacheObject(DefaultConstants.DEFAULT_RADAR_TIP));
        content.setCreateBy(materialJob.getCreateBy());
        content.setCreateTime(new Date());

        // 若来源为手动输入，则取任务配置中的标题
        JSONObject cardInfo = JSON.parseObject(materialJob.getCustomInfo());
        content.setTitle(cardInfo.getString("title"));
        content.setCover(cardInfo.getString("cover"));
        content.setDigest(getDigest(cardInfo.getString("digest")));

        return content;
    }
    /**
     * 构建智能物料信息
     * @param sourceMaterialTitle
     * @param materialJob
     * @return
     */
    private TbWxRadarInteract buildMaterialInfo(BuMaterialJob materialJob, String sourceMaterialTitle, String serialNumber) {
        String materialTitle = getMaterialTitle(sourceMaterialTitle, materialJob.getNamingRule(), serialNumber);

        TbWxRadarInteract material = new TbWxRadarInteract();
        material.setTitle(materialTitle);
        material.setCorpId(materialJob.getCorpId());
        material.setScope(materialJob.getMaterialScope());
        material.setType(materialJob.getMaterialType());
        material.setNamingRule(materialJob.getNamingRule());
        material.setCategoryId(materialJob.getMaterialCategoryId());
        material.setDeptId(materialJob.getDeptId());
        material.setCreateBy(materialJob.getCreateBy());
        material.setCreateTime(new Date());
        // 默认开启行为通知
        material.setBehaviorInform(StatusConstants.OPEN_FLAG);
        // 默认开启动态通知
        material.setDynamicInform(StatusConstants.OPEN_FLAG);
        return material;
    }

    /**
     * 智能物料标题处理
     * 1、若有启用命名规则，则直接取任务配置中的命名规则去自动生成标题：
     * 1.1 命名规则变量：固定字符FIXED_CHAR、分隔符SEPARATOR、自定义字符CUSTOM_CHAR、时间戳TIME_STAMP、自定义选项CUSTOM_OPTION
     * 1.2 命名规则示例：[{"variable":"CUSTOM_CHAR","value":"CUSTOM","sort":0},{"variable":"FIXED_CHAR","value":"FIX","sort":1},{"variable":"TIME_STAMP","value":"20250321","sort":2},{"variable":"SEPARATOR","value":"_","sort":3},{"variable":"SEPARATOR","value":"—","sort":4},{"variable":"CUSTOM_OPTION","options":"选项1,选项2","value":"选项1","sort":5},{"variable":"TIME_STAMP","value":"20250408","sort":6}]
     * 1.3 按命名规则拼装标题：sort越小越靠前，若sort相同，则按照变量名排序; 时间戳TIME_STAMP变量，取值取当天的日期，如20250321；其他变量直接取value值
     * 1.4 标题重名自动在末尾加上序号，示例：“物料标题 1”、“物料标题2”；在统一命名的情况下，若匹配到两篇及以上的文章，并且生成次数达到 2 次及以上，物料标题的命名格式应为：“物料标题1.1”、“物料标题1.2”、……
     *
     * 2、若命名规则未开启，则无需填写物料标题，直接采用公众号文章标题；
     * 3、若物料标题重名自动在末尾加上序号（当 index >= 1，才需要加序号）
     *
     * @param sourceTitle 公众号文章标题
     * @param namingRule 命名规则
     * @param serialNumber 序号
     * @return
     */
    private static String getMaterialTitle(String sourceTitle, String namingRule, String serialNumber) {
        if (StrUtil.isBlank(namingRule)) {
            // 若命名规则未开启，则无需填写物料标题，直接采用公众号文章标题
            return sourceTitle + serialNumber;
        }

        try {
            List<Map<String, Object>> ruleList = JSON.parseObject(namingRule, new TypeReference<List<Map<String, Object>>>() {});
            ruleList.sort((a, b) -> {
                int sortA = (int) a.get("sort");
                int sortB = (int) b.get("sort");
                if (sortA != sortB) {
                    return sortA - sortB;
                }
                return ((String) a.get("variable")).compareTo((String) b.get("variable"));
            });

            String today = DateUtil.format(new Date(), "yyyyMMdd");
            StringBuilder titleBuilder = new StringBuilder();
            for (Map<String, Object> rule : ruleList) {
                String variable = (String) rule.get("variable");
                switch (variable) {
                    case "TIME_STAMP":
                        titleBuilder.append(today);
                        break;
                    case "SEPARATOR":
                    case "FIXED_CHAR":
                    case "CUSTOM_CHAR":
                    case "CUSTOM_OPTION":
                        titleBuilder.append(rule.get("value"));
                        break;
                }
            }

            // 若命名规则中不包含时间戳变量，则必须在标题末尾加上当天日期
            boolean containsTimeStamp = ruleList.stream().anyMatch(r -> "TIME_STAMP".equals(r.get("variable")));
            if (!containsTimeStamp) {
                titleBuilder.append(StrUtil.UNDERLINE).append(today);
            }

            // 若标题重名自动在末尾加上序号
            titleBuilder.append(serialNumber);
            return titleBuilder.toString();
        } catch (Exception e) {
            log.error("【物料库 MQ 处理】智能物料【{}】生成标题失败！", sourceTitle, e);
            return sourceTitle + serialNumber;
        }
    }
    
    /**
     * 智能物料卡片封面图处理
     * 若封面图超过限制大小，需要进行自动压缩
     * 若封面图为空，则返回null
     *
     * @param cover
     * @return
     */
//    private String getMaterialCoverUrl(String cover) {
//        if (StrUtil.isBlank(cover)) {
//            log.info("【物料库 MQ 处理】智能物料【{}】封面图为空，返回默认封面图", cover);
//            return redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER);
//        }
//
//        // 若封面图超过限制大小，需要进行自动压缩
//        List<String> coverUrlList = fileService.compressImage(Arrays.asList(cover));
//        if (CollectionUtil.isEmpty(coverUrlList)) {
//            log.info("【物料库 MQ 处理】智能物料【{}】封面图压缩失败，返回默认封面图", cover);
//            return redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER);
//        }
//
//        return coverUrlList.get(0);
//    }
    /**
     * 获取智能物料标题序号
     * 规则：
     * 1、标题重名自动在末尾加上序号，示例：“物料标题 1”、“物料标题2”；
     * 2、在统一命名的情况下，若匹配到两篇及以上的文章，并且生成次数达到 2 次及以上，物料标题的命名格式应为：“物料标题1.1”、“物料标题1.2”、……
     * @param isSameName 是否为统一命名
     * @param sourceSize 来源物料数量
     * @param materialNumber 生成次数
     * @param i 当前来源物料序号
     * @param j 当前生成物料序号
     * @return
     */
    private static String generateSerialNumber(boolean isSameName, int sourceSize, Integer materialNumber, int i, int j) {
        if (isSameName && sourceSize > 1 && materialNumber > 1) {
            // 统一命名且来源物料数量大于1且生成次数大于1
            return String.format("_%d.%d", i + 1, j + 1);
        } else if (j > 0) {
            // 当前生成物料序号大于0，需要加序号
            return String.format("_%d", j + 1);
        } else {
            // 默认情况下不需要加序号
            return "";
        }
    }

    /**
     * 检查规则是否匹配，并检查是否已经生成过。
     * 返回 job和待生成jobDetail的映射
     * @param messageBody
     * @param jobList
     * @return
     */
    private Map<BuMaterialJob, BuMaterialJobDetail> getBuMaterialJobBuMaterialJobDetailMap(MaterialNoticeVO messageBody, List<BuMaterialJob> jobList) {
        Map<BuMaterialJob, BuMaterialJobDetail> jobDetailMap = new HashMap<>();
        for (BuMaterialJob job : jobList) {
            // 匹配规则检查
            if (isTagMatched(messageBody.getTagList(), job.getKeyword())) {
                // 检查是否已生成过智能物料
                if (buMaterialJobDetailWorkService.existsBySourceId(job.getId(), messageBody.getMaterialId())) {
                    continue;
                }
                /**
                 * {"meta_data_list":"允许自动发布=true&指数对外材料类型=R01",
                 * "create_time":"2024-02-02 13:45:00",
                 * "last_modify_time":"2024-02-02 13:39:34",
                 * "material_version":"NzIyQzM3OTAtQzE4RC0xMUVFLTg3NjMtMzdFMTAwQzAyN0ND",
                 * "msg_version":"V1.3",
                 * "storage_path":"/local-material-test03/指数投资部-任意类型/物料库项目一期_IT准出测试报告.docx",
                 * "file_type":"004",
                 * "tag_list":"公共标签/基金产品/被动股票指数类/000950-易方达沪深300非银联接A",
                 * "msg_type":"001","material_id":"MATL_NO2024020213393454701",
                 * "publish_module_name":"M02",
                 * "msg_id":"ECMC202402021345001411",
                 * "department":"指数投资部",
                 * "material_name":"物料库项目一期_IT准出测试报告.docx",
                 * "material_desc":"这一段摘要说明",
                 * "uploader":"pengjie",
                 * "owner":"pengjie"
                 * }
                 */

                /**
                 * {
                 *     "title": "图片",
                 *     "contactStatus": 1,
                 *     "matchType": 1,
                 *     "tbWxRadarContent": {
                 *         "type": 1,
                 *         "baseUrl": "",
                 *         "title": "图片",
                 *         "content": "<p><img src=\"https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/4509094e-1738-4193-acc9-f403cbf552b7.blob\" /></p>",
                 *         "digest": "摘要",
                 *         "cover": "https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/50daea40-2eb1-4ee8-9b47-fbf48386c9b2.jpg",
                 *         "author": "author",
                 *         "remark": "",
                 *         "baseReadNum": "",
                 *         "tips": "风险提示：本资料仅限于客户陪伴之目的，不构成易方达基金任何宣传推介材料、投资建议或投资收益保证。本资料的观点分析及内容展示基于相关公开信息整理，不排除信息后续发生任何更新变化，不对该等信息的完整性、及时性作保证。未经易方达基金书面同意，禁止非合作方摘引截取或以其他方式转载或传播本资料。基金有风险，投资须谨慎。投资者不应以该等信息取代其独立判断或仅根据该等信息作出投资决策。请投资者详阅基金法律文件，在全面了解基金产品的风险收益特征、运作特点及销售机构适当性意见的基础上，审慎作出投资决策。"
                 *     },
                 *     "behaviorInform": 1,
                 *     "dynamicInform": 1,
                 *     "customerTag": 0,
                 *     "viewPerm": "onlyWeComFri",
                 *     "namingRule": "",
                 *     "categoryId": "133",
                 *     "id": "",
                 *     "type": 1,
                 *     "scope": 1,
                 *     "tbWxRadarTagRuleList": null
                 * }
                 */
                // 2、转换数据为智能物料
//                    TbWxRadarInteract smartMaterial = convertToSmartMaterial(messageBody, job);
                // 3、保存物料库生成记录
                BuMaterialJobDetail materialJobDetail = new BuMaterialJobDetail();
                materialJobDetail.setJobId(job.getId());
                materialJobDetail.setSourceId(messageBody.getMaterialId());

                materialJobDetail.setTitle(messageBody.getMaterialName());
                materialJobDetail.setCreateTime(new Date());
                materialJobDetail.setDigest(messageBody.getMaterialDesc());
                materialJobDetail.setContent(messageBody.getOuterPreviewUrl());
                jobDetailMap.put(job, materialJobDetail);
            }
        }
        return jobDetailMap;
    }

    // 标签匹配检查方法
    private boolean isTagMatched(String tagList, String keywords) {
        if (StrUtil.isEmpty(tagList) || StrUtil.isEmpty(keywords)) {
            return false;
        }

        Set<String> messageTags = new HashSet<>(Arrays.asList(tagList.split(",")));
        Set<String> jobKeywords = new HashSet<>(Arrays.asList(keywords.split(",")));

        // 检查是否有交集
        messageTags.retainAll(jobKeywords);
        return !messageTags.isEmpty();
    }

    // 数据转换方法
    private TbWxRadarInteract convertToSmartMaterial(MaterialNoticeVO source, BuMaterialJob job) {
        TbWxRadarInteract target = new TbWxRadarInteract();
//        target.setId(IdWorker.getIdStr());
//        target.setTitle(source.getTitle());
//        target.setCoverUrl(source.getCoverUrl());
//        target.setContentUrl(source.getContentUrl());
//        target.setDescription(source.getDescription());
//        target.setJobId(job.getId());
//        target.setJobName(job.getJobName());
        target.setCreateTime(new Date());
        // 其他字段转换...
        return target;
    }

    /**
     * 构建应答消息
     * 
     * @param originalMessage 原始消息内容
     * @param messageId 消息ID
     * @param success 处理是否成功
     * @return 应答消息内容
     */
    private String buildReplyMessage(String originalMessage, String messageId, boolean success) {
        return buildReplyMessage(originalMessage, messageId, success, null);
    }

    /**
     * 构建应答消息
     * 
     * @param originalMessage 原始消息内容
     * @param messageId 消息ID
     * @param success 处理是否成功
     * @param errorMessage 错误信息
     * @return 应答消息内容
     */
    private String buildReplyMessage(String originalMessage, String messageId, boolean success, String errorMessage) {
        MaterialResponseVO msgContent = JSONObject.toJavaObject(JSONObject.parseObject(originalMessage), MaterialResponseVO.class);
        // 将原始消息ID设置为应答消息的originMsgId
        msgContent.setOriginMsgId(messageId);
        // 生成唯一的消息ID
        msgContent.setMsgId(UUID.randomUUID().toString());

        return JSONObject.toJSONString(msgContent);
    }

    /**
     * 应答消息队列的消息
     * 使用手动确认模式，成功处理后发送应答消息并确认消息
     *
     * @param message 接收到的消息
     * @param deliveryTag 消息投递标签
     * @param channel RabbitMQ 通道
     */
    @RabbitListener(
            queues = "#{libraryRabbitMQConfig.getReplyQueueName()}",
            containerFactory = LibraryRabbitMqQueuesConstant.LIBRARY_MQ_FACTORY_NAME_MANUAL_ACK
    )
    public void onReplyMessage(Message message,
                                      @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                      Channel channel) throws IOException {

        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        String messageId = message.getMessageProperties().getMessageId();

        log.info("【Library RabbitMQ】接收到通知消息，消息ID：{}, 消息内容：{}", messageId, messageBody);

        try {
             log.info("【Library RabbitMQ】开始处理通知消息，消息ID：{}", messageBody);

        } catch (Exception e) {
            log.error("【Library RabbitMQ】处理通知消息异常，消息ID：{}, 异常信息：{}", messageId, e.getMessage(), e);

            try {
                // 发送异常应答消息
                // 否定确认消息，重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (Exception replyException) {
                log.error("【Library RabbitMQ】发送异常应答消息失败，消息ID：{}, 异常信息：{}",
                        messageId, replyException.getMessage(), replyException);
                // 否定确认消息，重新入队
                channel.basicNack(deliveryTag, false, true);
            }
        }
    }
}
