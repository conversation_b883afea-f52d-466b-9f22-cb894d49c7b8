package com.cenker.scrm.service.bu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarStatisticsDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContentRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.vo.radar.*;

import java.util.Date;
import java.util.List;

public interface ITbWxRadarInteractWorkService extends IService<TbWxRadarInteract> {
}
