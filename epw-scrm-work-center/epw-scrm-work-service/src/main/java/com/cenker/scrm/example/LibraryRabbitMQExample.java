package com.cenker.scrm.example;

import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.config.LibraryRabbitMQConfig;
import com.cenker.scrm.manager.LibraryMqSendMessageManager;
import com.cenker.scrm.pojo.vo.radar.MaterialCancelNoticeVO;
import com.cenker.scrm.pojo.vo.radar.MaterialNoticeVO;
import com.cenker.scrm.pojo.vo.radar.MaterialResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Library RabbitMQ 使用示例
 * 展示如何使用 Library RabbitMQ 组件发送应答消息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LibraryRabbitMQExample {

    private final LibraryMqSendMessageManager libraryMqSendMessageManager;

    private final LibraryRabbitMQConfig libraryRabbitMQConfig;
    /**
     * 示例：发送成功应答消息
     */
    public boolean sendSuccessReplyExample(MaterialResponseVO responseVO) {
        if (responseVO == null) {
            responseVO = getMaterialResponseVO();
        }
        String responseData = JSONObject.toJSONString(responseVO);

        log.info("发送成功应答消息示例");
        libraryMqSendMessageManager.sendReplyMessage(responseData, libraryRabbitMQConfig.getReplyRoutingKey());
        return true;
    }

    /**
     * 示例：发送 物料取消发布通知消息
     */
    public boolean sendCancelNotificationExample(MaterialCancelNoticeVO materialNoticeVO) {
       if (materialNoticeVO == null) {
           materialNoticeVO = getCancelMaterialNoticeVO();
       }
        String notificationData = JSONObject.toJSONString(materialNoticeVO);

        log.info("发送物料发布通知示例");
        libraryMqSendMessageManager.sendReplyMessage(notificationData, libraryRabbitMQConfig.getNotificationRoutingKey());
        return true;
    }

    private MaterialCancelNoticeVO getCancelMaterialNoticeVO() {
        MaterialCancelNoticeVO materialNoticeVO = new MaterialCancelNoticeVO();
        // 生成各个属性的测试数据
        materialNoticeVO.setMsgId("MATERIAL_001");
        materialNoticeVO.setMsgType("002");
        materialNoticeVO.setMsgVersion("V1.0");
        materialNoticeVO.setCreateTime(new Date());
        materialNoticeVO.setMaterialId("MATERIAL_001");
        materialNoticeVO.setMaterialName("测试物料");
        materialNoticeVO.setStoragePath("http://www.example.com/material/001");
        materialNoticeVO.setPublishModuleName("北京");
        materialNoticeVO.setExistPublishRecord("1");
        return materialNoticeVO;
    }

    /**
     * 示例：发送 物料发布通知消息
     */
    public boolean sendNotificationExample(MaterialNoticeVO materialNoticeVO) {
       if (materialNoticeVO == null) {
           materialNoticeVO = getMaterialNoticeVO();
       }
        String notificationData = JSONObject.toJSONString(materialNoticeVO);

        log.info("发送物料发布通知示例");
        libraryMqSendMessageManager.sendReplyMessage(notificationData, libraryRabbitMQConfig.getNotificationRoutingKey());
        return true;
    }

    private static MaterialNoticeVO getMaterialNoticeVO() {
        MaterialNoticeVO materialNoticeVO = new MaterialNoticeVO();
        // 生成各个属性的测试数据
        materialNoticeVO.setMsgId("MATERIAL_001");
        materialNoticeVO.setMsgType("002");
        materialNoticeVO.setMsgVersion("V1.0");
        materialNoticeVO.setCreateTime(new Date());
        materialNoticeVO.setMaterialId("MATERIAL_001");
        materialNoticeVO.setMaterialName("测试物料");
        materialNoticeVO.setMaterialDesc("测试物料描述");
        materialNoticeVO.setLastModifyTime(new Date());
        materialNoticeVO.setStoragePath("http://www.example.com/material/001");
        materialNoticeVO.setMaterialVersion("V1.0");
        materialNoticeVO.setDepartment("001");
        materialNoticeVO.setFileType("doc");
        materialNoticeVO.setUploader("100.00");
        materialNoticeVO.setOwner("100");
        materialNoticeVO.setTagList("件,A,AB");
        materialNoticeVO.setMetaDataList("{}");
        materialNoticeVO.setPublishModuleName("北京");
        materialNoticeVO.setPublishRule("快递");
        materialNoticeVO.setOuterPreviewUrl("顺丰");
        materialNoticeVO.setOuterCdnUrl("1234567890");
        materialNoticeVO.setOuterShortUrl("https://www.example.com/material/001");
        return materialNoticeVO;
    }

    /**
     * 示例：发送失败应答消息
     */
//    public void sendFailureReplyExample() {
//        String originalMessageId = "MSG_" + System.currentTimeMillis();
//        String errorMessage = "业务处理失败：数据验证错误";
//
//        log.info("发送失败应答消息示例");
//        libraryMqSendMessageManager.sendFailureReply(originalMessageId, errorMessage);
//    }

    /**
     * 示例：发送自定义应答消息
     */
//    public void sendCustomReplyExample() {
//        String customReplyMessage = "{\"messageId\":\"CUSTOM_MSG_001\",\"success\":true,\"data\":\"自定义应答数据\"}";
//
//        log.info("发送自定义应答消息示例");
//        libraryMqSendMessageManager.sendReplyMessage(customReplyMessage);
//    }

    /**
     * 示例：异步发送应答消息
     */
    public boolean sendAsyncReplyExample(MaterialResponseVO responseVO) {
        if (responseVO == null) {
            responseVO = getMaterialResponseVO();
        }
        String replyMessage = JSONObject.toJSONString(responseVO);

        log.info("异步发送应答消息示例");
        libraryMqSendMessageManager.sendReplyMessageAsync(replyMessage, libraryRabbitMQConfig.getReplyRoutingKey());
        return true;
    }

    private static MaterialResponseVO getMaterialResponseVO() {
        MaterialResponseVO responseVO = new MaterialResponseVO();
        // 生成各个属性的测试数据
        responseVO.setMsgId("XXX202304111516170001");
        responseVO.setMsgType("003");
        responseVO.setMsgVersion("V1.0");
        responseVO.setCreateTime(new Date());
        responseVO.setMaterialId("MATERIAL_001");
        responseVO.setMaterialName("测试物料");
        responseVO.setOriginMsgId("MATERIAL_001");
        return responseVO;
    }
}
