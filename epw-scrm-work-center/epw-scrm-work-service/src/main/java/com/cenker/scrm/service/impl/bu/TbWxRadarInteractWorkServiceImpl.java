package com.cenker.scrm.service.impl.bu;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.mapper.bu.TbWxRadarInteractWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.service.bu.ITbWxRadarContentWorkService;
import com.cenker.scrm.service.bu.ITbWxRadarInteractWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxRadarInteractWorkServiceImpl extends ServiceImpl<TbWxRadarInteractWorkMapper, TbWxRadarInteract> implements ITbWxRadarInteractWorkService {
    private static final String DELIVERY_CHANNEL_KEY = "material_delivery_channel";

    private final ITbWxRadarContentWorkService radarContentService;

    private final RedisCache redisCache;
}
