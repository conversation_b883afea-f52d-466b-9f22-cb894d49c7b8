package com.cenker.scrm.service.impl.group;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.group.IWkWxCustomerGroupMemberLogWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroupMember;
import com.cenker.scrm.pojo.entity.wechat.group.WkWxCustomerGroupMemberLog;
import com.cenker.scrm.service.group.IWkWxCustomerGroupMemberLogWorkService;
import com.cenker.scrm.util.BeanUtils;
import com.cenker.scrm.util.DateUtils;
import com.google.common.collect.Lists;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/28
 * @Description
 */
@Service
public class WkWxCustomerGroupMemberLogWorkServiceImpl extends ServiceImpl<IWkWxCustomerGroupMemberLogWorkMapper, WkWxCustomerGroupMemberLog> implements IWkWxCustomerGroupMemberLogWorkService {
    @Override
    @Async
    public void saveJoinGroupLog(List<TbWxCustomerGroupMember> joinList, String chatId) {
        saveGroupLog(joinList, chatId, 1);
    }

    @Override
    @Async
    public void saveQuitGroupLog(List<TbWxCustomerGroupMember> delList, String chatId) {
        saveGroupLog(delList, chatId, 2);
    }

    private void saveGroupLog(List<TbWxCustomerGroupMember> list, String chatId, int changeType) {
        if (CollectionUtil.isNotEmpty(list)) {
            Date nowDate = DateUtils.getNowDate();
            Date dayStartTime = DateUtils.getDayStartTime(nowDate);
            List<WkWxCustomerGroupMemberLog> saveList = Lists.newArrayList();
            for (TbWxCustomerGroupMember tbWxCustomerGroupMember : list) {
                WkWxCustomerGroupMemberLog wkWxCustomerGroupMemberLog = new WkWxCustomerGroupMemberLog();
                BeanUtils.copyBeanProp(wkWxCustomerGroupMemberLog, tbWxCustomerGroupMember);
                wkWxCustomerGroupMemberLog.setId(null);
                wkWxCustomerGroupMemberLog.setGroupId(chatId);
                wkWxCustomerGroupMemberLog.setChangeDay(dayStartTime);
                wkWxCustomerGroupMemberLog.setChangeTime(nowDate);
                wkWxCustomerGroupMemberLog.setChangeType(changeType);
                saveList.add(wkWxCustomerGroupMemberLog);
            }
            saveBatch(saveList);
        }
    }
}
