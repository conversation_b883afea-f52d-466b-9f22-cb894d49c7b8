package com.cenker.scrm.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.system.SysUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface SysUserWorkMapper extends BaseMapper<SysUser> {
    SysUser selectUserById(Long userId);
}
