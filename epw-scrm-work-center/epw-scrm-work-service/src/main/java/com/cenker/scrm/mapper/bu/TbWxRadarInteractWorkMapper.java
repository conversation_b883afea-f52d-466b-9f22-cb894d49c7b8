package com.cenker.scrm.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarStatisticsDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.vo.radar.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TbWxRadarInteractWorkMapper extends BaseMapper<TbWxRadarInteract> {
    List<InteractRadarVo> getRadarList(InteractRadarVo radarVo);

    RadarStatisticsVO getRadarStatistics(Map<String, Object> param);

    List<RadarCustomerStatisticsVO> getRadarReadRecordStatistics(RadarStatisticsDTO radarStatisticsDTO);

    List<InteractRadarChatVO> getRadarChatList(InteractRadarVo radarVo);

    TbWxRadarInteract getOneById(String radarId);

    String getForwardUserById(String forwardUser);

    TbWxRadarInteract selectRadarByContentId(String contentId);

    List<InteractRadarVo> getRadarSource(InteractRadarVo radarVo);

    List<RadarStatisticsRankVO> getRadarStatisticsRank(
            @Param("corpId") String corpId,
            @Param("condition") QueryRadarStatisticsRankDTO condition);

}
