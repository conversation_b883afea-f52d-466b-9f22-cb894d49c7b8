package com.cenker.scrm.service.impl.contact;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.contact.TbWxUserWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.service.contact.ITbWxUserWorkService;
import com.cenker.scrm.util.ConvertUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通讯录相关客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@Slf4j
public class TbWxUserWorkServiceImpl extends ServiceImpl<TbWxUserWorkMapper, TbWxUser> implements ITbWxUserWorkService {

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询通讯录相关客户
     *
     * @param userId 通讯录相关客户ID
     * @return 通讯录相关客户
     */
    @Override
    public TbWxUser selectTbWxUserById(String corpId, String userId) {
        return baseMapper.selectTbWxUserById(corpId, userId);
    }


    @Override
    public void addWxUser(WxCpUser wxCpUser, String corpId) {
        if (null != wxCpUser && StringUtils.isNotBlank(corpId)) {
            List<WxCpUser> wxUserList = new ArrayList<>();
            wxUserList.add(wxCpUser);
            // 获取默认用户头像
            String defaultUserAvatar = redisCache.getCacheObject(DefaultConstants.DEFAULT_USER_AVATAR);
            // 对象转换
            List<TbWxUser> tbWxUserList = ConvertUtils.wxUserConvert(corpId, wxUserList, defaultUserAvatar);

            List<TbWxUser> saveUserList = new ArrayList<>();
            List<TbWxUser> editUserList = new ArrayList<>();
            LambdaQueryWrapper<TbWxUser> queryWrapper = Wrappers.lambdaQuery(TbWxUser.class);
            for (TbWxUser tbWxUser : tbWxUserList) {
                if (null != tbWxUser && StringUtils.isNotBlank(tbWxUser.getCorpId()) && StringUtils.isNotBlank(tbWxUser.getUserid())) {
                    queryWrapper.clear();
                    queryWrapper.eq(TbWxUser::getCorpId, tbWxUser.getCorpId());
                    queryWrapper.eq(TbWxUser::getUserid, tbWxUser.getUserid());
                    TbWxUser t = this.getOne(queryWrapper);
                    if (null != t) {
                        editUserList.add(tbWxUser);
                    } else {
                        saveUserList.add(tbWxUser);
                    }
                }
            }

            if (saveUserList.size() > 0) {
                this.saveBatch(saveUserList, 500);
            }

            if (editUserList.size() > 0) {
                for (TbWxUser tbWxUser : editUserList) {
                    UpdateWrapper<TbWxUser> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("corp_id", tbWxUser.getCorpId());
                    updateWrapper.eq("userid", tbWxUser.getUserid());
                    tbWxUser.setDelFlag("1");
                    // 不更新企微端权限
                    tbWxUser.setSideAble(null);
                    Integer rows = this.baseMapper.update(tbWxUser, updateWrapper);
                }
            }
        }
    }


    /**
     * 删除成员
     *
     * @param corpId 企业ID
     * @param userId 成员Id
     */
    @Override
    public void delWxUser(String corpId, String userId) {
        if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(corpId)) {
            TbWxUser tbWxUser = new TbWxUser();
            tbWxUser.setDimissionTime(new Date());
            tbWxUser.setIsAllocate(Integer.valueOf(UserStatus.OK.getCode()));
            tbWxUser.setDelFlag("2");
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("corp_id", corpId);
            wrapper.eq("userid", userId);
            baseMapper.update(tbWxUser, wrapper);
        }
    }

    @Override
    public void synchronizationWxUserByUserId(String userId, String corpId, WxCpServiceImpl wxCpService) {
        try {
            WxCpUser wxCpUser = wxCpService.getUserService().getById(userId);
            if (ObjectUtil.isNotNull(wxCpUser)) {
                List<WxCpUser> wxUserList = Lists.newArrayList();
                wxUserList.add(wxCpUser);
                // 获取默认用户头像
                String defaultUserAvatar = redisCache.getCacheObject(DefaultConstants.DEFAULT_USER_AVATAR);
                List<TbWxUser> tbWxUserList = ConvertUtils.wxUserConvert(corpId, wxUserList, defaultUserAvatar);
                if (CollectionUtil.isNotEmpty(tbWxUserList)) {
                    for (TbWxUser tbWxUser : tbWxUserList) {
                        log.info("【同步通讯录】开始同步成员：{}", tbWxUser);
                        // 更新信息重新
                        LambdaUpdateWrapper<TbWxUser> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(TbWxUser::getCorpId, corpId);
                        updateWrapper.eq(TbWxUser::getUserid, userId);
                        // 关注事件肯定是激活
                        tbWxUser.setDelFlag("1");
                        saveOrUpdate(tbWxUser, updateWrapper);
                    }
                }
            }
        } catch (WxErrorException e) {
            log.error("【同步通讯录】同步成员失败：{}", e.getMessage());
        }
    }

    @Override
    public TbWxUser checkUserIsCorpAdmin(String userId, String corpId) {
        return baseMapper.checkUserIsCorpAdmin(userId, corpId);
    }
}
