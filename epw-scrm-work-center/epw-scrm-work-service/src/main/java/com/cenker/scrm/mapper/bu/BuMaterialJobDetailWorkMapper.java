package com.cenker.scrm.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.system.BuMaterialJobDetail;
import com.cenker.scrm.pojo.vo.radar.LibraryMaterialVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料制作定时任务明细表(bu_material_job_detail)数据Mapper
 *
 * <AUTHOR>
 * @since 2025-04-06 21:04:13
 * @description 由 Mybatisplus Code Generator 创建
*/
@Mapper
public interface BuMaterialJobDetailWorkMapper extends BaseMapper<BuMaterialJobDetail> {

    List<LibraryMaterialVO> selectExistsBySourceId(@Param("sourceId") String materialId);
}
