package com.cenker.scrm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.TbWxCorpAgentConfigMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpAgentConfig;
import com.cenker.scrm.service.ITbWxCorpAgentConfigService;
import com.cenker.scrm.util.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 企业微信-应用相关配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxCorpAgentConfigServiceImpl extends ServiceImpl<TbWxCorpAgentConfigMapper, TbWxCorpAgentConfig> implements ITbWxCorpAgentConfigService {

    private final TbWxCorpAgentConfigMapper tbWxCorpAgentConfigMapper;


    /**
     * 查询企业微信-应用相关配置
     *
     * @param id 企业微信-应用相关配置ID
     * @return 企业微信-应用相关配置
     */
    @Override
    public TbWxCorpAgentConfig selectTbWxCorpAgentConfigById(Long id) {
        return tbWxCorpAgentConfigMapper.selectTbWxCorpAgentConfigById(id);
    }


    /**
     * 查询企业微信-应用相关配置
     *
     * @param agentId 企业微信-应用相关配置agentID
     * @return 企业微信-应用相关配置
     */
    @Override
    public TbWxCorpAgentConfig selectTbWxCorpAgentConfigByAgentId(Integer agentId) {
        return tbWxCorpAgentConfigMapper.selectTbWxCorpAgentConfigByAgentId(agentId);
    }

    /**
     * 查询企业微信-应用相关配置列表
     *
     * @param TbWxCorpAgentConfig 企业微信-应用相关配置
     * @return 企业微信-应用相关配置
     */
    @Override
    public List<TbWxCorpAgentConfig> selectTbWxCorpAgentConfigList(TbWxCorpAgentConfig TbWxCorpAgentConfig) {
        return tbWxCorpAgentConfigMapper.selectTbWxCorpAgentConfigList(TbWxCorpAgentConfig);
    }

    /**
     * 新增企业微信-应用相关配置
     *
     * @param TbWxCorpAgentConfig 企业微信-应用相关配置
     * @return 结果
     */
    @Override
    public int insertTbWxCorpAgentConfig(TbWxCorpAgentConfig TbWxCorpAgentConfig) {
        TbWxCorpAgentConfig.setCreateTime(DateUtils.getNowDate());
        return tbWxCorpAgentConfigMapper.insertTbWxCorpAgentConfig(TbWxCorpAgentConfig);
    }

    /**
     * 修改企业微信-应用相关配置
     *
     * @param TbWxCorpAgentConfig 企业微信-应用相关配置
     * @return 结果
     */
    @Override
    public int updateTbWxCorpAgentConfig(TbWxCorpAgentConfig TbWxCorpAgentConfig) {
        TbWxCorpAgentConfig.setUpdateTime(DateUtils.getNowDate());
        return tbWxCorpAgentConfigMapper.updateTbWxCorpAgentConfig(TbWxCorpAgentConfig);
    }

    /**
     * 批量删除企业微信-应用相关配置
     *
     * @param ids 需要删除的企业微信-应用相关配置ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpAgentConfigByIds(Long[] ids) {
        return tbWxCorpAgentConfigMapper.deleteTbWxCorpAgentConfigByIds(ids);
    }

    /**
     * 删除企业微信-应用相关配置信息
     *
     * @param id 企业微信-应用相关配置ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpAgentConfigById(Long id) {
        return tbWxCorpAgentConfigMapper.deleteTbWxCorpAgentConfigById(id);
    }
}
