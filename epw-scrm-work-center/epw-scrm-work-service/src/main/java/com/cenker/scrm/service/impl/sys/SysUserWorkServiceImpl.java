package com.cenker.scrm.service.impl.sys;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.sys.SysUserWorkMapper;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.service.sys.ISysUserWorkService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class SysUserWorkServiceImpl extends ServiceImpl<SysUserWorkMapper, SysUser> implements ISysUserWorkService {

    @Override
    public SysUser selectUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }
}
