package com.cenker.scrm.service.impl.sitecontact;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.sitecontact.TbWxDeliveryContactWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.TbWxDeliveryContact;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/6/1
 * @Description
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TbWxDeliveryContactWorkServiceImpl extends ServiceImpl<TbWxDeliveryContactWorkMapper, TbWxDeliveryContact> implements ITbWxDeliveryContactWorkService {
}
