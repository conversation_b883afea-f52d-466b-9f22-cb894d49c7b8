package com.cenker.scrm.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.system.BuMaterialJobListDto;
import com.cenker.scrm.pojo.entity.system.BuMaterialJob;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobListVo;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bu_material_job(物料制作定时任务)】的数据库操作Mapper
* @createDate 2025-03-25 17:19:39
* @Entity com.cenker.scrm.pojo.entity.system.BuMaterialJob
*/
public interface BuMaterialJobWorkMapper extends BaseMapper<BuMaterialJob> {

    /**
     * 查询物料制作定时任务列表
     * @param param
     * @return
     */
    List<BuMaterialJobListVo> listByCondition(@Param("param") BuMaterialJobListDto param);

    /**
     * 根据id查询物料制作定时任务详情
     * @param id
     * @return
     */
    BuMaterialJobVo getJobById(String id);
}




