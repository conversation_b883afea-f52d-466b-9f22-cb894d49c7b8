package com.cenker.scrm.service.impl.bu;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.bu.TbWxRadarContentWorkMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.service.bu.ITbWxRadarContentWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxRadarContentWorkServiceImpl extends ServiceImpl<TbWxRadarContentWorkMapper, TbWxRadarContent> implements ITbWxRadarContentWorkService {

}
