package com.cenker.scrm.service.group;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalGroupChatInfo;

/**
 * 企业微信客户群Service接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface ITbWxCustomerGroupWorkService extends IService<TbWxCustomerGroup> {

    /**
     * 客户群变更事件更新群数据改版
     * @param chatInfo 群信息
     * @param corpId 企业id
     * @param updateDetail 变更详情
     * @param quitScene 退群方式
     * @param memChangeCnt 变更人数 有值代表是进退群
     */
    void updateWxChatInfoV2(WxCpUserExternalGroupChatInfo chatInfo, String corpId, String updateDetail, String quitScene, String memChangeCnt);

    /**
     * 查询企业微信客户群
     *
     * @param chatId 企业微信客户群ID
     * @return 企业微信客户群
     */
    TbWxCustomerGroup selectTbWxCustomerGroupById(String chatId);

    /**
     * 修改企业微信客户群
     *
     * @param tbWxCustomerGroup 企业微信客户群
     * @return 结果
     */
    int updateTbWxCustomerGroup(TbWxCustomerGroup tbWxCustomerGroup);

    /**
     * 保存外部群聊信息
     *
     * @param cpChat
     */
    void saveWxChatInfo(WxCpUserExternalGroupChatInfo cpChat, String corpId, int chatStatus);
}
