package com.cenker.scrm.service.impl.bu;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.bu.BuMaterialJobWorkMapper;
import com.cenker.scrm.pojo.dto.system.BuMaterialJobListDto;
import com.cenker.scrm.pojo.entity.system.BuMaterialJob;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobListVo;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobVo;
import com.cenker.scrm.service.bu.IBuMaterialJobWorkService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bu_material_job(物料制作定时任务)】的数据库操作Service实现
* @createDate 2025-03-25 17:19:39
*/
@Service
public class BuMaterialJobWorkServiceImpl extends ServiceImpl<BuMaterialJobWorkMapper, BuMaterialJob>
    implements IBuMaterialJobWorkService {

    @Override
    public List<BuMaterialJobListVo> listByCondition(BuMaterialJobListDto param) {
        return baseMapper.listByCondition(param);
    }

    @Override
    public BuMaterialJobVo getJobById(String id) {
        return baseMapper.getJobById(id);
    }
}




