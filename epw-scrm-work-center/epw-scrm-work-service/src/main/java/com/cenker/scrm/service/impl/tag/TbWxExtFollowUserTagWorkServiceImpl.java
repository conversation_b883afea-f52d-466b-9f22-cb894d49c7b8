package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.OperTypeEnum;
import com.cenker.scrm.mapper.tag.TbWxExtFollowUserTagWorkMapper;
import com.cenker.scrm.pojo.entity.TbWxCustomerTagLog;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.service.tag.ITbWxCustomerTagLogWorkService;
import com.cenker.scrm.service.tag.ITbWxExtFollowUserTagWorkService;
import com.cenker.scrm.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/1
 * @Description 客户标签
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxExtFollowUserTagWorkServiceImpl extends ServiceImpl<TbWxExtFollowUserTagWorkMapper, TbWxExtFollowUserTag> implements ITbWxExtFollowUserTagWorkService {

    private final ITbWxCustomerTagLogWorkService tbWxCustomerTagLogService;
    @Override
    public boolean saveBatchWithLog(List<TbWxExtFollowUserTag> entityList) {
        boolean saved = this.saveBatch(entityList, 1000);
        // 组装tagLog,并入库
        Collection<TbWxCustomerTagLog> logList = entityList.stream().map(tag -> {
            return getTbWxCustomerTagLog(tag, CorpInfoProperties.getCorpId(), OperTypeEnum.ADD, tag.getOperId(), tag.getCreateBy());
        }).collect(Collectors.toList());
        tbWxCustomerTagLogService.saveBatch(logList);

        return saved;
    }

    @Override
    public void removeByWrapperWithLog(LambdaQueryWrapper<TbWxExtFollowUserTag> warpper) {
        // 查询待删除userTag，方便构建tagLog
        List<TbWxExtFollowUserTag> removeTags = list(warpper);
        removeUserTagWithLog(removeTags, null, null);
    }

    @Override
    public void removeUserTagWithLog(List<TbWxExtFollowUserTag> removeTags, String operatorUserId, String operId) {
        if (CollectionUtils.isEmpty(removeTags)) {
            return;
        }
        // 删除userTag
        remove(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .in(TbWxExtFollowUserTag::getId, removeTags.stream().map(TbWxExtFollowUserTag::getId).collect(Collectors.toSet()))
        );
        // 组装tagLog,并入库
        Collection<TbWxCustomerTagLog> logList = removeTags.stream().map(tag -> {
            return getTbWxCustomerTagLog(tag, CorpInfoProperties.getCorpId(), OperTypeEnum.DEL, operId, operatorUserId);
        }).collect(Collectors.toList());
        tbWxCustomerTagLogService.saveBatch(logList);
    }

    @Override
    public void remove(String corpId, String userId, String externalUserId, List<TagVO> removeList, String operId, String operatorUserId) {
        List<TbWxCustomerTagLog> addLogList = getTbWxCustomerTagLogs(corpId, userId, externalUserId, removeList, operId, operatorUserId, OperTypeEnum.DEL.getCode());
        this.remove(new LambdaQueryWrapper<>(TbWxExtFollowUserTag.class)
                .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                .in(TbWxExtFollowUserTag::getTagId, removeList.stream().map(TagVO::getTagId).collect(Collectors.toList()))
        );
        tbWxCustomerTagLogService.saveBatch(addLogList);
    }
    /**
     * 组装 TbWxCustomerTagLog
     *
     * @param corpId
     * @param userId
     * @param externalUserId
     * @param list
     * @param operId
     * @param operatorUserId
     * @param operType  新增/删除
     * @return
     */
    private List<TbWxCustomerTagLog> getTbWxCustomerTagLogs(String corpId, String userId, String externalUserId,
                                                            List<TagVO> list, String operId, String operatorUserId, String operType) {
        List<TbWxCustomerTagLog> addLogList = Lists.newArrayList();
        for (TagVO tagVO : list) {
            TbWxCustomerTagLog log = getTbWxCustomerTagLog(corpId, userId, externalUserId, TypeConstants.CORP_TAG_TYPE, operId, operatorUserId, operType, tagVO);
            addLogList.add(log);
        }
        return addLogList;
    }

    /**
     * 组装log
     * @param corpId
     * @param userId
     * @param externalUserId
     * @param type
     * @param operId
     * @param operatorUserId
     * @param operType
     * @param tagVO
     * @return
     */
    private TbWxCustomerTagLog getTbWxCustomerTagLog(String corpId, String userId, String externalUserId, String type,
                                                     String operId, String operatorUserId, String operType, TagVO tagVO) {
        TbWxCustomerTagLog log = TbWxCustomerTagLog.builder()
                .groupName(tagVO.getGroupName())
                .corpId(corpId)
                .externalUserId(externalUserId)
                .type(type)
                .userId(userId)
                .tag(tagVO.getTagName())
                .createBy(operatorUserId)
                .createTime(DateUtils.getNowDate())
                .tagId(tagVO.getTagId())
                .operId(operId)
                .operType(operType).build();
        return log;
    }

    private TbWxCustomerTagLog getTbWxCustomerTagLog(TbWxExtFollowUserTag tag, String corpId, OperTypeEnum del, String operId, String createBy) {
        TbWxCustomerTagLog log = TbWxCustomerTagLog.builder()
                .userId(tag.getUserId())
                .externalUserId(tag.getExternalUserId())
                .corpId(corpId)
                .tagId(tag.getTagId())
                .tag(tag.getTag())
                .groupName(tag.getGroupName())
                .type(tag.getType())
                .operId(operId)
                .operType(del.getCode())
                .createBy(createBy)
                .createTime(DateUtil.date())
                .build();
        return log;
    }
}
