package com.cenker.scrm.biz;

import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.constants.RabbitMqQueuesConstant;
import com.cenker.scrm.event.WxSendWelcomeCodeEvent;
import com.cenker.scrm.manager.BaseRabbitMessageQueueManager;
import com.cenker.scrm.pojo.dto.ApprovalWarningMsgDTO;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerSyncTagMsgDto;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.dto.system.SysNoticeMsgDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/6/12
 * @Description RocketMQ发送方法
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MqSendMessageManager extends BaseRabbitMessageQueueManager {

    @Async
    public void sendWelcomeMessage(WxSendWelcomeCodeEvent wxSendWelcomeCodeEvent){
        String msgBody = JSONObject.toJSONString(wxSendWelcomeCodeEvent);
        log.info("【客户联系回调】处理欢迎语事件发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.SEND_WELCOME_MESSAGE, msgBody);
    }

    @Async
    public void sendAddCorpTagContactMessage(WxSendWelcomeCodeEvent wxSendWelcomeCodeEvent) {
        String msgBody = JSONObject.toJSONString(wxSendWelcomeCodeEvent);
        log.info("【客户联系回调】处理活码添加事件,开始获取活码并尝试添加标签,发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CONTACT_ADD_TAG, msgBody);
    }

    @Async
    public void sendPullChatArchiveMessage(WxCpXmlMessage wxMessage) {
        String msgBody = JSONObject.toJSONString(wxMessage);
        log.info("【会话存档回调】有新会话消息产生，需要进行会话拉取，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CHAT_ARCHIVE_MSG_AUDIT_NOTIFY, msgBody);
    }

    @Async
    public void sendUserSyncCustomerMessage(JSONObject body) {
        String msgBody = JSONObject.toJSONString(body);
        log.info("【员工关注事件】有新员工关注事件产生，需要进行同步员工的客户数据，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER, msgBody);
    }

    @Async
    public void sendUserSyncCustomerGroupMessage(JSONObject body) {
        String msgBody = JSONObject.toJSONString(body);
        log.info("【员工关注事件】有新员工关注事件产生，需要进行同步员工的客户群数据，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER_GROUP, msgBody);
    }

    @Async
    public void sendCustomerAddTagMessage(CustomerAddTagMsgDto customerAddTagMsgDto) {
        String msgBody = JSONObject.toJSONString(customerAddTagMsgDto);
        log.info("【客户添加标签】为客户添加标签，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CUSTOMER_ADD_TAG, msgBody);
    }

    @Async
    public void sendCustomerRemoveTagMessage(CustomerRemoveTagMsgDto customerRemoveTagMsgDto) {
        String msgBody = JSONObject.toJSONString(customerRemoveTagMsgDto);
        log.info("【客户删除标签】为客户删除标签，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CUSTOMER_DEL_TAG, msgBody);
    }

    @Async
    public void sendCustomerOperTrackMessage(CustomerOperTrackMsgDto customerOperTrackMsgDto) {
        String msgBody = JSONObject.toJSONString(customerOperTrackMsgDto);
        log.info("【客户动态】记录客户动态，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CUSTOMER_OPER_TRACK, msgBody);
    }

    @Async
    public void sendCustomerSyncTagMessage(CustomerSyncTagMsgDto customerSyncTagMsgDto) {
        String msgBody = JSONObject.toJSONString(customerSyncTagMsgDto);
        log.info("【客户标签同步】客户标签同步，发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.SYS_MSG_SYSN_TAG, msgBody);
    }


    @Async
    public void sendSysMsg(SysNoticeMsgDto sysNoticeMsgDtoDto) {
        String msgBody = JSONObject.toJSONString(sysNoticeMsgDtoDto);
        log.info("【发送消息通知】处理发送消息通知,发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.SYS_MSG_NOTICE_SEND, msgBody);
    }

    /**
     * 发送审核预警
     * @param approvalWarningMsgDTO
     */
    @Async
    public void sendDelayMsg(ApprovalWarningMsgDTO approvalWarningMsgDTO) {
        String msgBody = JSONObject.toJSONString(approvalWarningMsgDTO);
        log.info("【发送审核预警】审核预警延时队列,发送mq消息:{}", msgBody);
        sendDelayMessage(RabbitMqQueuesConstant.DELAYED_ROUTING_KEY, msgBody, approvalWarningMsgDTO.getDelayTime());
    }

    /**
     * 发送应用消息
     * @param wxCpMessage
     */
    public void sendAgentMessage(WxCpMessage wxCpMessage) {
        String msgBody = JSONObject.toJSONString(wxCpMessage);
        log.info("【发送应用消息】处理发送应用消息事件发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.SEND_AGENT_MESSAGE, msgBody);
    }
}
