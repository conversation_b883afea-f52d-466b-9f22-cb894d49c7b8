package com.cenker.scrm.service.impl.external;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.constants.WeConstants;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.mapper.external.TbWxCustomerTrajectoryWorkMapper;
import com.cenker.scrm.mapper.external.TbWxExtCustomerWorkMapper;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroupMember;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerTrajectory;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.service.contact.ITbWxUserWorkService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryWorkService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalGroupChatInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/12
 * @Description 客户画像-轨迹
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxCustomerTrajectoryWorkServiceImpl extends ServiceImpl<TbWxCustomerTrajectoryWorkMapper, TbWxCustomerTrajectory> implements ITbWxCustomerTrajectoryWorkService {

    private final ITbWxUserWorkService userService;
    private final TbWxExtCustomerWorkMapper customerMapper;
    private final MqSendMessageManager mqSendMessageManager;

    /**
     * 进群退群这一批人方式肯定是一样的
     *
     * @param memberList 进/退群客户
     * @param cpChat  群信息
     * @param isJoin     是否进群
     */
    @Override
    @Async
    public void addCustomerTrajectory2Group(List<TbWxCustomerGroupMember> memberList,WxCpUserExternalGroupChatInfo cpChat, boolean isJoin) {
        String corpId = memberList.get(0).getCorpId();
        // 群主
        String owner = cpChat.getGroupChat().getOwner();
        RelatedResource relatedResource = new RelatedResource();
        RelatedResource.GroupVo groupVo = relatedResource.new GroupVo();
        relatedResource.setGroupVo(groupVo);
        // 群名
        groupVo.setGroupName(StringUtils.isEmpty(cpChat.getGroupChat().getName()) ? "群聊" : cpChat.getGroupChat().getName());

        TbWxUser ownerTbWxUser = userService.getOne(new LambdaQueryWrapper<TbWxUser>()
                .select(TbWxUser::getName)
                .eq(TbWxUser::getCorpId, corpId)
                .eq(TbWxUser::getUserid, owner));
        groupVo.setOwner(ownerTbWxUser != null ? ownerTbWxUser.getName() : owner);

        TrackEventTypeEnum eventTypeEnum;
        if (isJoin) {
            // 进群方式在头像旁边显示way
            String joinScene = memberList.get(0).getJoinScene();
            eventTypeEnum = "1".equals(joinScene) ? TrackEventTypeEnum.GROUP_JOIN_INVITE : TrackEventTypeEnum.GROUP_JOIN_SCAN;
        } else {
            // 退群方式在动态右上角显示
            String quitScene = memberList.get(0).getQuitScene();
            eventTypeEnum = "0".equals(quitScene) ? TrackEventTypeEnum.GROUP_LEAVE_VOLUNTARY : TrackEventTypeEnum.GROUP_LEAVE_REMOVED;
        }
        DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(eventTypeEnum.getSubEventType());
        for (TbWxCustomerGroupMember member : memberList) {
            // 查询是否外部客户
            int count = customerMapper.selectCount(new LambdaQueryWrapper<TbWxExtCustomer>()
                    .eq(TbWxExtCustomer::getCorpId, corpId)
                    .eq(TbWxExtCustomer::getExternalUserId, member.getUserId()));
            if (count > 0) {
                // 查找该客户 轨迹用户算到群主身上 如果存在邀请人 就算到邀请人身上
                String userId = StringUtils.isNotEmpty(member.getInvitorUserId()) ? member.getInvitorUserId() : owner;
                OperTrackParams operTrackParams = OperTrackParams.builder()
                        .relatedResource(relatedResource)
                        .externalUserId(member.getUserId())
                        .userId(userId)
                        .corpId(corpId)
                        .name(groupVo.getGroupName())
                        .build();
                BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
                mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
                log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
            }
        }
    }

    @Override
    @Async
    public void informationNews2(String userId, String externalUserId, String corpId, Integer trajectoryType, Integer behaviorType, String content) {
        TbWxUser tbWxUser = userService.selectTbWxUserById(corpId, userId);
        if (tbWxUser != null && StringUtils.isNotEmpty(content)) {
            save(TbWxCustomerTrajectory.builder().externalUserId(externalUserId).trajectoryType(trajectoryType)
                    .behaviorType(behaviorType).createDate(DateUtils.getNowDate()).createTime(DateUtils.getNowDate())
                    .userId(userId).corpId(corpId).content(content).build());
        }
    }
}
