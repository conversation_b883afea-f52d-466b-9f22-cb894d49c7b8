package com.cenker.scrm.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.SysNoticeEnum;
import com.cenker.scrm.pojo.dto.ApprovalWarningMsgDTO;
import com.cenker.scrm.pojo.dto.system.SysNoticeMsgDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentTaskInfo;
import com.cenker.scrm.service.IApprovalWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalWarningManager {

    private final MqSendMessageManager mqSendMessageManager;
    private final IApprovalWorkService approvalWorkService;
    public void handlerApprovalWarningMessage(ApprovalWarningMsgDTO msgContent) {
        log.info("【审核预警消息处理】收到审批提醒消息：{}", msgContent);
        ApprovalTypeEnum approvalType = msgContent.getApprovalType();
        JSONObject obj = (JSONObject) msgContent.getApprovalObj();
        switch (approvalType) {
            case SENDMOMENTS:
                TbWxMomentTaskInfo task = JSONObject.toJavaObject(obj, TbWxMomentTaskInfo.class);
                TbWxMomentTaskInfo newTask = approvalWorkService.getMomentTaskInfo(task.getId());
                boolean validateStatus = !ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(newTask.getCheckStatus());
                boolean valdiateName = !newTask.getTaskName().equals(task.getTaskName());
                boolean validateUpdateTime = newTask.getUpdateTime() != null && !compareDate(newTask.getUpdateTime(), task.getUpdateTime());
                boolean validateSetting = StatusConstants.TIMED_TASK_SEND_TIMING == task.getTimedTask() && !(newTask.getSettingTime() != null && compareDate(newTask.getSettingTime(), task.getSettingTime()));
                boolean validateTimedTask = newTask.getTimedTask() != null && newTask.getTimedTask() != task.getTimedTask();
                if (validateStatus || valdiateName || validateUpdateTime || validateSetting || validateTimedTask) {
                    log.info("【审核预警消息处理】任务{}审核提醒消息，内容不一致，不发送消息, 审核状态：{}, 任务名称：{}, 更新时间：{}, 定时设置时间：{}, 定时任务：{}",
                            task.getTaskName(), validateStatus, valdiateName, validateUpdateTime, validateSetting, validateTimedTask);
                    return;
                }
                log.info("【审核预警消息处理】任务{}审核提醒消息，内容一致，发送消息", task.getTaskName());
                if (StatusConstants.TIMED_TASK_SEND_ONCE == task.getTimedTask()) {
                    // 通知审核员
                    this.sendWarnApprovalMsg(task, approvalType.getType(), false,
                            false,
                            TbWxMomentTaskInfo::getTaskName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());
                    // 通知创建者
                    this.sendWarnApprovalMsg(task, approvalType.getType(), false,
                            true,
                            TbWxMomentTaskInfo::getTaskName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());

                } else {
                    // 通知审核员
                    this.sendWarnApprovalMsg(task, approvalType.getType(), true,
                            false,
                            TbWxMomentTaskInfo::getTaskName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());
                    // 通知创建者
                    this.sendWarnApprovalMsg(task, approvalType.getType(), true,
                            true,
                            TbWxMomentTaskInfo::getTaskName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());

                }
                break;
            case GROUPTOGROUP:
            case MASSCUSTOMER:
                TbWxMassMessageInfo taskInfo = JSONObject.toJavaObject(obj, TbWxMassMessageInfo.class);
                TbWxMassMessageInfo newTaskInfo = approvalWorkService.getMassMessageInfo(taskInfo.getId());
                // 时间比较到秒
                boolean validateStatus1 = !ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(newTaskInfo.getCheckStatus());
                boolean validateName1 = !newTaskInfo.getMassName().equals(taskInfo.getMassName());
                boolean validateUpdateTime1 = newTaskInfo.getUpdateTime() != null && !compareDate(newTaskInfo.getUpdateTime(), taskInfo.getUpdateTime());
                boolean validateSettingTime1 = StatusConstants.TIMED_TASK_SEND_TIMING == taskInfo.getTimedTask() && !(newTaskInfo.getSettingTime() != null && compareDate(newTaskInfo.getSettingTime(), taskInfo.getSettingTime()));
                boolean validateTimedTask1 = newTaskInfo.getTimedTask() != null && newTaskInfo.getTimedTask() != taskInfo.getTimedTask();
                if (validateStatus1 || validateName1 || validateUpdateTime1 || validateSettingTime1 || validateTimedTask1) {
                    log.info("【审核预警消息处理】任务{}审核提醒消息，内容不一致，不发送消息, 审核状态：{}, 任务名称：{}, 更新时间：{}, 定时设置时间：{}, 定时任务：{}",
                            taskInfo.getMassName(), validateStatus1, validateName1, validateUpdateTime1, validateSettingTime1, validateTimedTask1);
                    return;
                }
                log.info("【审核预警消息处理】任务{}审核提醒消息，内容一致，发送消息", taskInfo.getMassName());
                if (StatusConstants.TIMED_TASK_SEND_ONCE == taskInfo.getTimedTask()) {
                    // 通知审核员
                    this.sendWarnApprovalMsg(taskInfo, approvalType.getType(), false,
                            false,
                            TbWxMassMessageInfo::getMassName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());
                    // 通知创建者
                    this.sendWarnApprovalMsg(taskInfo, approvalType.getType(), false,
                            true,
                            TbWxMassMessageInfo::getMassName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());

                } else {
                    // 通知审核员
                    this.sendWarnApprovalMsg(taskInfo, approvalType.getType(), true,
                            false,
                            TbWxMassMessageInfo::getMassName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());
                    // 通知创建者
                    this.sendWarnApprovalMsg(taskInfo, approvalType.getType(), true,
                            true,
                            TbWxMassMessageInfo::getMassName,
                            t -> t.getCreateBy().toString(), t -> t.getId().toString());

                }
                break;
        }
    }

    /**
     * 比较两个日期是否相同
     * 比较到秒
     * @param oldDate
     * @param newDate
     * @return
     */
    boolean compareDate(Date oldDate, Date newDate) {
        // 将日前格式话为 yyyy-MM-dd HH:mm:ss
        String oldDateStr = DateUtil.formatDateTime(oldDate);
        String newDateStr = DateUtil.formatDateTime(newDate);
        return oldDateStr.equals(newDateStr);
    }
    public <T> void sendWarnApprovalMsg(T info, String type, boolean timeCorn, boolean isCreator, Function<T, String> nameGetter, Function<T, String> creatorGetter, Function<T, String> idGetter) {
        // 统一处理逻辑
        ApprovalTypeEnum approvalType = ApprovalTypeEnum.getType(type);
        String taskName = nameGetter.apply(info);
        String createBy = creatorGetter.apply(info);
        String id = idGetter.apply(info);
        // 【审核预警】任务{任务名称}长时间未审核。
        // 【审核预警】任务{任务名称}剩余30分钟。
        log.info("【审核预警消息处理】审核预警{}，发送消息：{}", approvalType.getDesc(), taskName);

        String title = "审核预警" ;
        String statusDesc = !timeCorn ? "长时间未审核。" : "剩余30分钟。";
        String content = StrUtil.format("{}任务【{}】{}",approvalType.getDesc(),
                taskName,statusDesc);
        String noticeUser = createBy;
        if (!isCreator) {
            // 非通知创建人，通知审批人
            List<String> approvalUserList = approvalWorkService.getApprovalUser(approvalType.getPerms(), createBy);
            if (CollectionUtil.isEmpty(approvalUserList)) {
                log.info("【审核预警消息处理】任务{}审核预警时，审批人列表为空，不发送消息");
                return;
            }
            noticeUser = approvalUserList.stream().collect(Collectors.joining(","));
        }
        this.handlerMsgNotify(
                title,
                content,
                content, // 保持 msgContent 与 content 一致
                noticeUser,
                CorpInfoProperties.getCorpId(),
                approvalType.getType(),
                id,
                isCreator ? null : "approval"
        );
    }


    /**
     * 处理消息通知
     *
     * @param corpId
     * @param businessType
     * @param businessId
     * @param businessOper
     */
    public void handlerMsgNotify(String title, String content, String msgContent,
                                 String userId, String corpId, String businessType,
                                 String businessId, String businessOper) {
        SysNoticeMsgDto sysNoticeMsgDto = new SysNoticeMsgDto();
        sysNoticeMsgDto.setNoticeTitle(title);
        sysNoticeMsgDto.setCorpId(corpId);
        sysNoticeMsgDto.setNoticeContent(content);
        sysNoticeMsgDto.setAgentMsgContent(msgContent);
        sysNoticeMsgDto.setNoticeUser(userId);
        sysNoticeMsgDto.setNoticeMode(SysNoticeEnum.NOTICE_MODE_ADMIN.getCode()+"");
        sysNoticeMsgDto.setBusinessId(businessId);
        sysNoticeMsgDto.setBusinessType(businessType);
        sysNoticeMsgDto.setBusinessOper(businessOper);
        mqSendMessageManager.sendSysMsg(sysNoticeMsgDto);
        log.info("【审核预警消息处理】发送系统消息通知：{}", sysNoticeMsgDto);
    }
}
