package com.cenker.scrm.manager;

import com.cenker.scrm.constants.RabbitMqQueuesConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatArchiveSendMessageManager extends BaseRabbitMessageQueueManager{

    @Async
    public void sendPullChatArchiveMessage(String msgBody) {
        log.info("【会话存档】处理会话消息拉取, 发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CHAT_ARCHIVE_PULL_MESSAGE, msgBody);
    }

    @Async
    public void sendSaveChatArchiveMessage(String msgBody) {
        log.info("【会话存档】处理会话消息保存, 发送mq消息:{}", msgBody);
        sendMessageQueueRetry(RabbitMqQueuesConstant.CHAT_ARCHIVE_SAVE_MESSAGE, msgBody);
    }
}
