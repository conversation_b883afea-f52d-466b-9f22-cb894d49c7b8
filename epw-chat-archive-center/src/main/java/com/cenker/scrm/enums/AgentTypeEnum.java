package com.cenker.scrm.enums;

/**
 * 应用类型枚举
 */
public enum AgentTypeEnum {
    /**
     * 通讯录
     */
    BOOK(1,"book", "通讯录", "Key值：CorpId +#+ book，例如：wx123#book"),
    CUSTOMER(2,"customer", "客户联系", "Key值：CorpId +#+ cutomer，例如：wx123#cutomer"),
    TPAGENT(0,"tpagent","待开发自建应用", "Key值：CorpId +#+ tpagent，例如：wx123#tpagent"),
    CPAGENT(0,"cpagent","自建应用", "Key值：CorpId +#+ cpagent +#+ AgentId，例如：wx123#cpagent#1234"),
    MCPAGENT(5,"mcpagent","主自建应用", "用于在没有通讯录和客户联系的情况下能够完成系统运行"),
    WECHATKF(3,"wechatkf","微信客服", "Key值：CorpId +#+ wechatkf，例如：wx123#wechatkf"),
    REDPACKET(4,"redpacket","企业红包", "Key值：CorpId +#+ redpacket，例如：wx123#redpacket"),
    CHATARCHIVE(6,"chatarchive","会话存档", "Key值：CorpId +#+ redpacket，例如：wx123#redpacket");

    private Integer type;

    private String key;

    private String name;

    private String desc;

    private AgentTypeEnum(Integer type, String key, String name, String desc){
        this.type = type;
        this.key = key;
        this.name = name;
        this.desc = desc;
    }

    public static AgentTypeEnum getAgentTypeEnum(Integer type){
        for(AgentTypeEnum typeEnum : AgentTypeEnum.values()){
            if(Integer.compare(typeEnum.type, type) == 0){
                return typeEnum;
            }
        }
        return null;
    }

    public Integer getType(){
        return this.type;
    }

    public int getTypeIntValue(){
        return this.type.intValue();
    }

    public String getKey(){
        return this.key;
    }
    public String getName(){
        return this.name;
    }

}
