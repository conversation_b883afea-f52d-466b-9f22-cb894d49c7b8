package com.cenker.scrm.enums;


/**
 * 缓存Key 枚举
 *
 * @ClassName ResultCodeEnum
 * <AUTHOR>
 * @Date 2021/6/19 16:39
 **/
public enum CacheKeyEnum {

    CORP_CONFIG_KEY("corp:config:", 24 * 60 * 60, "企业配置缓存Key"),
    CHAT_TASK_LOCK_KEY("chat:task:lock:", 5 * 60, "会话消息定时任务锁Key"),
    CHAT_COMPLETE_MQ_LOCK_KEY("chat:complete:mq:lock", 10, "会话消息完成锁Key"),
    CURRENT_PULLING_BATCH("current:pulling:batch:", 4 * 60, "正在拉取的批次"),
    CURRENT_PULLING_MAX_SEQ("current:pulling:maxSeq:", 4 * 60 * 60, "正在拉取最大批次"),
    CHECK_CONSUMED_KEY("check:consumed:key", 60, "检查定时任务是否被消费key"),
    CHECK_ARCHIVE_FILE_KEY("chatarchive:file", 4 * 60 * 60, "会话存档临时文件路径缓存");

    /**
     * 缓存Key
     */
    private String key;
    /**
     * 过期时间，单位：秒
     */
    private long expire;
    /**
     * 描述
     */
    private String descrition;

    CacheKeyEnum(String key, long expire, String descrition) {
        this.key = key;
        this.expire = expire;
        this.descrition = descrition;
    }

    public String getKey() {
        return key;
    }

    public long getExpire() {
        return expire;
    }

    public String getDescrition() {
        return descrition;
    }
}
