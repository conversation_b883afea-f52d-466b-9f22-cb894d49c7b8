package com.cenker.scrm.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class ChatArchiveConfig {

    @Value("${chat.archive.pull.limit:600}")
    private long chatLimit;

    @Value("${chat.archive.upload.path:D:\\Cenker\\Efunds\\efunds-epw-scrm\\upload\\chatarchive}")
    private String uploadPath;

    @Value("${chat.archive.pull.timeout:60}")
    private Long pullTimeOut;

}
