package com.cenker.scrm.task;

import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.service.PullChatArchiveService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName ChatArchiveTask
 * <AUTHOR>
 * @Date 2021/9/2 15:25
 **/
@Slf4j
@Component
public class ChatArchiveTask {


    @Resource
    private PullChatArchiveService pullChatArchiveService;


    // 每5分钟执行1次
//    @Scheduled(cron = "0 0/5 * * * ?")
//    @RabbitListener(queues = RabbitMqQueuesConstant.CHAT_ARCHIVE_MSG_AUDIT_NOTIFY, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    @XxlJob(XxlJobContant.PULL_CHAT_ARCHIVE)
    public void startPullChatArchive() {
        log.info("开始会话记录同步");
        pullChatArchiveService.pullChatArchive();
    }


}
