package com.cenker;

import com.cenker.scrm.constants.SpringBootConstant;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableEurekaClient
@EnableApolloConfig
@EnableAsync
@SpringBootApplication
@EnableFeignClients
@MapperScan(SpringBootConstant.MAPPER_SCAN)
@ComponentScan(value = {SpringBootConstant.OSS_SCAN, SpringBootConstant.COMPONENT_SCAN})
public class ChatArchiveCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(ChatArchiveCenterApplication.class, args);
    }

}
