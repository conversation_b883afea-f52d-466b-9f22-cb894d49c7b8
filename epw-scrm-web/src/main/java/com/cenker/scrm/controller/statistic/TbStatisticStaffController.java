package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.TbStatisticStaffFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticStaff;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticStaffListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 数据统计-员工数据 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/statistic/staff")
public class TbStatisticStaffController extends BaseController {

    private final TbStatisticStaffFeign tbStatisticStaffFeign;

    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(StatisticSummaryQuery query) {
        return tbStatisticStaffFeign.summary(query);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(StatisticGraphQuery query) {
        return tbStatisticStaffFeign.graph(query);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(StatisticStaffListQuery query) {
        return tbStatisticStaffFeign.list(query);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    @Log(module = ModuleEnum.STAFF_STATISTICS, businessType = BusinessType.EXPORT)
    public void export(StatisticStaffListQuery query, HttpServletResponse response) throws UnsupportedEncodingException {
        ExcelUtil<TbStatisticStaff> util = new ExcelUtil<>(TbStatisticStaff.class);
        util.exportExcel(tbStatisticStaffFeign.export(query), "员工数据", response);
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("员工数据-数据明细-"+ DateUtils.dateTimeNow(), "utf-8"));
    }

    /**
     * 更新数据
     */
    @PostMapping("/synData")
    @Log(module = ModuleEnum.STAFF_STATISTICS, businessType = BusinessType.UPDATE_DATA)
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        Result<Object> PARAM_ERROR = validateDate(query);
        if (PARAM_ERROR != null) return PARAM_ERROR;
        return tbStatisticStaffFeign.synData(query);
    }
}
