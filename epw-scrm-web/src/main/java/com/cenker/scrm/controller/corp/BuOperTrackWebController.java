package com.cenker.scrm.controller.corp;

import com.cenker.scrm.client.system.feign.SysDictDataFeign;

import com.cenker.scrm.client.wechat.feign.BuOperTrackFeign;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackSearchVO;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackVO;
import com.cenker.scrm.util.BuOperTrackUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 客户详情-客户动态
 * @date 2021/9/27 15:09
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/bu/oper/track")
public class BuOperTrackWebController {
    private final BuOperTrackFeign buOperTrackFeign;
    private final SysDictDataFeign sysDictDataFeign;
    @GetMapping("/list")
    public TableDataInfo<BuOperTrackVO> getOperTrackList(@RequestParam String externalUserId) {
        log.info("【获取客户动态列表】, externalUserId={}", externalUserId);
        TableDataInfo<BuOperTrackVO> customerOpeLogList = buOperTrackFeign.getCustomerOpeLogList(BuOperTrackSearchVO.builder().externalUserId(externalUserId).build());
        AjaxResult channel = sysDictDataFeign.dictType("material_delivery_channel");
        log.info("【获取客户动态列表】处理脚标");
        BuOperTrackUtils.dealData((List<BuOperTrackVO>)customerOpeLogList.getRows(), null, channel);
        return customerOpeLogList;
    }
}
