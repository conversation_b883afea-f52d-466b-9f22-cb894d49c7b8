package com.cenker.scrm.controller.corp;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.client.wechat.feign.TbWxUserFeign;
import com.cenker.scrm.client.wechat.feign.WxOauth2Feign;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.efunds.EfundAuthCenterFeignClient;
import com.cenker.scrm.efunds.constants.AuthCenterConstants;
import com.cenker.scrm.efunds.model.AuthCenterUserRoleRequest;
import com.cenker.scrm.efunds.model.Result;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.EditPermissionDto;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.system.SysUserFeign;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.wx.model.WxUserBaseInfoUpdateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 通讯录相关客户Controller
 */
//@Api(tags = "企业成员接口")
@RestController
@RequestMapping("/tp/wx_wx_user")
@Slf4j
public class TbWxUserController {

    @Autowired
    private TbWxUserFeign tbWxUserFeign;
    @Autowired
    private WxOauth2Feign wxOauth2Feign;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private EfundAuthCenterFeignClient  efundAuthCenterFeignClient;
    @Autowired
    private SysUserFeign sysUserFeign;

    /**
     * 查询通讯录相关客户列表
     */
    //@PreAuthorize("@ss.hasPermi('tp:wx_wx_user:list')")
    //@ApiOperation("查询客户列表")
    @GetMapping("/getUserList")
    public TableDataInfo getUserList() {
        return tbWxUserFeign.getUserList();
    }

    /**
     * 查询通讯录相关客户列表
     */
    //@ApiOperation("分页查询通讯录相关客户列表")
    //@PreAuthorize("@ss.hasPermi('tp:wx_wx_user:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbWxUser tbWxUser) {
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        tbWxUser.setCorpId(corpId);
        return tbWxUserFeign.list(tbWxUser);
    }

    /**
     * 根据部门Id获取部门下所有成员(同步成员)
     */
    // @PreAuthorize("@ss.hasPermi('modules:department:syncUserList,modules:permissionManagement:syncUserList')")
    @GetMapping("/synchronizationWxUserByDeptId/{deptId}")
    @Log(module = ModuleEnum.PERMISSION_MANAGEMENT, businessType = BusinessType.SYNCHRONIZATION)
    public AjaxResult synchronizationWxUserByDeptId(@PathVariable("deptId") Long deptId) {
        return tbWxUserFeign.synchronizationWxUserByDeptId(deptId);
    }

    /**
     * 获取自建应用/工作台jsapi_ticket 加入token
     */
    @GetMapping("/getJsapiTicket")
    public AjaxResult getJsapiTicketWeb(){
        return wxOauth2Feign.getJsapiAppTicket();
    }

    //@ApiOperation("设置企业管理员")
    // @PreAuthorize("@ss.hasPermi('tp:wx_wx_user:set_admin')")
    @GetMapping("/setCorpAdmin")
    @RepeatSubmit
    public AjaxResult setCorpAdmin(@Valid TbWxUser tbWxUser) {
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        tbWxUser.setCorpId(corpId);
        return tbWxUserFeign.setCorpAdmin(tbWxUser);
    }

    // @PreAuthorize("@ss.hasPermi('tp:wx_wx_user:set_sideAble')")
    @GetMapping("/setSideAble")
    @RepeatSubmit
    public AjaxResult setSideAble(@Valid TbWxUser tbWxUser) {
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        tbWxUser.setCorpId(corpId);
        return tbWxUserFeign.setSideAble(tbWxUser);
    }


//    @PreAuthorize("@ss.hasPermi('tp:wx_wx_user:set_sideAble')")
    @PostMapping("/editPermission")
    @Log(module = ModuleEnum.PERMISSION_MANAGEMENT, businessType = BusinessType.GRANT)
    @RepeatSubmit
    public AjaxResult editPermission(@RequestBody EditPermissionDto editPermissionDto) {
        TbWxUser tbWxUser = new TbWxUser();
        tbWxUser.setSideAble(editPermissionDto.getIsAdmin());
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        tbWxUser.setCorpId(corpId);
        tbWxUser.setRoleId(editPermissionDto.getRoleId());
        tbWxUser.setUserid(editPermissionDto.getUserId());
        tbWxUser.setSideAble(editPermissionDto.getIsAdmin());
        // administrator字段用于判断是否拥有管理后台权限，如果有管理后台的角色，则表示有后台权限
        tbWxUser.setAdministrator(Objects.nonNull(editPermissionDto.getRoleId()));
        tbWxUserFeign.setSideAble(tbWxUser);
        AjaxResult result = tbWxUserFeign.setCorpAdmin(tbWxUser);

        /** 3.1.2权限同步到易发现工作权限中心 **/
        if(HttpStatus.SUCCESS == Integer.valueOf(result.get(AjaxResult.CODE_TAG).toString())) {

            // 获取用户域账号
            RemoteResult<SysUser> sysUserResult = sysUserFeign.selectUserByWxUserId(tbWxUser.getUserid());
            if (Objects.nonNull(sysUserResult.getData())) {
                SysUser sysUserOld = sysUserResult.getData();
                if (StrUtil.isNotEmpty(sysUserOld.getDomainAccount())) {
                    AuthCenterUserRoleRequest authCenterRequest = new AuthCenterUserRoleRequest();
                    List<String> userIdList = new ArrayList<>();
                    userIdList.add(sysUserOld.getDomainAccount());
                    authCenterRequest.setUserIdList(userIdList);
                    authCenterRequest.setPlatformCode(AuthCenterConstants.PLATFORM_CODE);
                    authCenterRequest.setRelCode(AuthCenterConstants.REL_CODE);
                    authCenterRequest.setRelType(AuthCenterConstants.REL_TYPE_ROLE);
                    // 场景1：域账号不为空，并且角色不为空，添加新域账号角色; 场景2：域账号不为空，并且角色为空，删除域账号角色
                    authCenterRequest.setSaveType(StrUtil.isNotEmpty(tbWxUser.getRoleId()) ? AuthCenterConstants.SAVE_TYPE_ADD : AuthCenterConstants.SAVE_TYPE_DEL);
                    try {
                        log.info("【同步权限中心】,请求入参{}", JSON.toJSONString(authCenterRequest));
                        Result synAuthCenterResult = efundAuthCenterFeignClient.synAuthCenter(authCenterRequest);
                        log.info("【同步权限中心】,响应结果{}", JSON.toJSONString(synAuthCenterResult));
                    } catch (Exception e) {
                        log.error("【同步权限中心】失败！{}", e.getMessage());
                    }

                }
            }
        }

        return result;
    }

    @GetMapping("/getPermission/{userId}")
    public AjaxResult getPermission(@PathVariable("userId") String userId) {
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        TbWxUser tbWxUser = new TbWxUser();
        tbWxUser.setUserid(userId);
        tbWxUser.setCorpId(corpId);
        return tbWxUserFeign.getPermission(tbWxUser);
    }

    @PutMapping("/updateUserWelcomeContact")
    @Log(module = ModuleEnum.PERMISSION_MANAGEMENT, businessType = BusinessType.UPDATE)
    AjaxResult updateUserInfo(@RequestBody WxUserBaseInfoUpdateDto params){
        LogUtil.logOperDesc("修改个人名片");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        params.setCorpId(loginUser.getUser().getCorpId());
        params.setUpdateBy(loginUser.getUser().getUserId());
        return tbWxUserFeign.updateUserWelcomeContact(params);
    }

    @GetMapping("/userWelcomeContact")
    AjaxResult getUserWelcomeContact(@RequestParam("userId") String userId){
        return tbWxUserFeign.getUserWelcomeContact(userId);
    }
}
