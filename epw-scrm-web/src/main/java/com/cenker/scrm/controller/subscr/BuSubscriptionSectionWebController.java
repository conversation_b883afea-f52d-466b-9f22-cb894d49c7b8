package com.cenker.scrm.controller.subscr;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.subscr.BuSubscriptionSectionFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.*;
import com.cenker.scrm.util.LogUtil;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订阅栏目
 */
@AllArgsConstructor
@RestController
@RequestMapping("/bu/subscription/section")
public class BuSubscriptionSectionWebController {
    private final BuSubscriptionSectionFeign buSubscriptionSectionFeign;
    /**
     * 获取订阅栏目列表
     * @param query
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<BuSubscriptionSectionVO> list(BuSubscriptionQuery query) {
        return buSubscriptionSectionFeign.list(query);
    }
    /**
     * 新增订阅栏目
     * @param detail
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @Log(module = ModuleEnum.SUBSCRIBE_SECTION, businessType = BusinessType.INSERT)
    public Result add(@RequestBody @Validated(InsertGroup.class) BuSubscriptionSectionDetail detail) {
        LogUtil.logOperDesc(detail.getSectionName());
        return buSubscriptionSectionFeign.add(detail);
    }
    /**
     * 删除订阅栏目
     * @param id
     * @return
     */
    @DeleteMapping
    @Log(module = ModuleEnum.SUBSCRIBE_SECTION, businessType = BusinessType.DELETE)
    public Result remove(@RequestParam("id") String id) {
        Result<BuSubscriptionSectionDetail> detail = buSubscriptionSectionFeign.detail(id);
        if (detail.isSuccess() && detail.getData()!= null) {
            LogUtil.logOperDesc(detail.getData().getSectionName());
        }
        return buSubscriptionSectionFeign.remove(id);
    }
    /**
     * 根据id获取订阅栏目详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public Result<BuSubscriptionSectionDetail> detail(@RequestParam("id") String id) {
        return buSubscriptionSectionFeign.detail(id);
    }

    /**
     * 修改订阅栏目
     * @param detailVO
     * @return
     */
    @RepeatSubmit
    @PutMapping
    @Log(module = ModuleEnum.SUBSCRIBE_SECTION, businessType = BusinessType.UPDATE)
    public Result update(@RequestBody @Validated(UpdateGroup.class) BuSubscriptionSectionDetail detailVO) {
        LogUtil.logOperDesc(detailVO.getSectionName());
        return buSubscriptionSectionFeign.update(detailVO);
    }
}