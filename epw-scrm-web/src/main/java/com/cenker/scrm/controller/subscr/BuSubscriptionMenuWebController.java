package com.cenker.scrm.controller.subscr;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.subscr.BuSubscriptionMenuFeign;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.valid.*;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuDetail;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuVO;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionQuery;
import com.cenker.scrm.pojo.vo.subscr.ChangeStatusVO;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订阅菜单
 */
@AllArgsConstructor
@RestController
@RequestMapping("/bu/subscription/menu")
public class BuSubscriptionMenuWebController {
    private final BuSubscriptionMenuFeign buSubscriptionMenuFeign;
    private final TokenService tokenService;
    /**
     * 获取订阅菜单列表
     * @param query
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<BuSubscriptionMenuVO> list(BuSubscriptionQuery query) {
        return buSubscriptionMenuFeign.list(query);
    }
    /**
     * 新增订阅菜单
     * @param detailVO
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @Log(module = ModuleEnum.SUBSCRIBE_MENU, businessType = BusinessType.INSERT)
    public Result add(@RequestBody @Validated(InsertGroup.class) BuSubscriptionMenuDetail detailVO) {
        LogUtil.logOperDesc(detailVO.getMenuName());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        detailVO.setCreateBy(user.getUserId());
        detailVO.setCorpId(user.getCorpId());
        return buSubscriptionMenuFeign.add(detailVO);
    }
    /**
     * 删除订阅菜单
     * @param id
     * @return
     */
    @DeleteMapping
    @Log(module = ModuleEnum.SUBSCRIBE_MENU, businessType = BusinessType.DELETE)
    public Result remove(@RequestParam("id") String id) {
        Result<BuSubscriptionMenuDetail> detail = buSubscriptionMenuFeign.detail(id);
        if (detail.isSuccess() && detail.getData() != null) {
            LogUtil.logOperDesc(detail.getData().getMenuName());
        }
        return buSubscriptionMenuFeign.remove(id);
    }
    /**
     * 根据id获取订阅菜单详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public Result<BuSubscriptionMenuDetail> detail(@RequestParam("id") String id) {
        return buSubscriptionMenuFeign.detail(id);
    }
    /**
     * 修改订阅菜单
     * @param detailVO
     * @return
     */
    @RepeatSubmit
    @PutMapping
    @Log(module = ModuleEnum.SUBSCRIBE_MENU, businessType = BusinessType.UPDATE)
    public Result update(@RequestBody @Validated(UpdateGroup.class) BuSubscriptionMenuDetail detailVO) {
        LogUtil.logOperDesc(detailVO.getMenuName());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        detailVO.setCreateBy(user.getUserId());
        detailVO.setCorpId(user.getCorpId());
        detailVO.setUpdateBy(user.getUserId());
        return buSubscriptionMenuFeign.update(detailVO);
    }
    /**
     * 启用/禁用订阅菜单
     * @param statusVO
     * @return
     */
    @PostMapping("/changeStatus")
    @Log(module = ModuleEnum.SUBSCRIBE_MENU, businessType = BusinessType.OTHER)
    public Result changeStatus(@RequestBody ChangeStatusVO statusVO) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        BuSubscriptionMenuDetail detail = new BuSubscriptionMenuDetail();
        detail.setId(statusVO.getId());
        detail.setReleaseStatus(StatusConstants.SUBSCRIBE_MENU_PUBLISH_TRUE == statusVO.getStatus() ? StatusConstants.SUBSCRIBE_MENU_PUBLISH_TRUE : StatusConstants.SUBSCRIBE_MENU_PUBLISH_FALSE);
        detail.setUpdateBy(user.getUserId());
        return buSubscriptionMenuFeign.changeStatus(detail);
    }
    /**
     * 验证子菜单/栏目所在订阅菜单是否正式对客
     * @param id
     * @param type subMenu 子菜单 section 栏目
     * @return
     */
    @GetMapping("/validateIsPublish")
    public Result validateIsPublish(@RequestParam("id") String id, @RequestParam("type") String type) {
        return buSubscriptionMenuFeign.validateIsPublish(id , type);
    }
}