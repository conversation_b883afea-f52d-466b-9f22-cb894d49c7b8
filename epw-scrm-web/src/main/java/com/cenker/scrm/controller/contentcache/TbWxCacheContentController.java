package com.cenker.scrm.controller.contentcache;

import com.cenker.scrm.client.wechat.feign.TbWxCacheContentFeign;
import com.cenker.scrm.pojo.dto.cachecontent.CacheContentDTO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 暴露内容缓存接口
 * @date 2023/5/25 11:48
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/tp/cachecontent")
public class TbWxCacheContentController {
    private final TbWxCacheContentFeign cacheContentFeign;

    /**
     * 查询内容缓存
     *
     * @param type
     * @return
     */
    @GetMapping("/list/{type}")
    public AjaxResult list(@PathVariable("type") String type) {
        return cacheContentFeign.list(type);
    }

    /**
     * 根据id删除指定的缓存
     *
     * @param id
     * @return
     */
    @GetMapping("/deleteById/{id}")
    public AjaxResult deleteById(@PathVariable("id") String id) {
        return cacheContentFeign.deleteById(id);
    }

    /**
     * 根据id，内容修改指定缓存值
     * @param id
     * @param cacheContentDTO
     * @return
     */
    @PostMapping("/updateByCondition/{id}")
    public AjaxResult updateByCondition(@PathVariable("id") String id, @RequestBody CacheContentDTO cacheContentDTO) {
        return cacheContentFeign.updateByCondition(id,cacheContentDTO);
    }
}