package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.ListUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxRadarInteractFeign;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.radar.RadarBatchOperateDto;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.InteractRadarVo;
import com.cenker.scrm.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description 互动雷达-更名为智能物料
 */
@RestController
@RequestMapping("/interact/radar")
public class TbWxRadarInteractWebController {
    @Autowired
    private TbWxRadarInteractFeign radarInteractFeign;
    @Autowired
    private TokenService tokenService;

    @PostMapping
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:add')")
    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody @Valid InteractRadarVo radarVo){
        if (StringUtils.isEmpty(radarVo.getTbWxRadarContent().getTitle()) ||
                StringUtils.isEmpty(radarVo.getTbWxRadarContent().getContent())) {
            throw new CustomException("标题或内容为空！");
        }

        // 如果 InteractRadarVo.TbWxRadarContent.cover 字段不为空，判断其格式，如果不是JPG、JPEG、PNG格式，抛出异常
        // 还需兼容大小写
        if (StringUtils.isNotEmpty(radarVo.getTbWxRadarContent().getCover())) {
            String cover = radarVo.getTbWxRadarContent().getCover().toLowerCase();
            List<String> validExtensions = ListUtil.of(".jpg", ".jpeg", ".png");
            if (!validExtensions.stream().anyMatch(cover::endsWith)) {
                throw new CustomException("封面图片格式不正确，请上传JPG、JPEG、PNG格式图片！");
            }
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        radarVo.setCorpId(loginUser.getTenantId());
        radarVo.setCreateBy(loginUser.getUserId());
        radarVo.setCreateTime(DateUtils.getNowDate());
        radarVo.setDeptId(loginUser.getDeptId());

        LogUtil.logOperDesc(radarVo.getTitle());
        return radarInteractFeign.add(radarVo);
    }

    @GetMapping("/list")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:list')")
    public TableDataInfo<InteractRadarVo> list(InteractRadarVo radarVo){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        // 设置用户信息
        radarVo.setAdministrator(user.getAdministrator() != null && user.getAdministrator() ? 1 : 0);
        radarVo.setUserId(user.getUserId());
        radarVo.setCorpId(user.getCorpId());
        if (StringUtils.isNotEmpty(user.getCorpUserId())) {
            // 添加消息推送人等
            radarVo.setStaffId(user.getCorpUserId());
        }
        return radarInteractFeign.list(radarVo);
    }

    @DeleteMapping
    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.DELETE)
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:delete')")
    public AjaxResult remove(InteractRadarVo radarVo){
        if (StringUtils.isEmpty(radarVo.getId())) {
            return AjaxResult.error("参数错误");
        }

        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        radarVo.setCorpId(user.getCorpId());
        radarVo.setUserId(user.getUserId());
        return radarInteractFeign.remove(radarVo);
    }

//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:data')")
    @GetMapping("/{radarId}")
    public Result<InteractRadarVo> getById(@PathVariable("radarId")String radarId){
        return radarInteractFeign.getById(radarId);
    }

    @PutMapping
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi(',modules:intelligentMaterials:edit,tp:contentRadar:person:edit',#radarVo.scope)")
    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody @Valid InteractRadarVo radarVo){
        if (StringUtils.isEmpty(radarVo.getTbWxRadarContent().getTitle()) ||
                StringUtils.isEmpty(radarVo.getTbWxRadarContent().getContent())) {
            throw new CustomException("非法请求");
        }
        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        radarVo.setCreateTime(null);
        radarVo.setCreateBy(null);
        radarVo.setUpdateBy(userId);

        LogUtil.logOperDesc(radarVo.getTitle());
        return radarInteractFeign.edit(radarVo);
    }

    /**
     * 管理后台雷达统计数据
     */
    @GetMapping("/getRadarStatistics/{radarId}")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:data')")
    public AjaxResult getRadarStatistics(@PathVariable("radarId")String radarId){
        return radarInteractFeign.getRadarStatistics(radarId);
    }

    /**
     * 雷达图文点击数据
     */
    @GetMapping("/getRadarReadRecordStatistics/{radarId}")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:data')")
    public TableDataInfo getRadarReadRecordStatistics(@PathVariable("radarId")String radarId){
        return radarInteractFeign.getRadarReadRecordStatistics(radarId);
    }

    /**
     * 雷达点击详情数据
     */
    @GetMapping("/getRadarReadRecordDetail/{customerId}")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:data')")
    public TableDataInfo getRadarReadRecordDetail(@PathVariable("customerId")String customerId){
        return radarInteractFeign.getRadarReadRecordDetail(customerId);
    }

    @PostMapping("/extract/radarContent")
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:list')")
//    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.OTHER)
    public AjaxResult extractRadarContent(@RequestBody TbWxRadarContent content){
        if (StringUtils.isEmpty(content.getUrl()) && (content.getUrl().startsWith("https") || content.getUrl().startsWith("http"))) {
            return AjaxResult.error("链接地址请以http或https开头");
        }
        if (!content.getUrl().startsWith(DefaultConstants.ARTICLE_LINK_BEGIN)) {
            return AjaxResult.error("该文章链接存在异常，无法提取");
        }
        return radarInteractFeign.extractRadarContent(content);
    }

    /**
     * 提取链接数据
     */
    @PostMapping("/extract/linkData")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:list')")
    public AjaxResult extractLinkData(@RequestBody TbWxRadarContent content){
        return radarInteractFeign.extractLinkData(content);
    }

    /**
     * 获取所有企业物料 给其他如渠道活码功能使用
     */
    @GetMapping("/getRadarSource")
//    @PreAuthorize("@ss.hasPermi('modules:intelligentMaterials:list')")
    public TableDataInfo getRadarSource(InteractRadarVo radarVo){
        radarVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        // 只查询企业物料
        radarVo.setScope(1);
        return radarInteractFeign.getRadarSource(radarVo);
    }

    @PutMapping("/batchOperate")
    public AjaxResult batchOperate(@RequestBody RadarBatchOperateDto batchOperateDto) {
        batchOperateDto.setLoginUserId(AuthUtil.getUserId(ServletUtils.getRequest()));
        return radarInteractFeign.batchOperate(batchOperateDto);
    }

    @PostMapping("/approve")
    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.APPROVAL)
    public Result approve(@Validated @RequestBody ApprovalVO approvalVO){
        Result<InteractRadarVo> detail = radarInteractFeign.getById(approvalVO.getId());
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("审核智能物料【%s】：%s", detail.getData().getTitle(), approvalVO.getAgree() ? "通过" : "驳回");
            LogUtil.recordOperLog(operDesc);
        }
        return radarInteractFeign.approve(approvalVO);
    }

    @PostMapping("/revoked")
    @Log(module = ModuleEnum.INTELLIGENT_MATERIAL, businessType = BusinessType.REVOKED)
    public Result revoked(@Validated @RequestBody ApprovalVO approvalVO){
        Result<InteractRadarVo> detail = radarInteractFeign.getById(approvalVO.getId());
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("撤回智能物料【%s】", detail.getData().getTitle());
            LogUtil.recordOperLog(operDesc);
        }
        return radarInteractFeign.revoked(approvalVO);
    }
}
