package com.cenker.scrm.controller.subscr;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.subscr.BuSectionStatisticsFeign;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.valid.SelectGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsVO;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 栏目数据统计
 */
@AllArgsConstructor
@RestController
@RequestMapping("/bu/section/statistics")
public class BuSectionStatisticsWebController extends BaseController {

    private final BuSectionStatisticsFeign buSectionStatisticsFeign;
    /**
     * 获取栏目的数据详情列表
     * @param query
     * @return
     */
    @GetMapping("/list")
    public Result<BuSectionStatisticsVO> list(@Validated(SelectGroup.class) BuSectionStatisticsQuery query) {
        if (TypeConstants.SECTION_TYPE_HISTORY.equals(query.getType())) {
            // 时间格式验证，开始时间和结束时间都不能为空，开始时间不能大于结束时间，且只允许查询最多180天的数据
            if (StrUtil.isEmpty(query.getBeginTime()) || StrUtil.isEmpty(query.getEndTime())) {
                return Result.error(500, "开始时间和结束时间都不能为空");
            }
            Date beginTime = DateUtils.parseDate(query.getBeginTime());
            Date endTime = DateUtils.parseDate(query.getEndTime());
            if (beginTime.after(endTime)) {
                return Result.error(500 ,"开始时间不能大于结束时间");
            }
            if (beginTime.before(DateUtils.addDays(endTime, -180))) {
                return Result.error(500, "只允许查询最多180天的数据");
            }
        } else if (TypeConstants.SECTION_TYPE_TODAY.equals(query.getType())) {
             ;
        } else {
            return Result.error(500, "类型错误");
        }
        return buSectionStatisticsFeign.list(query);
    }

    /**
     * 同步数据
     */
    @PostMapping("/synData")
    @Log(module = ModuleEnum.SUBSCRIBE_SECTION, businessType = BusinessType.UPDATE_DATA)
    public Result synData(@RequestBody BuSectionStatisticsQuery query) {
        if (StringUtils.isEmpty(query.getSectionId())) {
            return Result.error(500, "栏目ID不能为空");
        }
        return buSectionStatisticsFeign.synData(query);
    }
}