package com.cenker.scrm.controller.session;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.SessionTimeSetFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.pojo.dto.session.AddTimeSetDto;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 会话超时时长设置Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@RequestMapping("/session/timeset")
public class CkSessionTimeoutSetController
{
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SessionTimeSetFeign sessionTimeSetFeign;
    /**
     * 获取会话超时时长设置详细信息
     */

    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo()
    {
        return sessionTimeSetFeign.getInfo();
    }


    /**
     * 保存会话超时时长设置
     */
    @PreAuthorize("@ss.hasPermi('module:replyTimeoutSetting:edit')")
    @Log(module = ModuleEnum.REPLY_TIMEOUT_SETTING, businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AddTimeSetDto addTimeSetDto)
    {
        LogUtil.logOperDesc(null);
        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        addTimeSetDto.setCreateBy(userId);
        return sessionTimeSetFeign.edit(addTimeSetDto);
    }
}
