package com.cenker.scrm.controller.corp;


import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxMaterialFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterialPoster;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.util.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 素材信息Controller
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@RestController
@RequestMapping("/tp/material")
public class TbWxMaterialController{

    @Autowired
    private TbWxMaterialFeign tbWxMaterialFeign;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询素材信息列表
     */
    @GetMapping("/list")
    public TableDataInfo<TbWxMaterial> list(MaterialListDto param) {
        return tbWxMaterialFeign.list(param);
    }

    /**
     * 导出素材信息列表
     */
    //@PreAuthorize("@ss.hasPermi('tp:material:export')")
    @GetMapping("/export")
    public AjaxResult export(TbWxMaterial tbWxMaterial) {
        return tbWxMaterialFeign.export(tbWxMaterial);
    }

    /**
     * 获取素材信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('tp:material:query')")
    @GetMapping(value = "/{id}")
    public Result<TbWxMaterial> getInfo(@PathVariable("id") Long id) {
        return tbWxMaterialFeign.getInfo(id);
    }

    /**
     * 新增素材信息
     */
    //@PreAuthorize("@ss.hasPermi('tp:imageMaterial:add,tp:audioMaterial:add,tp:videoMaterial:add,tp:textMaterial:add,tp:fileMaterial:add,tp:posterMaterial:add',#tbWxMaterial.mediaType)")
    @PostMapping
    @RepeatSubmit
    @Log(module = ModuleEnum.NORMAL_MATERIAL, businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody @Valid TbWxMaterial tbWxMaterial,BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            // 参数校验失败 默认返回第一条错误信息
            return AjaxResult.error(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }

        LogUtil.logOperDesc(tbWxMaterial.getMaterialName());

        return tbWxMaterialFeign.add(tbWxMaterial);
    }

    /**
     * 修改素材信息
     */
    //@PreAuthorize("@ss.hasPermi('tp:imageMaterial:edit,tp:audioMaterial:edit,tp:videoMaterial:edit,tp:textMaterial:edit,tp:fileMaterial:edit,tp:posterMaterial:edit',#tbWxMaterial.mediaType)")
    @PutMapping
    @Log(module = ModuleEnum.NORMAL_MATERIAL, businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody TbWxMaterial tbWxMaterial) {
        LogUtil.logOperDesc(tbWxMaterial.getMaterialName());
        return tbWxMaterialFeign.edit(tbWxMaterial);
    }

    /**
     * 删除素材信息
     */
    //@PreAuthorize("@ss.hasPermi('tp:imageMaterial:remove,tp:audioMaterial:remove,tp:videoMaterial:remove,tp:textMaterial:remove,tp:fileMaterial:remove,tp:posterMaterial:remove,',#tbWxMaterial.mediaType)")
    @DeleteMapping
    @Log(module = ModuleEnum.NORMAL_MATERIAL, businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestBody TbWxMaterial tbWxMaterial) {
        return tbWxMaterialFeign.remove(tbWxMaterial);
    }

    /**
     * 海报设置树结构
     * @param name
     * @return
     */
    @GetMapping("/treeSelect")
    public AjaxResult treeSelect(@RequestParam("name") String name) {
        return tbWxMaterialFeign.treeSelect(name);
    }

    /**
     * 根据链接类型获取海报信息
     * @param type
     * @return
     */
    @GetMapping("/poster/{type}")
    public AjaxResult poster(@PathVariable("type") String type) {
        return tbWxMaterialFeign.poster(type);
    }

    /**
     * 海报设置
     */
    @PutMapping("/posterSet")
    @Log(module = ModuleEnum.NORMAL_MATERIAL_POSTER, businessType = BusinessType.UPDATE)
    public AjaxResult posterSet(@RequestBody TbWxMaterialPoster tbWxMaterialPoster) {
        return tbWxMaterialFeign.posterSet(tbWxMaterialPoster);
    }

    @PostMapping("/approve")
    @Log(module = ModuleEnum.NORMAL_MATERIAL, businessType = BusinessType.APPROVAL)
    public Result approve(@Validated @RequestBody ApprovalVO approvalVO){
        Result<TbWxMaterial> detail = tbWxMaterialFeign.getInfo(Long.valueOf(approvalVO.getId()));
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("审核营销素材【%s】：%s", detail.getData().getMaterialName(), approvalVO.getAgree() ? "通过" : "驳回");
            LogUtil.recordOperLog(operDesc);
        }
        return tbWxMaterialFeign.approve(approvalVO);
    }

    @PostMapping("/revoked")
    @Log(module = ModuleEnum.NORMAL_MATERIAL, businessType = BusinessType.REVOKED)
    public Result revoked(@Validated @RequestBody ApprovalVO approvalVO){
        Result<TbWxMaterial> detail = tbWxMaterialFeign.getInfo(Long.valueOf(approvalVO.getId()));
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("撤回营销素材【%s】", detail.getData().getMaterialName());
            LogUtil.recordOperLog(operDesc);
        }
        return tbWxMaterialFeign.revoked(approvalVO);
    }
}
