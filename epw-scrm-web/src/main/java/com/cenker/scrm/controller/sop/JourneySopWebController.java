package com.cenker.scrm.controller.sop;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.JourneySopFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.sop.JourneySopStageDTO;
import com.cenker.scrm.pojo.dto.sop.SopJourneyContentDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.request.sop.ConditionSopQueryRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopQueryRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopRequest;
import com.cenker.scrm.pojo.valid.*;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.sop.JourneySopDetailVO;
import com.cenker.scrm.pojo.vo.sop.JourneySopListVO;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/7/25
 * @Description 旅程sop
 */
@RestController
@RequestMapping("/sop/journey")
@RequiredArgsConstructor
public class JourneySopWebController {

    private final JourneySopFeign journeySopFeign;
    private final TokenService tokenService;

    // @PreAuthorize("@ss.hasPermi('modules:enterpriseScript:add')")
    @RepeatSubmit
    @PostMapping
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody @Validated(InsertGroup.class) JourneySopRequest request) {
        LogUtil.logOperDesc(request.getSopName());

        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        request.setCreateBy(Long.valueOf(user.getUserId()));
        request.setCorpId(user.getCorpId());
        validContentSort(request);
        return journeySopFeign.add(request);
    }

    private void validContentSort(JourneySopRequest request) {
        List<JourneySopStageDTO> sopStageList = request.getSopStageList();
        Set<String> stageName = Sets.newHashSet();
        for (JourneySopStageDTO journeySopStageDTO : sopStageList) {
            List<SopJourneyContentDTO> sopContentList = journeySopStageDTO.getSopContentList();
            List<SopJourneyContentDTO> sortSopContentList = sopContentList.stream().sorted(Comparator.comparing(SopJourneyContentDTO::getContentSort)).collect(Collectors.toList());
            Set<Integer> setSopContentList = sopContentList.stream().map(SopJourneyContentDTO::getContentSort).collect(Collectors.toSet());
            for (int i = 0; i < sopContentList.size(); i++) {
                if (!sopContentList.get(i).getContentSort().equals(sortSopContentList.get(i).getContentSort())) {
                    throw new CustomException(ErrCodeEnum.SOP_CONTENT_SORT_ERROR);
                }
                if (sopContentList.size() != setSopContentList.size()) {
                    throw new CustomException(ErrCodeEnum.SOP_CONTENT_EXIST_REPEAT_ERROR);
                }
            }
            if (ObjectUtil.isNull(journeySopStageDTO.getStageId())) {
                List<SopJourneyContentDTO> collect =
                        sortSopContentList.stream().filter(sopJourneyContentDTO -> ObjectUtil.isNotNull(sopJourneyContentDTO.getContentId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
                }
            }
            stageName.add(journeySopStageDTO.getStageName());
        }
        if (sopStageList.size() != stageName.size()) {
            throw new ParameterException(ErrCodeEnum.EXTERNAL_JOURNEY_STAGE_NAME_REPEAT_ERROR);
        }

    }
    /**
     * 详情
     * @param request
     * @return
     */
    @GetMapping("/detail")
    public Result<JourneySopDetailVO> get(@Validated(DetailGroup.class) JourneySopRequest request) {
        return journeySopFeign.detail(request);
    }

    @GetMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@Validated(DataGroup.class) JourneySopQueryRequest request) {
        return journeySopFeign.getDataStatistics(request);
    }

    @RepeatSubmit
    @PutMapping
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody @Validated(UpdateGroup.class) JourneySopRequest request) {
        LogUtil.logOperDesc(request.getSopName());

        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        request.setUpdateBy(Long.valueOf(user.getUserId()));
        request.setCreateBy(Long.valueOf(user.getUserId()));
        request.setCorpId(user.getCorpId());
        validContentSort(request);
        return journeySopFeign.update(request);
    }

    /**
     * 条件查询旅程sop列表
     * @param request
     * @return
     */
    @GetMapping
    public TableDataInfo<JourneySopListVO> list(@Validated(SelectGroup.class) JourneySopRequest request) {
        return journeySopFeign.list(request);
    }

    @GetMapping("/sopTaskList")
    public TableDataInfo sopTaskList(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.sopTaskList(request);
    }

    @GetMapping("/taskExecuteList")
    public TableDataInfo taskExecuteList(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.taskExecuteList(request);
    }

    @GetMapping("/taskReachMessageList")
    public TableDataInfo taskReachMessageList(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.taskReachMessageList(request);
    }

    @GetMapping("/taskReachCustomerList")
    public TableDataInfo taskReachCustomerList(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.taskReachCustomerList(request);
    }

    @GetMapping("/churnCustomerList")
    public TableDataInfo churnCustomerList(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.churnCustomerList(request);
    }

    @PutMapping("/changeStatus")
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.OTHER)
    public AjaxResult changeStatus(@RequestBody @Validated(ChangeStatusGroup.class) JourneySopRequest request) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        request.setUpdateBy(Long.valueOf(user.getUserId()));
        return journeySopFeign.changeStatus(request);
    }

    @GetMapping("/remindToSend")
    public AjaxResult remindToSend(@Validated(DataGroup.class) ConditionSopQueryRequest request) {
        if (ObjectUtil.isNull(request.getStageId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        return journeySopFeign.remindToSend(request);
    }

    @DeleteMapping
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestBody @Validated(DeleteGroup.class) JourneySopRequest request) {
        return journeySopFeign.remove(request);
    }

    @PostMapping("/sync")
    @RepeatSubmit
    public AjaxResult synchronizeData(@RequestBody @Validated(DataGroup.class) ConditionSopQueryRequest request){
        return journeySopFeign.synchronizeData(request);
    }

    @PostMapping("/approve")
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.APPROVAL)
    public Result approve(@Validated @RequestBody ApprovalVO approvalVO){
        JourneySopRequest request = new JourneySopRequest();
        request.setSopId(Long.valueOf(approvalVO.getId()));
        Result<JourneySopDetailVO> detail = journeySopFeign.detail(request);
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("审核旅程SOP【%s】：%s", detail.getData().getSopName(), approvalVO.getAgree() ? "通过" : "驳回");
            LogUtil.recordOperLog(operDesc);
        }
        return journeySopFeign.approve(approvalVO);
    }

    @PostMapping("/revoked")
    @Log(module = ModuleEnum.JOURNEY_SOP, businessType = BusinessType.REVOKED)
    public Result revoked(@Validated @RequestBody ApprovalVO approvalVO){
        JourneySopRequest request = new JourneySopRequest();
        request.setSopId(Long.valueOf(approvalVO.getId()));
        Result<JourneySopDetailVO> detail = journeySopFeign.detail(request);
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("撤回旅程SOP【%s】", detail.getData().getSopName());
            LogUtil.recordOperLog(operDesc);
        }
        return journeySopFeign.revoked(approvalVO);
    }
}
