package com.cenker.scrm.controller.system;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.system.feign.SysUserOnlineFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/16
 * @Description 在线用户控制器
 */
@RequestMapping("/system/online")
@RestController
// @Api("在线用户")
public class SysUserOnlineController {

    @Autowired
    private SysUserOnlineFeign sysUserOnlineFeign;

    @GetMapping("/list")
    //@ApiOperation("在线用户列表")
    public TableDataInfo list(@RequestParam(value = "ipaddr", required = false) String ipAddr,
                              @RequestParam(value = "userName", required = false) String userName){
        Map<String, String> map = new HashMap<>();
        map.put("ipAddr", ipAddr);
        map.put("userName", userName);
        return sysUserOnlineFeign.list(map);
    }

    @DeleteMapping("/{tokenId}")
    //@ApiOperation("强退用户")
    @Log(module = ModuleEnum.ONLINE, businessType = BusinessType.FORCE)
    public AjaxResult forceLogout(@PathVariable("tokenId")String tokenId){
        return sysUserOnlineFeign.forceLogout(tokenId);
    }
}
