package com.cenker.scrm.controller.chatarchive;

import com.cenker.scrm.client.wechat.feign.ChatArchiveFeign;
import com.cenker.scrm.client.wechat.feign.ChatArchiveServiceFeign;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.request.chatarchive.ChatArchiveQuery;
import com.cenker.scrm.pojo.request.chatarchive.PermitUserQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

@RestController
@RequestMapping("/chat")
@Slf4j
@RequiredArgsConstructor
public class ChatArchiveController extends BaseController {

    @Autowired
    private ChatArchiveFeign chatArchiveFeign;

    @Autowired
    private ChatArchiveServiceFeign chatArchiveServiceFeign;

    @Autowired
    private TokenService tokenService;

    @GetMapping("/userTreeList")
    public AjaxResult userTreeList(HttpServletRequest request, PermitUserQuery query){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        query.setCorpId(user.getCorpId());
        return chatArchiveFeign.userTreeList(query);
    }

    @GetMapping("/user/chatListByPage/{userId}")
    public TableDataInfo userChatList(HttpServletRequest request, @PathVariable("userId") String userId, ChatArchiveQuery query){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        query.setCorpId(user.getCorpId());
        return chatArchiveFeign.userChatList(userId, query);
    }

    @GetMapping("/extcustomer/extcustomerListByPage")
    public TableDataInfo extCustomerList(HttpServletRequest request, ChatArchiveQuery query){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        query.setCorpId(user.getCorpId());
        return chatArchiveFeign.extCustomerList(query);
    }

    @GetMapping("/extcustomer/chatListByPage/{extUserId}")
    public TableDataInfo extCustomerChatList(HttpServletRequest request, @PathVariable("extUserId") String extUserId, ChatArchiveQuery query){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        query.setCorpId(user.getCorpId());
        return chatArchiveFeign.extCustomerChatList(extUserId, query);
    }

    @GetMapping("/chatMessageListByPage")
    public TableDataInfo chatMessageListByPage(ChatArchiveQuery query){
        return chatArchiveFeign.chatMessageListByPage(query);
    }

    @GetMapping("/chatMessageListBySendType")
    public TableDataInfo chatMessageListBySendType(ChatArchiveQuery query){
        return chatArchiveFeign.chatMessageListBySendType(query);
    }

    @GetMapping("/chatTimeList")
    public AjaxResult chatTimeList(ChatArchiveQuery query){
        return chatArchiveFeign.chatTimeList(query);
    }

    @GetMapping("/file/download")
    public void download(HttpServletResponse httpServletResponse, @RequestParam("fileName") String fileName) {
        Response response = chatArchiveServiceFeign.download(fileName);
        InputStream inputStream = null;
        OutputStream outputStream = null;

        try {
            String originFileName = fileName.substring(fileName.lastIndexOf("/") + 1);
            httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(originFileName, "utf-8"));

            inputStream = response.body().asInputStream();
            outputStream = httpServletResponse.getOutputStream();

            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
        } catch (IOException e) {
            log.error("下载文件失败", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }

                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

    }
}
