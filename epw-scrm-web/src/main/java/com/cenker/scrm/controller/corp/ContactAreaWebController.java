package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.ContactAreaFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.request.ContactAreaRequest;
import com.cenker.scrm.pojo.request.SiteRequest;
import com.cenker.scrm.pojo.request.StoreImportRequest;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.valid.*;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/contact/area")
@RequiredArgsConstructor
public class ContactAreaWebController {

    private final TokenService tokenService;
    private final ContactAreaFeign contactAreaFeign;

    /**
     * 全国城市区列表
     */
    @GetMapping("/treeAllAreaSelect")
    public AjaxResult treeAllAreaSelect(ContactAreaRequest contactAreaRequest) {
        return contactAreaFeign.treeAllAreaSelect(contactAreaRequest);
    }

    /**
     * 新增站点（活码）
     */
    @PostMapping("/site")
//    @PreAuthorize("@ss.hasPermi('tp:siteList:add')")
    @Log(title = "站点活码", businessType = BusinessType.INSERT)
    public AjaxResult addSite(@RequestBody @Validated(InsertGroup.class) SiteRequest siteRequest) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        siteRequest.setCreateBy(Long.valueOf(user.getUserId()));
        siteRequest.setCorpId(user.getCorpId());
        return contactAreaFeign.addSite(siteRequest);
    }

    /**
     * 站点列表
     */
    @GetMapping("/site")
    // @PreAuthorize("@ss.hasPermi('tp:siteList:list')")
    public TableDataInfo siteList(SiteRequest siteRequest) {
        // 角色管理返回全量列表
        return contactAreaFeign.siteList(siteRequest);
    }

    /**
     * 站点名列表
     */
    @GetMapping("/site/names")
    public TableDataInfo allSiteNameList(SiteRequest siteRequest) {
        // 角色管理返回全量列表
        return contactAreaFeign.allSiteNameList(siteRequest);
    }

    /**
     * 站点列表(携带id)
     */
    @GetMapping("/siteList")
    public TableDataInfo allSiteList(SiteRequest siteRequest) {
        return contactAreaFeign.allSiteList(siteRequest);
    }

    /**
     * 站点/活码详情
     * @param id
     * @return
     */
    @GetMapping("/site/getInfo/{id}")
    public AjaxResult getInfoById(@PathVariable("id") String id) {
        SiteRequest siteRequest = new SiteRequest();
        siteRequest.setId(id);
        return contactAreaFeign.getInfo(siteRequest);
    }


    /**
     * 数据统计
     * @param siteRequest
     * @return
     */
    @GetMapping("/site/getDataStatistics")
    public AjaxResult getDataStatistics(SiteRequest siteRequest) {
        return contactAreaFeign.getDataStatistics(siteRequest);
    }


    @GetMapping("/site/getAddCustomerInfo")
    public AjaxResult getAddCustomerInfo(SiteRequest siteRequest) {
        return contactAreaFeign.getAddCustomerInfo(siteRequest);
    }

    /**
     * 获取活码统计数据线性图
     */
    @GetMapping("/site/getBehaviorData")
    public AjaxResult getBehaviorData(StatisticQuery statisticQuery) {
        if (StringUtils.isEmpty(statisticQuery.getId()) || statisticQuery.getType() == null) {
            return AjaxResult.error("参数错误");
        }
        statisticQuery.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return contactAreaFeign.getBehaviorData(statisticQuery);
    }

    /**
     * 删除站点
     */
    @DeleteMapping("/site")
    @Log(title = "站点活码", businessType = BusinessType.DELETE)
//    @PreAuthorize("@ss.hasPermi('tp:siteList:remove')")
    public AjaxResult remove(@RequestBody SiteRequest siteRequest) {
        return contactAreaFeign.remove(siteRequest);
    }

    /**
     * 修改站点活码
     */
    @PutMapping("/site")
    @Log(title = "站点活码", businessType = BusinessType.UPDATE)
//    @PreAuthorize("@ss.hasPermi('tp:siteList:edit')")
    public AjaxResult update(@RequestBody @Validated(UpdateGroup.class) SiteRequest siteRequest) {
        siteRequest.setCreateBy(null);
        siteRequest.setCreateTime(null);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        siteRequest.setUpdateBy(Long.valueOf(loginUser.getUser().getUserId()));
        siteRequest.setUpdateTime(DateUtils.getNowDate());
        return contactAreaFeign.updateInfo(siteRequest);
    }

    /**
     * 新增门店
     */
    @PostMapping("/store")
//    @PreAuthorize("@ss.hasPermi('tp:storeList:add')")
    @Log(title = "门店", businessType = BusinessType.INSERT)
    public AjaxResult addStore(@RequestBody @Validated(InsertGroup.class) ContactAreaRequest contactAreaRequest) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        contactAreaRequest.setCreateBy(Long.valueOf(user.getUserId()));
        return contactAreaFeign.addStore(contactAreaRequest);
    }

    /**
     * 门店列表
     */
//    @PreAuthorize("@ss.hasPermi('tp:storeList:list')")
    @GetMapping("/store")
    public TableDataInfo storeList(@Validated(SelectGroup.class) ContactAreaRequest contactAreaRequest) {
        return contactAreaFeign.storeList(contactAreaRequest);
    }

    /**
     * 删除门店
     */
//    @PreAuthorize("@ss.hasPermi('tp:storeList:remove')")
    @DeleteMapping("/store")
    @Log(title = "门店", businessType = BusinessType.DELETE)
    public AjaxResult removeStore(@RequestBody @Validated(DeleteGroup.class) ContactAreaRequest contactAreaRequest) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        contactAreaRequest.setUpdateBy(Long.valueOf(user.getUserId()));
        return contactAreaFeign.removeStore(contactAreaRequest);
    }

    /**
     * 启用/停用门店
     */
    @PutMapping("/updateStoreStatus")
    public AjaxResult updateStoreStatus(@RequestBody ContactAreaRequest contactAreaRequest) {
        if (ObjectUtil.isNull(contactAreaRequest.getStoreId())) {
            return AjaxResult.error("参数错误");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        contactAreaRequest.setUpdateBy(Long.valueOf(user.getUserId()));
        contactAreaRequest.setAreaName(null);
        return contactAreaFeign.updateStore(contactAreaRequest);
    }

    /**
     * 门店回显
     */
    @GetMapping("/store/{storeId}")
    public AjaxResult getStoreById(@PathVariable("storeId") Long storeId) {
        ContactAreaRequest contactAreaRequest = new ContactAreaRequest();
        contactAreaRequest.setStoreId(storeId);
        return contactAreaFeign.getStoreById(contactAreaRequest);
    }

    /**
     * 修改门店
     */
    @PutMapping("/store")
//    @PreAuthorize("@ss.hasPermi('tp:storeList:edit')")
    @Log(title = "门店", businessType = BusinessType.UPDATE)
    public AjaxResult updateStore(@RequestBody @Validated(UpdateStoreGroup.class) ContactAreaRequest contactAreaRequest) {
        contactAreaRequest.setUpdateBy(Long.valueOf(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId()));
        return contactAreaFeign.updateStore(contactAreaRequest);
    }

    /**
     * 批量导入接口
     */
    @PostMapping("/storeImport")
//    @PreAuthorize("@ss.hasPermi('tp:storeList:import')")
    public AjaxResult storeImport(MultipartFile file) {
        if (file != null) {
            ExcelUtil<StoreImportRequest> util = new ExcelUtil<>(StoreImportRequest.class);
            try {
                List<StoreImportRequest> storeImportData = util.importExcel(file.getInputStream());
                if (CollectionUtil.isEmpty(storeImportData)) {
                    return AjaxResult.error("无门店数据导入");
                }
                if (storeImportData.size() > 1000) {
                    return AjaxResult.error("单次批量最大1000个门店");
                }
                List<StoreImportRequest> emptyStoreNames = storeImportData.stream().filter(s -> StringUtils.isBlank(s.getStore())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(emptyStoreNames)) {
                    return AjaxResult.error("存在门店名为空的数据");
                }
                List<StoreImportRequest> emptySiteNames = storeImportData.stream().filter(s -> StringUtils.isBlank(s.getSite())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(emptySiteNames)) {
                    return AjaxResult.error("存在站点名称为空的数据");
                }
                return contactAreaFeign.storeImport(storeImportData);
            } catch (Exception e) {
                log.error("批量导入接口发生错误");
                log.error("错误信息:{}",e);
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }


    /**
     * 门店模板下载
     */
    @PostMapping("/storeTemplateDownload")
    public void storeTemplateDownload() {
        ExcelUtil<StoreImportRequest> storeImportRequestExcelUtil = new ExcelUtil<>(StoreImportRequest.class);
        List<StoreImportRequest> list = Lists.newArrayList();
        storeImportRequestExcelUtil.exportExcel(list, "sheet", ServletUtils.getResponse());
    }

    /**
     * 门店搜索-指定格式（配送员门店列表）：广东省-深圳市-零一部
     */
    @GetMapping("/searchStore")
    public TableDataInfo selectStoreByTree(ContactAreaRequest contactAreaRequest) {
        return contactAreaFeign.selectStoreByTree(contactAreaRequest);
    }

    /**
     * 站点搜索-指定格式（角色-可见范围）：广东省-深圳市-零一站
     */
    @GetMapping("/searchSite")
    public TableDataInfo selectSiteByTree(SiteRequest siteRequest) {
        return contactAreaFeign.selectSiteByTree(siteRequest);
    }

    /**
     * 获取全国省市区
     */
    @GetMapping("/treeSelectAll")
    public AjaxResult treeSelectAll() {
        return contactAreaFeign.treeSelectAll();
    }
}
