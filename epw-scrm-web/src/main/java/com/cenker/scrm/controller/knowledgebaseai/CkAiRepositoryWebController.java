package com.cenker.scrm.controller.knowledgebaseai;

import com.cenker.scrm.client.knowledgebaseai.feign.CkAiRepositoryApiFeign;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiRepository;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/knowledgebaseai/repository")
@RequiredArgsConstructor
public class CkAiRepositoryWebController {

    private final CkAiRepositoryApiFeign ckAiRepositoryApiFeign;
    @GetMapping("/list")
    public TableDataInfo list(CkAiRepository ckAiRepository){
        return ckAiRepositoryApiFeign.list(ckAiRepository);
    }
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Valid CkAiRepository ckAiRepository){
        if (StringUtils.isBlank(ckAiRepository.getRepositoryName())){
            throw new ParameterException("缺失参数");
        }
        return ckAiRepositoryApiFeign.add(ckAiRepository);
    }
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody @Valid CkAiRepository ckAiRepository){
        if (ckAiRepository.getId() == null) {
            throw new ParameterException("缺失参数");
        }
        return ckAiRepositoryApiFeign.edit(ckAiRepository);
    }

    @RequestMapping("/get/{id}")
    public AjaxResult getCkAiRepositoryById(@PathVariable("id") Long id){
        return ckAiRepositoryApiFeign.getCkAiRepositoryById(id);
    }

}
