package com.cenker.scrm.controller.corp;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxMomentFeign;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.moment.MomentSyncDto;
import com.cenker.scrm.pojo.dto.moment.QueryMomentLikeAndCommentDTO;
import com.cenker.scrm.pojo.dto.moment.QueryPersonMomentListDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.moment.SendMomentListVO;
import com.cenker.scrm.pojo.vo.moment.SendMomentVo;
import com.cenker.scrm.pojo.vo.welcome.WelcomeLinkVo;
import com.cenker.scrm.pojo.vo.welcome.WelcomeVideoVo;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/11
 * @Description 发朋友圈
 */
@Slf4j
@RestController
@RequestMapping("/tp/moment")
public class TbWxMomentWebController {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TbWxMomentFeign tbWxMomentFeign;

    /**
     * 发朋友圈
     */
    @PostMapping
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:add')")
    @Log(module = ModuleEnum.SENDMOMENTS,businessType = BusinessType.INSERT)
    public Result add(@RequestBody @Valid SendMomentVo sendMomentVo) {
        LogUtil.logOperDesc(sendMomentVo.getTaskName());

        String validStr = validSendMomentVo(sendMomentVo);
        if (validStr != null) {
            return Result.error(500,validStr);
        }
        // 处理智能物料渠道
        setClickSource(sendMomentVo);

        // 对应附件判断
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        sendMomentVo.setCreateBy(user.getUserId());
        sendMomentVo.setCorpId(user.getCorpId());
        sendMomentVo.setCreateTime(DateUtils.getNowDate());
        return tbWxMomentFeign.add(sendMomentVo);
    }

    /**
     * 发朋友圈
     */
    @PutMapping
    @RepeatSubmit
    @Log(module = ModuleEnum.SENDMOMENTS,businessType = BusinessType.UPDATE)
    public Result update(@RequestBody @Valid SendMomentVo sendMomentVo) {
        LogUtil.logOperDesc(sendMomentVo.getTaskName());

        String validStr = validSendMomentVo(sendMomentVo);
        if (validStr != null) {
            return Result.error(500, validStr);
        }
        // 处理智能物料渠道
        setClickSource(sendMomentVo);

        // 对应附件判断
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        sendMomentVo.setCreateBy(user.getUserId());
        sendMomentVo.setCorpId(user.getCorpId());
        sendMomentVo.setCreateTime(DateUtils.getNowDate());
        return tbWxMomentFeign.update(sendMomentVo);
    }

    /**
     * 朋友圈列表
     */
    @GetMapping
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:list')")
    public TableDataInfo<SendMomentListVO> list(SendMomentVo sendMomentVo){
        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return tbWxMomentFeign.list(sendMomentVo);
    }

    /**
     * 取消发送朋友圈
     */
    @DeleteMapping
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:cancelTask')")
    @Log(module = ModuleEnum.SENDMOMENTS, businessType = BusinessType.OTHER)
    public Result remove(SendMomentVo sendMomentVo){
        LogUtil.logOperDesc("取消朋友圈发送任务，任务ID：" + sendMomentVo.getId());

        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return tbWxMomentFeign.remove(sendMomentVo);
    }

    /**
     * 朋友圈详情
     */
    @GetMapping("/{id}")
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    public Result<SendMomentListVO> detail(@PathVariable("id")String id){
        return tbWxMomentFeign.detail(id);
    }

    /**
     * 朋友圈发送数据
     */
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @GetMapping("/getMomentSendUserList")
    public TableDataInfo getMomentSendUserList(SendMomentVo sendMomentVo){
        return tbWxMomentFeign.getMomentSendUserList(sendMomentVo);
    }

//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @GetMapping("/getMomentSendCusList")
    public TableDataInfo getMomentSendCusList(SendMomentVo sendMomentVo){
        return tbWxMomentFeign.getMomentSendCusList(sendMomentVo);
    }

    /**
     * 提醒发送 提醒全部携带任务momentTaskId 单个则数据id 优先单个
     */
    @RequestMapping("/remind")
    public AjaxResult remindToSend(SendMomentVo sendMomentVo){
        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        if (StringUtils.isEmpty(sendMomentVo.getId()) && StringUtils.isEmpty(sendMomentVo.getMomentTaskId())) {
            return AjaxResult.error("参数为空");
        }
        return tbWxMomentFeign.remindToSend(sendMomentVo);
    }

    /**
     * 同步朋友圈数据
     */
    @PostMapping("/synchronizeMomentList")
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:sync')")
    @Log(module = ModuleEnum.SENDMOMENTS, businessType = BusinessType.SYNCHRONIZATION)
    public AjaxResult synchronizeMomentList(@RequestBody SendMomentVo sendMomentVo){

        AjaxResult valid = getAjaxResult(sendMomentVo);
        if (valid != null) {
            return valid;
        }

        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return tbWxMomentFeign.synchronizeMomentList(sendMomentVo);
    }

    /**
     * 检验时间
     * @param sendMomentVo
     * @return
     */
    private AjaxResult getAjaxResult(SendMomentVo sendMomentVo) {
        try {
            Date startTime = null;
            Date endTime = null;
            if (StrUtil.isNotBlank(sendMomentVo.getBeginTime())) {
                /*if (!sendMomentVo.getBeginTime().matches("\\d{4}-\\d{2}-\\d{2}")) {
                    return AjaxResult.error("开始时间格式错误，正确格式为：yyyy-MM-dd");
                }*/
                startTime = DateUtil.parseDate(sendMomentVo.getBeginTime());
            }
            if (StrUtil.isNotBlank(sendMomentVo.getEndTime())) {
                /*if (!sendMomentVo.getEndTime().matches("\\d{4}-\\d{2}-\\d{2}")) {
                    return AjaxResult.error("结束时间格式错误，正确格式为：yyyy-MM-dd");
                }*/
                endTime = DateUtil.parseDate(sendMomentVo.getEndTime());
            }
            if (startTime != null && endTime != null) {
                if (startTime.getTime() > endTime.getTime()) {
                    return AjaxResult.error("开始时间不能大于结束时间");
                }
            }
        } catch (Exception e) {
            return AjaxResult.error("时间格式错误！");
        }
        return null;
    }

    /**
     * 同步数据-获取任务创建结果
     */
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @PostMapping("/getTaskResult")
    public AjaxResult getTaskResult(@RequestBody SendMomentVo sendMomentVo){
        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        if (CollectionUtils.isEmpty(sendMomentVo.getIds())) {
            return AjaxResult.success();
        }
        return tbWxMomentFeign.getTaskResult(sendMomentVo);
    }

    /**
     * 同步数据-获取执行情况->可见范围->可见客户
     */
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @PostMapping("/getTaskUserResult")
    public AjaxResult getTaskUserResult(@RequestBody SendMomentVo sendMomentVo) {
        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return tbWxMomentFeign.getTaskUserResult(sendMomentVo);
    }

    /**
     * 同步朋友圈数据情况
     */
    @PostMapping("/syncMomentData")
    public AjaxResult syncMomentData(@RequestBody MomentSyncDto params) {
        return tbWxMomentFeign.syncMomentData(params);
    }

    /**
     * 数据统计
     */
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @GetMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(SendMomentVo sendMomentVo){
        return tbWxMomentFeign.getDataStatistics(sendMomentVo);
    }

    /**
     * 获取预见客户
     */
//    @PreAuthorize("@ss.hasPermi('modules:sendMoments:data')")
    @PostMapping("/getMomentPredictedNum")
    public AjaxResult getMomentPredictedNum(@RequestBody SendMomentVo sendMomentVo) {
        sendMomentVo.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        if (CollectionUtils.isEmpty(sendMomentVo.getUserList())
        && CollectionUtils.isEmpty(sendMomentVo.getDepartmentList())
        && CollectionUtils.isEmpty(sendMomentVo.getTagList())) {
            Integer num = 0;
            Map<String, Object> result = Maps.newHashMap();
            result.put("predictedNum",num);
            return AjaxResult.success(result);
        }
        return tbWxMomentFeign.getMomentPredictedNum(sendMomentVo);
    }

    @GetMapping("/getPersonalMomentList")
    public TableDataInfo getPersonalMomentList(QueryPersonMomentListDTO dto) {
        return tbWxMomentFeign.getPersonalMomentList(dto);
    }

    @GetMapping("/getMomentSendCusListByMomentId")
    public AjaxResult getMomentSendCusListByMomentId(QueryMomentLikeAndCommentDTO dto) {
        return tbWxMomentFeign.getMomentSendCusListByMomentId(dto);
    }
    @GetMapping("/getMomentVisibleUserList")
    public TableDataInfo getMomentVisibleUserList(QueryMomentLikeAndCommentDTO dto) {
        return tbWxMomentFeign.getMomentVisibleUserList(dto);
    }

    @PostMapping("/synchronizeAllMoment")
    @Log(module = ModuleEnum.SENDMOMENTS, businessType = BusinessType.SYNCHRONIZATION)
    public AjaxResult synchronizeAllMoment() {
        return tbWxMomentFeign.synchronizeAllMoment();
    }



    @PostMapping("/approve")
    @Log(module = ModuleEnum.SENDMOMENTS, businessType = BusinessType.APPROVAL)
    public Result approve(@Validated @RequestBody ApprovalVO approvalVO){
        Result<SendMomentListVO>  detail = tbWxMomentFeign.detail(approvalVO.getId());
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("审核发朋友圈【%s】：%s", detail.getData().getTaskName(), approvalVO.getAgree() ? "通过" : "驳回");
            LogUtil.recordOperLog(operDesc);
        }
        return tbWxMomentFeign.approve(approvalVO);
    }

    @PostMapping("/revoked")
    @Log(module = ModuleEnum.SENDMOMENTS, businessType = BusinessType.REVOKED)
    public Result revoked(@Validated @RequestBody ApprovalVO approvalVO){
        Result<SendMomentListVO>  detail = tbWxMomentFeign.detail(approvalVO.getId());
        if (detail.isSuccess() && detail.getData() != null) {
            String operDesc = String.format("撤回发朋友圈【%s】", detail.getData(). getTaskName());
            LogUtil.recordOperLog(operDesc);
        }
        return tbWxMomentFeign.revoked(approvalVO);
    }

    private void setClickSource(SendMomentVo sendMomentVo) {
        // 如果是雷达链接
        if (CommonConstants.LINK.equals(sendMomentVo.getMsgType()) && sendMomentVo.getLink().getType() == 2 && sendMomentVo.getLink().getUrl().contains("active/intelligentMaterial")) {
            String url = sendMomentVo.getLink().getUrl();
            // 拼接或替换雷达链接
            if (url.contains("&clickSource=")) {
                if (url.contains("&clickSource="+ TypeConstants.RADAR_SOURCE_MOMENT)) {
                    return;
                }
                sendMomentVo.getLink().setUrl(url.replace("&clickSource="+url.charAt(url.lastIndexOf("&clickSource=")+13),"&clickSource="+ TypeConstants.RADAR_SOURCE_MOMENT));
            }else {
                // 朋友圈类型
                sendMomentVo.getLink().setUrl(url+"&clickSource="+ TypeConstants.RADAR_SOURCE_MOMENT);
            }
        }
    }

    /**
     * 参数校验
     * @param sendMomentVo
     * @return
     */
    private String validSendMomentVo(SendMomentVo sendMomentVo) {
        // 全部客户帮前端去除筛选条件
        if (sendMomentVo.getSendScope() == 0) {
            sendMomentVo.setTagList(null);
        }
        if (StringUtils.isEmpty(sendMomentVo.getContent())
                && sendMomentVo.getLink() == null
                && StringUtils.isEmpty(sendMomentVo.getVideo())
                && CollectionUtils.isEmpty(sendMomentVo.getImage())) {
            return "发送内容不能为空";
        }
        if (sendMomentVo.getTimedTask() == 1 && sendMomentVo.getSettingTime() == null) {
            return "请选择发送时间";
        }
        if (StringUtils.isNotEmpty(sendMomentVo.getMsgType())) {
            switch (sendMomentVo.getMsgType()) {
                case "image":
                    if (StringUtils.isEmpty(sendMomentVo.getContent()) && CollectionUtils.isEmpty(sendMomentVo.getImage())) {
                        return "图片格式错误";
                    }else if (CollectionUtils.isEmpty(sendMomentVo.getImage())){
                        sendMomentVo.setMsgType(null);
                    }
                    sendMomentVo.setLink(null);
                    sendMomentVo.setVideo(null);
                    break;
                case "link":
                    if (StringUtils.isEmpty(sendMomentVo.getContent()) && (sendMomentVo.getLink() == null
                            || StringUtils.isEmpty(sendMomentVo.getLink().getCover())

                            || StringUtils.isEmpty(sendMomentVo.getLink().getUrl()))) {
                        return "链接格式错误";
                    }else if (sendMomentVo.getLink() == null
                            || StringUtils.isEmpty(sendMomentVo.getLink().getCover())

                            || StringUtils.isEmpty(sendMomentVo.getLink().getUrl())) {
                        sendMomentVo.setMsgType(null);
                        sendMomentVo.setLink(null);
                    }
                    sendMomentVo.setImage(null);
                    sendMomentVo.setVideo(null);
                    break;
                case "video":
                    if (StringUtils.isEmpty(sendMomentVo.getVideo()) && StringUtils.isEmpty(sendMomentVo.getContent())) {
                        return "视频格式错误";
                    }else if(StringUtils.isEmpty(sendMomentVo.getVideo())){
                        sendMomentVo.setMsgType(null);
                    }
                    sendMomentVo.setLink(null);
                    sendMomentVo.setImage(null);
                    break;
                default:
                    return "参数错误";
            }
        }
        return null;
    }
    
}
