package com.cenker.scrm.controller.system;


import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.system.feign.SysDictTypeFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysDictType;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.file.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController {

    @Autowired
    private SysDictTypeFeign sysDictTypeFeign;

    @GetMapping("/list")
    public TableDataInfo list(SysDictType dictType) {
       return sysDictTypeFeign.list(dictType);
    }

    @GetMapping("/export")
    @Log(module = ModuleEnum.DICT, businessType = BusinessType.EXPORT)
    public void export(SysDictType dictType, HttpServletResponse response) {
        LogUtil.logOperDesc("字典类型");
        List<SysDictType> list = sysDictTypeFeign.export(dictType);
        ExcelUtil<SysDictType> util = new ExcelUtil<>(SysDictType.class);
        util.exportExcel(list, "字典类型",response);
    }

    /**
     * 查询字典类型详细
     */
//    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictId}")
    public AjaxResult getInfo(@PathVariable("dictId") Long dictId) {
       return sysDictTypeFeign.getInfo(dictId);
    }

    /**
     * 新增字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @PostMapping
    @Log(module = ModuleEnum.DICT, businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody SysDictType dict) {
        LogUtil.logOperDesc(dict.getDictName());
        return sysDictTypeFeign.add(dict);
    }

    /**
     * 修改字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @PutMapping
    @Log(module = ModuleEnum.DICT, businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody SysDictType dict) {
        LogUtil.logOperDesc(dict.getDictName());
        return sysDictTypeFeign.edit(dict);
    }

    /**
     * 删除字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @DeleteMapping("/{dictIds}")
    @Log(module = ModuleEnum.DICT, businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable("dictIds") Long[] dictIds) {
        LogUtil.logOperDesc("字典编号：" + StrUtil.join(StrUtil.COMMA, dictIds));
        return sysDictTypeFeign.remove(dictIds);
    }

    /**
     * 清空缓存
     */
//    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @DeleteMapping("/clearCache")
    @Log(module = ModuleEnum.DICT, businessType = BusinessType.OTHER)
    public AjaxResult clearCache() {
        LogUtil.logOperDesc("清空缓存");
        return sysDictTypeFeign.clearCache();
    }

    /**
     * 获取字典选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionSelect() {
        return sysDictTypeFeign.optionSelect();
    }
}
