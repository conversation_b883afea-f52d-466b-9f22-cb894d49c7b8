package com.cenker.scrm.controller.corp;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.TbWxDismissUserFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.AllocateInfoDTO;
import com.cenker.scrm.pojo.dto.WxLeaveUserDto;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2021/9/2
 * @Description 离职继承分配
 */
@RestController
@RequestMapping("/tp/leaveUser")
//@Api(tags = "离职继承分配接口")
public class TbWxDismissUserController {
    @Autowired
    private TbWxDismissUserFeign tbWxDismissUserFeign;

    @GetMapping("/list")
    // @PreAuthorize("@ss.hasPermi('tp:dimission:list')")
    public TableDataInfo list(WxLeaveUserDto dto) {
        return tbWxDismissUserFeign.list(dto);
    }

    @GetMapping("/listAllocated")
    // @PreAuthorize("@ss.hasPermi('tp:dimission:list')")
    public TableDataInfo listAllocated(WxLeaveUserDto dto) {
        return tbWxDismissUserFeign.listAllocated(dto);
    }

    @GetMapping("/allocatedDetail")
    // @PreAuthorize("@ss.hasPermi('tp:dimission:filter')")
    public TableDataInfo allocatedDetail(WxLeaveUserDto dto) {
        return tbWxDismissUserFeign.allocatedDetail(dto);
    }

    @PostMapping("/allocate")
    @Log(module = ModuleEnum.DIMISSION, businessType = BusinessType.INSERT)
    // @PreAuthorize("@ss.hasPermi('tp:dimission:allocate')")
    public AjaxResult allocate(@RequestBody AllocateInfoDTO dto) {
        return tbWxDismissUserFeign.allocate(dto);
    }
}
