package com.cenker.scrm.controller.system;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.system.feign.SysJobFeign;
import com.cenker.scrm.client.system.feign.SysJobLogFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysJob;
import com.cenker.scrm.pojo.entity.system.SysJobLog;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.file.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/16
 * @Description 定时任务
 */
@RequestMapping("/system/job")
@RestController
//@Api("定时任务")
public class SysJobController {

    @Autowired
    private SysJobFeign sysJobFeign;
    @Autowired
    private SysJobLogFeign sysJobLogFeign;

    @GetMapping("/list")
    //@ApiOperation("定时任务列表")
    public TableDataInfo list(SysJob sysJob){
        return sysJobFeign.list(sysJob);
    }

    @GetMapping("/export")
    @Log(title = "定时任务", businessType = BusinessType.EXPORT)
    //@ApiOperation("导出定时任务列表")
    public void export(SysJob sysJob, HttpServletResponse response){
        List<SysJob> list = sysJobFeign.export(sysJob);
        ExcelUtil<SysJob> util = new ExcelUtil<>(SysJob.class);
        util.exportExcel(list, "定时任务", response);
    }

    @GetMapping(value = "/{jobId}")
    //@ApiOperation("获取定时任务详细信息")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId){
        return sysJobFeign.getInfo(jobId);
    }

    @PostMapping
    //@ApiOperation("新增定时任务")
    @Log(title = "定时任务", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody SysJob sysJob){
        return sysJobFeign.add(sysJob);
    }

    @PutMapping
    //@ApiOperation("修改定时任务")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody SysJob sysJob){
        return sysJobFeign.edit(sysJob);
    }

    @PutMapping("/changeStatus")
    //@ApiOperation("定时任务状态修改")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    public AjaxResult changeStatus(@RequestBody SysJob sysJob){
        return sysJobFeign.changeStatus(sysJob);
    }

    @PutMapping("/run")
    //@ApiOperation("定时任务立即执行一次")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    public AjaxResult run(@RequestBody SysJob sysJob){
        return sysJobFeign.run(sysJob);
    }

    @DeleteMapping("/{jobIds}")
    //@ApiOperation("删除定时任务")
    @Log(title = "定时任务", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable("jobIds") Long[] jobIds){
        return sysJobFeign.remove(jobIds);
    }

    @GetMapping("/log/list")
    //@ApiOperation("定时任务日志列表")
    public TableDataInfo list(SysJobLog sysJobLog){
        return sysJobLogFeign.list(sysJobLog);
    }

    @GetMapping("/log/export")
    //@ApiOperation("导出定时任务日志列表")
    @Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
    public AjaxResult export(SysJobLog sysJobLog){
        return sysJobLogFeign.export(sysJobLog);
    }

    @GetMapping("/log/{jobLogId}")
    //@ApiOperation("获取指定定时任务日志")
    public AjaxResult getLogInfo(@PathVariable("jobLogId") Long jobLogId){
        return sysJobLogFeign.getInfo(jobLogId);
    }

    @DeleteMapping("/log/{jobLogIds}")
    @Log(title = "任务调度日志", businessType = BusinessType.DELETE)
    //@ApiOperation("删除指定定时任务日志")
    public AjaxResult logRemove(@PathVariable("jobLogIds") Long[] jobLogIds){
        return sysJobLogFeign.remove(jobLogIds);
    }

    @DeleteMapping("/log")
    //@ApiOperation("清空定时任务日志")
    @Log(title = "任务调度日志", businessType = BusinessType.CLEAN)
    public AjaxResult logRemove(){
        return sysJobLogFeign.clean();
    }
}
