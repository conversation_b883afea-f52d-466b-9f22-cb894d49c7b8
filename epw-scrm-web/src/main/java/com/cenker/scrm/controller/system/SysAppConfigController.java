package com.cenker.scrm.controller.system;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.system.feign.SysAppConfigFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.system.SysAppConfigAddDto;
import com.cenker.scrm.pojo.dto.system.SysAppConfigListDto;
import com.cenker.scrm.pojo.dto.system.SysAppConfigUpdateDto;
import com.cenker.scrm.pojo.vo.base.Result;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应用配置菜单
 */
@AllArgsConstructor
@RestController
@RequestMapping("/app/config")
public class SysAppConfigController extends BaseController {

    private final SysAppConfigFeign sysAppConfigFeign;

    @Log(module = ModuleEnum.SYSTEM_APP_CONFIG, businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody SysAppConfigAddDto param) {
        return sysAppConfigFeign.add(param);
    }

    @Log(module = ModuleEnum.SYSTEM_APP_CONFIG, businessType = BusinessType.UPDATE)
    @PutMapping
    public Result update(@Validated @RequestBody SysAppConfigUpdateDto param) {
        return sysAppConfigFeign.update(param);
    }

    @Log(module = ModuleEnum.SYSTEM_APP_CONFIG, businessType = BusinessType.DELETE)
    @DeleteMapping
    public Result delete(@RequestParam("id") String id) {
        return sysAppConfigFeign.delete(id);
    }

    @GetMapping
    public Result detail(@RequestParam("id") String id) {
        return sysAppConfigFeign.detail(id);
    }

    @GetMapping("/list")
    public TableDataInfo list(SysAppConfigListDto param) {
        return sysAppConfigFeign.list(param);
    }

}