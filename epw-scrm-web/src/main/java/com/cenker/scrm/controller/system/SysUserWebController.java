package com.cenker.scrm.controller.system;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.efunds.EfundAuthCenterFeignClient;
import com.cenker.scrm.efunds.constants.AuthCenterConstants;
import com.cenker.scrm.efunds.model.AuthCenterUserRoleRequest;
import com.cenker.scrm.efunds.model.Result;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.system.SysUserFeign;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/8/25
 * @Description 用户管理
 */
@RequestMapping("/system/user")
@RestController
@AllArgsConstructor
@Slf4j
public class SysUserWebController {

    private SysUserFeign sysUserFeign;
    private EfundAuthCenterFeignClient efundAuthCenterFeignClient;

//    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    TableDataInfo list(SysUser user){
        return sysUserFeign.list(user);
    }

//    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @GetMapping("/export")
    public void export(SysUser user, HttpServletResponse response) {
        List<SysUser> list = sysUserFeign.export(user);
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.exportExcel(list, "user", response);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate() {
        return sysUserFeign.importTemplate();
    }

    /**
     * 根据用户编号获取详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        if (userId == null){
            return sysUserFeign.getInfo();
        }
        return sysUserFeign.getInfo(userId);
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @PostMapping
    @RepeatSubmit
    @Log(module = ModuleEnum.USER, businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        LogUtil.logOperDesc(user.getUserName());
        return sysUserFeign.add(user);
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @PutMapping
    @RepeatSubmit
    @Log(module = ModuleEnum.USER, businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        LogUtil.logOperDesc(user.getUserName());
        // 获取用户域账号
        AjaxResult sysUserResult = sysUserFeign.getInfo(Long.valueOf(user.getUserId()));
        SysUser sysUserOld = null;
        if (Objects.nonNull(sysUserResult.get(AjaxResult.DATA_TAG))) {
            Object sysUserObject = sysUserResult.get(AjaxResult.DATA_TAG);
            sysUserOld = JSON.parseObject(JSON.toJSONString(sysUserObject), SysUser.class);
        }

        AjaxResult result = sysUserFeign.edit(user);
        /** 3.1.2权限同步到易发现工作权限中心 **/
        if(HttpStatus.SUCCESS == Integer.valueOf(result.get(AjaxResult.CODE_TAG).toString()) && Objects.nonNull(sysUserOld)) {
            // 场景1：原域账号为空，更新域账号，调用添加角色接口
            // 场景2：原域账号不为空，更新域账号，原域账号调用接口删除角色， 新域账号调用接口添加角色
            // 场景3：原域账号不为空，删除域账号，原域账号调用接口删除角色， 新域账号调用接口添加角色
            if (StrUtil.isEmpty(sysUserOld.getDomainAccount()) && StrUtil.isNotEmpty(user.getDomainAccount())) {
                // 添加新域账号角色
                synAuthCenter(user.getDomainAccount(), AuthCenterConstants.SAVE_TYPE_ADD);
            } else if (StrUtil.isNotEmpty(sysUserOld.getDomainAccount()) && StrUtil.isNotEmpty(user.getDomainAccount())) {
                if (!user.getDomainAccount().equals(sysUserOld.getDomainAccount())) {
                    // 删除原来域账号角色
                    synAuthCenter(sysUserOld.getDomainAccount(), AuthCenterConstants.SAVE_TYPE_DEL);
                    // 添加新域账号角色
                    synAuthCenter(user.getDomainAccount(), AuthCenterConstants.SAVE_TYPE_ADD);
                }
            }  else if (StrUtil.isNotEmpty(sysUserOld.getDomainAccount()) && StrUtil.isEmpty(user.getDomainAccount())) {
                // 删除原来域账号角色
                synAuthCenter(sysUserOld.getDomainAccount(), AuthCenterConstants.SAVE_TYPE_DEL);
            }
        }
        return result;
    }

    /**
     * 同步权限中心
     * @param domainAccount 域账号
     * @param saveType 保存类型：ADD/DEL
     */
    private void synAuthCenter(String domainAccount, String saveType) {
        AuthCenterUserRoleRequest authCenterRequest = new AuthCenterUserRoleRequest();
        List<String> userIdList = new ArrayList<>();
        userIdList.add(domainAccount);
        authCenterRequest.setUserIdList(userIdList);
        authCenterRequest.setPlatformCode(AuthCenterConstants.PLATFORM_CODE);
        authCenterRequest.setRelCode(AuthCenterConstants.REL_CODE);
        authCenterRequest.setRelType(AuthCenterConstants.REL_TYPE_ROLE);
        authCenterRequest.setSaveType(saveType);
        try {
            log.info("【同步权限中心】,请求入参{}", JSON.toJSONString(authCenterRequest));
            Result synAuthCenterResult = efundAuthCenterFeignClient.synAuthCenter(authCenterRequest);
            log.info("【同步权限中心】,响应结果{}", JSON.toJSONString(synAuthCenterResult));
        } catch (Exception e) {
            log.error("【同步权限中心】失败！{}", e.getMessage());
        }

    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @DeleteMapping("/{userIds}")
    @Log(module = ModuleEnum.USER, businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Long[] userIds) {
        LogUtil.logOperDesc(userIds);
        return sysUserFeign.remove(userIds);
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @PutMapping("/resetPwd")
    @Log(module = ModuleEnum.USER, businessType = BusinessType.OTHER)
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        LogUtil.logOperDesc("重置用户密码");
        return sysUserFeign.resetPwd(user);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @PutMapping("/changeStatus")
    @Log(module = ModuleEnum.USER, businessType = BusinessType.OTHER)
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        LogUtil.logOperDesc("修改用户状态");
        return sysUserFeign.changeStatus(user);
    }
}
