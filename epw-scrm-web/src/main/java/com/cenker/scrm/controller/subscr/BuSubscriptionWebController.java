package com.cenker.scrm.controller.subscr;

import com.cenker.scrm.client.subscr.BuSubscriptionFeign;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuCustomerSubscrData;
import com.cenker.scrm.pojo.vo.subscr.BuSubscrQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionData;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订阅
 */
@AllArgsConstructor
@RestController
@RequestMapping("/bu/subscription")
public class BuSubscriptionWebController {
    private final BuSubscriptionFeign buSubscriptionFeign;
    /**
     * 查询客户订阅列表，用于客户详情
     * @param externalUserId
     * @return
     */
    @GetMapping("/list")
    public Result<Map<String, List<BuSubscriptionVO>>> list(@RequestParam("externalUserId") String externalUserId) {
        return buSubscriptionFeign.list(externalUserId);
    }

    /**
     * 查询最新订阅菜单信息
     * @param externalUserId
     * @return
     */
    @GetMapping("/getSubscrMenus")
    public Result<BuSubscriptionData> getSubscrMenus(@RequestParam(value = "externalUserId", required = false) String externalUserId) {
        return buSubscriptionFeign.getSubscrMenus(externalUserId);
    }

    /**
     * 保存客户订阅信息
     * @return
     */
    @PostMapping("/saveSubscrMenus")
    public Result saveSubscrMenus(@RequestBody BuCustomerSubscrData buCustomerSubscrData) {
        buCustomerSubscrData.setFromWechat(false);
        return buSubscriptionFeign.saveSubscrMenus(buCustomerSubscrData);
    }

    /**
     * 查询客户订阅栏目列表
     * @param query
     * @return
     */
    @GetMapping("/getSubscrSectionList")
    public TableDataInfo<BuSubscriptionVO> getSubscrSectionList(BuSubscrQuery query) {
        return buSubscriptionFeign.getSubscrSectionList(query);
    }
}