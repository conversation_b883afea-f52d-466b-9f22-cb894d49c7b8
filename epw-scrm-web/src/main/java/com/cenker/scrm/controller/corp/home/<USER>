package com.cenker.scrm.controller.corp.home;

import com.cenker.scrm.client.system.feign.SysProfileFeign;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.pojo.dto.login.LoginDto;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2021/8/24
 * @Description 个人中心
 */
@RequestMapping("/system/user/profile")
@RestController
public class UserController {

    @Autowired
    private SysProfileFeign sysProfileFeign;
    @Autowired
    private TokenService tokenService;

    @GetMapping
    public AjaxResult profile() {
        return sysProfileFeign.profile();
    }

    /**
     * 重置密码
     */
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(@RequestBody LoginDto loginDto) {
       return sysProfileFeign.updatePwd(loginDto.getOldPassword(),loginDto.getNewPassword());
    }

    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user){
        user.setUserId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId());
        return sysProfileFeign.updateProfile(user);
    }

}
