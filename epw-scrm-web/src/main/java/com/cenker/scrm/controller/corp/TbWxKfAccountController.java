package com.cenker.scrm.controller.corp;


import cn.hutool.core.collection.CollectionUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxKfAccountFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.kf.KfAccountDto;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.request.kf.KfAccountQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/03/04
 * @Description 微信客服
 */
@RestController
@RequestMapping("/tp/kfaccount")
public class TbWxKfAccountController {
    @Autowired
    private TbWxKfAccountFeign tbWxKfAccountFeign;
    @Autowired
    private TokenService tokenService;

    @GetMapping("/list")
//    @PreAuthorize("@ss.hasPermi('tp:kfaccount:list')")
    public TableDataInfo list(KfAccountQuery query){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        query.setCorpId(loginUser.getUser().getCorpId());
        return tbWxKfAccountFeign.list(query);
    }

    /**
     * 获取微信客服信息详细信息
     */
    @GetMapping("/{id}")
//    @PreAuthorize("@ss.hasPermi('tp:kfaccount:query')")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return tbWxKfAccountFeign.getInfo(id);
    }

    /**
     * 新增微信客服信息
     */
    @PostMapping
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('tp:kfaccount:add')")
    @Log(title = "新微信客服", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody KfAccountDto kfAccountDto){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser sysUser = loginUser.getUser();
        kfAccountDto.setCorpId(sysUser.getCorpId());
        kfAccountDto.setCreateBy(sysUser.getUserId());
        kfAccountDto.setCreateTime(new Date());
        if(CollectionUtil.isNotEmpty(kfAccountDto.getUserList())){
            List<String> userIds = new ArrayList<>(kfAccountDto.getUserList());
            kfAccountDto.setUserIdList(userIds);
        }
        return tbWxKfAccountFeign.add(kfAccountDto);
    }

    /**
     * 修改修改微信客服信息
     */
    @PutMapping
//    @PreAuthorize("@ss.hasPermi('tp:kfaccout:update')")
    @Log(title = "微信客服", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody KfAccountDto kfAccountDto){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser sysUser = loginUser.getUser();
        kfAccountDto.setCorpId(sysUser.getCorpId());
        kfAccountDto.setUpdateBy(sysUser.getUserId());
        kfAccountDto.setUpdateTime(new Date());
        if(CollectionUtil.isNotEmpty(kfAccountDto.getUserList())){
            List<String> userIds = new ArrayList<>(kfAccountDto.getUserList());
            kfAccountDto.setUserIdList(userIds);
        }

        return tbWxKfAccountFeign.edit(kfAccountDto);
    }

    /**
     * 删除微信客服
     */
    @DeleteMapping("/{id}")
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('tp:kfaccout:remove')")
    @Log(title = "微信客服", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Long id){
        return tbWxKfAccountFeign.remove(id);
    }

    /**
     * 同步企微端微信客服
     */
    @GetMapping("/synKfAccount")
    @RepeatSubmit
//    @PreAuthorize("@ss.hasPermi('tp:kfaccount:sync')")
    @Log(title = "微信客服", businessType = BusinessType.OTHER)
    public AjaxResult synKfAccount(){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser sysUser = loginUser.getUser();
        String corpId = sysUser.getCorpId();
        return tbWxKfAccountFeign.synKfAccount(corpId);
    }
}
