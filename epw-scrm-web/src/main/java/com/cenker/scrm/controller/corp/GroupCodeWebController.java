package com.cenker.scrm.controller.corp;

import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.GroupCodeFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.request.data.GroupCodeStatisticQuery;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.request.group.GroupCodeStatisticsRequest;
import com.cenker.scrm.pojo.valid.*;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailDateVO;
import com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailGroupVO;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description 社群活码
 */
@RestController
@RequestMapping("/code/group")
@RequiredArgsConstructor
public class GroupCodeWebController {

    private final TokenService tokenService;
    private final GroupCodeFeign groupCodeFeign;

    @RepeatSubmit
    @PostMapping
    @Log(module = ModuleEnum.COMMUNITY_CODE, businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody @Validated(InsertGroup.class) GroupCodeRequest request) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        request.setCreateBy(Long.valueOf(user.getUserId()));
        request.setCorpId(user.getCorpId());
        if (request.getAutoCreateRoom() && StringUtils.isEmpty(request.getRoomBaseName())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }
        if (request.getAutoCreateRoom() && ObjectUtil.isNull(request.getRoomBaseId())) {
            throw new ParameterException(ErrCodeEnum.PARAM_ERROR);
        }

        LogUtil.logOperDesc(request.getCodeName());
        return groupCodeFeign.add(request);
    }

    @GetMapping
    public TableDataInfo list(@Validated(SelectGroup.class) GroupCodeRequest request) {
        return groupCodeFeign.list(request);
    }

    @RepeatSubmit
    @PutMapping
    @Log(module = ModuleEnum.COMMUNITY_CODE,  businessType = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody @Validated(UpdateGroup.class) GroupCodeRequest request) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        request.setUpdateBy(Long.valueOf(user.getUserId()));
        request.setCorpId(user.getCorpId());
        request.setCreateBy(Long.valueOf(user.getUserId()));

        LogUtil.logOperDesc(request.getCodeName());
        return groupCodeFeign.update(request);
    }

    @GetMapping("/detail")
    public AjaxResult get(@Validated(DetailGroup.class) GroupCodeRequest request) {
        return groupCodeFeign.detail(request);
    }

    @GetMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@Validated(DetailGroup.class) GroupCodeRequest request) {
        return groupCodeFeign.getDataStatistics(request);
    }

    @GetMapping("/getDataStatisticsDetail")
    public TableDataInfo getDataStatisticsDetail(@Validated({DetailGroup.class, SelectGroup.class}) GroupCodeStatisticsRequest request) {
        return groupCodeFeign.getDataStatisticsDetail(request);
    }

    @DeleteMapping
    @Log(module = ModuleEnum.COMMUNITY_CODE, businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestBody @Validated(DeleteGroup.class) GroupCodeRequest request) {
        return groupCodeFeign.remove(request);
    }

    @DeleteMapping("/disable")
    @Log(module = ModuleEnum.COMMUNITY_CODE, businessType = BusinessType.DELETE)
    public AjaxResult disable(@RequestBody Map<String, String> params) {
        return groupCodeFeign.disable(params);
    }

    @GetMapping("/exportDataStatisticsDetail")
    @Log(module = ModuleEnum.COMMUNITY_CODE, businessType = BusinessType.EXPORT)
    public void exportDataStatisticsDetail(@Validated({DetailGroup.class}) GroupCodeStatisticsRequest request) {
        if (request.getStatisticsType() == 1) {
            List<GroupCodeDataStatisticsDetailGroupVO> list = groupCodeFeign.exportDataStatisticsDetailByGroup(request);
            ExcelUtil<GroupCodeDataStatisticsDetailGroupVO> util = new ExcelUtil<>(GroupCodeDataStatisticsDetailGroupVO.class);
            util.exportExcel(list, "统计明细-按群聊查看", ServletUtils.getResponse(),false,null);
            LogUtil.logOperDesc("统计明细-按群聊查看");
        }else {
            List<GroupCodeDataStatisticsDetailDateVO> list = groupCodeFeign.exportDataStatisticsDetailByDate(request);
            ExcelUtil<GroupCodeDataStatisticsDetailDateVO> util = new ExcelUtil<>(GroupCodeDataStatisticsDetailDateVO.class);
            util.exportExcel(list, "统计明细-按日期查看", ServletUtils.getResponse(),false,null);
            LogUtil.logOperDesc("统计明细-按日期查看");
        }
    }

    @GetMapping("/getDataStatisticsTendency")
    public AjaxResult getDataStatisticsTendency(GroupCodeStatisticQuery statisticQuery){
        return groupCodeFeign.getDataStatisticsTendency(statisticQuery);
    }

}
