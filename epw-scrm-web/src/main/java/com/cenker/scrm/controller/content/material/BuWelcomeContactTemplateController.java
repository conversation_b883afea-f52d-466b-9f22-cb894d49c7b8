package com.cenker.scrm.controller.content.material;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.WelcomeContactTemplateFegin;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.content.material.BuWelcomeContactTemplateDTO;
import com.cenker.scrm.pojo.request.content.material.BuWelcomeContactTemplateRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 欢迎语模板Controller
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/bu/welcome/template")
public class BuWelcomeContactTemplateController extends BaseController {

    private final WelcomeContactTemplateFegin welcomeContactTemplateFegin;

    private final TokenService tokenService;

    /**
     * 查询欢迎语模板列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BuWelcomeContactTemplateRequest queryRequest) {
        return welcomeContactTemplateFegin.list(queryRequest);
    }

    /**
     * 获取欢迎语模板详细信息
     */
    @GetMapping()
    public AjaxResult getInfo(@RequestParam("id") Long id) {
        return welcomeContactTemplateFegin.getInfo(id);
    }

    /**
     * 新增欢迎语模板
     */
    @Log(module = ModuleEnum.WELCOME_CONTACT_TEMPLATE, businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        LogUtil.logOperDesc(welcomeContactTemplateDTO.getName());

        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        welcomeContactTemplateDTO.setCreateBy(userId);
        welcomeContactTemplateDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId());
        return welcomeContactTemplateFegin.add(welcomeContactTemplateDTO );
    }

    /**
     * 修改欢迎语模板
     */

    @Log(module = ModuleEnum.WELCOME_CONTACT_TEMPLATE, businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        LogUtil.logOperDesc(welcomeContactTemplateDTO.getName());

        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        welcomeContactTemplateDTO.setUpdateBy(userId);
        return welcomeContactTemplateFegin.edit(welcomeContactTemplateDTO);
    }

    /**
     * 修改欢迎语模板的状态
     */
    @Log(module = ModuleEnum.WELCOME_CONTACT_TEMPLATE, businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        LogUtil.logOperDesc("欢迎语状态");

        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        welcomeContactTemplateDTO.setUpdateBy(userId);
        return welcomeContactTemplateFegin.updateStatus(welcomeContactTemplateDTO);
    }

    /**
     * 查询欢迎语模板使用场景
     */
    @GetMapping("/sceneList")
    public TableDataInfo getUsedSceneList(BuWelcomeContactTemplateRequest queryRequest) {
        return welcomeContactTemplateFegin.getUsedSceneList(queryRequest);
    }
    /**
     * 查询启用状态欢迎语模板列表
     */
    @GetMapping("/enableList")
    public TableDataInfo enableList(BuWelcomeContactTemplateRequest queryRequest) {
        return welcomeContactTemplateFegin.enableList(queryRequest);
    }
}
