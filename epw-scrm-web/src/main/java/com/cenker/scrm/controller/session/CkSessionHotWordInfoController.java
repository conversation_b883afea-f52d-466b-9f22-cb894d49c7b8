package com.cenker.scrm.controller.session;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.SessionHotWordFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.AddHotWordDto;
import com.cenker.scrm.pojo.dto.session.UserDto;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 热词信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@RequestMapping("/session/hotword")
@Slf4j
public class CkSessionHotWordInfoController
{

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SessionHotWordFeign sessionHotWordFeign;

    /**
     * 查询热词信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CkSessionHotWordInfo ckSessionHotWordInfo)
    {
        return sessionHotWordFeign.list(ckSessionHotWordInfo);
    }

    /**
     * 获取热词信息详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long hotId)
    {
        return sessionHotWordFeign.getInfo(hotId);
    }

    /**
     * 新增热词信息
     */
    //@PreAuthorize("@ss.hasPermi('session:info:add')")
    @Log(module = ModuleEnum.HOT_WORK_SETTING, businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AddHotWordDto addHotWordDto)
    {
        LogUtil.logOperDesc(addHotWordDto.getHotWord());

        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        addHotWordDto.setCreateBy(userId);
        String hotWord = addHotWordDto.getHotWord();
        boolean errFlag = false;
        StringBuffer buffer = new StringBuffer();
        if(StringUtils.isEmpty(hotWord)){
            errFlag = true;
            buffer.append("热词不能为空");
        }else {
            if (hotWord.length() > 20) {
                errFlag = true;
                buffer.append("热词不能超过20个字");
            }else{
                CkSessionHotWordInfo qryInfo = new CkSessionHotWordInfo();
                qryInfo.setHotWord(hotWord);
                Long num = sessionHotWordFeign.cntHotWordRep(qryInfo);
                if(num !=null && num.longValue()>0){
                    errFlag = true;
                    buffer.append("热词已存在");
                }
            }
        }
        String synword =  addHotWordDto.getSynonWords();
        if(StringUtils.isNotEmpty(synword)){
            String[] word = synword.split(",");
            Boolean emptyBo = false;
            Boolean lengthBo = false;
            for (int i=0;i<word.length;i++){
                String wd = word[i];
                if (StringUtils.isEmpty(wd)) {
                    emptyBo = true;
                    errFlag = true;
                } else if (wd.length() > 20) {
                    lengthBo =true;
                    errFlag = true;
                }
            }
            String errInfo = "";
            if(emptyBo){
                errInfo = errInfo+"近似词逗号之前不能有空值;";
            }
            if(lengthBo){
                errInfo = errInfo+"近似词单个长度不能超过20个字;";
            }
            if(StringUtils.isNotEmpty(errInfo)) {
                buffer.append(errInfo);
            }
        }

        if(errFlag){
            return AjaxResult.error(buffer.toString());
        }


        return sessionHotWordFeign.add(addHotWordDto);
    }

    /**
     * 修改热词信息
     */
   // @PreAuthorize("@ss.hasPermi('session:info:edit')")
    @Log(module = ModuleEnum.HOT_WORK_SETTING, businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AddHotWordDto addHotWordDto)
    {
        LogUtil.logOperDesc(addHotWordDto.getHotWord());

        String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
        addHotWordDto.setUpdateBy(userId);
        String hotWord = addHotWordDto.getHotWord();
        boolean errFlag = false;
        StringBuffer buffer = new StringBuffer();
        if(StringUtils.isEmpty(hotWord)){
            errFlag = true;
            buffer.append("热词不能为空");
        }else {
            if (hotWord.length() > 20) {
                errFlag = true;
                buffer.append("热词不能超过20个字");
            }else{
                CkSessionHotWordInfo qryInfo = new CkSessionHotWordInfo();
                qryInfo.setHotWord(hotWord);
                qryInfo.setNotEqHotId(addHotWordDto.getHotId());
                Long num = sessionHotWordFeign.cntHotWordRep(qryInfo);
                if(num !=null && num.longValue()>0){
                    errFlag = true;
                    buffer.append("热词已存在");
                }
            }
        }
        String synword =  addHotWordDto.getSynonWords();
        if(StringUtils.isNotEmpty(synword)){
            String[] word = synword.split(",");
            Boolean emptyBo = false;
            Boolean lengthBo = false;
            for (int i=0;i<word.length;i++){
                String wd = word[i];
                if (StringUtils.isEmpty(wd)) {
                    emptyBo = true;
                    errFlag = true;
                } else if (wd.length() > 20) {
                    lengthBo =true;
                    errFlag = true;
                }
            }
            String errInfo = "";
            if(emptyBo){
                errInfo = errInfo+"近似词逗号之前不能有空值;";
            }
            if(lengthBo){
                errInfo = errInfo+"近似词单个长度不能超过20个字;";
            }
            if(StringUtils.isNotEmpty(errInfo)) {
                buffer.append(errInfo);
            }
        }
        if(errFlag){
            return AjaxResult.error(buffer.toString());
        }
        return sessionHotWordFeign.edit(addHotWordDto);
    }

    /**
     * 删除热词信息
     */
    //@PreAuthorize("@ss.hasPermi('session:info:remove')")
    @Log(module = ModuleEnum.HOT_WORK_SETTING, businessType = BusinessType.DELETE)
	@DeleteMapping
    public AjaxResult remove(String hotIds)
    {
        return sessionHotWordFeign.remove(hotIds);
    }


    @Log(module = ModuleEnum.HOT_WORK_SETTING, businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletResponse response)
    {
        ExcelUtil<AddHotWordDto> util = new ExcelUtil<AddHotWordDto>(AddHotWordDto.class);
        util.exportExcel(null,"热词",response);
        LogUtil.logOperDesc("热词");
    }

    @Log(module = ModuleEnum.HOT_WORK_SETTING, businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file, @RequestParam("checkUserIds") String checkuserIds )
    {
        LogUtil.logOperDesc(file.getOriginalFilename());

        try {
            long indx = 1;
            StringBuffer buffer = new StringBuffer();
            boolean errFlag = false;
            String userId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId();
            ExcelUtil<AddHotWordDto> util = new ExcelUtil<AddHotWordDto>(AddHotWordDto.class);
            List<AddHotWordDto> wordList = util.importExcel(file.getInputStream());
            Set<String> charSet = new HashSet<>();
            for (AddHotWordDto dato:wordList) {
                indx++;
                String indxStr = "第"+indx+"行";

                String hotWord = dato.getHotWord();
                if(StringUtils.isEmpty(hotWord)){
                    errFlag = true;
                    buffer.append(indxStr+"热词不能为空;");
                }else {
                    if (hotWord.length() > 20) {
                        errFlag = true;
                        buffer.append(indxStr + "热词不能超过20个字;");
                    }else{
                        CkSessionHotWordInfo qryInfo = new CkSessionHotWordInfo();
                        qryInfo.setHotWord(hotWord);
                        Long num = sessionHotWordFeign.cntHotWordRep(qryInfo);
                        if(num !=null && num.longValue()>0){
                            errFlag = true;
                            buffer.append(indxStr + "热词已存在;");
                        }else{
                            if(charSet.contains(hotWord)){
                                errFlag = true;
                                buffer.append(indxStr + "热词名称重复;");
                            }else{
                                charSet.add(hotWord);
                            }
                        }
                    }

                }
                dato.setCreateBy(userId);
                dato.setCheckUserIds(checkuserIds);
                String userids = dato.getCheckUserIds();
                if(StringUtils.isNotEmpty(userids)){
                    String[] userIdArry = userids.split(",");
                    List<UserDto> userConditionList = new ArrayList<>();
                    for(int i= 0;i<userIdArry.length;i++){
                        String userid = userIdArry[i];
                        if(StringUtils.isNotEmpty(userid)) {
                            UserDto userDto = new UserDto();
                            userDto.setUserId(userid);
                            userConditionList.add(userDto);
                        }
                    }
                    dato.setUserConditionList(userConditionList);
                }
                String synword =  dato.getSynonWords();
                if(StringUtils.isNotEmpty(synword)){
                    String[] word = synword.split(",");
                    Boolean emptyBo = false;
                    Boolean lengthBo = false;
                    for (int i=0;i<word.length;i++){
                        String wd = word[i];
                        if (StringUtils.isEmpty(wd)) {
                            emptyBo = true;
                            errFlag = true;
                        } else if (wd.length() > 20) {
                            lengthBo =true;
                            errFlag = true;
                        }
                    }
                    String errInfo = "";
                    if(emptyBo){
                      errInfo = errInfo+"近似词逗号之前不能有空值;";
                    }
                    if(lengthBo){
                        errInfo = errInfo+"近似词单个长度不能超过20个字;";
                    }
                    if(StringUtils.isNotEmpty(errInfo)) {
                        buffer.append(indxStr + errInfo);
                    }
                }
            }
            if(errFlag){
                return AjaxResult.error("导入失败;"+buffer.toString());
            }
            return sessionHotWordFeign.importExcel(wordList);
        }catch (Exception e){
            log.error("###########导入热词失败",e);
        }
        return AjaxResult.error("导入失败");
    }

    @PostMapping("/cntHotWordRep")
    public AjaxResult cntHotWordRep(@RequestBody CkSessionHotWordInfo ckSessionSensRuleInfo){
        return AjaxResult.success(sessionHotWordFeign.cntHotWordRep(ckSessionSensRuleInfo));
    }

}
