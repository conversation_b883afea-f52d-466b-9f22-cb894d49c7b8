package com.cenker.scrm.controller.ai;


import com.cenker.scrm.client.ai.feign.AzureOpenApiFeign;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.ai.AzureAiSmartWriteRequest;
import com.cenker.scrm.pojo.dto.ai.AzureOpenApiRequest;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.SelectGroup;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023/5/24
 * @Description chatAI
 * 聊天以会话（浏览器）为一次记录并区分登录账号 2023-05-25产品确认
 */
@RestController
@RequestMapping("/ai/chat")
@RequiredArgsConstructor
public class AzureOpenApiWebController {

    private final AzureOpenApiFeign azureOpenApiFeign;

    //302ebd70-c574-46db-a737-2b9d01f333b8
    @PostMapping("/ask")
    public AjaxResult ask(@RequestBody @Validated(InsertGroup.class) AzureOpenApiRequest azureOpenApiRequest){
        return azureOpenApiFeign.ask(azureOpenApiRequest);
    }

    @GetMapping("/getChatRecord")
    public AjaxResult getChatRecord(@Validated(SelectGroup.class) AzureOpenApiRequest azureOpenApiRequest){
        return azureOpenApiFeign.getChatRecord(azureOpenApiRequest);
    }

    @GetMapping("/stopResponse")
    public AjaxResult interruptResponse(@NotNull String groupId){
        return azureOpenApiFeign.stopResponse(groupId);
    }

    @PostMapping("/create")
    public AjaxResult create(@RequestBody @Validated AzureAiSmartWriteRequest azureAiSmartWriteRequest){
        return azureOpenApiFeign.create(azureAiSmartWriteRequest);
    }

    @GetMapping("/getSmartWriteRecord")
    public TableDataInfo getSmartWriteRecord(String type){
        return azureOpenApiFeign.getSmartWriteRecord(type);
    }
}
