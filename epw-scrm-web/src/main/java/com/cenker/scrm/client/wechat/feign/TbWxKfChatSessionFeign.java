package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgSessionResp;
import com.cenker.scrm.pojo.request.kf.WkWxKfChatRecordQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/tp/session")
public interface TbWxKfChatSessionFeign {

    /**
     * 查询列表
     */
    @RequestMapping("/list")
    TableDataInfo querySession(@RequestBody WkWxKfMsgSessionResp wkWxKfMsgSessionResp);

    /**
     * 查询会话 全部聊天记录
     */
    @RequestMapping("/chat/record")
    TableDataInfo queryChatRecord(@RequestBody WkWxKfChatRecordQuery chatRecordQueryDTO);

}
