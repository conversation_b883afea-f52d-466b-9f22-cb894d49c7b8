package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.custlink.CustLinkListDto;
import com.cenker.scrm.pojo.dto.session.AddSensRuleDto;
import com.cenker.scrm.pojo.dto.session.QrySensRuleDto;
import com.cenker.scrm.pojo.dto.session.UpdSensRuleStatusDto;
import com.cenker.scrm.pojo.request.TbWxContactConfigRequest;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.contact.ContactVO;
import com.cenker.scrm.pojo.vo.custlink.CustlinkVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 获客链接
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/tp/customerLink")
public interface TbWxCustlinkFeign {

    @GetMapping("/list")
    public TableDataInfo list(@RequestBody CustLinkListDto custLinkDto);

    @GetMapping(value = "qryLinkInfo")
    public AjaxResult getInfo(Long id);

    @PostMapping
    public AjaxResult add(@RequestBody CustlinkVO custlinkVO);

    @PutMapping
    public AjaxResult edit(@RequestBody CustlinkVO custlinkVO);


    @DeleteMapping
    public AjaxResult updStatus(@RequestBody CustlinkVO custlinkVO);

    @GetMapping(value = "qryDataStat")
    public AjaxResult qryDataStat(@RequestBody CustlinkVO custlinkVO);

    @GetMapping(value = "qryDataChart")
    public AjaxResult qryDataChart(@RequestBody CustlinkVO custlinkVO);

    @GetMapping("/qryCustList")
    public TableDataInfo qryCustList(@RequestBody CustlinkVO custlinkVO);

}
