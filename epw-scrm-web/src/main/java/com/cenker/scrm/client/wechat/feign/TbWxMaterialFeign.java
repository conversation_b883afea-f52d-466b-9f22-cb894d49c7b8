package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterialPoster;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/tp/material")
public interface TbWxMaterialFeign {

    /**
     * 查询素材信息列表
     */
    @RequestMapping("/list")
    TableDataInfo<TbWxMaterial> list(@RequestBody MaterialListDto param);

    /**
     * 导出素材信息列表
     */
    @RequestMapping("/export")
    AjaxResult export(@RequestBody TbWxMaterial tbWxMaterial);

    /**
     * 获取素材信息详细信息
     */
    @RequestMapping("/{id}")
    Result<TbWxMaterial> getInfo(@PathVariable("id") Long id);

    /**
     * 新增素材信息
     */
    @RequestMapping("/add")
    AjaxResult add(@RequestBody TbWxMaterial tbWxMaterial);

    /**
     * 修改素材信息
     */
    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody TbWxMaterial tbWxMaterial);

    /**
     * 删除素材信息
     */
    @RequestMapping("/remove")
    AjaxResult remove(@RequestBody TbWxMaterial tbWxMaterial);

    /**
     * 营销海报分类树结构（叶子节点没有海报不返回该节点）
     * @param name
     * @return
     */
    @GetMapping("/treeSelect")
    AjaxResult treeSelect(@RequestParam("name") String name);

    /**
     * 根据链接类型获取海报信息
     * @param type
     * @return
     */
    @GetMapping("/poster/{type}")
    AjaxResult poster(@PathVariable("type") String type);

    /**
     * 海报设置
     */
    @PutMapping("/posterSet")
    AjaxResult posterSet(@RequestBody TbWxMaterialPoster tbWxMaterialPoster);
    /**
     * 审批
     */
    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO);

    /**
     * 撤回
     */
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO);
}
