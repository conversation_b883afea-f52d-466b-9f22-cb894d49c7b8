package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysRole;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.system.SysUserRole;
import com.cenker.scrm.pojo.request.role.RoleRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/system/role")
public interface SysRoleFeign {

    @RequestMapping("/list")
    TableDataInfo list(@RequestBody SysRole role);

    @RequestMapping("/listNotAdmin")
    AjaxResult listNotAdmin();

    @RequestMapping("/export")
    List<SysRole> export(@RequestBody SysRole role);

    /**
     * 根据角色编号获取详细信息
     */
    @RequestMapping(value = "/{roleId}")
    AjaxResult getInfo(@PathVariable("roleId") Long roleId);

    /**
     * 新增角色
     */
    @RequestMapping("/add")
    AjaxResult add(@RequestBody SysRole role);

    /**
     * 修改保存角色
     */
    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody SysRole role);

    /**
     * 修改保存数据权限
     */
    @RequestMapping("/dataScope")
    AjaxResult dataScope(@RequestBody SysRole role);

    /**
     * 状态修改
     */
    @RequestMapping("/changeStatus")
    AjaxResult changeStatus(@RequestBody SysRole role);

    /**
     * 删除角色
     */
    @RequestMapping("/remove/{roleIds}")
    AjaxResult remove(@PathVariable("roleIds") Long[] roleIds);

    /**
     * 获取角色选择框列表
     */
    @RequestMapping("/optionSelect")
    AjaxResult optionSelect();

    /**
     * 查询已分配用户角色列表
     */
    @GetMapping("/authUser/allocatedList")
    TableDataInfo allocatedList(SysUser user);

    /**
     * 取消授权用户
     */
    @PutMapping("/authUser/cancel")
    AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole);

    /**
     * 批量取消授权用户
     */
    @PutMapping("/authUser/cancelAll")
    AjaxResult cancelAuthUserAll(@RequestBody RoleRequest roleRequest);

    @GetMapping("/getApprovalUser")
    Result<List<String>> getApprovalUser(@RequestParam("type") String type, @RequestParam("userId") String userId);
}
