package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(ServiceNameConstants.WX_CENTER_SERVICE)
public interface WxOauth2Feign {

    @RequestMapping("/h5/getJsapiTicket")
    AjaxResult getJsapiTicket(@RequestParam("url") String url,
                              @RequestParam("corpId") String corpId);

    @RequestMapping(value = "/h5/getUserInfo",method = RequestMethod.GET)
    AjaxResult getUserInfo();

    @RequestMapping("/h5/getJsapiAppTicket")
    AjaxResult getJsapiAppTicket();

    @RequestMapping("/h5/getToken")
    AjaxResult getToken();
}
