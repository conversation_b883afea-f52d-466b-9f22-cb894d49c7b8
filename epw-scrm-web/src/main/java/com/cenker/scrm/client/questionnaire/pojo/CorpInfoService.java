package com.cenker.scrm.client.questionnaire.pojo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cenker.scrm.client.wechat.feign.TbWxCorpConfigFeign;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/1/17 17:07
 **/
@Service
public class CorpInfoService {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private TbWxCorpConfigFeign tbWxCorpConfigFeign;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取企业唯一标识
     *
     * @return
     */
    public Long getCorpPriId() {
        SysUser sysUser = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        String corpId = sysUser.getCorpId();
        TbWxCorpConfigVo wxCorpConfigVo = getCorpInfo(corpId);

        return wxCorpConfigVo.getId();
    }

    /**
     * 获取企业唯一标识
     *
     * @return
     */
    public Long getCorpPriId(String corpId) {
        TbWxCorpConfigVo wxCorpConfigVo = getCorpInfo(corpId);
        return wxCorpConfigVo.getId();
    }

    /**
     * 获取企业唯一标识
     *
     * @return
     */
    public TbWxCorpConfigVo getCorpInfo(String corpId) {
        String cacheKey = CacheKeyConstants.CORP_BASE_INFO + corpId;
        String data = redisCache.getCacheObject(cacheKey);
        TbWxCorpConfigVo wxCorpConfigVo = null;
        if (StrUtil.isBlank(data)) {
            AjaxResult corpFigResult = tbWxCorpConfigFeign.getCorpInfoByCorpId(corpId);
            wxCorpConfigVo = JSONUtil.toBean(JSONUtil.toJsonStr(corpFigResult.get(AjaxResult.DATA_TAG)), TbWxCorpConfigVo.class);
        } else {
            wxCorpConfigVo = JSONUtil.toBean(data, TbWxCorpConfigVo.class);
        }

        return wxCorpConfigVo;
    }
}
