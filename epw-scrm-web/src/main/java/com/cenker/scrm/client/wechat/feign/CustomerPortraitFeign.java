package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerTrajectory;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/customer/portrait")
public interface CustomerPortraitFeign {

    @RequestMapping("/findTrajectory2")
    AjaxResult findTrajectory2(@RequestBody TbWxCustomerTrajectory tbWxCustomerTrajectory);

    @RequestMapping("/findTrajectory3")
    TableDataInfo findTrajectory3(@RequestBody TbWxCustomerTrajectory tbWxCustomerTrajectory);

    @RequestMapping("/getTrajectoryCount")
    AjaxResult getTrajectoryCount(@RequestBody TbWxCustomerTrajectory trajectory);
}
