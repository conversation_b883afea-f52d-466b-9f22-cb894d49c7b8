package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.export.ChurnCustomerExport;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/api/wx/tp/customer/churn")
public interface TbWxChurnCustomerFeign {
    /**
     * 查询企业微信外部客户信息列表
     */
    @RequestMapping("/list")
    TableDataInfo list(CustomerChurnDTO dto);

    /**
     * 导出企业微信外部客户信息列表
     */
    @RequestMapping("/export")
    List<ChurnCustomerExport> export(@RequestBody CustomerChurnDTO dto);

    /**
     * 获取企业微信外部客户信息详细信息
     */
    @RequestMapping("/{externalUserid}")
    AjaxResult getInfo(@PathVariable("externalUserid") String externalUserid);

    /**
     * 新增企业微信外部客户信息
     */
    @RequestMapping("/add")
    AjaxResult add(@RequestBody TbWxExtCustomer tbWxExtCustomer);

    /**
     * 修改企业微信外部客户信息
     */
    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody TbWxExtCustomer tbWxExtCustomer);

    /**
     * 删除企业微信外部客户信息
     */
    @RequestMapping("/remove/{externalUserids}")
    AjaxResult remove(@PathVariable("externalUserids") String[] externalUserids);
}
