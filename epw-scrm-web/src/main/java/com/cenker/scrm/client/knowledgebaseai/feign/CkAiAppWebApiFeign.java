package com.cenker.scrm.client.knowledgebaseai.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.knowledgebaseai.CkAiRequest;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiApp;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ServiceNameConstants.KNOWLEDGEBASEAI_CENTER_SERVICE,path = "/knowledgebaseai/app")
public interface CkAiAppWebApiFeign {
    @RequestMapping("/list")
    TableDataInfo list(CkAiRequest ckAiRequest);
    @RequestMapping("/add")
    AjaxResult add(@RequestBody CkAiApp ckAiApp);
    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody CkAiApp ckAiApp);
    @RequestMapping("/get/{id}")
    AjaxResult getCkAiAppById(@RequestParam("id") Long id);
}
