package com.cenker.scrm.client.ai.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.ai.AzureAiSmartWriteRequest;
import com.cenker.scrm.pojo.dto.ai.AzureOpenApiRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/ai/chat")
public interface AzureOpenApiFeign {

    /**
     * 校验会话
     * @param azureOpenApiRequest
     * @return
     */
    @PostMapping("/validSession")
    AjaxResult validSession(@RequestBody AzureOpenApiRequest azureOpenApiRequest);

    /**
     * 提问
     * @param azureOpenApiRequest
     * @return
     */
    @PostMapping("/ask")
    AjaxResult ask(@RequestBody AzureOpenApiRequest azureOpenApiRequest);

    /**
     * 获取聊天记录
     * @param azureOpenApiRequest
     * @return
     */
    @PostMapping("/getChatRecord")
    AjaxResult getChatRecord(@RequestBody AzureOpenApiRequest azureOpenApiRequest);

    /**
     * 中断响应
     * @param groupId
     * @return
     */
    @GetMapping("/stopResponse")
    AjaxResult stopResponse(String groupId);

    /**
     * 智能创作
     * @param azureAiSmartWriteRequest
     * @return
     */
    @PostMapping("/create")
    AjaxResult create(@RequestBody AzureAiSmartWriteRequest azureAiSmartWriteRequest);

    @GetMapping("/getSmartWriteRecord")
    TableDataInfo getSmartWriteRecord(String type);
}
