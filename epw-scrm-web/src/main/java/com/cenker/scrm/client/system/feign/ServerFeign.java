package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/monitor/server")
public interface ServerFeign {

    @RequestMapping("/getInfo")
    AjaxResult getInfo();
}
