package com.cenker.scrm.client.wechat.feign;


import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarBatchOperateDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.InteractRadarVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/interact/radar")
public interface TbWxRadarInteractFeign {
    @RequestMapping("/add")
    AjaxResult add(@RequestBody InteractRadarVo radarVo);

    @RequestMapping("/list")
    TableDataInfo<InteractRadarVo> list(@RequestBody InteractRadarVo interactRadarVo);

    @RequestMapping("/removeRadar")
    AjaxResult remove(@RequestBody InteractRadarVo radarVo);

    @RequestMapping("/{radarId}")
    Result<InteractRadarVo> getById(@PathVariable("radarId") String radarId);

    @RequestMapping("/edit")
    AjaxResult edit(InteractRadarVo radarVo);

    @RequestMapping("/getRadarStatistics/{radarId}")
    AjaxResult getRadarStatistics(@PathVariable("radarId") String radarId);

    @RequestMapping("/getRadarReadRecordStatistics/{radarId}")
    TableDataInfo getRadarReadRecordStatistics(@PathVariable("radarId") String radarId);

    @RequestMapping("/getRadarReadRecordDetail/{customerId}")
    TableDataInfo getRadarReadRecordDetail(@PathVariable("customerId") String customerId);

    @RequestMapping("/extract/radarContent")
    AjaxResult extractRadarContent(@RequestBody TbWxRadarContent content);

    @RequestMapping("/extract/linkData")
    AjaxResult extractLinkData(TbWxRadarContent content);

    @RequestMapping("/getRadarSource")
    TableDataInfo getRadarSource(@RequestBody InteractRadarVo radarVo);
    @RequestMapping("/getRadarStatisticsRank")
    TableDataInfo getRadarStatisticsRank(QueryRadarStatisticsRankDTO dto);

    @RequestMapping("/batchOperate")
    public AjaxResult batchOperate(@RequestBody RadarBatchOperateDto batchOperateDto);

    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO);

    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO);
}
