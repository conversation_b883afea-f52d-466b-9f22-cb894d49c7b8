package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = ServiceNameConstants.CHAT_ARCHIVE_SERVICE, path = "/chatarchive")
public interface ChatArchiveServiceFeign {

    @GetMapping("/file/download")
    Response download(String fileName);
}
