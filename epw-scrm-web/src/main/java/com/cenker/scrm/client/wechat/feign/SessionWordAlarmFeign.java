package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.AddHotWordDto;
import com.cenker.scrm.pojo.dto.session.QryActAlarmDto;
import com.cenker.scrm.pojo.dto.session.QryWordAlarmDto;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.session.ExportCkSessionSensActAlarmVO;
import com.cenker.scrm.pojo.vo.session.ExportCkSessionSensWordAlarmVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 热词信息Controller
 * <AUTHOR>
 * @date 2024-03-15
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/session/wordalarm")
public interface SessionWordAlarmFeign
{
    /**
     * 查询热词信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody QryWordAlarmDto qryWordAlarmDto);

    @GetMapping("/export")
    public List<ExportCkSessionSensWordAlarmVO> export(QryWordAlarmDto qryWordAlarmDto);

}
