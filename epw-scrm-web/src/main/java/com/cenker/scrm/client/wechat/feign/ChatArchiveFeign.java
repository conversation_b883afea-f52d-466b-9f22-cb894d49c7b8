package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.request.chatarchive.ChatArchiveQuery;
import com.cenker.scrm.pojo.request.chatarchive.PermitUserQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/chatarchive")
public interface ChatArchiveFeign {

    @GetMapping("/userTreeList")
    AjaxResult userTreeList(@RequestBody PermitUserQuery query);

    @GetMapping("/user/chatListByPage/{userId}")
    TableDataInfo userChatList(@PathVariable("userId") String userId, @RequestBody ChatArchiveQuery query);

    @GetMapping("/extcustomer/extcustomerListByPage")
    TableDataInfo extCustomerList(@RequestBody ChatArchiveQuery query);

    @GetMapping("/extcustomer/chatListByPage/{extUserId}")
    TableDataInfo extCustomerChatList(@PathVariable("extUserId") String extUserId, @RequestBody ChatArchiveQuery query);

    @GetMapping("/chatMessageListByPage")
    TableDataInfo chatMessageListByPage(@RequestBody ChatArchiveQuery query);

    @GetMapping("/chatMessageListBySendType")
    TableDataInfo chatMessageListBySendType(@RequestBody ChatArchiveQuery query);

    @GetMapping("/chatTimeList")
    AjaxResult chatTimeList(@RequestBody ChatArchiveQuery query);
}
