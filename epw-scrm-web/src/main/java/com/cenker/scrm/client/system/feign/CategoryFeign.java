package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.dto.system.*;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/category")
public interface CategoryFeign {

    @PostMapping()
    AjaxResult add(@Valid @RequestBody CategoryAddDto categoryAddDto);

    @PutMapping()
    AjaxResult update(@Valid @RequestBody CategoryUpdateDto categoryUpdateDto);

    @PutMapping("/move")
    AjaxResult move(@Valid @RequestBody CategoryMoveDto categoryMoveDto);

    @DeleteMapping()
    AjaxResult delete(@Valid @RequestBody CategoryRemoveDto categoryRemoveDto);

    @PostMapping("/treeSelect")
    AjaxResult treeSelect(@RequestBody CategoryTreeSelectDto categoryTreeSelectDto);
}
