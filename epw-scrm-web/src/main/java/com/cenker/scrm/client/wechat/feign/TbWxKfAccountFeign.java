package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.kf.KfAccountDto;
import com.cenker.scrm.pojo.request.kf.KfAccountQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/tp/kfaccount")
public interface TbWxKfAccountFeign {
    @RequestMapping("/list")
    TableDataInfo list(@RequestBody KfAccountQuery query);

    /**
     * 获取微信客服信息详细信息
     */
    @RequestMapping("/getInfo")
    AjaxResult getInfo(@RequestParam("id") Long id);

    /**
     * 新增微信客服信息
     */
    @RequestMapping("/add")
    AjaxResult add(@RequestBody KfAccountDto kfAccountDto);

    /**
     * 修改修改微信客服信息
     */
    @RequestMapping("/update")
    AjaxResult edit(@RequestBody KfAccountDto kfAccountDto);

    /**
     * 删除微信客服
     */
    @RequestMapping("/delete")
    AjaxResult remove(@RequestParam("id") Long id);

    /**
     * 同步企微端微信客服
     */
    @RequestMapping("/synKfAccount")
    AjaxResult synKfAccount(@RequestParam("corpId") String corpId);
}
