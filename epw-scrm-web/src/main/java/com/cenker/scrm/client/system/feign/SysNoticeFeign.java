package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysNotice;
import com.cenker.scrm.pojo.request.system.SysNoticeRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE, path = "/system/notice")
public interface SysNoticeFeign {

    @RequestMapping("/list")
    TableDataInfo list(@RequestBody SysNoticeRequest request);

    @RequestMapping("/getInfo/{noticeId}")
    AjaxResult getInfo(@PathVariable("noticeId") Long noticeId);

    @RequestMapping("/add")
    AjaxResult add(@RequestBody SysNotice notice);

    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody SysNotice notice);

    @RequestMapping("/remove/{noticeIds}")
    AjaxResult remove(@PathVariable("noticeIds") Long[] noticeIds);

    @RequestMapping("/noticeList")
    AjaxResult noticeList();

    @RequestMapping("/record/list")
    TableDataInfo recordList(@RequestBody SysNoticeRequest request);

    @RequestMapping("/record/{id}")
    AjaxResult recordInfo(@PathVariable("id") Long recordId);

    @RequestMapping("/record/unread")
    AjaxResult unRead(@RequestBody SysNoticeRequest request);

    @RequestMapping("/revocation")
    AjaxResult revocation(@RequestBody SysNoticeRequest request);

    @RequestMapping("/send")
    AjaxResult sendNotice(@RequestBody SysNoticeRequest request);

    @RequestMapping("/cancel")
    AjaxResult cancelSend(@RequestBody SysNoticeRequest request);

    @RequestMapping("/record/unreadStatus")
    AjaxResult unreadStatus(@RequestBody SysNoticeRequest request);

    @RequestMapping("/record/setAllRead")
    AjaxResult setAllRead(@RequestBody SysNoticeRequest request);
}
