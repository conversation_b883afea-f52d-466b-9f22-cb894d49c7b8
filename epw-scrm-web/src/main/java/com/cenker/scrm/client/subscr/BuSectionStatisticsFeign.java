package com.cenker.scrm.client.subscr;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/bu/section/statistics")
public interface BuSectionStatisticsFeign {
    @GetMapping("/list")
    Result<BuSectionStatisticsVO> list(BuSectionStatisticsQuery query);

    @PostMapping("/synData")
    Result synData(@RequestBody BuSectionStatisticsQuery query);
}
