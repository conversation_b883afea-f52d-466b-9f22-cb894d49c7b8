package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.system.*;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/material/job")
public interface BuMaterialJobFeign {

    @PostMapping
    Result add(@Validated @RequestBody BuMaterialJobAddDto param);

    @PutMapping
    Result update(@Validated @RequestBody BuMaterialJobUpdateDto param);

    @DeleteMapping
    Result delete(@RequestParam("id") String id);

    @GetMapping
    Result<BuMaterialJobVo> detail(@RequestParam("id") String id);

    @GetMapping("/list")
    TableDataInfo list(BuMaterialJobListDto param);

    @PutMapping("/run")
    Result runJob(@Validated @RequestBody BuMaterialJobRunDto param);

    @PutMapping("/status")
    Result enable(@Validated @RequestBody BuMaterialJobEnableDto param);

}
