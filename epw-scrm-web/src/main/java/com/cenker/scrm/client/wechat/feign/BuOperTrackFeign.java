package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackSearchVO;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/bu/oper/track")
public interface BuOperTrackFeign {
    @GetMapping("/getCustomerOpeLogList")
    TableDataInfo<BuOperTrackVO> getCustomerOpeLogList(@RequestBody BuOperTrackSearchVO buOperTrack);

    @GetMapping("/getTrajectoryCount")
    AjaxResult getTrajectoryCount(@RequestBody BuOperTrackSearchVO buOperTrack);
}
