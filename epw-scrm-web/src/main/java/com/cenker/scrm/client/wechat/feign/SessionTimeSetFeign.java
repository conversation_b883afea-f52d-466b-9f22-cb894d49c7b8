package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.AddHotWordDto;
import com.cenker.scrm.pojo.dto.session.AddTimeSetDto;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * 热词信息Controller
 * <AUTHOR>
 * @date 2024-03-15
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/session/timeset")
public interface SessionTimeSetFeign
{
    /**
     * 获取超时设置详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo();

    /**
     * 修改热词信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody AddTimeSetDto addTimeSetDto);

}
