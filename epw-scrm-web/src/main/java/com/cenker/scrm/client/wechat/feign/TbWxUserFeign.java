package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.wx.model.WxUserBaseInfoUpdateDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/wechat/tbWxUser")
public interface TbWxUserFeign {
    @RequestMapping("/selectTbWxUserListNum")
    Integer selectTbWxUserListNum(@RequestParam("corpId") String corpId);

    @RequestMapping("/getUserList")
    TableDataInfo getUserList();

    @RequestMapping("/list")
    TableDataInfo list(@RequestBody TbWxUser tbWxUser);

    /**
     * 同步部门下组织成员
     */
    @RequestMapping("/synchronizationWxUserByDeptId/{deptId}")
    AjaxResult synchronizationWxUserByDeptId(@PathVariable("deptId") Long deptId);

    @RequestMapping("/setCorpAdmin")
    AjaxResult setCorpAdmin(@RequestBody TbWxUser tbWxUser);
    @RequestMapping("/setSideAble")
    AjaxResult setSideAble(@RequestBody TbWxUser tbWxUser);

    @PostMapping("/getPermission")
    AjaxResult getPermission(@RequestBody TbWxUser tbWxUser);

    @PutMapping("/updateUserWelcomeContact")
    AjaxResult updateUserWelcomeContact(@RequestBody WxUserBaseInfoUpdateDto params);
    @GetMapping("/userWelcomeContact/{userId}")
    AjaxResult getUserWelcomeContact(@PathVariable("userId") String userId);
}
