package com.cenker.scrm.client.wechat.feign;


import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.message.*;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.request.ListRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.message.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/tp/massMessage")
public interface TbWxMassMessageFeign {

    @PostMapping("/add")
    AjaxResult add(@RequestBody MassMessageChainWebDTO massMessageChainDTO);

    @PostMapping("/selectMassMessageList")
    TableDataInfo<MassMessageListVO> selectMassMessageList(@RequestBody ListRequest listRequest);

    @PostMapping("/sendUserDetailList")
    TableDataInfo sendUserDetailList(@RequestBody ListRequest listRequest);

    @PostMapping("/sender/detail")
    TableDataInfo listSenderDetail(@RequestBody MassMessageSenderDetailDto req);

    @PostMapping("/customer/detail")
    TableDataInfo listCustomerDetail(@RequestBody MassMessageCustomerDetailDto req);

    @PostMapping("/remind")
    AjaxResult remind(@RequestBody ListRequest listRequest);

    @PostMapping("/getMessagePredictedNum")
    AjaxResult getMessagePredictedNum(@RequestBody MassMessageChainWebDTO massMessageChainDTO);

    @PostMapping("/getInfo")
    Result<MassMessageDetailVO> getInfo(@RequestBody ListRequest listRequest);

    @PostMapping("/getDataStatistics")
    AjaxResult getDataStatistics(@RequestBody ListRequest listRequest);

    @PostMapping("/exportSendUserDetailList")
    List<MassMessageExportVo> exportSendUserDetailList(@RequestBody ListRequest listRequest);

    @PostMapping("/synchronizeMassMessage")
    AjaxResult synchronizeMassMessage(@RequestBody ListRequest listRequest);

    @PostMapping("/cancelMassMessageTask")
    AjaxResult cancelMassMessageTask(@RequestBody ListRequest listRequest);

    @PostMapping("/synchronizeMassMessageList")
    AjaxResult synchronizeMassMessageList(@RequestBody TbWxCorpConfig tbWxCorpConfig);

    @PostMapping("/getTaskResult")
    AjaxResult getMassMessageTaskResult(@RequestBody QueryMassMessageVo queryMassMessageVo);

    @GetMapping("/getInfoGroup")
    Result<MassMessageContentVO> getInfoGroup(String infoId);
    @GetMapping("/listGroup")
    TableDataInfo<MassMessageInfoGroupVO> listGroup(@RequestBody QueryMassMessageDTO dto);

    @GetMapping("/getDataStatisticsGroup")
    AjaxResult getDataStatisticsGroup(ListRequest listRequest);

    @GetMapping("/sendUserDetailListGroup")
    TableDataInfo sendUserDetailListGroup(@RequestBody ListRequest listRequest);

    @RequestMapping("/sendGroupDetailList")
    TableDataInfo sendGroupDetailList(@RequestBody ListRequest listRequest);
    @RequestMapping("/exportSendGroupDetailList")
    List<MassMessageSenderListVO> exportSendGroupDetailList(@RequestBody ListRequest listRequest);
    @RequestMapping("/exportSendUserDetailListGroup")
    List<MassMessageGroupSenderListVO> exportSendUserDetailListGroup(@RequestBody ListRequest listRequest);

    /**
     * 1v1群发根据条件查询发送用户
     * @param qrySendMsgCustUserDTO
     * @return
     */
    @PostMapping("/qrySendMsgCustUserList")
    public TableDataInfo qrySendMsgCustUserList(@RequestBody QryListSendMsgCustUserDTO qrySendMsgCustUserDTO);
    /**
     * 审批
     */
    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO);

    /**
     * 撤回
     */
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO);

}
