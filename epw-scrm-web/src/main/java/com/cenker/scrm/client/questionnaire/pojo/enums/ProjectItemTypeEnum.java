package com.cenker.scrm.client.questionnaire.pojo.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description : 表单项类型枚举
 * @create : 2020-11-19 10:51
 **/
@AllArgsConstructor
@Getter
public enum ProjectItemTypeEnum {

    INPUT("INPUT", "单行文本"),
    TEXTAREA("TEXTAREA", "多行文本"),
    PASSWORD_INPUT("PASSWORD_INPUT", "密码输入"),
    NUMBER_INPUT("NUMBER_INPUT", "数字输入"),
    SELECT("SELECT", "下拉框"),
    CASCADER("CASCADER", "级联选择"),
    RADIO("RADIO", "单选框"),
    CHECKBOX("CHECKBOX", "多选框"),
    SWITCH("SWITCH", "开关"),
    SLIDER("SLIDER", "滑块"),
    TIME("TIME", "时间选择"),
    TIME_RANGE("TIME_RANGE", "时间范围"),
    DATE("DATE", "日期选择"),
    DATE_RANGE("DATE_RANGE", "日期范围"),
    RATE("RATE", "评分"),
    COLOR("COLOR", "颜色"),
    UPLOAD("UPLOAD", "文件上传组件"),
    IMAGE_UPLOAD("IMAGE_UPLOAD", "图片上传组件"),
    IMAGE("IMAGE", "图片展示"),
    IMAGE_SELECT("IMAGE_SELECT", "图片选择"),
    IMAGE_CAROUSEL("IMAGE_CAROUSEL", "图片轮播"),
    DESC_TEXT("DESC_TEXT", "文字描述"),
    SIGN_PAD("SIGN_PAD", "手写签名"),
    PAGINATION("PAGINATION", "分页"),
    DIVIDER("DIVIDER", "分割线"),
    PROVINCE_CITY("PROVINCE_CITY", "省市联动"),
    PHONE_VERIFICATION("PHONE_VERIFICATION", "手机号验证"),
    INPUT_MAP("INPUT_MAP", "地理位置");

    @JsonValue
    private String value;

    private String desc;

    /**
     * 枚举入参注解
     *
     * @param value
     * @return
     */
    @JsonCreator
    public static ProjectItemTypeEnum getByValue(String value) {
        for (ProjectItemTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }


}
