package com.cenker.scrm.client.subscr;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/bu/section/radar")
public interface BuSectionRadarFeign  {
    @GetMapping("/list")
    TableDataInfo<BuSectionRadarVO> list(BuSectionRadarQuery query);

    @PostMapping("/add")
    Result<Void> add(@RequestBody @Validated(InsertGroup.class) BuSectionRadarVO detail);

    @DeleteMapping("/remove")
    Result<Void> remove(@RequestParam("id") String id);

//    @GetMapping("/detail")
//    Result<BuSubscriptionSectionDetail> detail(@RequestParam("id") String id);

    @PutMapping("/update")
    Result<Void> update(@RequestBody BuSectionRadarVO detail);

    @PostMapping("/uploadRadar")
    Result<Void> uploadRadar(@RequestBody BuSectionRadarVO detail);
}
