package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.AddHotWordDto;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 热词信息Controller
 * <AUTHOR>
 * @date 2024-03-15
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/session/hotword")
public interface SessionHotWordFeign
{
    /**
     * 查询热词信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody CkSessionHotWordInfo ckSessionHotWordInfo);

    /**
     * 获取热词信息详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long hotId);

    /**
     * 新增热词信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody AddHotWordDto ckSessionHotWordInfoDto);

    @PostMapping("import")
    public AjaxResult importExcel(@RequestBody List<AddHotWordDto> wordList);
    /**
     * 修改热词信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody AddHotWordDto ckSessionHotWordInfoDto);

    /**
     * 删除热词信息
     */
	@DeleteMapping
    public AjaxResult remove(String hotIds);

    @PostMapping("/cntHotWordRep")
    public Long cntHotWordRep(@RequestBody CkSessionHotWordInfo ckSessionSensRuleInfo);

    }
