package com.cenker.scrm.client.wechat.feign;


import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.external.CustomerSearchDTO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.external.CustomerExportVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/api/wx/tp/customer")
public interface TbWxExtCustomerFeign {

    /**
     * 查询企业微信外部客户信息列表
     */
    @RequestMapping("/list")
    TableDataInfo list(@RequestBody CustomerChurnDTO dto);

    /**
     * 导出企业微信外部客户信息列表
     */
    @RequestMapping("/export")
    List<CustomerExportVo> export(@RequestBody CustomerChurnDTO dto);

    /**
     * 获取企业微信外部客户信息详细信息
     */
    @RequestMapping("/getInfo")
    AjaxResult getInfo(@RequestBody CustomerChurnDTO customerChurnDTO);


    @RequestMapping("/synCustomer")
    AjaxResult synCustomer(@RequestBody CustomerChurnDTO dto);

    /**
     * 提醒成员给前一天新增客户打标签
     */
    @RequestMapping("/remindTags")
    void remindTags(@RequestBody CustomerChurnDTO dto);

    @RequestMapping("/getAssociationInfo")
    TableDataInfo getAssociationInfo(@RequestBody CustomerChurnDTO customerChurnDTO);

    @RequestMapping("/getExtCustomerList")
    TableDataInfo getExtCustomerList(@RequestBody CustomerChurnDTO dto);

    @RequestMapping("/getAddWayList")
    AjaxResult getAddWayList(@RequestBody CustomerChurnDTO dto);

    @RequestMapping("/getStaffLog")
    TableDataInfo getStaffLog(CustomerChurnDTO dto);

    /**
     * 客户属性查询-添加标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/setLabel")
    AjaxResult setLabel(@RequestBody CustomerChurnDTO dto);

    /**
     * 客户列表 - 自定义搜索
     */
    @PostMapping("/getExtCustomerListByCustom")
    TableDataInfo getExtCustomerListByCustom(@RequestBody CustomerChurnDTO dto);

    /**
     * 客户属性查询-移除标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/delLabel")
    AjaxResult delLabel(@RequestBody CustomerChurnDTO dto);

    /**
     * 自定义查询-添加标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/custom/setLabel")
    AjaxResult setLabelByCustom(@RequestBody CustomerChurnDTO dto);
    /**
     * 自定义查询-移除标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/custom/delLabel")
    AjaxResult delLabelByCustom(@RequestBody CustomerChurnDTO dto);

    /**
     * 客户列表-自定义筛选条件-列表
     * @return
     */
    @GetMapping(value = "/custom/search")
    AjaxResult searchByCustom(@RequestParam("name") String name);

    /**
     * 客户列表-自定义筛选条件-新增
     * @return
     */
    @PostMapping(value = "/custom/addSearch")
    AjaxResult addSearchByCustom(@RequestBody CustomerSearchDTO dto);

    /**
     * 客户列表-自定义筛选条件-编辑
     * @return
     */
    @PutMapping(value = "/custom/editSearch")
    AjaxResult editSearchByCustom(@RequestBody CustomerSearchDTO dto);

    /**
     * 客户列表-自定义筛选条件-删除
     * @return
     */
    @DeleteMapping("/custom/delSearch/{id}")
    AjaxResult delSearchByCustom(@PathVariable("id") Long id);
}
