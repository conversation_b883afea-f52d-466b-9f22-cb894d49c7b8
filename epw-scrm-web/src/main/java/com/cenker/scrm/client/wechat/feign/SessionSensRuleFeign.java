package com.cenker.scrm.client.wechat.feign;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.AddHotWordDto;
import com.cenker.scrm.pojo.dto.session.AddSensRuleDto;
import com.cenker.scrm.pojo.dto.session.QrySensRuleDto;
import com.cenker.scrm.pojo.dto.session.UpdSensRuleStatusDto;
import com.cenker.scrm.pojo.entity.session.CkSessionSensRuleInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 敏感规则信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/session/sensitive")
public interface SessionSensRuleFeign
{

    @GetMapping("/list")
    public TableDataInfo list(QrySensRuleDto qrySensRuleDto);
    /**
     * 获取敏感规则信息详细信息
     */
    @GetMapping(value = "getInfo")
    public AjaxResult getInfo(Long ruleId);
    /**
     * 新增敏感规则信息
     */
    //@Log(title = "新增敏感规则信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AddSensRuleDto addSensRuleDto);
    /**
     * 修改敏感规则信息
     */
    //@Log(title = "修改敏感规则信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AddSensRuleDto addSensRuleDto);

    //@Log(title = "启用停用敏感规则信息", businessType = BusinessType.INSERT)
    @PostMapping("/updStatus")
    public AjaxResult updStatus(@RequestBody UpdSensRuleStatusDto updSensRuleStatusDto);
    @PostMapping("import")
    public AjaxResult importExcel(@RequestBody List<AddSensRuleDto> ruleList);

    @PostMapping("/cntRuleNameRep")
    public Long cntRuleNameRep(@RequestBody CkSessionSensRuleInfo ckSessionSensRuleInfo);
}
