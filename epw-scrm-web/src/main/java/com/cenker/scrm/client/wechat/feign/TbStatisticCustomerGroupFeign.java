package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomerGroup;
import com.cenker.scrm.pojo.request.statistic.StatisticCustomerGroupListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 数据统计-客户数据 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/statistic/customerGroup")
public interface TbStatisticCustomerGroupFeign {

    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(@RequestBody StatisticSummaryQuery query);

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(@RequestBody StatisticGraphQuery query);

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody StatisticCustomerGroupListQuery query);

    /**
     * 导出
     */
    @GetMapping("/export")
    public List<TbStatisticCustomerGroup> export(@RequestBody StatisticCustomerGroupListQuery query);

    @PostMapping("/synData")
    Result synData(@RequestBody StatisticSummaryQuery query);
}
