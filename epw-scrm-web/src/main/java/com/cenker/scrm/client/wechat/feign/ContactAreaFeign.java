package com.cenker.scrm.client.wechat.feign;


import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.request.ContactAreaRequest;
import com.cenker.scrm.pojo.request.SiteRequest;
import com.cenker.scrm.pojo.request.StoreImportRequest;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/contact/area")
public interface ContactAreaFeign {

    /**
     * 获取全国省市区
     * @param contactAreaRequest 参数
     * @return 全国省市区
     */
    @RequestMapping("/treeAllAreaSelect")
    AjaxResult treeAllAreaSelect(@RequestBody ContactAreaRequest contactAreaRequest);

    /**
     * 新增站点
     * @param siteRequest 参数
     * @return 结果
     */
    @RequestMapping("/addSite")
    AjaxResult addSite(@RequestBody SiteRequest siteRequest);

    /**
     * 站点列表
     * @param siteRequest
     * @return 站点列表
     */
    @RequestMapping("/siteList")
    TableDataInfo siteList(@RequestBody SiteRequest siteRequest);

    /**
     * 站点名列表
     * @param siteRequest
     * @return 站点名列表
     */
    @RequestMapping("/allSiteNameList")
    TableDataInfo allSiteNameList(@RequestBody SiteRequest siteRequest);

    /**
     * 获取站点活码信息
     * @param siteRequest
     * @return
     */
    @RequestMapping("/getInfo")
    AjaxResult getInfo(@RequestBody SiteRequest siteRequest);

    /**
     * 数据统计
     * @param siteRequest
     * @return
     */
    @RequestMapping("/getDataStatistics")
    AjaxResult getDataStatistics(@RequestBody SiteRequest siteRequest);
    @RequestMapping("/getAddCustomerInfo")
    AjaxResult getAddCustomerInfo(@RequestBody SiteRequest siteRequest);
    @RequestMapping("/getBehaviorData")
    AjaxResult getBehaviorData(@RequestBody StatisticQuery statisticQuery);


    /**
     * 删除站点
     * @param siteRequest
     * @return
     */
    @RequestMapping("/remove")
    AjaxResult remove(@RequestBody SiteRequest siteRequest);

    /**
     * 新增门店
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/addStore")
    AjaxResult addStore(@RequestBody ContactAreaRequest contactAreaRequest);
    /**
     * 新增门店
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/storeList")
    TableDataInfo storeList(@RequestBody ContactAreaRequest contactAreaRequest);
    /**
     * 修改门店
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/updateStore")
    AjaxResult updateStore(@RequestBody ContactAreaRequest contactAreaRequest);
    /**
     * 删除门店
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/removeStore")
    AjaxResult removeStore(@RequestBody ContactAreaRequest contactAreaRequest);

    // 以下
    @RequestMapping("/treeSelect")
    AjaxResult treeSelect(@RequestBody ContactAreaRequest contactAreaRequest);

    @RequestMapping("/update")
    AjaxResult update(@RequestBody ContactAreaRequest contactAreaRequest);

    @RequestMapping("/treeAreaList")
    AjaxResult treeAreaList(@RequestBody ContactAreaRequest contactAreaRequest);

    @RequestMapping("/getStoreById")
    AjaxResult getStoreById(@RequestBody ContactAreaRequest contactAreaRequest);

    @RequestMapping("/searchStore")
    TableDataInfo selectStoreByTree(@RequestBody ContactAreaRequest contactAreaRequest);

    /**
     * 门店导入
     * @param importExcel 导入数据
     * @return 导入结果
     */
    @RequestMapping("/storeImport")
    AjaxResult storeImport(@RequestBody List<StoreImportRequest> importExcel);

    @RequestMapping("/treeSelectAll")
    AjaxResult treeSelectAll();

    @RequestMapping("/updateInfo")
    AjaxResult updateInfo(@RequestBody SiteRequest siteRequest);

    /**
     * 站点列表 格式：全国、...
     * @param siteRequest
     * @return
     */
    @RequestMapping("/allSiteList")
    TableDataInfo allSiteList(@RequestBody SiteRequest siteRequest);

    /**
     * 站点搜索-指定格式（角色-可见范围）：广东省-深圳市-零一站
     * @param siteRequest
     * @return
     */
    @RequestMapping("/searchSite")
    TableDataInfo selectSiteByTree(@RequestBody SiteRequest siteRequest);
}
