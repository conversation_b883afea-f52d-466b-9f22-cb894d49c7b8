package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxOpenQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/wx_open")
public interface MpWxAppFeign {
    @RequestMapping("/list")
    TableDataInfo list(@RequestBody WxOpenQuery wxOpenQuery);
    @RequestMapping("/getAuthorityLink")
    AjaxResult getAuthorityLink(@RequestBody WxOpenQuery wxOpenQuery);
    @RequestMapping("/getShortLink")
    AjaxResult getShortLink();
    @RequestMapping("/selectVerifyFileNameById")
    String selectVerifyFileNameById(@RequestParam("id") String id);
    @RequestMapping("/setWxAppByWxAppId")
    AjaxResult setWxAppByWxAppId(@RequestBody WxOpenQuery wxOpenQuery);
    @RequestMapping("/getTestShortLink")
    AjaxResult getTestShortLink(@RequestBody WxOpenQuery wxOpenQuery);
    @RequestMapping("/verityMiniProgramInstall")
    AjaxResult verityMiniProgramInstall(@RequestBody WxOpenQuery wxOpenQuery);
    @RequestMapping("/selectPagePathById")
    String selectPagePathById(@RequestParam("id") String id);
}
