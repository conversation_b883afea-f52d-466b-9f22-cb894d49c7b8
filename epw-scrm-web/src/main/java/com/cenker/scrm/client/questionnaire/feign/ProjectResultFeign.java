package com.cenker.scrm.client.questionnaire.feign;


import com.cenker.scrm.client.questionnaire.pojo.QueryProjectResultRequest;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;


@FeignClient(value = ServiceNameConstants.QUESTIONNAIRE_SERVICE,path = "/user/project/result")
public interface ProjectResultFeign {


    /***
     * 查看项目
     *  记录查看的IP 统计查看用户数
     * @return
     */
    @PostMapping("view/{projectKey}")
    AjaxResult viewProject(HttpServletRequest request, @PathVariable("projectKey") String projectKey);


    /**
     * 查询调查结果
     *
     * @param request
     * @return
     */
    @PostMapping("/result")
    AjaxResult queryProjectResult(@RequestBody QueryProjectResultRequest request);

    /**
     * 填写附件导出
     *
     * @param request
     * @return
     */
    @GetMapping("/download/file")
    AjaxResult downloadProjectResultFile(QueryProjectResultRequest request);


    /**
     * 结果分页
     *
     * @param request
     * @return
     */
    @GetMapping("/page")
    AjaxResult queryProjectResults(QueryProjectResultRequest request);


    /**
     * 查询公开结果
     *
     * @param request
     * @return
     */
    @GetMapping("/public/page")
    AjaxResult queryProjectPublicResults(QueryProjectResultRequest request);



    /**
     * 查询数据反馈结果
     *
     * @param
     * @return
     */
    @GetMapping("/analysis")
    AjaxResult queryProjectAnalysisResult(String projectKey);
}
