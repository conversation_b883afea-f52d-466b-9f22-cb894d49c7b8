package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysJobLog;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/monitor/jobLog")
public interface SysJobLogFeign {

    @RequestMapping("/list")
    TableDataInfo list(SysJobLog sysJobLog);

    @RequestMapping("/export")
    AjaxResult export(SysJobLog sysJobLog);

    @RequestMapping(value = "/{jobLogId}")
    AjaxResult getInfo(@PathVariable("jobLogId") Long jobLogId);

    @RequestMapping("/{jobLogIds}")
    AjaxResult remove(@PathVariable("jobLogIds") Long[] jobLogIds);

    @RequestMapping("/remove/clean")
    AjaxResult clean();
}
