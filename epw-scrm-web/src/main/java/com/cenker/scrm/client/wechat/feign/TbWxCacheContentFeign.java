package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.dto.cachecontent.CacheContentDTO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 调用内容缓存接口
 * @date 2023/5/25 11:51
 */
@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/tp/cachecontent")
public interface TbWxCacheContentFeign {
    /**
     * 查询内容缓存列表
     *
     * @param type
     * @return
     */
    @RequestMapping("/list/{type}")
    AjaxResult list(@PathVariable("type") String type);

    /**
     * 根据id删除指定缓存
     *
     * @param id
     * @return
     */
    @RequestMapping("/deleteById/{id}")
    AjaxResult deleteById(@PathVariable("id") String id);

    /**
     * 根据id修改指定缓存
     *
     * @param id
     * @param cacheContentDTO
     * @return
     */
    @RequestMapping("/updateByCondition/{id}")
    AjaxResult updateByCondition(@PathVariable("id") String id, @RequestBody CacheContentDTO cacheContentDTO);

}
