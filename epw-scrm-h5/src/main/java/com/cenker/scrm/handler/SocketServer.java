package com.cenker.scrm.handler;

import com.alibaba.fastjson.JSON;
import com.cenker.scrm.client.wechat.feign.TbWxRadarFeign;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.Client;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContentRecord;
import com.cenker.scrm.util.SpringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import io.jsonwebtoken.MalformedJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.EOFException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

@ServerEndpoint(value = "/socketServer/{token}")
@Component
@Slf4j
public class SocketServer {

    /**
     * 用线程安全的CopyOnWriteArraySet来存放客户端连接的信息
     */
    private static CopyOnWriteArraySet<Client> socketServers = new CopyOnWriteArraySet<>();

    /**
     * websocket封装的session,信息推送，就是通过它来信息推送
     */
    private Session session;


    /**
     * 用户连接时触发，我们将其添加到
     * 保存客户端连接信息的socketServers中
     *
     * @param session
     * @param token
     */
    @OnOpen
    public void open(Session session, @PathParam(value = "token") String token) {
        TokenParseUtil tokenService = SpringUtils.getBean("tokenParseUtil");
        H5LoginUser h5LoginUser = tokenService.getH5LoginUser(token);
        if (h5LoginUser == null) {
            throw new MalformedJwtException("");
        }
        MpWxUser mpWxUser = h5LoginUser.getMpWxUser();
        this.session = session;
        socketServers.add(new Client(token, session, null));
        log.info("客户端:【{}】连接成功，当前连接数量：{}", token, socketServers.size());
    }

    /**
     * 收到客户端发送信息时触发
     */
    @OnMessage
    public void onMessage(String message) {
        Client client = socketServers.stream().filter(cli -> cli.getSession() == session)
                .collect(Collectors.toList()).get(0);
        TbWxRadarContentRecord record = JSON.parseObject(message, TbWxRadarContentRecord.class);
        client.setRecord(record);
        log.info("客户端接收消息:【{}】", record);
    }

    /**
     * 连接关闭触发，通过sessionId来移除
     * socketServers中客户端连接信息
     */
    @OnClose
    public void onClose() {
        if (!CollectionUtils.isEmpty(socketServers)) {
            socketServers.forEach(client -> {
                if (client.getSession().getId().equals(session.getId())) {
                    TbWxRadarContentRecord record = client.getRecord();
                    if (record != null) {
                        log.info("客户端:【{}】断开连接,本次浏览结束，开始记录信息", client.getUserName(), record);
                        TbWxRadarFeign tbWxRadarFeign = SpringUtils.getBean(TbWxRadarFeign.class);
                        tbWxRadarFeign.saveReadRecord(record, "Bearer " + client.getUserName());
                    }
                    log.info("客户端:【{}】断开连接", client.getUserName());
                    socketServers.remove(client);
                }
            });
        }
    }

    /**
     * 发生错误时触发
     *
     * @param error
     */
    @OnError
    public void onError(Throwable error) {
        if (error instanceof MalformedJwtException) {
            log.error("【websocket】身份校验失败不允许连接");
            return;
        }
        if (error instanceof EOFException) {
            if (!CollectionUtils.isEmpty(socketServers)) {
                for (Client client : socketServers) {
                    if (client.getSession().getId().equals(session.getId())) {
                        TbWxRadarContentRecord record = client.getRecord();
                        if (record != null) {
                            log.info("客户端:【{}】发生异常,本次浏览结束，开始记录信息", client.getUserName());
                            TbWxRadarFeign tbWxRadarFeign = SpringUtils.getBean(TbWxRadarFeign.class);
                            tbWxRadarFeign.saveReadRecord(record, "Bearer " + client.getUserName());
                        }
                        socketServers.remove(client);
                    }
                }
            }
            return;
        }
        log.error("客户端:【{}】发生异常");
        log.error("错误信息:{}",error);
	/*	socketServers.forEach(client ->{
			if (client.getSession().getId().equals(session.getId())) {
				TbWxRadarContentRecord record = client.getRecord();
				if (record != null) {
					log.info("客户端:【{}】发生异常,本次浏览结束，开始记录信息",client.getUserName());
					TbWxRadarFeign tbWxRadarFeign = SpringUtils.getBean(TbWxRadarFeign.class);
					tbWxRadarFeign.saveReadRecord(record,"Bearer "+client.getUserName());
				}
				log.error("客户端:【{}】发生异常",client.getUserName(),record);
				socketServers.remove(client);
				error.printStackTrace();
			}
		});*/
    }

    /**
     * 获取服务端当前客户端的连接数量，
     * 因为服务端本身也作为客户端接受信息，
     * 所以连接总数还要减去服务端
     * 本身的一个连接数
     * <p>
     * 这里运用三元运算符是因为客户端第一次在加载的时候
     * 客户端本身也没有进行连接，-1 就会出现总数为-1的情况，
     * 这里主要就是为了避免出现连接数为-1的情况
     *
     * @return
     */
    public synchronized static int getOnlineNum() {
        return new ArrayList<>(socketServers).size();
    }

    /**
     * 获取在线用户名，前端界面需要用到
     *
     * @return
     */
    public synchronized static List<String> getOnlineUsers() {
        return socketServers.stream()
                .map(Client::getUserName)
                .collect(Collectors.toList());
    }

}
