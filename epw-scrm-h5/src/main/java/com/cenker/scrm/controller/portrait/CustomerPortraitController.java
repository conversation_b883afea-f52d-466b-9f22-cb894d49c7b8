package com.cenker.scrm.controller.portrait;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.client.subscr.BuSubscriptionFeign;
import com.cenker.scrm.client.wechat.feign.*;
import com.cenker.scrm.client.wechat.pojo.TbWxCustomerTrajectoryDTO;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.CategoryType;
import com.cenker.scrm.handler.security.SignatureVerificationService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.SignatureVo;
import com.cenker.scrm.pojo.dto.external.CustomerAuthDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.fund.GetAssetDailyVariationDTO;
import com.cenker.scrm.pojo.dto.fund.GetTotalAssetsDTO;
import com.cenker.scrm.pojo.dto.fund.GetTradeRecordDTO;
import com.cenker.scrm.pojo.dto.journey.ExternalJourneyTransferDTO;
import com.cenker.scrm.pojo.dto.open.AppCategoryDTO;
import com.cenker.scrm.pojo.dto.tag.PersonalTagAddDto;
import com.cenker.scrm.pojo.dto.tag.PersonalTagDelDto;
import com.cenker.scrm.pojo.dto.tag.QueryCustomerTagDTO;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.valid.DeleteGroup;
import com.cenker.scrm.pojo.valid.SingleGroup;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.external.CustomerPortraitVo;
import com.cenker.scrm.pojo.vo.external.WxCustomerBindingVO;
import com.cenker.scrm.pojo.vo.subscr.BuCustomerSubscrData;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionData;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionVO;
import com.cenker.scrm.system.SysUserFeign;
import com.cenker.scrm.system.model.UserCategoryVo;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/10/11
 * @Description 客户画像
 */
@AllArgsConstructor
@RestController
@RequestMapping("/customer/portrait")
public class CustomerPortraitController {

    private static final Logger log = LoggerFactory.getLogger(CustomerPortraitController.class);
    private TokenParseUtil tokenService;
    private CustomerPortraitFeign portraitFeign;
    private TbWxExtJourneyFeign tbWxExtJourneyFeign;
    private UserTagsFeign userTagsFeign;
    private SignatureVerificationService signatureVerificationService;
    private PersonTagFeign personTagFeign;
    private SysUserFeign sysUserFeign;
    private TbWxMaterialFeign tbWxMaterialFeign;
    private final BuSubscriptionFeign buSubscriptionFeign;
    /**
     * 查询客户订阅列表，用于客户画像
     * @param externalUserId
     * @return
     */
    @GetMapping("/getSubscrList")
    public Result<Map<String, List<BuSubscriptionVO>>> getSubscrList(@RequestParam("externalUserId") String externalUserId) {
        return buSubscriptionFeign.list(externalUserId);
    }

    /**
     * 查询最新订阅菜单信息，用于客户画像
     * @param externalUserId
     * @return
     */
    @GetMapping("/getSubscrMenus")
    public Result<BuSubscriptionData> getSubscrMenus(@RequestParam(value = "externalUserId") String externalUserId) {
        return buSubscriptionFeign.getSubscrMenus(externalUserId);
    }

    /**
     * 保存客户订阅信息，用于客户画像
     * @return
     */
    @PostMapping("/saveSubscrMenus")
    public Result saveSubscrMenus(@RequestBody BuCustomerSubscrData buCustomerSubscrData) {
        buCustomerSubscrData.setFromWechat(false);
        return buSubscriptionFeign.saveSubscrMenus(buCustomerSubscrData);
    }


    @GetMapping("/findWeCustomerInfo")
    public AjaxResult findWeCustomerInfo(String externalUserId) {
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = getTbWxCustomerTrajectoryDTO(externalUserId);
        return portraitFeign.findWeCustomerInfo(tbWxCustomerTrajectory);
    }

    private TbWxCustomerTrajectoryDTO getTbWxCustomerTrajectoryDTO(String externalUserId) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = new TbWxCustomerTrajectoryDTO();
        tbWxCustomerTrajectory.setExternalUserId(externalUserId);
        tbWxCustomerTrajectory.setCorpId(mobileUser.getCorpId());
        tbWxCustomerTrajectory.setUserId(mobileUser.getUserId());
        return tbWxCustomerTrajectory;
    }

    /**
     * 获取当前系统所有可用标签
     *
     * @return
     */
    @GetMapping(value = "/findAllTags")
    public AjaxResult findAllTags() {
        TbWxCorpTagGroup tbWxCorpTagGroup = new TbWxCorpTagGroup();

        H5LoginUser loginUser = tokenService.getWorkLoginUser(ServletUtils.getRequest());
        tbWxCorpTagGroup.setCorpId(loginUser.getTenantId());

        UserCategoryVo userCategoryVo;
        RemoteResult<UserCategoryVo> userCategoryResult = sysUserFeign.getUserPermissionCategory(loginUser.getUserId(), CategoryType.CUSTOMER_TAG.name());
        if (userCategoryResult.isSuccess() && userCategoryResult.getData()!= null) {
            userCategoryVo = userCategoryResult.getData();
        } else {
            userCategoryVo = new UserCategoryVo();
            userCategoryVo.setTagCategoryScope(CommonConstants.CUSTOM);
            userCategoryVo.setTagCategoryIds(null);
        }

        // 当查询全部分组时
        if (CommonConstants.ALL.equalsIgnoreCase(userCategoryVo.getTagCategoryScope())) {
            // 若拥有全部标签分组权限，则查询全部标签组
            tbWxCorpTagGroup.setCategoryScope(null);
            tbWxCorpTagGroup.setCategoryIdList(null);
        } else if (CollectionUtil.isEmpty(userCategoryVo.getTagCategoryIds())){
            // 若没有标签分组权限，则仅能查询未分组的标签
            tbWxCorpTagGroup.setCategoryScope(CommonConstants.NONE);
            tbWxCorpTagGroup.setCategoryIdList(null);
        } else {
            // 若有部分标签分组权限，则查询拥有权限的标签组
            tbWxCorpTagGroup.setCategoryScope(CommonConstants.ALL);
            tbWxCorpTagGroup.setCategoryIdList(userCategoryVo.getTagCategoryIds());
        }

        TableDataInfo result = userTagsFeign.getTagList(tbWxCorpTagGroup);
        return AjaxResult.success(result.getRows());

        /*String corpId = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId();
        return portraitFeign.findAllTags(corpId);*/
    }

    /**
     * 废弃
     */
    @GetMapping(value = "/findAddGroupNum")
    public AjaxResult findAddGroupNum(String externalUserId) {
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = getTbWxCustomerTrajectoryDTO(externalUserId);
        return portraitFeign.findAddGroupNum(tbWxCustomerTrajectory);
    }

    @GetMapping("/findTrajectory")
    public TableDataInfo findTrajectory() {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        String userId = mobileUser.getUserId();
        String corpId = mobileUser.getCorpId();
        return portraitFeign.findTrajectory(userId,corpId);
    }

    @GetMapping("/findTrajectory2")
    public AjaxResult findTrajectory2(TbWxCustomerTrajectoryDTO trajectory) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        trajectory.setCorpId(mobileUser.getCorpId());
        trajectory.setUserId(mobileUser.getUserId());
        return portraitFeign.findTrajectory2(trajectory);
    }


    @GetMapping("/findAddEmployers/{externalUserId}")
    public AjaxResult findAddEmployers(@PathVariable("externalUserId") String externalUserId) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        String corpId = mobileUser.getCorpId();
        return portraitFeign.findAddEmployers(externalUserId,corpId);
    }

    @PostMapping("/updateWeCustomerPorTraitTag")
    @RepeatSubmit
    public AjaxResult updateWeCustomerPorTraitTag(@RequestBody WeMakeCustomerTag weMakeCustomerTag) {
        H5LoginUser loginUser = tokenService.getWorkLoginUser(ServletUtils.getRequest());
        weMakeCustomerTag.setCorpId(loginUser.getTenantId());
        weMakeCustomerTag.setUserId(loginUser.getWxUserId());
        weMakeCustomerTag.setPermissionDeptIds(loginUser.getPermissionDeptIds());
        weMakeCustomerTag.setDataScope(loginUser.getDataScope());

        return portraitFeign.updateWeCustomerPorTraitTag(weMakeCustomerTag);
    }

    /**
     * 删除轨迹
     *
     * @param trajectoryId
     * @return
     */
    @DeleteMapping("/removeTrajectory/{trajectoryId}")
    public AjaxResult removeTrajectory(@PathVariable("trajectoryId") String trajectoryId) {
        return portraitFeign.removeTrajectory(trajectoryId);
    }

    /**
     * 添加或编辑轨迹(待办)
     *
     * @param trajectory
     * @return
     */
    @PostMapping(value = "/addOrEditWaitHandle")
    public AjaxResult addOrEditWaitHandle(@RequestBody TbWxCustomerTrajectoryDTO trajectory) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        if (StringUtils.isNotEmpty(trajectory.getStartTime())) {
            trajectory.setStartTime(trajectory.getStartTime() + ":00");
            trajectory.setEndTime(trajectory.getEndTime() + ":00");
        }
        trajectory.setCorpId(mobileUser.getCorpId());
        trajectory.setUserId(mobileUser.getUserId());
        return portraitFeign.addOrEditWaitHandle(trajectory);
    }

    /**
     * 完成待办
     *
     * @param trajectoryId
     * @return
     */
    @DeleteMapping(value = "/handleWait/{trajectoryId}")
    public AjaxResult handleWait(@PathVariable("trajectoryId") String trajectoryId) {
        return portraitFeign.handleWait(trajectoryId);
    }

    /**
     * 客户画像资料更新
     *
     * @param weCustomerPortrait
     * @return
     */
    @PostMapping(value = "/updateWeCustomerInfo")
    public AjaxResult updateWeCustomerInfo(@RequestBody @Valid CustomerPortraitVo weCustomerPortrait) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        weCustomerPortrait.setCorpId(mobileUser.getCorpId());
        weCustomerPortrait.setUserId(mobileUser.getUserId());
        return portraitFeign.updateWeCustomerInfo(weCustomerPortrait);
    }

    /**
     * 添加跟进
     */
    @PostMapping("/addFollowTrajectory")
    @RepeatSubmit
    public AjaxResult addFollowTrajectory(@RequestBody @Valid TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        tbWxCustomerTrajectory.setCorpId(mobileUser.getCorpId());
        tbWxCustomerTrajectory.setUserId(mobileUser.getUserId());
        return portraitFeign.addFollowTrajectory(tbWxCustomerTrajectory);
    }

    /**
     * 删除跟进
     */
    @DeleteMapping("/removeFollowTrajectory")
    @RepeatSubmit
    public AjaxResult removeFollowTrajectory(@RequestParam("id") String id) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = new TbWxCustomerTrajectoryDTO();
        tbWxCustomerTrajectory.setId(id);
        tbWxCustomerTrajectory.setUserId(mobileUser.getUserId());
        return portraitFeign.removeFollowTrajectory(tbWxCustomerTrajectory);
    }

    /**
     * 客户画像3.0
     * 获取首页客户信息
     */
    @GetMapping("/getCustomerInfoById")
    public AjaxResult getCustomerInfoById(String externalUserId) {
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = getTbWxCustomerTrajectoryDTO(externalUserId);
        return portraitFeign.getCustomerInfoById(tbWxCustomerTrajectory);
    }

    /**
     * 获取客户标签
     */
    @GetMapping("/getCustomerTagByCustomerId")
    public AjaxResult getCustomerTagByCustomerId(QueryCustomerTagDTO dto) {
        dto.setShowTag(true);
        return userTagsFeign.getCustomerTagByCustomerId(dto);
    }

    /**
     * 客户画像3.0.1 2022.12.14 补充标签接口 修改标签为聚合展示（统计所有员工打标签）
     */
    @GetMapping("/getCustomerTag")
    public AjaxResult getCustomerTag(String externalUserId){
        TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory = getTbWxCustomerTrajectoryDTO(externalUserId);
        return portraitFeign.getCustomerTag(tbWxCustomerTrajectory);
    }


    @GetMapping(value = "/getAssociationInfo")
    public TableDataInfo getAssociationInfo(CustomerChurnDTO customerChurnDTO) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        customerChurnDTO.setCorpUserId(mobileUser.getUserId());
        customerChurnDTO.setCorpId(mobileUser.getCorpId());
        customerChurnDTO.setLoginType(mobileUser.getLoginType() == null ? 0 : mobileUser.getLoginType());
        return portraitFeign.getAssociationInfo(customerChurnDTO);
    }

    /**
     * 企业互动
     */
    @GetMapping("/getCorpInteract")
    public AjaxResult getCorpInteract(CustomerChurnDTO dto){
        dto.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        return portraitFeign.getCorpInteract(dto);
    }

    /**
     * 查询员工行为日志
     */
    @GetMapping("/getStaffLog")
    public TableDataInfo getStaffLogTrajectory(CustomerChurnDTO dto) {
        dto.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        return portraitFeign.getStaffLogTrajectory(dto);
    }

    /**
     * RFM 消费信息
     */
    @GetMapping("/getConsumeRFM")
    public AjaxResult getConsumeRFM(AppCategoryDTO appCategoryDTO){
        Optional.ofNullable(appCategoryDTO.getExtUserId()).orElseThrow(() -> new ParameterException("缺失客户id参数"));
        appCategoryDTO.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpConfigId());
        return portraitFeign.getConsumeRFM(appCategoryDTO);
    }

    /**
     * 用户订单信息
     */
    @GetMapping("/getOrderInfoList")
    public TableDataInfo getOrderInfoList(@Valid AppCategoryDTO appCategoryDTO){
        Optional.ofNullable(appCategoryDTO.getExtUserId()).orElseThrow(() -> new ParameterException("缺失客户id参数"));
        appCategoryDTO.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpConfigId());
        return portraitFeign.getOrderInfoList(appCategoryDTO);
    }

    /**
     * 获取客户客户旅程信息
     */
    @GetMapping("/getJourneyStages")
    public AjaxResult getJourneyStages(@Valid CustomerChurnDTO dto){
        Long corpConfigId = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpConfigId();
        dto.setCorpId(String.valueOf(corpConfigId));
        dto.setWeb(false);
        return tbWxExtJourneyFeign.getJourneyStages(dto);
    }

    /**
     * 设置客户到阶段 有初始阶段
     */
    @PostMapping("/setStageSingle")
    public AjaxResult setStageSingle(@Validated(SingleGroup.class) @RequestBody ExternalJourneyTransferDTO journeyTransferDTO){
        return setStage(journeyTransferDTO);
    }

    /**
     * 移除客户阶段
     */
    @PostMapping("/setStageOut")
    public AjaxResult setStageOut(@Validated(DeleteGroup.class) @RequestBody ExternalJourneyTransferDTO journeyTransferDTO){
        return setStage(journeyTransferDTO);
    }

    @GetMapping("/getWxUserInfo")
    public AjaxResult getUserInfo(@RequestParam @NotBlank String code) {
       return portraitFeign.getWxUserByCode(new CustomerAuthDTO(null,null,code,null));
    }

    /**
     * 绑定ESB账号
     * @return
     */
    @PostMapping("/auth/callback")
    public AjaxResult bindFromESB(@RequestBody String body, HttpServletRequest request) {
        try {
            CustomerAuthDTO dto =new ObjectMapper().readValue(body, CustomerAuthDTO.class);
            if (signatureVerificationService.verifyRequestSignature(request,body)) {
                return portraitFeign.bindingByEsb(dto);
            } else {
                return AjaxResult.error("非法请求");
            }
        } catch (Exception e) {
            return AjaxResult.error("请求处理异常：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否已绑定ESB，并更新 SCRM 客户认证状态
     *
     * @param unionId 微信用户的唯一标识
     * @return Result 包含绑定状态的响应结果
     *         - true: 已绑定
     *         - false: 未绑定
     */
    @GetMapping("/checkEsbBindingByUnionId")
    public Result<WxCustomerBindingVO> checkEsbBindingByUnionId(@RequestParam @NotBlank String unionId) {
        try {
            return portraitFeign.checkEsbBindingByUnionId(new CustomerAuthDTO(unionId, null, null, null));
        } catch (Exception e) {
            log.error("检查ESB绑定状态失败, unionId: {}", unionId, e);
            return Result.error(500,"检查绑定状态失败");
        }
    }

    @PostMapping("/access/auth/callback")
    public AjaxResult accessAuthCallBack(@RequestBody String body, HttpServletRequest request) {
        try {
            CustomerAuthDTO dto =new ObjectMapper().readValue(body, CustomerAuthDTO.class);
            if (signatureVerificationService.verifyRequestSignature(request,body)) {
                return portraitFeign.accessAuthCallBack(dto);
            } else {
                return AjaxResult.error("非法请求");
            }
        } catch (Exception e) {
            return AjaxResult.error("请求处理异常：" + e.getMessage());
        }
    }


    /**
     * 根据code查询绑定状态
     * @param unionId
     * @return
     */
    @GetMapping("/checkBindStatusByUnionId")
    public AjaxResult checkBindStatusByCode(@RequestParam @NotBlank String unionId) {
        return portraitFeign.checkEsbBinding(new CustomerAuthDTO(unionId,null,null,null));
    }

    /**
     * 获取签名
     * @param code
     * @param redirect_uri
     * @return
     */
    @GetMapping("/access/auth/signature")
    public AjaxResult getSignatureVo(@RequestParam @NotBlank String code,
                                     @RequestParam @NotBlank String redirect_uri) {
        try{
            Object data = portraitFeign.getWxUserByCode(new CustomerAuthDTO(null,null,code,null)).get("data");
            if(data==null) {
                return AjaxResult.error("获取微信用户信息失败");
            }
            String dataJson = JSONUtil.toJsonStr(data);
            WxOAuth2AccessToken dto = JSONUtil.toBean(dataJson, WxOAuth2AccessToken.class);
            return AjaxResult.success(new SignatureVo(dto.getUnionId(), redirect_uri));
        }
        catch (Exception ex){
            log.error("获取签名失败",ex);
            return  AjaxResult.error("获取签名失败");
        }
    }

    /**
     * 根据code查询认证状态
     * @param code
     * @return
     */
    @GetMapping("/checkAuthStatusByCode")
    public AjaxResult checkAuthStatusByUnionId(@RequestParam @NotBlank String code) {
        Object data = portraitFeign.getWxUserByCode(new CustomerAuthDTO(null,null,code,null)).get("data");
        if(data==null) {
            return AjaxResult.error("获取微信用户信息失败");
        }
        String dataJson = JSONUtil.toJsonStr(data);
        WxOAuth2AccessToken dto = JSONUtil.toBean(dataJson, WxOAuth2AccessToken.class);
        if(dto == null||dto.getUnionId()==null){
            return AjaxResult.error("获取微信用户Id失败");
        }
        return portraitFeign.checkAuthStatusByUnionId(new CustomerAuthDTO(dto.getUnionId(),null,null,null));
    }

    /**
     * 查看是否授权
     * @param custNo
     * @return
     */
    @GetMapping("/checkAccessAuthStatus")
    public AjaxResult checkAccessAuthStatus(@RequestParam @NotBlank String custNo) {
        return portraitFeign.checkAccessAuthStatus(new CustomerAuthDTO(null,custNo,null,null));
    }


    /**
     * 资产趋势查询
     * @param dto
     * @return
     */
    @RequestMapping("/getAssetDailyVariation")
    public AjaxResult getAssetDailyVariation(GetAssetDailyVariationDTO dto) {
        return portraitFeign.getAssetDailyVariation(dto);
    }


    /**
     * 收益趋势查询
     * @param dto
     * @return
     */
    @RequestMapping("/getListProfitOfTotalAssets")
    public AjaxResult getListProfitOfTotalAssets( GetAssetDailyVariationDTO dto) {
        return portraitFeign.getListProfitOfTotalAssets(dto);
    }

    /**
     * 获取总资产
     */
    @RequestMapping("/getTotalAssets")
    public AjaxResult getTotalAssets(GetTotalAssetsDTO dto) {
        return portraitFeign.getTotalAssets(dto);
    }

    /**
     * 获取总资产
     */
    @RequestMapping("/getLatestTotalAsset")
    public AjaxResult getLatestTotalAsset(GetTotalAssetsDTO dto) {
        return portraitFeign.getLatestTotalAsset(dto);
    }

    /**
     * 获取交易记录
     */
    @RequestMapping("/getTradeRecord")
    public AjaxResult getTradeRecord(GetTradeRecordDTO dto) {
        return portraitFeign.getTradeRecord(dto);
    }

    @GetMapping("/personTag")
    AjaxResult list(@RequestParam("extUserId") String extUserId) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        String userId = mobileUser.getUserId();
        return personTagFeign.list(extUserId, userId);
    }

    @PostMapping("/personTag")
    AjaxResult add(@Valid @RequestBody PersonalTagAddDto personalTagAddDto) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        String userId = mobileUser.getUserId();
        String corpId = mobileUser.getCorpId();

        personalTagAddDto.setUserId(userId);
        personalTagAddDto.setCorpId(corpId);
        return personTagFeign.add(personalTagAddDto);
    }

    @DeleteMapping("/personTag")
    AjaxResult delete(@Valid @RequestBody PersonalTagDelDto personalTagDelDto) {
        return personTagFeign.delete(personalTagDelDto);
    }

    private AjaxResult setStage(ExternalJourneyTransferDTO journeyTransferDTO) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        journeyTransferDTO.setDragTransfer(true);
        journeyTransferDTO.setCorpId(mobileUser.getCorpConfigId());
        journeyTransferDTO.setSource(TypeConstants.CREATE_SOURCE_SINCE_SIDE);
        // 企微企业id
        journeyTransferDTO.setCorpEpwId(mobileUser.getCorpId());
        return tbWxExtJourneyFeign.setStage(journeyTransferDTO);
    }

    /**
     * 根据链接类型获取海报信息
     * @param type
     * @return
     */
    @GetMapping("/posterByH5/{type}")
    public AjaxResult poster(@PathVariable("type") String type) {
        return tbWxMaterialFeign.posterByH5(type);
    }
}
