package com.cenker.scrm.controller.subscr;

import com.bes.enterprise.webtier.servlet4preview.http.HttpServletRequest;
import com.cenker.scrm.client.subscr.BuSectionRadarFeign;
import com.cenker.scrm.client.subscr.BuSubscriptionFeign;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.valid.SelectGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.RadarContactVo;
import com.cenker.scrm.pojo.vo.subscr.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订阅，用于微信端
 * <AUTHOR>
 */
@AllArgsConstructor
@RestController
@RequestMapping("/bu/subscr")
public class BuSubscriptionH5Controller {
    private final BuSubscriptionFeign buSubscriptionFeign;
    private final BuSectionRadarFeign buSectionRadarFeign;
    /**
     * 查询客户订阅列表
     * @return
     */
    @GetMapping("/list")
    public Result<Map<String, List<BuSubscriptionVO>>> list(HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            return Result.error(500, "获取客户信息失败");
        }
        return buSubscriptionFeign.list(externalUserId);
    }

    private String getExternalUserId(HttpServletRequest request) {
        Result<TbWxExtCustomer> customer = buSubscriptionFeign.getCustomer();
        if (customer.isSuccess()) {
            return customer.getData().getExternalUserId();
        }
        return null;
    }

    /**
     * 查询最新订阅菜单信息
     * @return
     */
    @GetMapping("/getSubscrMenus")
    public Result<BuSubscriptionData> getSubscrMenus(HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            return Result.error(500, "获取客户信息失败");
        }
        return buSubscriptionFeign.getSubscrMenus(externalUserId);
    }

    /**
     * 保存客户订阅信息
     * @return
     */
    @PostMapping("/saveSubscrMenus")
    public Result saveSubscrMenus(@RequestBody BuCustomerSubscrData buCustomerSubscrData, HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            return Result.error(500, "获取客户信息失败");
        }
        buCustomerSubscrData.setExternalUserId(externalUserId);
        buCustomerSubscrData.setFromWechat(true);
        return buSubscriptionFeign.saveSubscrMenus(buCustomerSubscrData);
    }

    /**
     * 查询客户订阅栏目列表
     * @return
     */
    @GetMapping("/getSubscrSectionList")
    public TableDataInfo<BuSubscriptionVO> getSubscrSectionList(BuSubscrQuery query, HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            throw new RuntimeException("获取客户信息失败");
        }
        query.setExternalUserId(externalUserId);
        return buSubscriptionFeign.getSubscrSectionList(query);
    }

    /**
     * 获取栏目的物料列表
     * @param query
     * @return
     */
    @GetMapping("/getRadarList")
    public TableDataInfo<BuSectionRadarVO> getRadarList(@Validated(SelectGroup.class) BuSectionRadarQuery query, HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            throw new RuntimeException("获取客户信息失败!");
        }
        query.setExternalUserId(externalUserId);
        query.setDelFlag(StatusConstants.DEL_FLAG_FALSE_INT);
        // 客户订阅详情页中，只查询使用中的物料
        query.setRadarStatus(ApprovalStatusEnum.EXECUTING.getStatus());
        return buSectionRadarFeign.list(query);
    }

    /**
     * 获取订阅升级标识
     * @return
     */
    @GetMapping("/getUpgradeFlag")
    public Result<Boolean> getUpgradeFlag(HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            return Result.error(500, "获取客户信息失败");
        }
        return buSubscriptionFeign.getUpgradeFlag(externalUserId);
    }

    /**
     * 查询客户是否首次订阅
     * 返回客户订阅的栏目数量，若数量为 0 则为首次订阅
     * @return
     */
    @GetMapping("/subscrCount")
    public Result<Integer> subscrCount(HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            return Result.error(500, "获取客户信息失败");
        }
        return buSubscriptionFeign.subscrCount(externalUserId);
    }

    /**
     * 获取客户联系人信息
     * @return
     */
    @GetMapping("/getContactInfo")
    public Result<RadarContactVo> getContactInfo(@RequestParam(value = "staffId", required = false) String staffId, HttpServletRequest request) {
        String externalUserId = getExternalUserId(request);
        if (externalUserId == null) {
            //return Result.error(500, "获取客户信息失败");
        }
        return buSubscriptionFeign.getContactInfo(externalUserId, staffId);
    }
}