package com.cenker.scrm.controller.system;

import com.cenker.scrm.client.system.feign.SysDictDataFeign;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字典数据信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dict/data")
public class SysDictDataController extends BaseController {

    @Autowired
    private SysDictDataFeign sysDictDataFeign;
    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable("dictType") String dictType) {
        return sysDictDataFeign.dictType(dictType);
    }
}
