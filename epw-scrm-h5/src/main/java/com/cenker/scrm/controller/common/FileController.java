package com.cenker.scrm.controller.common;

import cn.hutool.core.io.FileUtil;
import com.cenker.scrm.enums.OssStoragePathEnum;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.StringUtils;
import com.cky.common.storage.cloud.OssStorageFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2022/1/18
 * @Description 文件操作
 */
@Slf4j
@RequestMapping("/common/upload")
@RestController
public class FileController {

    /**
     * 允许上传的图片类型
     */
    public static final String[] ALLOW_IMAGE_TYPES = { "jpg", "png", "jpeg" };

    public static final String[] ALLOW_FILE_TYPE = { "pdf" };

    @Resource
    private OssStorageFactory ossStorageFactory;

    @PostMapping("/image")
    public AjaxResult uploadImage(MultipartFile file) {
        log.info("上传图片: {}", file.getOriginalFilename());
        if (!isAllowedImageFile(file)) {
            return AjaxResult.error("上传图片格式不正确，请上传jpg、png、jpeg格式图片");
        }

        String dir = OssStoragePathEnum.COMMON_PATH.getPath();
        return getAjaxResult(file, dir);
    }

    @PostMapping("/pdf")
    public AjaxResult uploadPdf(MultipartFile file) {
        log.info("上传pdf文件: {}", file.getOriginalFilename());
        if (!isAllowedPdfFile(file)) {
            return AjaxResult.error("请上传pdf格式文件");
        }

        String dir = OssStoragePathEnum.COMMON_PATH.getPath();
        return getAjaxResult(file, dir);
    }

    private AjaxResult getAjaxResult(MultipartFile file, String dir) {
        try {
            // 上传文件
            if (file.isEmpty()) {
                log.error("上传文件为空! fileName: {}", file.getOriginalFilename());
                return AjaxResult.error("上传图片异常，请联系管理员");
            }

            String fileUrl = ossStorageFactory.build().upload(file.getInputStream(), file.getOriginalFilename(), dir);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("uploadUrl", fileUrl);
            return ajax;
        } catch (IOException e) {
            log.error("上传文件异常", e);
            return AjaxResult.error("上传图片异常，请联系管理员");
        }
    }



    /**
     * 判断是否是允许上传的图片类型
     *
     * @param file 文件
     * @return true：允许上传，false：不允许上传
     */
    private boolean isAllowedImageFile(MultipartFile file) {
        String extName = FileUtil.extName(file.getOriginalFilename());

        if (StringUtils.isBlank(extName)) {
            return false;
        }

        if (Arrays.asList(ALLOW_IMAGE_TYPES).contains(extName.toLowerCase())) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否pdf类型文件
     * @param file
     * @return
     */
    private boolean isAllowedPdfFile(MultipartFile file) {
        String extName = FileUtil.extName(file.getOriginalFilename());

        if (StringUtils.isBlank(extName)) {
            return false;
        }

        if (Arrays.asList(ALLOW_FILE_TYPE).contains(extName.toLowerCase())) {
            return true;
        }

        return false;
    }

}
