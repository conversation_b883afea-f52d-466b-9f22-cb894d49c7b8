package com.cenker.scrm.controller.chatmaterial;

import com.cenker.scrm.client.system.feign.NamingRuleFeign;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/naming/rule")
public class NamingRuleController extends BaseController {
    private final NamingRuleFeign namingRuleFeign;

    @GetMapping()
    public AjaxResult get(@RequestParam("scope") String scope) {
        return namingRuleFeign.get(scope);
    }

}
