package com.cenker.scrm.controller.system;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.client.system.feign.SysMenuFeign;
import com.cenker.scrm.pojo.entity.enums.SysMenuTerminalEnum;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
public class SysMenuController {

    @Autowired
    private SysMenuFeign sysMenuFeign;
    @GetMapping("/list")
    public AjaxResult list(SysMenu menu) {
        dealDefaultTerminal(menu);
        return sysMenuFeign.list(menu);
    }
    /**
     * 企微端接口，不传belongTerminal则默认终端为企微端
     * @param menu
     */
    private void dealDefaultTerminal(SysMenu menu) {
        // 如果 belongTerminal 为空，则默认 02
        if (menu != null && StrUtil.isEmpty(menu.getBelongTerminal())) {
            menu.setBelongTerminal(SysMenuTerminalEnum.WECOM.getCode());
        }
    }
}
