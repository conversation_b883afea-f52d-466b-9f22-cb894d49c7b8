package com.cenker.scrm.controller.common;

import com.cenker.scrm.client.wechat.feign.TbWxMaterialFeign;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.message.TemporaryMaterialDto;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2021/10/9
 * @Description 微信通用处理
 */
@RestController
@RequestMapping("/wx/common")
public class WxCommonController {
    @Autowired
    private TbWxMaterialFeign tbWxMaterialFeign;
    @Autowired
    private TokenParseUtil tokenService;

    /**
     * 上传微信素材
     */
    @PostMapping("/temporaryMaterialMediaId")
    public AjaxResult temporaryMaterialMediaIdForWeb(@RequestBody TemporaryMaterialDto temporaryMaterialDto){
        temporaryMaterialDto.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        return tbWxMaterialFeign.temporaryMaterialMediaIdForWeb(temporaryMaterialDto);
    }

    /**
     * 当前登录人信息
     * @param request
     * @return
     */
    @GetMapping("/getUserInfo")
    public AjaxResult getUserInfo(HttpServletRequest request){
        MpWxUser mpWxUser = tokenService.getH5LoginUser(request).getMpWxUser();
        MpWxUser mpWxUser1 = new MpWxUser();
        mpWxUser1.setNickName(mpWxUser.getNickName());
        mpWxUser1.setOpenId(mpWxUser.getOpenId());
        mpWxUser1.setHeadImgUrl(mpWxUser.getHeadImgUrl());
        return AjaxResult.success(mpWxUser1);
    }
}
