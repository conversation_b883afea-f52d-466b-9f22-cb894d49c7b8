package com.cenker.scrm.controller.common;

import com.cenker.scrm.client.system.feign.SysConfigFeign;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constant.LoginType;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.TokenParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/sysparam")
public class ConfigController {
    @Autowired
    private SysConfigFeign configFeign;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TokenParseUtil tokenService;
    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey) {
        return configFeign.getConfigKey(configKey);
    }

    /**
     * 根据参数键名查询参数值
     */
    /*@GetMapping(value = "/getRedisConfigKey/{configKey}")
    public AjaxResult getRedisConfigKey(@PathVariable String configKey) {
        Object cacheObject = redisCache.getCacheObject(configKey);
        return AjaxResult.success(cacheObject);
    }*/

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/removeRedisConfigKey/{configKey}")
    public AjaxResult removeRedisConfigKey(@PathVariable String configKey) {
        boolean result = redisCache.deleteObject(configKey);
        return AjaxResult.success(result ? "删除成功" : "删除失败");
    }

    /**
     * 根据参数键名查询参数值
     * type 2 表示企微， 3 表示微信
     */
    @GetMapping(value = "/deleteLoginRedis/{token}/{loginType}")
    public AjaxResult deleteLoginRedis(@PathVariable String token, @PathVariable String loginType) {
        boolean weComLogin = LoginType.WECOM.equals(loginType);
        boolean result = tokenService.delLoginUserToken(token, weComLogin);
        return AjaxResult.success(result ? "删除成功" : "删除失败");
    }

}
