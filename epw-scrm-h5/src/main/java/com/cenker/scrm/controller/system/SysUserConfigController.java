package com.cenker.scrm.controller.system;


import com.cenker.scrm.client.system.feign.SysUserConfigFeign;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.pojo.entity.system.SysUserConfig;
import com.cenker.scrm.pojo.vo.base.NewAjaxResult;
import com.cenker.scrm.util.StringUtils;
import com.google.common.base.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 企微侧边栏开关配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tb/user/config")
public class SysUserConfigController extends BaseController {

    @Autowired
    private SysUserConfigFeign sysUserConfigFeign;

    /**
     * 查询企微侧边栏开关配置
     */
    @GetMapping("/{configKey}")
    public NewAjaxResult getConfig(@PathVariable("configKey") String configKey) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        H5LoginUser loginUser = authentication != null? (H5LoginUser) authentication.getPrincipal() : null;
        if (loginUser == null || loginUser.getMobileUser() == null) {
            return NewAjaxResult.error(403, "获取不到用户！");
        }
        String userName = loginUser.getUserName();
        if (StringUtils.isEmpty(userName)) {
            if (loginUser.getMobileUser() != null && StringUtils.isNotEmpty(loginUser.getMobileUser().getUserId())) {
                userName = loginUser.getMobileUser().getUserId();
            } else if (loginUser.getMpWxUser() != null && StringUtils.isNotEmpty(loginUser.getMpWxUser().getNickName())) {
                userName = loginUser.getMpWxUser().getNickName();
            }
        }
        return sysUserConfigFeign.getConfig(configKey, Long.valueOf(loginUser.getUserId()), userName);
    }
    /**
     * 修改企微侧边栏开关配置
     */
    @PostMapping("/{configKey}")
    public NewAjaxResult editConfig(@PathVariable("configKey") String configKey, @RequestBody SysUserConfig config) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        H5LoginUser loginUser = authentication != null? (H5LoginUser) authentication.getPrincipal() : null;
        if (loginUser == null || loginUser.getMobileUser() == null) {
            return NewAjaxResult.error(403, "获取不到用户！");
        }
        config.setConfigKey(configKey);
        config.setUserId(Long.valueOf(loginUser.getUserId()));
        if (Objects.equal(configKey, "sidebarSwitch") && !Objects.equal(config.getConfigValue(), "1")) {
            // sidebarSwitch 的configValue 只能是1或者0，若不是1，则其他值都设置为0
            config.setConfigValue("0");
            System.err.println(1);
        }
        String userName = loginUser.getUserName();
        if (StringUtils.isEmpty(userName)) {
            if (loginUser.getMobileUser() != null && StringUtils.isNotEmpty(loginUser.getMobileUser().getUserId())) {
                userName = loginUser.getMobileUser().getUserId();
            } else if (loginUser.getMpWxUser() != null && StringUtils.isNotEmpty(loginUser.getMpWxUser().getNickName())) {
                userName = loginUser.getMpWxUser().getNickName();
            }
        }
        config.setUpdateBy(userName);
        return sysUserConfigFeign.editConfig(config);
    }
}
