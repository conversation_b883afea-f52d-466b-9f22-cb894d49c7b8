package com.cenker.scrm.controller.workbench;

import com.cenker.scrm.client.wechat.feign.TbWxCategoryFeign;
import com.cenker.scrm.client.wechat.feign.TbWxMaterialFeign;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategory;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/7/7
 * @Description
 */
@RequestMapping("/workMaterial")
@RestController
@RequiredArgsConstructor
public class WorkMaterialController {

    private final TbWxMaterialFeign tbWxMaterialFeign;
    private final TokenParseUtil tokenService;
    private final TbWxCategoryFeign tbWxCategoryFeign;

    @GetMapping("/list")
    public TableDataInfo list(TbWxMaterial tbWxMaterial) {
        tbWxMaterial.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        return tbWxMaterialFeign.list(tbWxMaterial);
    }

    @GetMapping("/treeSelect")
    public AjaxResult list(TbWxCategory tbWxCategory) {
        tbWxCategory.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        return tbWxCategoryFeign.treeSelect(tbWxCategory);
    }
}
