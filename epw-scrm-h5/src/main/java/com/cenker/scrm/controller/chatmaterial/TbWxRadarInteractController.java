package com.cenker.scrm.controller.chatmaterial;

import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxRadarFeign;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.radar.InteractRadarVo;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2021/12/7
 * @Description 聊天素材-互动雷达
 */
@RequestMapping("/chat/radar")
@RestController
public class TbWxRadarInteractController extends BaseController {
    @Autowired
    private TbWxRadarFeign tbWxRadarFeign;
    @Autowired
    private TokenParseUtil tokenService;

    /**
     * 侧边栏获取智能物料列表
     * @param radarVo
     * @param request
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(InteractRadarVo radarVo, HttpServletRequest request) {
        H5LoginUser loginUser = tokenService.getWorkLoginUser(request);
        filterCondition(loginUser, radarVo);

        radarVo.setStaffId(loginUser.getWxUserId());
        // 区分渠道 给智能物料发送时渠道用
        radarVo.setClickSource(TypeConstants.RADAR_SOURCE_SIDEBAR);
        // 企微对客时，只查询 使用中 的数据
        radarVo.setStatus(ApprovalStatusEnum.EXECUTING.getStatus());
        return tbWxRadarFeign.list(radarVo);
    }

    /**
     * 管理后台雷达统计数据
     */
    @GetMapping("/getRadarStatistics/{radarId}")
    public AjaxResult getRadarStatistics(@PathVariable("radarId") String radarId) {
        return tbWxRadarFeign.getRadarStatistics(radarId);
    }

    /**
     * 雷达图文链接客户数据
     */
    @GetMapping("/getRadarReadRecordStatistics/{radarId}")
    public TableDataInfo getRadarReadRecordStatistics(@PathVariable("radarId") String radarId) {
        return tbWxRadarFeign.getRadarReadRecordStatistics(radarId);
    }

    /**
     * 雷达点击详情数据
     */
    @GetMapping("/getRadarReadRecordDetail/{customerId}")
    public TableDataInfo getRadarReadRecordDetail(@PathVariable("customerId") String customerId) {
        return tbWxRadarFeign.getRadarReadRecordDetail(customerId);
    }

    /**
     * 侧边栏删除雷达
     */
    @DeleteMapping
    public AjaxResult remove(InteractRadarVo radarVo) {
        MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        radarVo.setCorpId(mobileUser.getCorpId());
        radarVo.setUserId(mobileUser.getSysUserId());
        return tbWxRadarFeign.remove(radarVo);
    }

    /**
     * 侧边栏新建雷达
     */
    @PostMapping
    public AjaxResult add(@RequestBody @Valid InteractRadarVo radarVo) {
        if (StringUtils.isEmpty(radarVo.getTbWxRadarContent().getTitle()) ||
                StringUtils.isEmpty(radarVo.getTbWxRadarContent().getContent())) {
            throw new CustomException("标题或内容为空！");
        }

        H5LoginUser loginUser = tokenService.getWorkLoginUser(ServletUtils.getRequest());
        radarVo.setCorpId(loginUser.getTenantId());
        radarVo.setCreateBy(loginUser.getUserId());

        MobileUser mobileUser = loginUser.getMobileUser();

        /*MobileUser mobileUser = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser();
        radarVo.setCorpId(mobileUser.getCorpId());
        String sysUserId = mobileUser.getSysUserId();
        if (StringUtils.isEmpty(sysUserId)) {
            return AjaxResult.error("请联系管理员获取权限");
        }
        radarVo.setCreateBy(sysUserId);*/

        // 新增创建来源 2022-05-31
        if (mobileUser.getLoginType() == TypeConstants.LOGIN_FROM_WORKBENCH) {
            radarVo.setSince(TypeConstants.CREATE_SOURCE_SINCE_WORKBENCH);
        }else {
            radarVo.setSince(TypeConstants.CREATE_SOURCE_SINCE_SIDE);
        }
        return tbWxRadarFeign.add(radarVo);
    }

    /**
     * 侧边栏编辑雷达
     */
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody @Valid InteractRadarVo radarVo) {
        if (StringUtils.isEmpty(radarVo.getTbWxRadarContent().getTitle()) ||
                StringUtils.isEmpty(radarVo.getTbWxRadarContent().getContent())) {
            throw new CustomException("标题或内容为空！");
        }
        radarVo.setCreateTime(null);
        radarVo.setCreateBy(null);

        H5LoginUser loginUser = tokenService.getWorkLoginUser(ServletUtils.getRequest());
        radarVo.setCorpId(loginUser.getTenantId());
        radarVo.setUpdateBy(loginUser.getUserId());

        /*String sysUserId = tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getSysUserId();
        if (StringUtils.isEmpty(sysUserId)) {
            return AjaxResult.error("请联系管理员获取权限");
        }
        radarVo.setUpdateBy(sysUserId);*/
        return tbWxRadarFeign.edit(radarVo);
    }

    @GetMapping("/{radarId}")
    public AjaxResult getById(@PathVariable("radarId") String radarId) {
        return tbWxRadarFeign.getById(radarId);
    }

    @PostMapping("/extract/linkData")
    public AjaxResult extractLinkData(@RequestBody TbWxRadarContent content) {
        return tbWxRadarFeign.extractLinkData(content);
    }

    /**
     * 获取所有企业物料 给其他如渠道活码功能使用
     */
    @GetMapping("/getRadarSource")
    public TableDataInfo getRadarSource(InteractRadarVo radarVo) {
        radarVo.setCorpId(tokenService.getWorkLoginUser(ServletUtils.getRequest()).getMobileUser().getCorpId());
        // 只查询企业物料
        radarVo.setScope(1);
        return tbWxRadarFeign.getRadarSource(radarVo);
    }
}
