package com.cenker.scrm.listener;

import com.cenker.scrm.client.bu.feign.ChatAgentFeign;
import com.cenker.scrm.event.BuChatAgentLogEvent;
import com.cenker.scrm.pojo.entity.bu.BuChatAgentLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@RequiredArgsConstructor
public class BuChatAgentLogEventListener  implements ApplicationListener<BuChatAgentLogEvent> {
    private final ChatAgentFeign chatAgentFeign;

    private static final Pattern patternOutput = Pattern.compile("\"output\":\"(.*?)\"");

    private static final Pattern patternMsgSn = Pattern.compile("\"msgSn\":\"(.*?)\"");

    private static final Pattern patternMsgTime = Pattern.compile("\"msgTime\":(\\d+)");
    @Override
    public void onApplicationEvent(BuChatAgentLogEvent event) {
        String chunk = event.getChunk();
        log.info("收到分块消息，BuChatAgentLogID：{}，消息内容：{}",event.getId(), chunk);
        BuChatAgentLog agentLog = new BuChatAgentLog();
        agentLog.setId(event.getId());
        try {
            Matcher matcher1 = patternOutput.matcher(chunk);
            if (matcher1.find()) {
                agentLog.setAgentAnswer(matcher1.group(1));
            }

            Matcher msgSn = patternMsgSn.matcher(chunk);
            if (msgSn.find()) {
                agentLog.setMsgSn(msgSn.group(1));
            }

            Matcher msgTime = patternMsgTime.matcher(chunk);
            if (msgTime.find()) {
                agentLog.setMsgTime(new Date(Long.parseLong(msgTime.group(1))));
            }

        } catch (Exception e) {
            ;
        }
        chatAgentFeign.edit(agentLog);
    }
}
