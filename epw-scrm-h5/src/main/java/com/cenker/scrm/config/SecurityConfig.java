package com.cenker.scrm.config;

import com.cenker.scrm.handler.AuthenticationEntryPointImpl;
import com.cenker.scrm.handler.MyAccessDeniedHandler;
import com.cenker.scrm.handler.security.JwtAuthenticationTokenFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * spring security配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    /**
     * 忽略的接口
     */
    private static final String[] IGNORE_URI = new String[]{
            "/login",
            "/captchaImage",
            "/*.html",
            "/**/*.html",
            "/**/*.css",
            "/**/*.js",
            // 互动雷达文章不需要登录
            "/activity/radarContent/getRadarContent",
            // 问卷设置免登录
            "/mobile/api/questionnaire/query_project_setting/**",
            "/mobile/api/questionnaire/get_anonymity_token",
            "/mobile/api/marketing/common/getUserInfo",
            "/webjars/**",
            "/*/api-docs",
            "/druid/**",
            "/wxcp/oauth/getToken",
            "/wxcp/oauth/getWxjsApiSignature",
            "/socketServer/**",
            "/customer/portrait/checkEsbBindingByUnionId",
            "/customer/portrait/checkBindStatusByUnionId",
            "/customer/portrait/checkAuthStatusByCode",
            "/customer/portrait/auth/callback",
            "/customer/portrait/access/auth/callback",
            "/customer/portrait/access/auth/signature",
            "/customer/portrait/getWxUserInfo",
            "/customer/portrait/checkAccessAuthStatus",
            "/customer/portrait/posterByH5/**",
            // 读取参数配置
            "/common/sysparam/**"
    };


    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * token认证过滤器
     */
    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    @Autowired
    private MyAccessDeniedHandler myAccessDeniedHandler;


    /**
     * 解决 无法直接注入 AuthenticationManager
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                // CSRF禁用，因为不使用session
                .csrf().disable()
                // 认证失败处理类
                .exceptionHandling().authenticationEntryPoint(unauthorizedHandler).and()
                // 基于token，所以不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                // 过滤请求
                .authorizeRequests()
                // 允许未登录访问
                .antMatchers(IGNORE_URI).permitAll()
                // 除上面外的所有请求全部需要鉴权认证
                .anyRequest().authenticated()
                .and()
                .headers().frameOptions().disable();
        // 添加JWT filter
        httpSecurity.addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        // 登录用户无权限时处理
        httpSecurity.exceptionHandling().accessDeniedHandler(myAccessDeniedHandler);
    }


    /**
     * 强散列哈希加密实现
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }


}
