package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.client.wechat.pojo.TbWxCustomerTrajectoryDTO;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.external.CustomerAuthDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.fund.GetAssetDailyVariationDTO;
import com.cenker.scrm.pojo.dto.fund.GetTotalAssetsDTO;
import com.cenker.scrm.pojo.dto.fund.GetTradeRecordDTO;
import com.cenker.scrm.pojo.dto.open.AppCategoryDTO;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.external.CustomerPortraitVo;
import com.cenker.scrm.pojo.vo.external.WxCustomerBindingVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/customer/portrait")
public interface CustomerPortraitFeign {
    @RequestMapping("/findWeCustomerInfo")
    AjaxResult findWeCustomerInfo(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    /**
     * 获取当前系统所有可用标签
     *
     * @return
     */
    @RequestMapping("/findAllTags")
    AjaxResult findAllTags(@RequestParam("corpId") String corpId);

    @RequestMapping("/findAddGroupNum")
    AjaxResult findAddGroupNum(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    @RequestMapping("/findAddEmployers/{externalUserId}")
    AjaxResult findAddEmployers(@PathVariable("externalUserId") String externalUserId, @RequestParam("corpId") String corpId);

    /**
     * 获取轨迹信息
     */
    @RequestMapping("/findTrajectory")
    TableDataInfo findTrajectory(@RequestParam("userId") String userId, @RequestParam("corpId") String corpId);

    @RequestMapping("/updateWeCustomerPorTraitTag")
    AjaxResult updateWeCustomerPorTraitTag(@RequestBody WeMakeCustomerTag weMakeCustomerTag);

    @RequestMapping("/removeTrajectory/{trajectoryId}")
    AjaxResult removeTrajectory(@PathVariable("trajectoryId") String trajectoryId);

    /**
     * 添加或编辑轨迹(待办)
     *
     * @param trajectory
     * @return
     */
    @RequestMapping("/addOrEditWaitHandle")
    AjaxResult addOrEditWaitHandle(@RequestBody TbWxCustomerTrajectoryDTO trajectory);

    /**
     * 完成待办
     *
     * @param trajectoryId
     * @return
     */
    @RequestMapping("/handleWait/{trajectoryId}")
    AjaxResult handleWait(@PathVariable("trajectoryId") String trajectoryId);

    @RequestMapping("/updateWeCustomerInfo")
    AjaxResult updateWeCustomerInfo(@RequestBody CustomerPortraitVo weCustomerPortrait);

    @RequestMapping("/findTrajectory2")
    AjaxResult findTrajectory2(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    @RequestMapping("/addFollowTrajectory")
    AjaxResult addFollowTrajectory(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    @RequestMapping("/remove/FollowTrajectory")
    AjaxResult removeFollowTrajectory(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    @RequestMapping("/getCustomerInfoById")
    AjaxResult getCustomerInfoById(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    /**
     * 企业关系
     * @param customerChurnDTO s
     * @return 企业关系信息
     */
    @RequestMapping("/getAssociationInfo")
    TableDataInfo getAssociationInfo(CustomerChurnDTO customerChurnDTO);

    @RequestMapping("/getCorpInteract")
    AjaxResult getCorpInteract(CustomerChurnDTO dto);

    @RequestMapping("/getStaffLog")
    TableDataInfo getStaffLogTrajectory(CustomerChurnDTO dto);

    /**
     * RFM消费信息
     * @param appCategoryDTO
     * @return
     */
    @RequestMapping("/getConsumeRFM")
    AjaxResult getConsumeRFM(@RequestBody AppCategoryDTO appCategoryDTO);

    @RequestMapping("/getOrderInfoList")
    TableDataInfo getOrderInfoList(@RequestBody AppCategoryDTO appCategoryDTO);

    @RequestMapping("/findTrajectory3")
    TableDataInfo findTrajectory3(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    /**
     * 获取客户标签信息（所有员工版本）
     * @param tbWxCustomerTrajectory
     * @return
     */
    @RequestMapping("/getCustomerTag")
    AjaxResult getCustomerTag(@RequestBody TbWxCustomerTrajectoryDTO tbWxCustomerTrajectory);

    @RequestMapping("/bindingByEsb")
    AjaxResult bindingByEsb(@RequestBody CustomerAuthDTO dto);

    @RequestMapping("/checkEsbBinding")
    AjaxResult checkEsbBinding(@RequestBody CustomerAuthDTO dto);

    @RequestMapping("/checkEsbBindingByUnionId")
    Result<WxCustomerBindingVO> checkEsbBindingByUnionId(@RequestBody CustomerAuthDTO customerAuthDTO);

    @RequestMapping("/checkAuthStatusByUnionId")
    AjaxResult checkAuthStatusByUnionId(@RequestBody CustomerAuthDTO dto);

    @RequestMapping("/getWxUserByCode")
    AjaxResult getWxUserByCode(@RequestBody CustomerAuthDTO dto);

    @RequestMapping("/accessAuthCallBack")
    AjaxResult accessAuthCallBack(@RequestBody  CustomerAuthDTO dto);

    @RequestMapping("/checkAccessAuthStatus")
    AjaxResult checkAccessAuthStatus(@RequestBody CustomerAuthDTO customerAuthDTO);

    @RequestMapping("/fund/getListProfitOfTotalAssets")
    AjaxResult getListProfitOfTotalAssets(@RequestBody GetAssetDailyVariationDTO dto);
    @RequestMapping("/fund/getTotalAssets")
    AjaxResult getTotalAssets(@RequestBody GetTotalAssetsDTO dto);
    @RequestMapping("/fund/getLatestTotalAsset")
    AjaxResult getLatestTotalAsset(@RequestBody GetTotalAssetsDTO dto);
    @RequestMapping("/fund/getTradeRecord")
    AjaxResult getTradeRecord(@RequestBody GetTradeRecordDTO dto);
    @RequestMapping("/fund/getAssetDailyVariation")
    AjaxResult getAssetDailyVariation(GetAssetDailyVariationDTO dto);
}
