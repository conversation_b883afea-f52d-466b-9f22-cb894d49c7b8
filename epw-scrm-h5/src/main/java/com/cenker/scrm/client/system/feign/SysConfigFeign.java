package com.cenker.scrm.client.system.feign;


import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysConfig;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/system/config")
public interface SysConfigFeign {

    @RequestMapping("/list")
    TableDataInfo list(@RequestBody SysConfig config);

    @RequestMapping("/export")
    List<SysConfig> export(@RequestBody SysConfig config);

    @RequestMapping("/{configId}")
    AjaxResult getInfo(@PathVariable("configId") Long configId);

    @RequestMapping("/configKey/{configKey}")
    AjaxResult getConfigKey(@PathVariable("configKey") String configKey);

    @RequestMapping("/add")
    AjaxResult add(@Validated @RequestBody SysConfig config);

    @RequestMapping("/edit")
    AjaxResult edit(@Validated @RequestBody SysConfig config);

    @RequestMapping("/remove/{configIds}")
    AjaxResult remove(@PathVariable("configIds") Long[] configIds);

    @RequestMapping("/clearCache")
    AjaxResult clearCache();
}
