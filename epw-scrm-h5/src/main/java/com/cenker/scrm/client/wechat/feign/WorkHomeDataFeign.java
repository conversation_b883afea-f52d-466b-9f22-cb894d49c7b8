package com.cenker.scrm.client.wechat.feign;

import com.cenker.scrm.client.wechat.pojo.TbWxCustomerTrajectoryDTO;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE,path = "/work/homeData")
public interface WorkHomeDataFeign {

    @RequestMapping("/homeData")
    AjaxResult getHomeData(@RequestBody MobileUser mobileUser);

    @RequestMapping("/getUserInfo")
    AjaxResult getUserInfo(@RequestBody MobileUser mobileUser);

    @RequestMapping("/findTrajectory")
    TableDataInfo findTrajectory();

    @RequestMapping(value = "/addOrEditWaitHandle")
    AjaxResult saveOrEditWaitHandle(@RequestBody TbWxCustomerTrajectoryDTO trajectory);
}
