package com.cenker.scrm.client.system.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysDictType;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE, path = "/system/dict/type")
public interface SysDictTypeFeign {

    /**
     * 查询字典类型详细
     */
    @RequestMapping("/byDictType/{dictType}")
    SysDictType selectDictTypeByType(@PathVariable("dictType") String dictType);

}
