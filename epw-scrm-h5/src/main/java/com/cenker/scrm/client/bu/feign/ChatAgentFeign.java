package com.cenker.scrm.client.bu.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.dto.bu.ChatAgentDto;
import com.cenker.scrm.pojo.entity.bu.BuChatAgentLog;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, path = "/chat/agent")
public interface ChatAgentFeign {

    /**
     * 新增ChatAgent会话记录
     */
    @PostMapping("/add")
    AjaxResult add(@RequestBody BuChatAgentLog buChatAgentLog);
    /**
     * 修改ChatAgent会话记录
     */
    @PutMapping("/edit")
    AjaxResult edit(@RequestBody BuChatAgentLog buChatAgentLog);

    /**
     * 发送话术记录
     */
    @PutMapping("/sendAnswer")
    AjaxResult sendAnswer(@RequestBody ChatAgentDto chatAgentDto);

    /**
     * 标记为停止生成
     * @param chatAgentDto
     * @return
     */
    @PutMapping("/stop")
    AjaxResult stop(@RequestBody ChatAgentDto chatAgentDto);
}
