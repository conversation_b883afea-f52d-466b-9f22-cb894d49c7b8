package com.cenker.scrm.client.questionnaire;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/1/11 20:46
 **/
@Data
public class QueryProjectRequest {


    @Data
    public static class List {
        private Integer status;
        private Long corpPriId;
        private String corpId;
        private String userId;
    }


    /**
     * 分页查询
     */
    @Data
    public static class Page extends PageRequest {
        private Integer status;
        private Long corpPriId;
        private String corpId;
        private String userId;

        private String name;

        @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        private LocalDateTime beginDateTime;

        @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        private LocalDateTime endDateTime;
    }
}
