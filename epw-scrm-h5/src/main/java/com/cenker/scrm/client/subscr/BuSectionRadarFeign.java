package com.cenker.scrm.client.subscr;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE,path = "/bu/section/radar")
public interface BuSectionRadarFeign  {
    @GetMapping("/list")
    TableDataInfo<BuSectionRadarVO> list(@SpringQueryMap BuSectionRadarQuery query);
}
