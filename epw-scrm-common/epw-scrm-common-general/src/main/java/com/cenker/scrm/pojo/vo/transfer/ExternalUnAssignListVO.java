package com.cenker.scrm.pojo.vo.transfer;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalUnAssignListVO implements java.io.Serializable {
    /**
     * 移交人ID
     */
    private String handoverUserId;

    /**
     * 移交人姓名或标识。
     */
    private String handoverUserName;

    /**
     * 移交人头像
     */
    private String handoverUserAvatar;

    /**
     * 移交人部门
     */
    private String handoverUserDepartment;

    /**
     * 待分配客户数
     */
    private int unAssignCustomerCount;

    /**
     * 待分配群聊数
     */
    private int unAssignGroupChatCount;

    /**
     * 离职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date dimissionTime;

}
