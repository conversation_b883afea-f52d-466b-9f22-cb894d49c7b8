package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 微信客服联系方式对象 tb_wx_kf_content_way
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxKfContactWay extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 客户账号ID，tb_wx_kf_account的id
     */
    private Long kfId;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 场景值参数，回调会回传
     */
    private String sceneParam;

    /**
     * 客服链接（企微返回）
     */
    private String kfUrl;

    /**
     * 状态 0 表示正常、1 停用
     */
    private String status;

    @TableLogic
    private Integer delFlag;

}
