package com.cenker.scrm.model.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Builder
@Data
public class Tree implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String parentId;

    /**
     * 节点值
     */
    private String value;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Tree> children;

    /**
     * 添加子节点
     * @param child
     * @return
     */
    public boolean addChild(Tree child) {
        if (Objects.isNull(children)) {
            this.children = new ArrayList<>();
        }

        return this.children.add(child);
    }

}
