package com.cenker.scrm.pojo.entity.subscr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 栏目统计实体类
 * 用于存储栏目统计的相关信息
 */
@Data
@TableName("bu_section_statistics")
public class BuSectionStatistics {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 栏目ID，关联订阅栏目表的id
     */
    private String sectionId;

    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date statisticDate;

    /**
     * 本栏目订阅人数
     */
    private Integer subscriptionCount;

    /**
     * 总订阅人数
     */
    private Integer subscriptionCountTotal;

    /**
     * 订阅比率
     */
    private BigDecimal subscriptionRatio;

    /**
     * 新增订阅人数
     */
    private Integer newSubscriptionCount;

    /**
     * 取消订阅人数
     */
    private Integer cancelSubscriptionCount;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}