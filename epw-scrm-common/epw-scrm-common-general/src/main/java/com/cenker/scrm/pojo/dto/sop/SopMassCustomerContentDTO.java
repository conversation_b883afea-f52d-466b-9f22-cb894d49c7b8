package com.cenker.scrm.pojo.dto.sop;

import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description 群发客户内容
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SopMassCustomerContentDTO extends SopContentDTO {
    /**
     * 重复类型 0 永不重复 1 每天 2 每周 3 每两周 4 每月 5 每年
     */
    @NotNull(message = "请选择重复类型",groups = {InsertGroup.class, UpdateGroup.class})
    private Integer repeatType;

    /**
     * 结束重复 0 永不 1 指定日期
     */
    private Integer repeatExpire;

    /**
     * 重复单位 0 永不 1 日 2 周 3 月 4 年
     */
    private Integer repeatUnit;

    /**
     * 执行单位的值 1-31 逗号分隔 如单位为周1,2代表周一、周二
     */
    private String executeValue;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "请选择开始发送时间",groups = {InsertGroup.class, UpdateGroup.class})
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
