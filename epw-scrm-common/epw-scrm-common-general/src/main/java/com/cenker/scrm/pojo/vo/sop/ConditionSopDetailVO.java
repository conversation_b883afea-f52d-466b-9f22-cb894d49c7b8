package com.cenker.scrm.pojo.vo.sop;

import com.cenker.scrm.pojo.dto.condition.SopTriggerConditionDTO;
import com.cenker.scrm.pojo.dto.sop.SopConditionContentDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/11
 * @Description 回显视图
 */
@Data
public class ConditionSopDetailVO extends SopInfoVO {
    /**
     * 前端存储条件json
     */
    private String viewSendCondition;
    /**
     * 前端存储条件json
     */
    private String sendCondition;
    /**
     * 触发条件
     */
    @Deprecated
    private SopTriggerConditionDTO sopTriggerCondition;

    /**
     * 内容序列
     */
    private List<SopConditionContentDTO> sopContentList;
}
