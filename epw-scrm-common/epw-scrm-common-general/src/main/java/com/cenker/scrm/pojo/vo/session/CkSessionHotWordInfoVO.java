package com.cenker.scrm.pojo.vo.session;

import com.cenker.scrm.model.base.BaseEntity;
import com.cenker.scrm.pojo.dto.session.UserDto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 热词信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionHotWordInfoVO
{
    private static final long serialVersionUID = 1L;

    /** 热词ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long hotId;

    /** 热词名称 */
    private String hotWord;

    /** 近似词，多个逗号分隔 */
    private List<String> synonList;

    private List<UserDto> userConditionList;

    private String  createUserName;
    private String  createTime;

}
