package com.cenker.scrm.pojo.vo.journey;

import com.cenker.scrm.pojo.vo.external.ExtCustomerInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/30
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExternalJourneyCustomerVO extends ExtCustomerInfoVO {

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;
    /**
     * 前端所需
     */
    private Boolean hover = false;
    private Boolean show = false;
}
