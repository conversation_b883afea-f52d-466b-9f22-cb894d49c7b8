package com.cenker.scrm.pojo.vo.session;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 热词信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionSensActAlarmVO
{
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String actType;
    private String ruleName;
    private RuleUserVO sendUserObj;
    private RuleUserVO acceptUserObj;
    private String  triggerTime;

}
