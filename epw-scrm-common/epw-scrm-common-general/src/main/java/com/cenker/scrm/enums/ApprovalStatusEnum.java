package com.cenker.scrm.enums;

import lombok.Getter;

/**
 * 审批状态枚举类
 * <AUTHOR>
 */
@Getter
public enum ApprovalStatusEnum {
    CREATED("CREATE_STEP", "CREATED","创建中"),
    CREATED_SUCCESS("CREATE_STEP", "CREATED_SUCCESS","创建成功"),
    CREATED_FAIL("CREATE_STEP", "CREATED_FAIL","创建失败"),

    PENDING_APPROVAL("APPROVAL_STEP", "PENDING_APPROVAL","待审核"),
    REVOKED("APPROVAL_STEP", "REVOKED","已撤回"),

    APPROVED("APPROVAL_STEP", "APPROVED","审核通过"),

    REJECTED("APPROVAL_STEP", "REJECTED","审核驳回"),
    PENDING_EXEC("EXECUTION_STEP", "PENDING_EXEC","待执行"),
    EXECUTING("EXECUTION_STEP", "EXECUTING","执行中/运行中"),
    EXEC_INTERRUPTED("EXECUTION_STEP", "EXEC_INTERRUPTED","已停用"),
    EXEC_EXCEPTION("EXECUTION_STEP", "EXEC_EXCEPTION","执行异常"),
    FINISHED("FINISH_STEP", "FINISHED","已完成"),
    CANCELED("FINISH_STEP", "CANCELED","已取消");

    private String step;

    private String status;

    private String desc;

    ApprovalStatusEnum(String step, String status, String desc) {
        this.step = step;
        this.status = status;
        this.desc = desc;
    }

    public static ApprovalStatusEnum getApprovalStatusEnum(String status) {
        for (ApprovalStatusEnum approvalStatusEnum : ApprovalStatusEnum.values()) {
            if (approvalStatusEnum.getStatus().equals(status)) {
                return approvalStatusEnum;
            }
        }
        return null;
    }

    public static boolean isRunning(String status) {
        ApprovalStatusEnum approvalStatusEnum = getApprovalStatusEnum(status);
        if (approvalStatusEnum == null) {
            return false;
        }
        return ApprovalStatusEnum.EXECUTING.equals(approvalStatusEnum);
    }

    public static boolean isStoped(String status) {
        ApprovalStatusEnum approvalStatusEnum = getApprovalStatusEnum(status);
        if (approvalStatusEnum == null) {
            return false;
        }
        return ApprovalStatusEnum.EXEC_INTERRUPTED.equals(approvalStatusEnum);
    }
}
