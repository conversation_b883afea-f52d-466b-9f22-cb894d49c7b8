package com.cenker.scrm.pojo.dto.external;


import com.cenker.scrm.model.base.BaseRequest;
import com.cenker.scrm.pojo.dto.condition.StageConditionDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 客户列表自定义常用搜索
 */
@Data
public class CustomerSearchDTO implements Serializable {

    private Long id;
    /**
     * name
     */
    private String name;
    /**
     * 筛选条件
     */
    private String condition;

}
