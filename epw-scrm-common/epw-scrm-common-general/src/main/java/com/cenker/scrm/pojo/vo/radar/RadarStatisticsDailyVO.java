package com.cenker.scrm.pojo.vo.radar;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RadarStatisticsDailyVO {
    // @ApiModelProperty(value = "日期")
    private String day;
    // @ApiModelProperty(value = "新增点击次数")
    private Integer clickNum = 0;
    // @ApiModelProperty(value = "新增访客数")
    private Integer visitorNum = 0;
    // @ApiModelProperty(value = "新增客户数")
    private Integer customerNum = 0;
}
