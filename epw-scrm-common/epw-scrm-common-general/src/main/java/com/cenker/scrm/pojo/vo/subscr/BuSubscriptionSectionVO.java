package com.cenker.scrm.pojo.vo.subscr;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订阅栏目VO类
 */
@Data
public class BuSubscriptionSectionVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 栏目名称
     */
    private String sectionName;

    /**
     * 栏目简介
     */
    private String sectionIntro;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;
    /**
     * 创建者
     */
    private String createByName;
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 本栏目订阅人数
     */
    private Integer subscriptionCount;

    /**
     * 订阅比率
     */
    private BigDecimal subscriptionRatio;
    /**
     * 部门id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer deptId;
    /**
     * 查询条件传 radarId 时，判断栏目是否关联此智能物料
     * true：关联，false：未关联
     */
    private boolean selected;
}