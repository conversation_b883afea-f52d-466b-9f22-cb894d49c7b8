package com.cenker.scrm.pojo.vo.group;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/29
 * @Description
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroupCodeDataStatisticsDailyVO {

    /**
     * 日期
     */
    private String day;
    /**
     * 日期date
     */
    private Date date;

    /**
     * 扫码次数
     */
    private Integer scanCodeCnt;
    /**
     * 扫码入群总客户数
     */
    private Integer addGroupCnt;

    /**
     * 扫码退群总客户数
     */
    private Integer quitGroupCnt;

    /**
     * 扫码入群净增客户数 = 扫码入群总客户数 - 扫码退群总客户数
     */
    private Integer netAddGroupCnt;
}
