package com.cenker.scrm.pojo.vo.statistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * 数据统计-客户数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
public class StatisticCustomerSummaryVo {

    /**
     * 统计日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String statisticDate;

    /**
     *客户总数
     */
    private Integer customerTotal;
    /**
     *已认证总数
     */
    private Integer customerAuthTotal;
    /**
     *新认证客户数
     */
    private Integer customerNewAuthTotal;
    /**
     *新增客户数
     */
    private Integer customerNewTotal;
    /**
     *流失客户数
     */
    private Integer customerLossTotal;
    /**
     *活跃客户数
     */
    private Integer customerActiveTotal;
    /**
     *客户发消息条数(1v1)
     */
    private Integer aloneChatTotal;
    /**
     *客户发消息条数(群聊)
     */
    private Integer customerGroupChatTotal;
    /**
     *净增客户数
     */
    private Integer netNewTotal;

}
