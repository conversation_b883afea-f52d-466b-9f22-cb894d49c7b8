package com.cenker.scrm.pojo.dto.knowledgebaseai;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
public class KnowledgeBaseAiAskApiRequest {

    @NotNull(message = "问题不能为空")
    private String question;
    private List<String[]> history;
    private String prompt;
    private Float temperature;
    @NotNull(message = "来源类型不能为空")
    private Integer sourceType;
    @NotNull(message = "来源应用不能为空")
    private Long appId;
    private Long repositoryId;
}
