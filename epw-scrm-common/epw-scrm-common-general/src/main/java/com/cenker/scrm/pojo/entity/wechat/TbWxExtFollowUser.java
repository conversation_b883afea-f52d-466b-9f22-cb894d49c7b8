package com.cenker.scrm.pojo.entity.wechat;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部联系人与企业员工关系对象 tb_wx_ext_follow_user
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
 // @ApiModel("外部联系人与企业员工关系实体")
public class TbWxExtFollowUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
     // @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
     // @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
     // @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
     // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
     // @ApiModelProperty("备注")
    private String remark;
    /**
     * $column.columnComment
     */
     // @ApiModelProperty("id")
    private Long id;

    /**
     * 添加了此外部联系人的企业成员userid
     */
     // @ApiModelProperty("添加了此外部联系人的企业成员userid")
     // @Excel(name = "添加了此外部联系人的企业成员userid")
    private String userId;

    /**
     * 该成员对此外部联系人的描述
     */
     // @ApiModelProperty("该成员对此外部联系人的描述")
     // @Excel(name = "该成员对此外部联系人的描述")
    private String description;

    /**
     * 该成员对此客户备注的企业名称
     */
     // @ApiModelProperty("该成员对此客户备注的企业名称")
     // @Excel(name = "该成员对此客户备注的企业名称")
    private String remarkCorpName;

    /**
     * 该成员对此客户备注的手机号码
     */
     // @ApiModelProperty("该成员对此客户备注的手机号码")
     // @Excel(name = "该成员对此客户备注的手机号码")
    private String remarkMobiles;

    /**
     * 发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid
     */
     // @ApiModelProperty("操作人ID")
     // @Excel(name = "发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid")
    private String operatorUserId;

    /**
     * 该成员添加此客户的来源，
     */
     // @ApiModelProperty("该成员添加此客户的来源")
     // @Excel(name = "该成员添加此客户的来源，")
    private String addWay;

    /**
     * 企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定
     */
     // @ApiModelProperty("企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定")
     // @Excel(name = "企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定")
    private String state;

    /**
     * 客户id
     */
     // @ApiModelProperty("客户id")
     // @Excel(name = "客户id")
    private String externalUserId;

    /**
     * 状态（0正常 1删除）
     */
     // @ApiModelProperty("状态")
     // @Excel(name = "状态", readConverterExp = "0=正常,1=删除")
    private String status;

    /**
     * 企业id
     */
     // @ApiModelProperty("企业id")
     // @Excel(name = "企业id")
    private String corpId;

    /**
     * 客户删除企业成员时间
     */
     // @ApiModelProperty("客户删除企业成员时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
     // @Excel(name = "客户删除企业成员时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date delTime;

     // @ApiModelProperty("客户标签")
     // @Excel(name = "客户标签")
    private String tag;

     // @ApiModelProperty("个人标签")
     // @Excel(name = "个人标签")
    private String selfTag;

    /** 邮件 */
    private String email;
    /** qq */
    private String qq;
    /**地址*/
    private String address;

    /**
     * 客户同意会话存档状态，0表示不同意，1表示同意
     */
    private Integer chatStatus;

    /**
     * 同意会话存档时间或者不同意会话存档时间
     */
    private Date chatApprovedTime;

}
