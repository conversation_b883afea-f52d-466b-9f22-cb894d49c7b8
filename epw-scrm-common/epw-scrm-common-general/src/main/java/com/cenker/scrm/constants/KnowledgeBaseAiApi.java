package com.cenker.scrm.constants;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConfigurationProperties(prefix = "knowledgebaseai.config")
public class KnowledgeBaseAiApi {

    //  = "https://ncenker-batchfunc.azurewebsites.net/api/"
    public static String HTTP_URL;

    /**
     * POST 请求
     * @return
     */
    public static HttpRequest getHttpRequestByPost(String methodPath) {
        return getHttpRequest(Method.POST,methodPath);
    }

    /**
     * GET 请求
     *
     * @return
     */
    public static HttpRequest getHttpRequestByGet(String methodPath) {
        return getHttpRequest(Method.GET,methodPath);
    }

    /**
     * HTTP 请求
     *
     * @param method
     * @return
     */
    public static HttpRequest getHttpRequest(Method method,String methodPath) {
        String urlComplete = HTTP_URL + methodPath;
        HttpRequest request = HttpUtil.createRequest(method, urlComplete);
        log.info("请求地址：【{}】",urlComplete);
        return request;
    }

    public static String getHttpUrl() {
        return HTTP_URL;
    }

    public void setHttpUrl(String httpUrl) {
        KnowledgeBaseAiApi.HTTP_URL = httpUrl;
    }
}
