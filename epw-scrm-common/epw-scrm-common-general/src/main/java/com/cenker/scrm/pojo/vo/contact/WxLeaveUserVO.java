package com.cenker.scrm.pojo.vo.contact;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class WxLeaveUserVO implements Serializable {
    private String userName;

    private String department;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dimissionTime;

    private Integer allocateCustomerNum;

    private Integer allocateGroupNum;

    private String userId;

    private Integer isAllocate;

}