package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxQuickReply {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 分类id
   */
  private Long categoryInfoId;
  /**
   * 话术标题
   */
  private String title;
  /**
   * 1 话术 2 组合话术
   */
  private Integer type;
  /**
   * 附件 json格式
   */
  private String attachment;
  /**
   * 更新通知 0 未开启 1 开启
   */
  private Integer updateInform;
  /**
   * 更新通知范围 json格式
   */
  private String sendCondition;
  /**
   * 发送次数
   */
  private Integer sendCnt;
  /**
   * 创建人
   */
  private String createBy;
  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  private String updateBy;
  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
  private Date updateTime;
  private Integer delFlag;
  /**
   * 企业配置id
   */
  private Long corpConfigId;
  /**
   * 选择范围
   */
  private Integer sendScope;

  /**
   * 部门ID
   */
  private Integer deptId;
}
