package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 企业客户标签组对象 tb_wx_corp_tag_group
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
// @ApiModel("企业客户标签组实体")
public class TbWxCorpTagGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    // @ApiModelProperty("标签ID")
    private Long id;

    /**
     * 标签分组id
     */
    private String categoryId;

    @TableField(exist = false)
    private String categoryScope;

    @TableField(exist = false)
    private List<Long> categoryIdList;

    /**
     * 标签组id
     */
    // @ApiModelProperty("标签组id")
    private String groupId;

    /**
     * 标签组名称
     */
    // @ApiModelProperty("标签组名称")
    // @Excel(name = "标签组名称")
    private String groupName;

    private String createBy;

    /**
     * 创建时间
     */
    // @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    // @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    // @ApiModelProperty("备注")
    private String remark;

    /**
     * 帐号状态（0正常 2删除）
     */
    // @ApiModelProperty("帐号状态0=正常,2=删除")
    // @Excel(name = "帐号状态", readConverterExp = "0=正常,2=删除")
    private String status;

    /**
     * 次序值
     */
    // @ApiModelProperty("次序值")
    // @Excel(name = "次序值")
    @TableField(value = "`order`")
    private Integer order;

    /**
     * 企业id
     */
    // @ApiModelProperty("企业id")
    // @Excel(name = "企业id")
    private String corpId;

    private String synStatus;
    /**
     * 标签名称
     */
    // @ApiModelProperty("标签名称")
    @TableField(exist = false)
    private List<String> name;

    /**
     * 标签类型 CORP:企业标签、CUSTOMER 客户标签
     */
    // @ApiModelProperty("标签类型 CORP:企业标签、CUSTOMER 客户标签")
    // @Excel(name = "标签类型 CORP:企业标签、CUSTOMER 客户标签")
    private String type;

    // @ApiModelProperty("企业微信标签")
    @TableField(exist = false)

    private List<TbWxCorpTag> tagList = new ArrayList<>();

    /**
     * 操作人
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 标签功能类型：unique：唯一型，progressive：递进型，overlapping：重叠型
     */
    private String groupTagType;

}
