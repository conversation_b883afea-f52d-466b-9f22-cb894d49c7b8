package com.cenker.scrm.pojo.dto.session;

import com.cenker.scrm.annotation.Excel;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 热词信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class AddHotWordDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 热词ID */
    private Long hotId;
    @Excel(name = "热词", cellType = Excel.ColumnType.STRING, prompt = "热词名称：不能超过20个字")
    /** 热词名称 */
    private String hotWord;

    @Excel(name = "近似词", cellType = Excel.ColumnType.STRING, prompt = "近似词多个以英文逗号分隔(近似词1,近似词2)")
    /** 近似词，多个逗号分隔 */
    private String synonWords;

    private List<UserDto> userConditionList;

    private String checkUserIds;

    /**
     * 部门id
     */
    private Integer deptId;


}
