package com.cenker.scrm.pojo.dto;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 为客户添加企业标签的MQ消息体内容
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CustomerRemoveTagMsgDto extends AbstractCustomerTagMsgDto {

    private String corpId;

    /**
     * 外部联系人ID集合
     */
    private List<String> externalUserIds;
    /**
     * 是否来自回调，默认false
     * 为true时，表示来自回调，员工使用userId
     */
    private boolean fromCallback;

    /**
     * 标签集合(映射对象：TagVO）
     */
    private List<TagVO> tagList;

    /**
     * 标签来源类型
     */
    private String tagSource;

    /**
     * 是否重试，默认false
     * 非重试场景，如果失败，会插入一条重试任务
     */
    private boolean isRetry;

    /**
     * 操作id
     */
    private String operId;
    /**
     * 操作人
     */
    private String userId;
    private String nickName;
    /**
     * 企业运营动态：批量编辑客户标签，是否进行消息通知
     */
    private boolean isMsgNotify;

    private TrackEventTypeEnum eventTypeEnum;
    /**
     * 名称，条件打标签规则名称，智能物料名称，获客链接名称，获客链接名称，渠道活码名称等
     */
    private String name;
    /**
     * 批量删除标签 需要的参数
     */
    private OperTrackParams operTrackParams;

    // 实现父类的方法
    @Override
    public TrackEventTypeEnum getEventTypeEnum() {
        return this.eventTypeEnum;
    }
    @Override
    public Integer getRadarType() {
        return 0;
    }
    @Override
    public String getRadarRuleNum() {
        return "";
    }
    @Override
    public OperTrackParams getOperTrackParams() {
        return this.operTrackParams;
    }
    @Override
    public String getUserId() {
        return this.userId;
    }
    @Override
    public String getCorpId() {
        return this.corpId;
    }
    @Override
    public String getOperId() {
        return this.operId;
    }
    @Override
    public String getName() {
        return this.name;
    }
}
