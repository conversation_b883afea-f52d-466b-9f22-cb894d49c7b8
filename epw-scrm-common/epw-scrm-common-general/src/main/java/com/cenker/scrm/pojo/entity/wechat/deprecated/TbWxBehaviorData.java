package com.cenker.scrm.pojo.entity.wechat.deprecated;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 联系客户数据实体
 */
@Data
public class TbWxBehaviorData {
  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  private String userId;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date statTime;
  private Integer newApplyCnt;
  private Integer newContactCnt;
  private Integer chatCnt;
  private Integer messageCnt;
  private double replyPercentage;
  private Integer avgReplyTime;
  private Integer negativeFeedbackCnt;
  private String corpId;
}
