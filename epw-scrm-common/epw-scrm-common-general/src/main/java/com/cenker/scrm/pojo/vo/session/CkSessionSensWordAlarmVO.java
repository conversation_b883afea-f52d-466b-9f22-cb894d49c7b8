package com.cenker.scrm.pojo.vo.session;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 热词信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionSensWordAlarmVO
{
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String contentValue;
    private String ruleName;
    private String sensitiveWord;
    private RuleUserVO sendUserObj;
    private RuleUserVO acceptUserObj;
    private String  triggerTime;

}
