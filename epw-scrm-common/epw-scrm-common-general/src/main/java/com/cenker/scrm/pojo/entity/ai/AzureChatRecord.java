package com.cenker.scrm.pojo.entity.ai;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AzureChatRecord {

  @TableId(type = IdType.AUTO)
  private Long id;
  private String sessionKey;
  /**
   * 对话分组标识（前端分组）
   */
  private String messageGroupId;
  private String messageRole;
  private String contentType;
  private Integer messageType;
  private String messageContent;
  private Long messageTime;
  private String azureRequest;
  private String azureResponse;
  private Long corpConfigId;
  private String remark;
  /**
   * 是否正确消息
   */
  private Boolean messageStatus;
  @TableLogic
  private Boolean delFlag;
  private Long createBy;
  private Date createTime;
  private Long updateBy;
  private Date updateTime;

  /**
   * 发送状态 0发送中，1发送成功，2发送失败
   */
  private Integer sendStatus;


}
