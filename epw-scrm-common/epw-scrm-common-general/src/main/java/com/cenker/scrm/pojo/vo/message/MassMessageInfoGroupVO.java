package com.cenker.scrm.pojo.vo.message;

import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 重构社群列表vo
 * @date 2023/5/26 18:11
 */
@Data
public class  MassMessageInfoGroupVO implements Serializable {
    /**
     * 发布时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    private String createByUser;
    /**
     * 任务名称
     */
    private String taskName;

//    /**
//     * 实际发送数
//     */
//    private Integer actualSend;
//    /**
//     * 预计发送数
//     */
//    private Integer expectSend;

    /**
     * 社群数
     */
    private Integer groupNum;

    /**
     * 员工完成率
     */
    private String finishRate;
    /**
     * 群发内容
     */
    private String massContent;
    /**
     * 群发消息id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long infoId;

//    /**
//     * 发送信息详情表
//     */
//    private Long detailId;
//    /**
//     * 发送员工
//     */
//    private List<String> userList;

    /**
     * 消息内容
     */
    private List<WelcomeAttachmentVo> attachments;

    private Boolean timedTask;

    private String checkStatus;
    /**
     * 启用了审批
     * true:启用审批
     * false:未启用审批
     */
    private boolean enableApproval;
    /**
     * 审批权限
     * true:有审批权限
     * false:无审批权限
     */
    private boolean canApproval;
    /**
     * 审批人账号
     */
    private String approvalUser;
    /**
     * 审批人姓名
     */
    private String approvalUserName;
    /**
     * 意见
     */
    private String approvalRemark;
    /**
     * 执行异常时的错误信息
     */
    private String errorMsg;
}
