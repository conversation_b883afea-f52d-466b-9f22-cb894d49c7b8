package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCustomFunctionMenu {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private String menuId;
  private String orderNum;
  private String remark;
  private Long corpPriId;
  private Integer delFlag;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  private Date updateTime;
}
