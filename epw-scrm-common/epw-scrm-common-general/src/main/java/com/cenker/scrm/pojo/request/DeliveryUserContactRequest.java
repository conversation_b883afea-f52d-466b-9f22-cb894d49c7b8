package com.cenker.scrm.pojo.request;

import com.cenker.scrm.pojo.valid.SelectGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2022/10/28
 * @Description
 */
@Data
public class DeliveryUserContactRequest {

    @NotBlank(message = "活码id为空",groups = {SelectGroup.class})
    private String codeId;
    /**
     * 配送员id
     */
    private Long deliveryUserId;
    private String userId;
    /**
     * 活码刷新间隔（秒）
     */
    private Integer refreshRate;

    private Boolean getCode;
}
