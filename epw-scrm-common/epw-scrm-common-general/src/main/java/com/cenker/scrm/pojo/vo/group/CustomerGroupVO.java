package com.cenker.scrm.pojo.vo.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 企业微信客户群对象 tb_wx_customer_group
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CustomerGroupVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * chatId
     */
    private String chatId;

    private String joinScene;

    /**
     * 群名
     */
    private String groupName;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群主userId
     */
    private String owner;

    /**
     * 群主名称
     */
    private String ownerName;
    /**
     * 0 - 正常;1 - 跟进人离职;2 - 离职继承中;3 - 离职继承完成
     */
    private Integer status;

    /**
     * 群解散时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dismissDate;
    /**
     * 成员数据量
     */
    private int memberCnt;

    /**
     * 创建时间 格式 yyyy-MM-dd创建
     */
    private String createTimeStr;
    /**
     * 今日加群
     */
    private Integer todayJoinCnt;
    /**
     * 今日退群
     */
    private Integer todayDepartureCnt;
    /**
     * 客户数
     */
    private Integer customerCnt;
    /**
     * 非客户数
     */
    private Integer unCustomerCnt;
    /**
     * 员工数
     */
    private Integer staffCnt;
    /**
     * 群头像
     */
    private List<String> groupHeadImg;
    /**
     * 共同群聊
     */
    private Boolean commonGroup;
}