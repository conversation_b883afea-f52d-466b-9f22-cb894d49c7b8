package com.cenker.scrm.pojo.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 通知公告表 sys_notice
 *
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class SysNotice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    @TableId(type = IdType.AUTO)
    private Long noticeId;

    /**
     * 公告标题
     */
    @NotBlank(message = "公告标题不能为空")
    @Size(min = 0, max = 30, message = "公告标题不能超过30个字符")
    private String noticeTitle;

    /**
     * 公告类型，1-公告，2-站内信
     */
    private Integer noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态，0-未发布，1-待发布，2-已发布，3-已撤回
     */
    private Integer status;

    private String corpId;

    @TableLogic
    private Integer delFlag;

    /**
     * 是否通知全部用户，0-否，1-是
     */
    private Integer isNoticeAllUser;

    /**
     * 通知员工名单，记录sys_user表的user_id，多个以英文逗号分隔
     */
    private String noticeUser;

    /**
     * 通知方式，1-管理后台，2-企微端，多个以英文逗号分隔
     */
    private String noticeMode;

    /**
     * 是否定时发送，0-否，1-是
     */
    private Integer timedTask;

    /**
     * 定时发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 业务类型：condition 条件 SOP、journeysop 旅程 SOP、
     * oneseparateone 1V1SOP、masscustomer 1V1 群发、
     * sendmoments 发朋友圈、groupToGroup 社群群发、
     * intelligentmaterials_enterprise 智能物料、
     * intelligentmaterials_person 个人智能物料、
     * material 营销素材
     */
    private String businessType;

    private String businessId;

    private String businessOper;

    private String businessJson;

}
