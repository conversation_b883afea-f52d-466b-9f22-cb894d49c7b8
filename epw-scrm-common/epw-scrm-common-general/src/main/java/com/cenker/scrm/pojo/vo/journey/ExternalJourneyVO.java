package com.cenker.scrm.pojo.vo.journey;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/28
 * @Description 客户旅程视图
 */
@Data
public class ExternalJourneyVO {
    /**
     * 旅程id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long journeyId;
    /**
     * 旅程名
     */
    private String journeyName;
    /**
     * 阶段
     */
    private List<ExternalJourneyStageVO> stages;

    /**
     * 是否自动化 0 否 1是
     */
    private Boolean automation;

    /**
     * sopId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sopId;
}
