package com.cenker.scrm.pojo.dto.fund;

/**
 * 币种
 */
public enum EnumCurrency {
    /**
     * 人民币
     */
    CNY("156"),

    /**
     * 美元
     */
    USA("840"),

    /**
     * 港币
     */
    HK("344");
    private final String value;

    EnumCurrency(String value) {this.value = value;}

    public String getValue() {
        return value;
    }

    public static String getCurrencyDesc(String currency){
        if (EnumCurrency.CNY.getValue().equals(currency)) {
            return "元";
        }
        if (EnumCurrency.USA.getValue().equals(currency)) {
            return "美元";
        }
        return null;
    }

    public static EnumCurrency parse(String value) {
        for (EnumCurrency currency : EnumCurrency.values()) {
            if (currency.getValue().equals(value)) {
                return currency;
            }
        }
        throw new IllegalArgumentException("币种代码错误");
    }
}
