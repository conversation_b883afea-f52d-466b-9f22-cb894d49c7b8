package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * 朋友圈消息实体类，用于同步来自企业微信的朋友圈消息。
 * 本实体包含朋友圈消息的基本信息，如创建者、内容、媒体文件等，
 * 以及消息的可见范围和地理位置信息（如果有）。
 * */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMoment implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 朋友圈ID，唯一标识每一条朋友圈消息 */
    private String momentId;

    /** 朋友圈创建者的userid，标识消息的发送者 */
    private String creator;

    /** 朋友圈创建来源，0：企业发表，1：个人发表 */
    private Integer createType;

    /** 可见范围类型，0：部分可见，1：公开 */
    private Integer visibleType;

    /** 朋友圈的文本内容 */
    private String content;

    /** 图片文件ID的JSON数组，表示一条朋友圈可能包含的多个图片 */
    private String imageIds;

    /** 图片文件URL的JSON数组，与mediaIds一一对应 */
    private String imageUrls;

    /** 视频媒体ID，如果朋友圈包含视频内容，则此字段非空 */
    private String videoMediaId;

    /** 视频URL */
    private String videoUrl;

    /** 视频缩略图媒体ID，与视频配套使用 */
    private String videoThumbMediaId;

    /** 视频缩略图URL */
    private String videoThumbUrl;

    /** 朋友圈包含的链接标题，如果有的话 */
    private String linkTitle;

    /** 朋友圈包含的链接URL */
    private String linkUrl;

    /** 地理位置的纬度，如果朋友圈包含地理位置信息 */
    private String locationLatitude;

    /** 地理位置的经度 */
    private String locationLongitude;

    /** 地理位置的名称 */
    private String locationName;

    /** 企业ID，用于区分不同企业的朋友圈数据 */
    private String corpId;

    /** 记录创建者 */
    private String createBy;

    /** 记录更新者 */
    private String updateBy;

    /** 朋友圈消息的创建时间，Unix时间戳单位为秒 */
    private Long momentCreateTime;

    /** 朋友圈消息的创建时间，转换为Date类型 */
    private Date momentCreateTimeDate;

    /** 记录的创建时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 记录的更新时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 评论数
     */
    private Integer commentCnt;
    /**
     * 点赞数
     */
    private Integer likeCnt;

    public TbWxMoment(String momentId,
                      String creator,
                      Long momentCreateTime,
                      Integer createType,
                      Integer visibleType,
                      String content,
                      String mediaIds,
                      String videoMediaId,
                      String videoThumbMediaId,
                      String linkTitle,
                      String linkUrl,
                      String locationLatitude,
                      String locationLongitude,
                      String locationName,
                      String corpId,
                      String createBy,
                      String imagesUrl,
                      String videoUrl,
                      String videoThumbUrl
                      ) {
        this.momentId = momentId;
        this.creator = creator;
        this.momentCreateTime = momentCreateTime;
        this.createType = createType;
        this.visibleType = visibleType;
        this.content = content;
        this.imageIds = mediaIds;
        this.videoMediaId = videoMediaId;
        this.videoThumbMediaId = videoThumbMediaId;
        this.linkTitle = linkTitle;
        this.linkUrl = linkUrl;
        this.locationLatitude = locationLatitude;
        this.locationLongitude = locationLongitude;
        this.locationName = locationName;
        this.corpId = corpId;
        this.createBy = createBy;
        this.imageUrls = imagesUrl;
        this.videoUrl = videoUrl;
        this.videoThumbUrl = videoThumbUrl;
        this.momentCreateTimeDate = new Date(momentCreateTime * 1000);
        this.createTime = new Date();
        this.setCommentCnt(0);
        this.setLikeCnt(0);
    }
}
