package com.cenker.scrm.pojo.dto.kf;

import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgItemCommonResp;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Date;

@Data
@AllArgsConstructor
public class WkWxKfMsgChatDTO {
    private FromInfo fromInfo;
    private Date msgTime; // 消息发送时间
    private String msgType; // 消息类型
    private WkWxKfMsgItemCommonResp typeMsg; // 不同类型消息详细信息
    private String action; // 消息发送状态

    @Data
    @AllArgsConstructor
    public static class FromInfo {
        private String name; // 消息发送者
    }
}

