package com.cenker.scrm.pojo.vo.session;

import com.cenker.scrm.pojo.dto.session.UserDto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 超时设置信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionTimeOutInfoVO
{
    private static final long serialVersionUID = 1L;


    /** 设置ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long setId;

    /** 超时时长单位分钟 */
    private Long timeNum;

    /** 检测范围列表 */
    private List<UserDto> userConditionList;

    private String startTime;
    private String endTime;

}
