package com.cenker.scrm.pojo.dto.system;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 物料制作定时任务
 * @TableName bu_material_job
 */
@Data
public class BuMaterialJobAddDto implements Serializable {

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String jobName;

    /**
     * 应用类型
     * 微信公众号：WX_PUBLIC_ACCOUNT
     * 微信小程序：WX_MINI_PROGRAM
     * 物料库：MATERIAL_LIBRARY
     */
    @Pattern(regexp = "^(WX_PUBLIC_ACCOUNT|WX_MINI_PROGRAM|MATERIAL_LIBRARY)$", message = "应用类型取值不正确")
    private String appType;

    /**
     * 应用配置ID，对应sys_app_config表的主键
     */
    @NotNull(message = "应用不能为空")
    private Long appConfigId;

    /**
     * 匹配方式，最近一条：latest，模糊匹配：keyword
     */
    @Pattern(regexp = "^(LATEST|KEYWORD)$", message = "匹配方式取值不正确")
    private String matchMethod;

    /**
     * 模糊匹配关键词
     */
    private String keyword;

    /**
     * 任务执行时间，格式：HH:mm
     */
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "任务执行时间格式不正确")
    private String jobTime;

    /**
     * 通知成员，员工企微账号id，多个以逗号分隔
     */
    private String noticeUser;

    /**
     * 生成物料数
     */
    @NotNull(message = "生成物料数不能为空")
    private Integer materialNumber;

    /**
     * 物料分类 1 企业物料 2 个人物料
     */
    @NotNull(message = "物料分类不能为空")
    private Integer materialScope;

    /**
     * 物料标题命名规则
     */
    private String namingRule;

    /**
     * 物料分组ID
     */
    private Long materialCategoryId;

    /**
     * 物料类型 1 图文 2 链接 3 PDF
     */
    @NotNull(message = "物料类型不能为空")
    private Integer materialType;

    /**
     * 获取方式：auto 自动获取，customize 自定义
     */
    @Pattern(regexp = "^(AUTO|CUSTOMIZE)$", message = "卡片信息获取方式取值不正确")
    private String acquisitionMethod;

    /**
     * 自定义卡片信息
     */
    private String customInfo;

    private static final long serialVersionUID = 1L;
}