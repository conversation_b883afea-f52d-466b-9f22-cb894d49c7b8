package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户业务数据表
 * @TableName v_f_scrm_label
 */
@TableName(value ="v_f_scrm_label")
@Data
public class VFScrmLabel implements Serializable {
    /**
     * 客户号
     */
    @TableId
    private Integer custno;

    /**
     * 性别
     */
    private String cGenName;

    /**
     * 年龄段
     */
    private String cAgeStra;

    /**
     * 生日
     */
    private String cBthDate;

    /**
     * 城市
     */
    private String cCityName;

    /**
     * 省份
     */
    private String cProvName;

    /**
     * 职业
     */
    private String cOccoName;

    /**
     * 是否直销
     */
    private String cIfDsCust;

    /**
     * 是否代销
     */
    private String cIfCsCust;

    /**
     * 是否投顾
     */
    private String cIfIaCustRul;

    /**
     * 当日资产金额
     */
    private String cDayHldAstStra;

    /**
     * 近一年日均资产
     */
    private String cL1yAvgHldAstStra;

    /**
     * 近三年日均资产
     */
    private String cL3yAvgHldAstStra;

    /**
     * 开户日期
     */
    private String cCustOpenDate;

    /**
     * 风险测评等级
     */
    private String cRiskTypeName;

    /**
     * 近一个年内平均一支基金交易次数
     */
    private Integer fL1yTracnt;

    /**
     * 是否定投
     */
    private String cL3mIfRfap;

    /**
     * 最近一次定投行为的时间
     */
    private String cLastRfapCfmDate;

    /**
     * 历史以来累计收益
     */
    private BigDecimal fHisTtlCfPrft;

    /**
     * 近三年累计收益
     */
    private BigDecimal fL3yTtlCfPrft;

    /**
     * R1仓位比例
     */
    private BigDecimal fR1AstRatio;

    /**
     * R2+R3仓位比例
     */
    private BigDecimal fR23AstRatio;

    /**
     * R4+R5仓位比例
     */
    private BigDecimal fR45AstRatio;

    /**
     * 仓位标签
     */
    private String cAstrRatioLabel;

    /**
     * 是否贵宾客户
     */
    private String cIfVipCust;

    /**
     * 历史以来盈利情况
     */
    private String cHisTtlCfPrftLabel;

    /**
     * 近三年盈利情况
     */
    private String cL3yTtlCfPrftLabel;

    /**
     * 电话客服话务次数
     */
    private Integer fMobileCnt;

    /**
     * 是否投诉客户
     */
    private String cIfComplaintCust;

    /**
     * 是否免打扰客户
     */
    private String cIfNotdisturbCust;

    /**
     * 日期
     */
    private String cDate;

    /**
     * 持仓产品
     */
    private String cDayTtlHldFund;

    /**
     * 货币型基金占比
     */
    private BigDecimal fMmfAstRatio;

    /**
     * 债券型基金占比
     */
    private BigDecimal fBondAstRatio;

    /**
     * 股票型基金占比
     */
    private BigDecimal fEquityAstRatio;

    /**
     * 混合型基金占比
     */
    private BigDecimal fBlendAstRatio;

    /**
     * FOF基金占比
     */
    private BigDecimal fFofAstRatio;

    /**
     * 是否有过Y份额持仓
     */
    private String cIfYShareCust;

    /**
     * 是否有过非Y份额养老基金持仓
     */
    private String cIfNotYPensCust;

    /**
     * 是否有过ETF基金持仓
     */
    private String cIfEtfCust;

    /**
     * 是否有过ETF联接基金持仓
     */
    private String cIfEtfCnetCust;

    /**
     * 是否有过普通指数基金持仓
     */
    private String cIfIndexCust;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}