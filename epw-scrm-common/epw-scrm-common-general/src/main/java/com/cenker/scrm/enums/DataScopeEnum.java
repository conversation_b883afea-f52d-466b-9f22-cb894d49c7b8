package com.cenker.scrm.enums;

import lombok.Getter;

/**
 * 数据权限枚举类
 */
@Getter
public enum DataScopeEnum {
    ALL("1", "全部数据"),

    CUSTOM("2", "自定义"),

    CURRENT_DEPARTMENT("3", "本部门数据"),

    DEPARTMENT_AND_CHILD("4", "部门及子部门数据"),

    SELF("5", "个人数据");

    private String value;

    private String desc;

    DataScopeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
