package com.cenker.scrm.pojo.vo.radar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料应答消息VO类
 *
 * <AUTHOR>
 */
@Data
public class MaterialResponseVO {

    /** 消息编号，发送方全局唯一 */
    @JsonProperty("msg_id")
    private String msgId;

    /** 消息类型，固定填写"001" */
    @JsonProperty("msg_type")
    private String msgType;

    /** 消息版本号，当前填写"V1.3" */
    @JsonProperty("msg_version")
    private String msgVersion;

    /** 消息创建时间 */
    @JsonProperty("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 物料名称 */
    @JsonProperty("material_name")
    private String materialName;

    /** 物料唯一性标识 */
    @JsonProperty("material_id")
    private String materialId;

    /** 对应物料发布通知的消息编号 */
    @JsonProperty("origin_msg_id")
    private String originMsgId;
}

