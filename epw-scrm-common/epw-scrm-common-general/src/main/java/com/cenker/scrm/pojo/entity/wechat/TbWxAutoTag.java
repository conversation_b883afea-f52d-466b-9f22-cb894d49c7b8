package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.vo.TagVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
/**
 * 自动打标签 tb_wx_corp_tag_group
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxAutoTag implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long autoTagId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 条件
     */
    private String tagCondition;

    /**
     * 标签
     */
    private String tag;

    private Long createBy;

    /**
     * 创建时间
     */
    // @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    // @ApiModelProperty("更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    // @ApiModelProperty("备注")
    private String remark;

    /**
     * 帐号状态（0正常 2停用）
     */
    private String status;

    /**
     * 次序值
     */
    @TableField(value = "`order`")
    private Integer order;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 0 标识未删除 1 标识删除 '
     */
    private Integer delFlag;


    @TableField(exist = false)
    private List<TagVO> tagDetailLists;

    @TableField(exist = false)
    private String createByName;

    /**
     * 是否为满足条件的客户重新打标签
     * 默认：false
     */
    @TableField(exist = false)
    private Boolean isRetry = false;

}
