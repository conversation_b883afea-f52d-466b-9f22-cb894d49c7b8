package com.cenker.scrm.pojo.entity.wechat.sop;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description sop基本信息表
 */
@TableName(value = "ck_sop_info",autoResultMap = true)
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class SopInfo {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * sop名称
     */
    private String sopName;

    /**
     * sop类型 1 条件sop 2 旅程sop 3 1V1sop 4 社群sop
     */
    private Integer sopType;

    /**
     * 筛选发送条件
     */
    private String sendCondition;

    private String viewSendCondition;

    /**
     * 状态
     */
    @TableField(value = "is_alive")
    private String alive;

    /**
     * xxl-job任务id
     */
    private Long jobId;

    /**
     * 旅程id
     */
    private Long journeyId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 部门id
     */
    private Integer deptId;
    /**
     * 启用了审批
     * true:启用审批
     * false:未启用审批
     */
    private boolean enableApproval;
    /**
     * 审批人账号
     */
    private String approvalUser;
    /**
     * 意见
     */
    private String approvalRemark;
}
