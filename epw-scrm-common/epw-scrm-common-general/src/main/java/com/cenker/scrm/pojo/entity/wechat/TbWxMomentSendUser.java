package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentSendUser {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 发朋友圈表id
   */
  private String momentTaskId;
  /**
   * 发送者id
   */
  private String userId;
  /**
   * 消息发送状态 0:未发表 1：已发表
   */
  private Integer publishStatus;
  /**
   * 企业id
   */
  private String corpId;

}
