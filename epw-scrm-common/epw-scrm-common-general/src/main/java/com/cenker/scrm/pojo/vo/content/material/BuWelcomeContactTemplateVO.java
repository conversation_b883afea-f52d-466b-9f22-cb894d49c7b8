package com.cenker.scrm.pojo.vo.content.material;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 欢迎语模板对象 bu_welcome_contact_template
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
 @Data
public class BuWelcomeContactTemplateVO implements Serializable{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 企业ID */
    private String corpId;

    /** 欢迎语模板名称 */
    private String name;

    /** 欢迎语 */
    private String content;

    /** 欢迎语附件 */
    private List<WelcomeAttachmentVo> attachments;

    /** 是否删除：1：删除，0:正常 */
    private Integer delFlag;

    /** 状态：1:启用，0:停用 */
    private Integer status;

    /**
     * 创建者
     */
    private String createByName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateByName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
