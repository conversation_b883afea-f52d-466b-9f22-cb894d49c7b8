package com.cenker.scrm.constants;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {


    /**
     * 环境类型：生产环境
     */
    public static final String ENV_PROD = "prod";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 30分钟内最大重登录次数
     */
    public static final int MAX_RELOGIN_TIMES = 5;

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 默认用户
     */
    public static final String DEFAULT_USER = "SYS";

    /**
     * 默认昵称
     */
    public static final String DEFAULT_NICK = "AiMi";
    /**
     * 默认密码
     */
    public static final String DEFAULT_PWD = "AiMi@2021";

    /**
     * 外部联系人Service标识
     */
    public static final int WX_APP_EXT_CUSTOMER = 10008;

    /**
     * 通讯录Service标识
     */
    public static final int WX_APP_CORP_INFO = 10006;

    /**
     * 通讯录Service标识
     */
    public static final int WX_APP_INFO = 10007;

    public static final String AGENT_ID = "agentId";

    public static final String SOURCE_TH = "1";

    public static final String SOURCE_CP = "2";

    public static final String TAG = "tag";

    public static final String TAG_GROUP = "tag_group";

    public static final String YES = "Y";

    public static final String NO = "N";

    public static final String CORP = "CORP";

    public static final String CUSTOMER = "CUSTOMER";

    public static final String ALLOCATED = "0";

    public static final String ALLOCAT_ING = "2";

    public static final String ALLOCAT_FAIL = "3";

    public static final String CHAT_TYPE_SINGLE = "single";

    public static final String CHAT_TYPE_GROUP = "group";

    public static final String STAFF = "1";

    public static final String DEPT = "2";

    public static final String IN_TAG = "3";

    public static final String NOT_IN_TAG = "4";

    public static final String WAIT = "0";

    public static final String SENDED = "1";

    public static final String WX_MASS_MSG_WAIT = "0";

    public static final String WX_MASS_MSG_SUCCESS = "1";

    public static final String WX_MASS_MSG_FAIL = "2";

    /**
     * 范围 0全部 1 筛选
     */
    public static final int DATA_PUSH_RANGE_ALL = 0;
    public static final int DATA_PUSH_RANGE_PART = 1;

    /**
     * 来源：企业微信
     */
    public static final String SOURCE_QW = "1";

    /**
     * 来源：智能物料
     */
    public static final String SOURCE_MATERIAL = "2";

    /**
     * 跟进动作：1表示新增，2表示修改，3表示删除，4表示查看
     */
    public static final String FOLLOW_TYPE_ADD= "1";
    public static final String FOLLOW_TYPE_UPDATE = "2";
    public static final String FOLLOW_TYPE_DELETE = "3";
    public static final String FOLLOW_TYPE_VIEW = "4";

    // 系统用户类型：00系统用户，01企业用户
    public static final String SYS_USER_TYPE_QW = "01";

    // 获取JsapiTicket类型，1表示通讯录，2表示外部联系人
    public static final String JSAPI_TICKET_TYPE_EXT_USER = "1";
    public static final String JSAPI_TICKET_TYPE_BOOK = "2";
    public static final String JSAPI_TICKET_TYPE_APP = "3";

    /**
     * 请求追踪ID
     */
    public static final String TRACE_ID = "traceId";

    /**
     * 企微智能物料
     */
    public static final Integer RADAR_SCOPE_ENTERPRISE = Integer.valueOf(1);
    /**
     * 企微个人物料
     */
    public static final Integer RADAR_SCOPE_PERSONAL = Integer.valueOf(2);
}
