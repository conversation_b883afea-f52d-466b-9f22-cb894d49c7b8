package com.cenker.scrm.pojo.entity.wechat.kf;

import lombok.Data;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

import java.sql.Date;
import java.time.LocalDate;

/**
 * 微信客服 聊天记录 listMsg 共用字段 对象
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
public class WkWxKfMsgItemCommonResp {

    private Long id; // 主键 id
    private String msgId;
    private Date sendTime;
    private Date createTime;

    public WkWxKfMsgItemCommonResp init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        this.msgId = msgItem.getMsgId();
        this.sendTime = new Date(msgItem.getSendTime()); // 时间戳转换为Date日期对象
        this.createTime = Date.valueOf(LocalDate.now()); // 创建时间

        return this;
    }
}
