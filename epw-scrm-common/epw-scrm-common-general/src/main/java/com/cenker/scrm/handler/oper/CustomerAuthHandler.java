package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;

/**
 * 客户认证
 */
public class CustomerAuthHandler extends DefaultBuOperTrackHandler {
    public CustomerAuthHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        return eventType.getContent();
    }
}
