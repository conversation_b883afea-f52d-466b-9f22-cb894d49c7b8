package com.cenker.scrm.pojo.vo.fission;

import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/25
 * @Description
 */
@Data
@Builder
public class TaskFissionProgressVO {
    // 总数
    private Integer total;
    // 完成
    private Integer completed;
    private List<TbWxExtCustomer> customers;

    private String rewardUrl;
    private String rewardImageUrl;
    private String rewardRule;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
