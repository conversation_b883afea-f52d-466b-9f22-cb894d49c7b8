package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbDcUserTags implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键。
     */
    @TableId(type = IdType.AUTO)
    private int id;

    /**
     * 一级分类。
     */
    private String primaryCategory;

    /**
     * 二级分类。
     */
    private String secondaryCategory;

    /**
     * 标签名称。
     */
    private String tagName;

    /**
     * 前端排序字段
     */
    private String displayOrder;

    /**
     * 画像是否展示，默认不展示
     */
    private Boolean isShow;

    /**
     * 记录的创建者。
     */
    private String createBy;

    /**
     * 记录的创建时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 记录的更新者。
     */
    private String updateBy;

    /**
     * 记录的更新时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 引用的数据中心表的对应列名
     */
    private String referenceColumn;

    /**
     * 数据格式化类型
     */
    private String dataFormatType;

    private String dictType;

    /**
     * 标签来源：1 企微，2 数据中心
     */
    private Integer tagSource;


    public TbDcUserTags(int id, boolean show) {
        this.id = id;
        this.isShow = show;
        this.updateTime = new Date();
    }
}