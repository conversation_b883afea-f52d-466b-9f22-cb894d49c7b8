package com.cenker.scrm.pojo.entity.statistic;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

/**
 * 数据统计-客户数据-服务员工明细
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_customer_detail")
public class TbStatisticCustomerDetail {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据统计-客户数据id
     */
    private Long statisticCustomerId;

    /**
     * 员工UserID
     */
    private String userid;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 头像
     */
    private String thumbAvatar;

    /**
     * 部门id
     */
    private Integer deptId;

}
