package com.cenker.scrm.pojo.entity.open.store;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/5/25
 * @Description 订单商品
 */
@Data
public class AppOrderGoods {
    @NotBlank(message = "invalid goodName")
    private String goodName;
    @NotBlank(message = "invalid cover")
    private String cover;
    @NotBlank(message = "invalid goodPrice;")
    private String goodPrice;
    @NotNull(message = "invalid goodNum")
    private Integer goodNum;
    /**
     * 商品描述
     */
    private String goodDesc;
}
