package com.cenker.scrm.pojo.dto.group;

import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2023/8/23
 * @Description
 */
@Data
public class GroupCodeConfigDTO {

    @NotBlank(message = "群id不能为空",groups = {InsertGroup.class, UpdateGroup.class})
    private String chatId;

    /**
     * 群名
     */
    private String chatGroupName;

    /**
     * 群主名
     */
    private String chatOwnerName;

    /**
     * 群主id
     */
    private String chatOwnerUserId;

    /**
     * 群人数
     */
    private Integer chatMemberCnt;
}
