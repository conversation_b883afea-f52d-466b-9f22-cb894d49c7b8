package com.cenker.scrm.pojo.vo.sitecontact;

import com.cenker.scrm.pojo.vo.UserVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/25
 * @Description 回显编辑配送员视图
 */
@Data
public class ContactDeliveryUserEditVO {
    private String id;
    private String siteId;
    private Long storeId;
    private String signId;
    private Integer userStatus;
    private String areaName;
    /**
     * 所在站点
     */
    private String siteName;
    /**
     * 所属门店名
     */
    private String storeName;
    private List<UserVO> userList;
}
