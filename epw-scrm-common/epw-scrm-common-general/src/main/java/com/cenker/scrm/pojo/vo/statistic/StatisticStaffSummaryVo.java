package com.cenker.scrm.pojo.vo.statistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * 数据统计-员工数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
public class StatisticStaffSummaryVo {

    /**
     * 统计日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String statisticDate;
    /**
     *总员工数
     */
    private Integer staffTotal;
    /**
     *被删除/拉黑次数
     */
    private Integer delTotal;
    /**
     *单聊发消息数量
     */
    private Integer aloneChatTotal;
    /**
     *及时回复聊天占比(%)
     */
    private BigDecimal chatTimelyRate;
    /**
     *平均回复时长(S)
     */
    private Integer averageReplyTime;
    /**
     *员工加入群聊总数
     */
    private Integer staffGroupJoinTotal;
    /**
     *员工群聊发消息总数
     */
    private Integer staffGroupChatTotal;

}
