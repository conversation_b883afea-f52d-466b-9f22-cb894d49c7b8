package com.cenker.scrm.pojo.entity.wechat.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseDbEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;

/**
 * @Description 社群活码表
 * <AUTHOR>
 * @Date 2023-08-22 
 */
@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("ck_group_code_info")
public class GroupCodeInfo extends BaseDbEntity implements Serializable {
	private static final long serialVersionUID =  7240587217255671434L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 活码名称
	 */
	private String codeName;

	/**
	 * 活码类型 1 顺序入群 2 分类入群
	 */
	private Integer codeType;

	/**
	 * 群活码Url
	 */
	private String groupCodeUrl;

	private String state;
	/**
	 * 是否自动创建新群 0 否 1 是
	 */
	@TableField(value = "is_auto_create_room")
	private Boolean autoCreateRoom;

	/**
	 * 自动建群新群名称 当is_auto_create_room为1时有效 最长40个utf8字符
	 */
	private String roomBaseName;

	/**
	 * 自动建群的群起始序号
	 */
	private Long roomBaseId;

	/**
	 * 备用员工关联活码id
	 */
	private Long spareCodeId;

	/**
	 * 备用员工活码/群码地址
	 */
	private String spareCodeUrl;

	/**
	 * 用于存储社群活码自动打标签的标签内容json格式
	 */
	private String tag;

	/**
	 * 使用状态，1：使用中，0：已废弃
	 */
	private Integer useStatus;

	/**
	 * 部门id
	 */
	private Integer deptId;
}

