package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智能物料标签规则表
 */
@TableName("tb_wx_radar_tag_rule")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TbWxRadarTagRule {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 智能物料id
   */
  private String radarId;
  /**
   * 规则类型 1 点击次数 2 阅读时长 3 转发次数
   */
  private Integer type;
  /**
   * 标签id，逗号分割
   */
  private String tagId;
  /**
   * 规定次数/秒数...
   */
  private String ruleNum;
  private String remark;
  /**
   * 0 未删除 1 已删除
   */
  private Integer delFlag;

  /**
   * 标签名 如果逗号隔开 会导致标签中存在逗号的前端分割出现问题
   */
  @TableField(exist = false)
  private String tagName;

  /**
   * 标签名数组
   */
  @TableField(exist = false)
  private List<String> tagNameList;
}
