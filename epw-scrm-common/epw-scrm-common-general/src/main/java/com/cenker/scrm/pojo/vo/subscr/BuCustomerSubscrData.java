package com.cenker.scrm.pojo.vo.subscr;

import lombok.Data;

import java.util.List;

/**
 * 客户订阅数据对象
 */
@Data
public class BuCustomerSubscrData {
    /**
     * 客户ID
     */
    private String externalUserId;
    /**
     * 订阅菜单id
     */
    private String menuId;

    private List<BuSectionSelect> selectSectionList;
    /**
     * 是否来自微信，来自微信端时，是客户操作，其他情况是顾问操作
     */
    private Boolean fromWechat;

    private String userId;
}
