package com.cenker.scrm.pojo.dto.fission;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/18
 * @Description 统计信息查询
 */
@Data
public class WeTaskFissionStatisticDTO {
    // @ApiModelProper(value = "任务id", required = true)
    @NotNull(message = "不能为空")
    private String taskFissionId;
    // @ApiModelProper(value = "查询最近7天数据，与thirty属性互斥，不使用请设置为false")
    private Boolean seven;
    // @ApiModelProper(value = "查询最近30天数据，与seven属性互斥，不使用请设置为false")
    private Boolean thirty;
    // @ApiModelProper(value = "开始时间")
    private String beginTime;
    // @ApiModelProper(value = "结束时间")
    private String endTime;
    // 1 每日累计 2 每日新增
    @NotNull(message = "不能为空")
    private Integer type;
    /**
     * 记录id
     */
    private String recordId;
    /**
     * 客户名
     */
    private String nickName;
    /**
     * 完成状态 1 已完成 2 进行中
     */
    private Integer status;
}
