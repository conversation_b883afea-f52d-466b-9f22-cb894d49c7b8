package com.cenker.scrm.pojo.entity.ai;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/***
 * @description
 * <AUTHOR>
 * @date 2023/6/8 11:46
 */
@Accessors(chain = true)
@TableName(value = "azure_smart_config",autoResultMap = true)
@Data
public class AzureSmartConfig {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 0 标识未删除 1 标识删除
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创作主键id
     */
    private Long writeId;
    /**
     * 营销目的
     */
    private String market;
    /**
     * 营销目的格式
     */
    private String marketType;
    /**
     * 营销对象
     */
    private String object;
    /**
     * 营销对象的格式
     */
    private String objectType;
    /**
     * 营销内容重点
     */
    private String keyPoint;
    /**
     * 营销内容重点的格式
     */
    private String keyPointType;
    /**
     * 营销受众群体
     */
    private String audience;
    /**
     * 营销受众群体的格式
     */
    private String audienceType;
    /**
     * 语气
     */
    private String tone;
    /**
     * 语气格式
     */
    private String toneType;
    /**
     * 使用表情
     */
    private String expression;
    /**
     * 使用表情格式
     */
    private String expressionType;
    /**
     * 字数
     */
    private Long count;

}

