package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@TableName(value = "ck_ai_repository",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class CkAiRepository {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String repositoryName;
    private String repositoryDescription;
    @DecimalMax(value = "1",message = "温度不能大于1")
    @DecimalMin(value = "0",message = "温度不能小于0")
    private float temperature;
    private String status;
    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;
    private String updateBy;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @TableField(exist = false)
    private List<CkAiUploadFileInfo> uploadFileInfoList;

    @TableField(exist = false)
    private String userName;
}
