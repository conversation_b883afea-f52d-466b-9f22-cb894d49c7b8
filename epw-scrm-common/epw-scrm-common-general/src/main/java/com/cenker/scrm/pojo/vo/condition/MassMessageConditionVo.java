package com.cenker.scrm.pojo.vo.condition;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/16
 * @Description
 */
@Data
@Deprecated
public class MassMessageConditionVo extends ConditionVo {
    /**
     * 发送者列表（指定员工发送）
     */
    // private List<String> senderList;

    /**
     * 添加开始时间
     */
    private String startTime;
    /**
     * 添加结束时间
     */
    private String endTime;

    /**
     * 客户旅程条件
     */
    private List<JourneyConditionVo>journeyList;

    /**
     * 阶段id
     */
    private List<String> stageIdList;

    /**
     * 客户剔除标签列表
     */
    private List<String> removeTagList;

    private String corpId;
}
