package com.cenker.scrm.pojo.vo.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2022/9/7
 * @Description 群发成员发送详情
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MassMessageSenderDetailVo {

    private String userId;
    private String name;
    private String avatar;
    /**
     * 预计送达客户数
     */
    private Integer preSendCustomerCnt;
    /**
     * 已送达客户数
     */
    private Integer sendCustomerCnt;
    /**
     * 送达失败客户数
     */
    private Integer sendFailCustomerCnt;
    /**
     * 送达率
     */
    private Integer sendRate;

    /**
     * 发送时间
     */
    private String sendTime;
}
