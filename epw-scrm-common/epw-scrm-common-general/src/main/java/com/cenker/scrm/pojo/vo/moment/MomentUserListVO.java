package com.cenker.scrm.pojo.vo.moment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 动态相关的用户列表视图对象。
 * 用于封装展示给前端的用户相关信息，如头像、用户名等。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MomentUserListVO implements Serializable {

    /**
     * 用户ID，唯一标识一个用户。
     */
    private String userId;

    /**
     * 用户名，显示用户的名称。
     */
    private String userName;

    /**
     * 用户头像URL，用于在前端显示用户的头像。
     */
    private String avatar;

    /**
     * 用户操作时间，记录用户进行相关操作的时间点。
     */
    private String operateTime;

    /**
     * 1代表点赞，2代表评论。
     */
    private String type;

    /**
     * 公司名称. NULL为外部用户
     */
    private String corpName;

    public MomentUserListVO(String userid, Long createTime, String type, String corpName) {
        this.userId = userid;
        if(createTime != null){
            //将Unix时间戳转换为年月日时分秒字符串
            this.operateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(createTime * 1000));
        }
        this.type = type;
        this.corpName = corpName;
    }
    public MomentUserListVO(String userid, Date createTime, String type, String corpName) {
        this.userId = userid;
        //将时间转换为年月日时分秒字符串
        if(createTime!=null)
        this.operateTime=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createTime);
        this.type = type;
        this.corpName = corpName;
    }
}
