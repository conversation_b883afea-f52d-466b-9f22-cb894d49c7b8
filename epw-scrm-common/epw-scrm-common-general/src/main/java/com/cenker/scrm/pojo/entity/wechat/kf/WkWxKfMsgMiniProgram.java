package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 MiniProgram 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_miniprogram")
public class WkWxKfMsgMiniProgram extends WkWxKfMsgItemCommonResp {

    private String appId;
    @TableField("`title`")
    private String title;
    private String thumbMediaId;
    private String pagePath;

    @Override
    public WkWxKfMsgMiniProgram init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.appId = msgItem.getMiniProgram().getAppId();
        this.title = msgItem.getMiniProgram().getTitle();
        this.thumbMediaId = msgItem.getMiniProgram().getThumbMediaId();
        this.pagePath = msgItem.getMiniProgram().getPagePath();

        return this;
    }
}
