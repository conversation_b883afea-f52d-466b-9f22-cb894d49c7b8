package com.cenker.scrm.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 微信客服常量
 *
 * <AUTHOR>
 * @Date 2023/7/18
 */
public class WkWxKfConstants {
    public static final String WK_KF_TOKEN_PREFIX = "wk.kf.token."; // 微信客服 redis key 前缀
    public static final String WK_KF_CURSOR_PREFIX = "wk.kf.cursor"; // 微信客服 cursorId 自增长
    public static final String WK_KF_MSG_PREFIX = "wk.kf.msg"; // 微信客服 消息Id 自增长
    public static final String WK_KF_MSG_TEXT_PREFIX = "wk.kf.msg.text"; // 微信客服 text消息Id 自增长
    public static final String WK_KF_MSG_MEDIA_PREFIX = "wk.kf.msg.media"; // 微信客服 media消息Id 自增长
    public static final String WK_KF_MSG_BUSINESS_CARD_PREFIX = "wk.kf.msg.businessCard"; // 微信客服 businessCard消息Id 自增长
    public static final String WK_KF_MSG_LINK_PREFIX = "wk.kf.msg.link"; // 微信客服 link消息Id 自增长
    public static final String WK_KF_MSG_MINIPROGRAM_PREFIX = "wk.kf.msg.miniprogram"; // 微信客服 miniprogram消息Id 自增长
    public static final String WK_KF_MSG_CHANNELS_SHOP_PRODUCT_PREFIX = "wk.kf.msg.miniprogram"; // 微信客服 channels_Shop_Product消息Id 自增长
    public static final String WK_KF_MSG_CHANNELS_SHOP_ORDER_PREFIX = "wk.kf.msg.miniprogram"; // 微信客服 channels_shop_order消息Id 自增长
    public static final String WK_KF_MSG_EVENT_PREFIX = "wk.kf.msg.event"; // 微信客服 event消息Id 自增长
    public static final String WK_KF_SESSION_PREFIX = "wk.kf.session"; // 微信客服 session 自增长

    public static final String WK_KF_SESSION_CHANNEL = "微信客服"; // 微信客服 channel 默认值
    public static final String WK_KF_SESSION_STATUS_SERVICER = "人工接入"; // 微信客服 channel 默认值
    public static final String WK_KF_SESSION_STATUS_END = "已结束"; // 微信客服 channel 默认值
    public static final String WK_KF_SESSION_STATUS_NOT_HANDLE = "新接入未处理"; // 微信客服 channel 默认值

    public static final Integer LIMIT = 1000; // limit 期望请求的数据量，默认值和最大值都为1000。
    public static final Integer VOICE_FORMAT = 0;  // voiceFormat 语音消息类型，0-Amr 1-Silk，默认0。

    public static final String OPEN_KF_ID_KEY = "OpenKfId";  // 回调事件消息: 客服 id key
    public static final String TOKEN_KEY = "Token"; // 回调事件消息: token key

    public static final List<String> MEDIA_TYPE_LIST = new ArrayList<>(Arrays.asList("image", "voice", "video", "file"));

    public static final String EVENT_TYPE_ENTER_SESSION = "enter_session"; // 用户进入会话事件
    public static final String EVENT_TYPE_MSG_SEND_FAIL = "msg_send_fail"; // 消息发送失败事件
    public static final String EVENT_TYPE_SERVICER_STATUS_CHANGE = "servicer_status_change"; // 接待人员接待状态变更事件
    public static final String EVENT_TYPE_SESSION_STATUS_CHANGE = "session_status_change"; // 会话状态变更事件
    public static final String EVENT_TYPE_USER_RECALL_MSG = "user_recall_msg"; // 用户撤回消息事件
    public static final String EVENT_TYPE_SERVICER_RECALL_MSG = "servicer_recall_msg"; // 接待人员撤回消息事件
    public static final String EVENT_TYPE_REJECT_CUSTOMER_MSG_SWITCH_CHANGE = "reject_customer_msg_switch_change"; // 拒收客户消息变更事件

}
