package com.cenker.scrm.pojo.dto.system;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 物料制作定时任务执行操作Dto
 *
 */
@Data
public class BuMaterialJobRunDto implements Serializable {

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String id;

    /**
     * 任务名称
     */
    private String jobName;

    private static final long serialVersionUID = 1L;
}