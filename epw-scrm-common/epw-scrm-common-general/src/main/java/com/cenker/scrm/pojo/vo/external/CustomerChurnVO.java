package com.cenker.scrm.pojo.vo.external;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * 流失客户页面展示VO
 */
@Data
public class CustomerChurnVO implements Serializable {
    /**
     * 外部客户编号
     */
    private String extUserId;
    /**
     * 客户名称
     */
    // @Excel(name = "客户名称")
    private String custName;
    /**
     * 添加人
     */
    // @Excel(name = "添加人")
    private String corpUserName;
    /**
     * 标签
     */
    // @Excel(name = "客户标签")
    private String tag;
    /**
     * 流失时间
     */
    // @Excel(name = "流失时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date churnDate;

    /**
     * 流失状态，2表示客户删除员工，3表示员工删除客户
     */
    private String churnStatus;
    /**
     * 1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
     */
    // @Excel(name = "客户名称", readConverterExp = "1=微信用户, 2=企业微信用户)")
    private String type;

    // @Excel(name = "性别", readConverterExp = "0=未知, 1=男性, 2=女性)")
    private String gender;

    // @Excel(name = "客户企业简称")
    private String corpName;

    /**
     * 客户生日
     */
    // @Excel(name = "客户生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    private String avatar;

    // @Excel(name = "客户职位")
    private String position;

    // @Excel(name = "客户企业全称")
    private String corpFullName;

    // @Excel(name = "添加时间")
    private Date createTime;
}
