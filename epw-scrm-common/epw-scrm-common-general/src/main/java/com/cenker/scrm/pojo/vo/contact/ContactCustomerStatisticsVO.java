package com.cenker.scrm.pojo.vo.contact;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/29
 * @Description 渠道活码客户数据
 */
@Data
public class ContactCustomerStatisticsVO {
    /**
     * 客户名
     */
    private String name;
    /**
     * 客户头像
     */
    private String avatar;
    /**
     * 客户id
     */
    private String externalUserId;
    /**
     * 类型 1 微信 2 企业微信
     */
    private Integer type;
    /**
     * 企业名称 当 type = 2 时返回
     */
    private String corpName;
    /**
     * 所属员工名
     */
    private String staffName;
    /**
     * 0 正常 2 流失
     */
    private Integer status;
    /**
     * 0 正常 2 完全流失
     */
    private Integer completeDel;
    /**
     * 添加时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
