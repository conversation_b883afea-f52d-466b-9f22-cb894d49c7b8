package com.cenker.scrm.pojo.entity.chatarchive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话记录基础记录对象 wk_chat_archive_relation
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@TableName(value = "wk_chat_archive_relation",autoResultMap = true)
public class WkChatArchiveRelation implements Serializable {

    private static final long serialVersionUID = -2703539538665678846L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 企业ID */
    private String corpId;

    /** 群聊类型，0表示未知，1表示单聊，2表示群聊 */
    private Integer chatType;

    /** 发送方Id，同一企业内容为userid，非相同企业为external_userid。消息如果是机器人发出，也为external_userid */
    private String fromId;

    /** 群聊消息，则是群ID */
    private String roomId;

    /** 消息接受者ID */
    private String consumeId;

    /** 群聊消息接受者ID */
    private String roomConsumeId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
