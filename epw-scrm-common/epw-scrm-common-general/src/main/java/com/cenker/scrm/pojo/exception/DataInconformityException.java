package com.cenker.scrm.pojo.exception;

/**
 * <AUTHOR>
 * @Date 2022/6/28
 * @Description 数据不一致
 */
public class DataInconformityException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected String message;

    private Integer code;

    public DataInconformityException() {
    }

    public DataInconformityException(String message)
    {
        this.message = message;
    }

    public DataInconformityException(Integer code, String message)
    {
        this.code=code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
