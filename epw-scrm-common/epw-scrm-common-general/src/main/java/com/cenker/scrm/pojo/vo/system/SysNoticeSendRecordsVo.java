package com.cenker.scrm.pojo.vo.system;

import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统通知发送记录对象 sys_notice_send_records
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
public class SysNoticeSendRecordsVo implements Serializable {
    private static final long serialVersionUID = -8517920147672081308L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公告ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long noticeId;
    /**
     * 公告类型，1-公告，2-站内信
     */
    private Integer noticeType;

    /**
     * 通知标题
     */
    private String noticeTitle;
    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 接收通知的用户Id，记录sys_user表的user_id
     */
    private String userId;

    /**
     * 通知方式，1-管理后台，2-企微端
     */
    private Integer noticeMode;

    /**
     * 是否已读，0-未读，1-已读
     */
    private Integer readStatus;

    /**
     * 已读时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /**
     * 应用消息id
     */
    private String msgId;

    /**
     * 是否撤回，0-标识未撤回 1-标识撤回
     */
    private Integer delFlag;

    /**
     * 撤回时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date delTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 是否已提醒，0-未提醒，1-已提醒 */
    private String hasReminder;

    /**
     * 业务类型：condition 条件 SOP、journeysop 旅程 SOP、
     * oneseparateone 1V1SOP、masscustomer 1V1 群发、
     * sendmoments 发朋友圈、groupToGroup 社群群发、
     * intelligentmaterials_enterprise 智能物料、
     * intelligentmaterials_person 个人智能物料、
     * material 营销素材
     */
    private String businessType;

    private String businessId;

    private String businessOper;

    private String businessJson;

}
