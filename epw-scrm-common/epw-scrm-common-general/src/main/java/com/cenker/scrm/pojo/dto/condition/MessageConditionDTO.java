package com.cenker.scrm.pojo.dto.condition;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/23
 * @Description 群发条件传递
 */
@Data
public class MessageConditionDTO extends OriginalConditionDTO {
    /**
     * 添加开始时间
     */
    private String startTime;
    /**
     * 添加结束时间
     */
    private String endTime;

    /**
     * 旅程条件
     */
    private List<StageConditionDTO> stageConditionList;

    /**
     * 客户列表 后面优化成实体对象
     */
    private List<String> externalUserIdList;

    private String corpId;
}
