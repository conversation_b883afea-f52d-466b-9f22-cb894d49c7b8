package com.cenker.scrm.pojo.dto.message;

import com.cenker.scrm.pojo.dto.condition.MessageConditionDTO;
import com.cenker.scrm.pojo.vo.condition.MassMessageConditionVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/16
 * @Description
 */
@Data
public class MassMessageChainDetailDTO {
    private Long id;
    private String msgId;
    private Long expectSend;
    private Long actualSend;
    private Long corpConfigId;
    private String sender;
    private String remark;
    private Long messageInfoId;
    private Integer delFlag;
    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
    /**
     *  发送状态 0 失败 1 成功 2 待发送
     */
    private Integer checkStatus;
    /**
     * 筛选发送条件json
     */
    private MessageConditionDTO messageCondition;
    /**
     * 企微错误码 0 代表该消息成功
     */
    private Integer errCode;
    /**
     * 发送失败的客户列表
     */
    private List<String> failList;
    /**
     * 报错信息原文
     */
    private String errJson;
}
