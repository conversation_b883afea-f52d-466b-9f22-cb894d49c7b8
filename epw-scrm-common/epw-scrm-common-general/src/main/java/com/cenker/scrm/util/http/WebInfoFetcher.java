package com.cenker.scrm.util.http;

import com.cenker.scrm.util.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.http.HttpMethod;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * 网页信息抓取器
 * <AUTHOR>
 */
public class WebInfoFetcher {

    /**
     * 判断页面是否为静态页面
     *  仅判断Content-Type是否以text/html开头
     * @param url
     * @return
     * @throws IOException
     */
    public static boolean isStaticPage(String url) throws IOException {
        if (StringUtils.isBlank(url)) {
            return false;
        }

        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod(HttpMethod.HEAD.name());
        String contentType = connection.getContentType();
        connection.disconnect();
        return contentType.startsWith("text/html");
    }

    /**
     * 获取页面描述
     * @param doc
     * @return
     */
    public static String getDescription(Document doc) {
        Elements metaElements = doc.select("meta[name=description], meta[property^='og:description']");
        for (Element element : metaElements) {
            if ("description".equalsIgnoreCase(element.attr("name")) ||
                    "og:description".equalsIgnoreCase(element.attr("property"))) {
                return element.attr("content");
            }
        }

        return "";
    }

    /**
     * 获取页面图标链接
     * @param doc
     * @param websiteUrl
     * @return
     * @throws MalformedURLException
     */
    public static String getIconUrl(Document doc, String websiteUrl) throws MalformedURLException {
        Elements linkElements = doc.select("link[rel=icon], link[rel=shortcut icon]");
        for (Element element : linkElements) {
            String href = element.attr("href");
            if (!href.startsWith("http")) {
                URL url = new URL(websiteUrl);
                href = new URL(url.getProtocol(), url.getHost(), url.getPort(), href).toString();
            }
            return href;
        }

        return "";
    }

    public static void main(String[] args) {
        String url = "https://cdn.efunds.com.cn/eda/data/resource/hoi/a4bbd3e6-b2d7-46d8-9c4d-fd1428d90681.mp4"; // 替换为你想要抓取的网址
        try {
            if (isStaticPage(url)) {
                Document doc = Jsoup.connect(url).get();
                String title = doc.title(); // 获取页面标题
                String description = getDescription(doc); // 获取描述
                String iconUrl = getIconUrl(doc, url); // 获取图标链接

                System.out.println("Title: " + title);
                System.out.println("Description: " + description);
                System.out.println("Icon URL: " + iconUrl);
            } else {
                System.out.println("The page is not static.");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
