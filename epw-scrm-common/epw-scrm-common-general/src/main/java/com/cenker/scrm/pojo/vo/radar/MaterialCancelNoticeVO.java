package com.cenker.scrm.pojo.vo.radar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料取消发布通知VO类
 *
 * <AUTHOR>
 */
@Data
public class MaterialCancelNoticeVO {

    /** 消息编号，发送方全局唯一 */
    @JsonProperty("msg_id")
    private String msgId;

    /** 消息类型，固定填写"001" */
    @JsonProperty("msg_type")
    private String msgType;

    /** 消息版本号，当前填写"V1.3" */
    @JsonProperty("msg_version")
    private String msgVersion;

    /** 消息创建时间 */
    @JsonProperty("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 物料名称 */
    @JsonProperty("material_name")
    private String materialName;

    /** 物料唯一性标识 */
    @JsonProperty("material_id")
    private String materialId;

    /** 物料存储路径 */
    @JsonProperty("storage_path")
    private String storagePath;

    /** 发布模块名称 */
    @JsonProperty("publish_module_name")
    private String publishModuleName;

    /**
     *  是否存在发布记录
     *  0-否；1-是，物料库是否存在有效发布记录，用于标识是否是物料库主动发布的物料
     **/
    @JsonProperty("exist_publish_record")
    private String existPublishRecord;
}
