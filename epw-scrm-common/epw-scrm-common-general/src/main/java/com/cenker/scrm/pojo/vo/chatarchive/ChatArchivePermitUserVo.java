package com.cenker.scrm.pojo.vo.chatarchive;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatArchivePermitUserVo implements Serializable {

    private static final long serialVersionUID = 7564987076173102983L;
    private String userId;

    private String name;

    private String avatar;

    /**
     * 用户状态：激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    private Integer userStatus;

    /**
     * 标记删除：1：正常   2：已删除
     */
    private Integer delFlag;

    /**
     * 性别：0表示未定义，1表示男性，2表示女性
     */
    private Integer gender;

    /**
     * 开启会话存档时间
     */
    private Date startTime;

    /**
     * 开启会话状态，0表示关闭，1表示开启
     */
    private Integer chatStatus;

    /**
     * 主部门ID
     */
    private Integer mainDepartment;

}
