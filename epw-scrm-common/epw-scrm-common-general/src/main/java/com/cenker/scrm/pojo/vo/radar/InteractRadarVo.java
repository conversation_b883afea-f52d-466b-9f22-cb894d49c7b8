package com.cenker.scrm.pojo.vo.radar;

import com.cenker.scrm.model.base.BaseRequest;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarTagRule;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description 智能物料视图对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InteractRadarVo extends BaseRequest {

    private String id;

    /**
     * 雷达分类 1 企业雷达 2 个人雷达
     */
    @NotNull(message = "雷达分类不能为空")
    @DecimalMax(value = "2",message = "请选择智能物料分类")
    private Integer scope;
    /**
     * 雷达类型 1 图文 2 链接 3 PDF
     */
    @NotNull(message = "请选择智能物料类型")
    private Integer type;
    /**
     * 雷达标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 30,message = "标题字数受限")
    private String title;
    /**
     * 匹配方式 1 昵称匹配 2 精准匹配
     */
    @NotNull(message = "匹配方式不能为空")
    @DecimalMax(value = "1",message = "目前只支持昵称匹配")
    private Integer matchType;
    /**
     * 成员名片 0 未开启 1 已开启
     */
    @NotNull(message = "成员名片不能为空")
    @DecimalMax(value = "1",message = "非法参数")
    private Integer contactStatus;
    /**
     * 行为通知 0 未开启 1 已开启
     */
    @DecimalMax(value = "1",message = "非法参数")
    @NotNull(message = "行为通知不能为空")
    private Integer behaviorInform;
    /**
     * 动态通知 0 未开启 1 已开启
     */
    @DecimalMax(value = "1",message = "非法参数")
    @NotNull(message = "动态通知不能为空")
    private Integer dynamicInform;
    /**
     * 客户标签 0 未开启 1 已开启
     */
    @DecimalMax(value = "1",message = "非法参数")
    @NotNull(message = "客户标签不能为空")
    private Integer customerTag;
    private String remark;
    private Integer delFlag;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String createBy;
    private String createByName;
    private String updateBy;

    /**
     * 内容参数
     */
    @NotNull(message = "内容参数不能为空")
    private TbWxRadarContent tbWxRadarContent;
    /**
     * 标签参数
     */
    private List<TbWxRadarTagRule> tbWxRadarTagRuleList;

    /**
     * 点击人数
     */
    private Integer clickNum;

    /**
     * 员工id
     */
    private String staffId;

    /**
     * 内容h5路径
     */
    private String page;

    /**
     * 是否企业管理员 0否 1是
     */
    private Integer administrator;

    /**
     * 智能物料点击渠道 1 侧边栏 2 朋友圈 3 工作台 4 渠道活码 5 企业话术 6 群发客户
     */
    private Integer clickSource;

    /**
     * 2022-05-31 新增智能物料创建来源标识 1 web  2 侧边栏 3 工作台
     */
    private Integer since;

    private List<String> userList;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 分组ID
     */
    private String categoryId;

    /**
     * 分组范围: ALL 全部  NONE 未分组  CUSTOM 指定分组
     */
    private String categoryScope;

    /**
     * 分组ID列表
     */
    private List<String> categoryIdList;

    /**
     * 标题命名规则
     */
    private String namingRule;

    /**
     * 是否展示在侧边栏 0 否 1 是
     */
    private Integer showStatus;

    /**
     * 查看权限 onlyWeComFri:仅企微好友; onlyWeComAuthFri:仅企微认证好友; none:不限制
     */
    @NotBlank(message = "查看权限不能为空")
    @Pattern(regexp = "^(onlyWeComFri|onlyWeComAuthFri|none)$", message = "查看权限参数非法")
    private String viewPerm;
    /**
     * 状态 PENDING_APPROVAL 待审核  REVOKED 已撤回 REJECTED 审核驳回 EXECUTING 使用中
     */
    private String status;
    /**
     * 启用了审批
     * true:启用审批
     * false:未启用审批
     */
    private boolean enableApproval;
    /**
     * 审批权限
     * true:有审批权限
     * false:无审批权限
     */
    private boolean canApproval;
    /**
     * 审批人
     */
    private String approvalUser;
    /**
     * 审批人姓名
     */
    private String approvalUserName;
    /**
     * 意见
     */
    private String approvalRemark;
}
