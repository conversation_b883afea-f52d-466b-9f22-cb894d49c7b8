package com.cenker.scrm.model.login;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MpWxUser implements Serializable {

  private static final long serialVersionUID = -4697207592394341513L;
  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  private String nickName;
  private String headImgUrl;
  private String openId;
  private String unionId;
  private Integer sex;
  private String country;
  private String province;
  private String city;
  private String privilege;
  private Integer enabled;
  /**
   * 注册平台 0未知 1 WEB 2 H5 3小程序
   */
  private Integer platform;
  /**
   * 注册来源链接
   */
  private String source;
  private String remark;
  private String createBy;
  private Date createTime;
  private String updateBy;
  private Date updateTime;

}
