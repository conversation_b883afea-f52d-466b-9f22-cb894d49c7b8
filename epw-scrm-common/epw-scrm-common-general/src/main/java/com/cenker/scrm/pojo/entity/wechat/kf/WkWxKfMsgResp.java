package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_resp")
public class WkWxKfMsgResp extends WkWxKfMsgItemCommonResp {
    @TableField("errcode")
    protected Long errcode;
    @TableField("errmsg")
    protected String errmsg;
    private String nextCursor;
    private Integer hasMore;
    private String openKfId;
    private String externalUserId;
    @TableField("`origin`")
    private Integer origin;
    private String servicerUserId;
    private String msgType;

    public void setMsgResp(WxCpKfMsgListResp msgListResp) {
        this.errcode = msgListResp.getErrcode();
        this.errmsg = msgListResp.getErrmsg();
        this.nextCursor = msgListResp.getNextCursor();
        this.hasMore = msgListResp.getHasMore();
    }

    public void setMsgResp(WkWxKfMsgResp wkWxKfMsgResp) {
        this.errcode = wkWxKfMsgResp.getErrcode();
        this.errmsg = wkWxKfMsgResp.getErrmsg();
        this.nextCursor = wkWxKfMsgResp.getNextCursor();
        this.hasMore = wkWxKfMsgResp.getHasMore();
    }

    public void setMsgResp(WkWxKfMsgResp wkWxKfMsgResp, WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        setMsgResp(wkWxKfMsgResp);
        init(msgItem);
    }

    @Override
    public WkWxKfMsgResp init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.openKfId = msgItem.getOpenKfid();
        this.externalUserId = msgItem.getExternalUserId();
        this.origin = msgItem.getOrigin();
        this.servicerUserId = msgItem.getServicerUserId();
        this.msgType = msgItem.getMsgType();

        return this;
    }
}
