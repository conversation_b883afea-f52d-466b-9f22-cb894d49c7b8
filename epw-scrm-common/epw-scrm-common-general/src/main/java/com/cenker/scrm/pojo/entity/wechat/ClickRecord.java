package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 点击记录表
 * <AUTHOR> @Date 2023-08-25 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("ck_click_record")
public class ClickRecord implements Serializable {
	private static final long serialVersionUID =  3705026020384097236L;

	/**
	 * 主键id
	 */
	@TableId(type = IdType.AUTO)
	private Long id;

	/**
	 * 场景0 其他 1 社群活码
	 */
	private Integer scene;

	/**
	 * 场景唯一标识
	 */
	private Long sceneId;

	/**
	 * 访问ip
	 */
	private String ip;

	/**
	 * 点击时间戳
	 */
	private Long clickTimestamp;

	/**
	 * 点击时间
	 */
	private Date clickDate;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业id
	 */
	private String corpId;

	/**
	 * 逻辑删除 0 表示未删除，1 表示删除
	 */
	@TableField(value = "is_deleted")
	@TableLogic
	private Boolean deleted;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}

