
package com.cenker.scrm.pojo.entity.wechat.wxmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 微信账号配置
 *
 * <AUTHOR>
 * @date 2019-03-23 21:26:35
 */
@Data
@TableName("mp_wx_app")
@EqualsAndHashCode(callSuper = true)
public class WxApp extends Model<WxApp> {
private static final long serialVersionUID = 1L;

    /**
   * 应用ID
   */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
   * 创建者
   */
    private String createBy;
    /**
   * 创建时间
   */
    private Date createTime;
    /**
   * 更新者
   */
    private String updateBy;
    /**
   * 更新时间
   */
    private Date updateTime;
    /**
   * 逻辑删除标记（0：显示；1：隐藏）
   */
    private String delFlag;
    /**
   * 所属企业
   */
    private String corpConfigId;
    /**
   * 微信原始标识
   */
    private String weixinSign;
    /**
   * 应用类型(1:小程序，2:公众号)
   */
	@NotNull(message = "应用类型不能为空")
    private String appType;
    private String appId;
    /**
   * 应用密钥
   */
    private String secret;
    /**
   * token
   */
    private String token;
    /**
   * EncodingAESKey
   */
    private String aesKey;
    /**
   * 微信号名称
   */
    private String name;
    /**
   * 公众号类型（0：订阅号；1：由历史老帐号升级后的订阅号；2：服务号）
   */
	@NotNull(message = "公众号类型不能为空")
    private String weixinType;
    /**
   * 公众号微信号
   */
    private String weixinHao;
	/**
	 * logo
	 */
	private String logo;
	/**
	 * 二维码
	 */
	private String qrCode;
    /**
   * 微社区URL
   */
    private String community;
	/**
	 * 认证类型
	 * 类型	说明
	 * -1	未认证
	 * 0	微信认证
	 * 1	新浪微博认证
	 * 2	腾讯微博认证
	 * 3	已资质认证通过但还未通过名称认证
	 * 4	已资质认证通过、还未通过名称认证，但通过了新浪微博认证
	 * 5	已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
	 */
	@NotNull(message = "认证类型不能为空")
	private String verifyType;
	/**
	 * 主体名称
	 */
	private String principalName;
    /**
	* 是否第三方平台应用（1：是；0：否）
	*/
	private String isComponent;
    /**
   * 备注信息
   */
    private String remarks;
    /**
   * 绑定的会员卡ID
   */
    private String vipCardId;
	/**
	 * 第三方平台服务商appId
	 */
	@TableField(exist = false)
	private String componentAppId;
    /**
     * 微信校验文件名
     */
    private String verifyFileName;
    /**
     * 默认路径
     */
    private String defaultPage;
    /**
     * 默认使用 0 否 1是
     */
    private Integer defaultUse;
}
