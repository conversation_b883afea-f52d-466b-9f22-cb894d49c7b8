package com.cenker.scrm.pojo.request.reply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/18
 * @Description 搜索封装多分组
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuickReplyListQuery {
    private Long categoryId;
    private String categoryName;
    private Integer orderNum;
    private List<QuickReplyQuery> list;
}
