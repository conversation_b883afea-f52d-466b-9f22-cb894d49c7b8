package com.cenker.scrm.pojo.vo.drainage;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/5/11
 * @Description 小程序端展示引流短链信息
 */
@Data
public class DrainageShortLinkShowInfoVO {

    @JsonSerialize(using = ToStringSerializer.class)
    public Long id;
    /**
     * 活码/群码地址
     */
    private String codeUrl;
    /**
     * 显示头像昵称 0 不开启 1 开启
     */
    private Integer showBaseInfo;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 显示企业信息 0 不开启 1 开启
     */
    private Integer showCorpInfo;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 企业logo
     */
    private String corpAvatar;
    /**
     * 引导语
     */
    private String guideContent;
    /**
     * 页面标题
     */
    private String pageTitle;
    /**
     * 小程序页面地址
     */
    private String currentPage;
}
