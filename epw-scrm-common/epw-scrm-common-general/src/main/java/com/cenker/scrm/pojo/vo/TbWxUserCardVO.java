package com.cenker.scrm.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title TbWxUserVO
 * @date 2025/5/26 15:02
 * @description TODO
 */
@Data
public class TbWxUserCardVO implements Serializable {
    private static final long serialVersionUID = 8921759573448763070L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 成员UserID。对应管理端的帐号
     */
    private String userId;

    /**
     * 成员名称
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 成员所属部门id列表，仅返回该应用有查看权限的部门id
     */
    private String department;

    /**
     * 职务信息
     */
    private String position;

    /**
     * 0表示未定义，1表示男性，2表示女性
     */
    private String gender;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像url
     */
    private String avatar;

    /**
     * 头像缩略图url
     */
    private String thumbAvatar;

    /**
     * 座机
     */
    private String telephone;

    /**
     * 别名
     */
    private String alias;

    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    private Integer status;

    /**
     * 企业ID
     */
    private String corpId;

    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 角色id集合
     */
    private String roleNames;


    /** 欢迎语模板 */
    private String welTplId;
    /** 欢迎语模板 */
    private String welTplName;

    /** 欢迎语 */
    private String welContent;

    /** 欢迎语附件 */
    private List<WelcomeAttachmentVo> attachments;
}
