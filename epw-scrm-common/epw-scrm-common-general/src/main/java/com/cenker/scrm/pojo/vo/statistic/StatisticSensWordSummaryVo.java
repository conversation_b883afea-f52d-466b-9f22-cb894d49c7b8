package com.cenker.scrm.pojo.vo.statistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-敏感词数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
public class StatisticSensWordSummaryVo {

    /**
     * 统计日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String statisticDate;
    /**
     *触发次数
     */
    private Integer triggerTimes;
    /**
     *触发人数
     */
    private Integer triggerNum;

}
