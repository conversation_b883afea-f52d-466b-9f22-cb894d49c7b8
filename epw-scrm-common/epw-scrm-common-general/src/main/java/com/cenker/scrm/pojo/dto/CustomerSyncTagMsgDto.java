package com.cenker.scrm.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 为客户同步企业标签的MQ消息体内容
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CustomerSyncTagMsgDto {

    private String corpId;

    /**
     * 外部联系人ID集合
     */
    private List<String> externalUserIds;

    /**
     * 是否重试，默认false
     * 非重试场景，如果失败，会插入一条重试任务
     */
    private boolean isRetry;
}
