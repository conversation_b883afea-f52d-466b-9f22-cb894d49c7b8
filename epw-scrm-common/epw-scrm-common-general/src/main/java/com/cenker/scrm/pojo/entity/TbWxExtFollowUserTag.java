package com.cenker.scrm.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 外部联系人与企业员工tag tb_wx_ext_follow_user_tag
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
// @ApiModel("外部联系人与企业员工tag")
@Builder
public class TbWxExtFollowUserTag implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 添加了此外部联系人的企业成员userid
     */
    private String userId;

    /**
     * 客户id
     */
    private String externalUserId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 标签名
     */
    private String tag;

    /**
     * 标签id
     */
    private String tagId;

    /**
     * 标签类型 1 表示企业 2 表示个人
     */
    private String type;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Integer delFlag;

    /**
     * 标签添加来源描述
     */
    private String remark;

    @TableField(exist = false)
    private List<TbWxCorpTag> tbWxCorpTagList;

    //oper_id
    private String operId;
    /**
     * 同步状态 0 待同步 1 已同步
     */
    private String syncStatus;
}
