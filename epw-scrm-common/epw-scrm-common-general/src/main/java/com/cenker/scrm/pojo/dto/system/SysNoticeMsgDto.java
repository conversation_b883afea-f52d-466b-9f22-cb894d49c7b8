package com.cenker.scrm.pojo.dto.system;

import com.cenker.scrm.pojo.entity.system.SysNotice;
import com.cenker.scrm.pojo.entity.system.SysNoticeSendRecords;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 系统通知的MQ消息对象
 * <AUTHOR>
 * @title SysNoticeMqDto
 * @date 2024/12/5 16:35
 * @description TODO
 */
@Data
public class SysNoticeMsgDto implements Serializable {
    private static final long serialVersionUID = -2503249117964378855L;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 消息标题
     */
    private String noticeTitle;

    /**
     * 通知人
     */
    private String noticeUser;

    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 应用消息通知内容
     */
    private String agentMsgContent;

    /**
     * 通知模式
     */
    private String noticeMode;

    /**
     * 应用消息Id
     */
    private List<String> msgIds;

    /**
     * 业务类型：condition 条件 SOP、journeysop 旅程 SOP、
     * oneseparateone 1V1SOP、masscustomer 1V1 群发、
     * sendmoments 发朋友圈、groupToGroup 社群群发、
     * intelligentmaterials_enterprise 智能物料、
     * intelligentmaterials_person 个人智能物料、
     * material 营销素材
     */
    private String businessType;

    private String businessId;

    private String businessOper;

    private String businessJson;
}
