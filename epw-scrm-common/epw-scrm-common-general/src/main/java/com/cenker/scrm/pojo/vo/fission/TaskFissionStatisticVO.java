package com.cenker.scrm.pojo.vo.fission;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/18
 * @Description
 */
@Data
public class TaskFissionStatisticVO {
    private String taskFissionId;
    private String taskName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    private List<TaskFissionDailyDataVO> data;
}
