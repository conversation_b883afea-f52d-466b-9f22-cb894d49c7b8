package com.cenker.scrm.pojo.dto.session;

import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 敏感规则信息对象 ck_session_sens_rule_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class UpdSensRuleStatusDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则id */
    private Long ruleId;
    /**状态 字典： 1：正常 2：废弃**/
    private String status;

}
