package com.cenker.scrm.pojo.entity.wechat.sitecontact;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ContactSite {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;

  /**
   * 唯一id
   */
  private String signId;
  /**
   * 创建者
   */
  private String createBy;

  /**
   * 创建时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  /**
   * 企业ID
   */
  private String corpId;

  /**
   * 使用该联系方式的部门id列表，只在type为2时有效
   */
  private String party;

  /**
   * 联系方式类型,1-单人, 2-多人
   */
  private Integer type;

  /**
   * 场景，1-在小程序中联系，2-通过二维码联系
   */
  private String scene;

  /**
   * 外部客户添加时是否无需验证，默认为true(1)
   */
  private Integer skipVerify;

  /**
   * 企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详请
   */
  private String state;

  /**
   * 是否删除1：删除
   */
  @TableLogic
  private String delFlag;

  /**
   * 欢迎语
   */
  private String welContent;

  /**
   * 欢迎语附件
   */
  @TableField(updateStrategy = FieldStrategy.IGNORED)
  private String welcomeAttachment;


  /**
   * 员工活码标签信息
   */
  @TableField(exist = false)
  private String tagIds;

  /**
   * 站点id
   */
  private Long siteId;

  private String remark;

  /**
   * 前段字段
   */
  @TableField(value = "weEmpleCodeTags",updateStrategy = FieldStrategy.IGNORED)
  private String weEmpleCodeTags;

  /**
   * 前段字段
   */
  @TableField("weEmpleCodeUseScops")
  private String weEmpleCodeUseScops;

  /**
   * 更新者
   */
  private String updateBy;

  /**
   * 更新时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date updateTime;

  /**
   * 2022-05-31 新增渠道活码创建来源标识 1 web  2 侧边栏 3 工作台
   */
  private Integer since;


  @TableField(exist = false)
  private Long storeId;
  /**
   * 配送员活码id
   */
  @TableField(exist = false)
  private String deliveryContactId;

  /**
   * 活码刷新间隔（秒）
   */
  @TableField(exist = false)
  private Integer refreshRate;

  /**
   * 新增联系方式的配置id
   */
  @TableField(exist = false)
  private String configId;

  /**
   * 联系我二维码链接，仅在scene为2时返回
   */
  @TableField(exist = false)
  private String qrCode;

}
