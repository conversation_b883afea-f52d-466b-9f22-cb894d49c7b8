package com.cenker.scrm.pojo.vo.tag;

import lombok.Data;
import lombok.NoArgsConstructor;
import me.chanjar.weixin.cp.bean.school.user.WxCpUserResult;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ExternalUserTagVO
 * <AUTHOR>
 * @Date 2021/4/13 14:49
 **/
@Data
public class ExtQryWhereVO implements Serializable {
    /**
     * 标签名
     */
    private String qryTypeName;
    private String qryType;
    private String qryTypeIcon;
    private List<Condition> conditionList;

    @Data
    @NoArgsConstructor
    public static class OperRel implements Serializable {
        private String operRelId;
        private String operRelName;
        // 构造方法、Getter和Setter省略
    }

    @Data
    @NoArgsConstructor
    public static class Condition implements Serializable {
        private String value;
        private String label;
        private List<OperRel> operRelList;
        private String dictType;
        private String dataFormatType;
        private List<ExtSecCategoryNodeVO> children;

        // 构造方法、Getter和Setter省略
    }

}
