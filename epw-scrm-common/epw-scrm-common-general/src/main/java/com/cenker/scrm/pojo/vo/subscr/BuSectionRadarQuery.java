package com.cenker.scrm.pojo.vo.subscr;

import com.cenker.scrm.pojo.valid.SelectGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 查询条件类
 */
@Data
public class BuSectionRadarQuery {
    /**
     * 名称
     */
    private String name;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;

    /**
     * 上传时间开始
     */
    private String beginTime;
    /**
     * 上传时间结束
     */
    private String endTime;
    /**
     * 栏目id
     */
    @NotBlank(message = "栏目id不能为空", groups = {SelectGroup.class})
    private String sectionId;
    /**
     * 客户id，若不为空，则需根据客户是否订阅该栏目以及客户的订阅时间进行过滤
     */
    private String externalUserId;
    /**
     * 物料状态：EXECUTING 使用中
     */
    private String radarStatus;
}