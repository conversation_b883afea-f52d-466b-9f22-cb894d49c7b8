package com.cenker.scrm.pojo.request.content.material;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title BuWelcomeContactTemplateRequest
 * @date 2025/5/24 19:25
 * @description TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BuWelcomeContactTemplateRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = -750712459309108458L;

    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 名称
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 使用场景名称
     */
    private String sceneName;
}
