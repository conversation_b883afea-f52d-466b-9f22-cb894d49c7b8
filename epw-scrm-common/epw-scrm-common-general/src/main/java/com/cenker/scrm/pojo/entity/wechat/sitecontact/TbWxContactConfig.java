package com.cenker.scrm.pojo.entity.wechat.sitecontact;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_contact_config")
public class TbWxContactConfig {
  @TableId(type = IdType.AUTO)
  private Long id;
  /**
   * 所属活码id
   */
  private Long contactId;
  private Integer refreshRate;
  private Long deliveryUserId;
  private String backImg;
  private Boolean viewTop;
  @TableLogic
  private Integer delFlag;
  private String remark;
  private Long createBy;
  private Date createTime;
  private Long updateBy;
  private Date updateTime;
}
