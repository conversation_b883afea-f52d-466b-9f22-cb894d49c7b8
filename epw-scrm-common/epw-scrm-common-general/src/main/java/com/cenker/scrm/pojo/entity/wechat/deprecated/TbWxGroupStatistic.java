package com.cenker.scrm.pojo.entity.wechat.deprecated;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TbWxGroupStatistic {
  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  private String owner;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date statTime;
  private Integer newChatCnt;
  private Integer chatTotal;
  private Integer chatHasMsg;
  private Integer newMemberCnt;
  private Integer memberTotal;
  private Integer memberHasMsg;
  private Integer msgTotal;
  private String corpId;
}
