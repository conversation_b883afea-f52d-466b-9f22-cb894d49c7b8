package com.cenker.scrm.pojo.dto.radar;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 智能物料批量操作request对象
 */
@Data
public class RadarBatchOperateDto {

    /**
     * 全部展示：SHOW，全部隐藏HIDE，全部删除REMOVE
     */
    private String operateType;

    /**
     * 全选：ALL，指定：SPECIFY
     */
    private String batchType;

    /**
     * 指定ID集合，当batchType为SPECIFY时有效
     */
    private String ids;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 物料标题
     */
    private String title;

    /**
     * 创建人姓名
     */
    private String createBy;

    /**
     * 物料类型 1 图文 2 链接 3 PDF
     */
    private Integer type;

    /**
     * 分组范围: ALL 全部  NONE 未分组  CUSTOM 指定分组
     */
    private String categoryScope;

    /**
     * 分组ID,多个以逗号分隔
     */
    private String categoryId;

    /**
     * 登录用户ID
     */
    private String loginUserId;

}
