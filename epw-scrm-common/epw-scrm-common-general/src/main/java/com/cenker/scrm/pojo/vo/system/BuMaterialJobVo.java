package com.cenker.scrm.pojo.vo.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年04月07日 21:17
 */
@Data
public class BuMaterialJobVo implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 应用类型
     * 微信公众号：WX_PUBLIC_ACCOUNT
     * 微信小程序：WX_MINI_PROGRAM
     * 物料库：MATERIAL_LIBRARY
     */
    private String appType;

    /**
     * 应用配置ID，对应sys_app_config表的主键
     */
    private Long appConfigId;

    /**
     * 匹配方式，最近一条：LATEST，模糊匹配：KEYWORD
     */
    private String matchMethod;

    /**
     * 模糊匹配关键词
     */
    private String keyword;

    /**
     * 任务执行时间
     */
    private String jobTime;

    /**
     * 通知成员
     */
    private List<UserConditionDTO> noticeUser;

    /**
     * 生成物料数
     */
    private Integer materialNumber;

    /**
     * 物料分类 1 企业物料 2 个人物料
     */
    private Integer materialScope;

    /**
     * 物料标题命名规则
     */
    private String namingRule;

    /**
     * 物料分组ID
     */
    private Long materialCategoryId;

    /**
     * 物料类型 1 图文 2 链接 3 PDF
     */
    private Integer materialType;

    /**
     * 获取方式：AUTO 自动获取，CUSTOMIZE 自定义
     */
    private String acquisitionMethod;

    /**
     * 自定义卡片信息
     */
    private String customInfo;

    /**
     * 定时任务id
     */
    private Integer xxlJobId;

    /**
     * 任务状态 0 停止  1 运行中
     */
    private Integer jobStatus;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
