package com.cenker.scrm.pojo.dto.system;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class SysAppConfigAddDto {

    /**
     * 应用类型
     * 微信公众号：WX_PUBLIC_ACCOUNT
     * 微信小程序：WX_MINI_PROGRAM
     * 物料库：MATERIAL_LIBRARY
     */
    @Pattern(regexp = "^(WX_PUBLIC_ACCOUNT|WX_MINI_PROGRAM|MATERIAL_LIBRARY)$", message = "应用类型取值不正确")
    private String appType;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    private String appName;

    /**
     * 应用配置，JSON格式存储
     */
    @NotBlank(message = "应用配置不能为空")
    private String appConfig;

    /**
     * 备注
     */
    private String remark;
}