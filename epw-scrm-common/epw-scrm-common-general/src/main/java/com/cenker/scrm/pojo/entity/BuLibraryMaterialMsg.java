package com.cenker.scrm.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

// 实体类 BuLibraryMaterialMsg.java
@Data
@TableName("bu_library_material_msg")
public class BuLibraryMaterialMsg {
    @TableId
    private String msgId;
    private String msgType;
    private String msgVersion;
    private Date createTime;
    private String materialName;
    private String materialId;
    private String materialDesc;
    private Date lastModifyTime;
    private String storagePath;
    private String materialVersion;
    private String department;
    private String fileType;
    private String uploader;
    private String owner;
    private String tagList;
    private String metaDataList;
    private String publishModuleName;
    private String publishRule;
    private String outerPreviewUrl;
    private String outerCdnUrl;
    private String outerShortUrl;
    private String existPublishRecord;
    private String receiveMsgType;
}
