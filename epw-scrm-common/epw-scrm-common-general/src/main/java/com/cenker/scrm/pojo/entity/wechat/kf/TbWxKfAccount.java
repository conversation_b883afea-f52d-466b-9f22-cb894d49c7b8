package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 微信客服记录对象 tb_wx_kf_account
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxKfAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 客服账号名称
     */
    private String name;

    /**
     * 客服账号头像
     */
    private String headImageUrl;

    /**
     * 客服账号头像（企微返回）
     */
    private String avatar;

    /**
     * 客服账号ID（企微返回）
     */
    private String openKfId;

    /**
     * 新客欢迎语
     */
    private String welcomeMsg;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 客服基础链接（不带参）
     */
    private String baseUrl;

    /**
     * 状态 0 表示正常、1 停用
     */
    private String status;

    /**
     * 状态 0 表示正常、2 删除
     */
    private String delFlag;

    /**
     * 描述
     */
    private String remark;

    /**
     * 当前调用接口的应用身份，是否有该客服账号的管理权限（编辑客服帐号信息、分配会话和收发消息）,0标识拥有，1表示没有
     */
    private String managePrivilege;

}
