package com.cenker.scrm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "esb")
public class EsbServiceConfig {

    private String url="http://************/ESB/WS_MDC_WX_SERVICE?WSDL";

    private String salt="111";

    /**
     * 数据中心接口地址
     */
    private String sqlserviceUrl = "http://localhost:33303/comsvr/sqlservice";

    /**
     * 数据中心接口token
     */
    private String esbToken = "V0VDT00tQ1JNLUVN";

    /**
     * 请求系统标识
     */
    private String reqSys = "WECOM-CRM-EM";
}
