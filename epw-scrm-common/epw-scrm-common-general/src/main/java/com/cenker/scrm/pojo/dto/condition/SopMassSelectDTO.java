package com.cenker.scrm.pojo.dto.condition;

import com.cenker.scrm.pojo.valid.InsertGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description sop群发筛选条件
 */
@Data
public class SopMassSelectDTO extends SopConditionDTO {
    /**
     * 标签、客户阶段、添加时间、员工 是否筛选 0 否 1 是
     */
//    @NotNull(message = "请选择是否筛选标签",groups = {InsertGroup.class})
    private Integer tagSelectType;
//    @NotNull(message = "请选择是否筛选客户阶段",groups = {InsertGroup.class})
    private Integer stageSelectType;
//    @NotNull(message = "请选择是否筛选添加时间",groups = {InsertGroup.class})
    private Integer addSelectType;
//    @NotNull(message = "请选择是否筛选客户",groups = {InsertGroup.class})
    private Integer userSelectType;

    /**
     * 员工条件
     */
    private List<UserConditionDTO> userConditionList;

    /**
     * 标签条件
     */
    private List<TagConditionDTO> tagConditionList;

    /**
     * 旅程条件
     */
    private List<StageConditionDTO> stageConditionList;

    /**
     * 添加开始时间
     */
    private String startTime;
    /**
     * 添加结束时间
     */
    private String endTime;

    /**
     * 是否员工去重 是-单条走企微去重 否-多条指定员工
     */
//    @NotNull(message = "请选择发送的方式",groups = {InsertGroup.class})
    private Boolean userDistinct;
}
