package com.cenker.scrm.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 会话存档-消息类型枚举
 */
@Getter
public enum ChatArchiveMsgTypeEnum {

    // 文本消息
    TEXT("text", 1),
    // 图片消息
    IMAGE("image", 2),
    // 链接消息
    LINK("link", 3),
    // 小程序消息
    WEAPP("weapp", 4),
    // 红包消息
    REDPACKET("redpacket", 5),
    // 互通红包消息
    EXTERNAL_REDPACKET("external_redpacket", 6),
    // 视频号消息
    SPHREED("sphfeed", 7),
    // 图文消息
    NEWS("news", 8),
    // 撤回消息
    REVOKE("revoke", 9),
    // 同意会话消息
    AGREE("agree", 10),
    // 不同意会话消息
    DISAGREE("disagree", 11),
    // 语音
    VOICE("voice", 12),
    // 视频消息
    VIDEO("video", 13),
    // 位置消息
    LOCATION("location", 14),
    // 表情消息
    EMOTION("emotion", 15),
    // 文件消息
    FILE("file", 16),
    // 会话记录消息
    CHATRECORD("chatrecord", 17),
    // 投票信息消息
    VOTE("vote", 18),
    // 名片消息
    CARD("card", 19),
    // 混合消息
    MIXED("mixed", 20);

    private String type;
    private Integer code;

    ChatArchiveMsgTypeEnum(String type, Integer code) {
        this.type = type;
        this.code = code;
    }

    /**
     * 根据类型获取对应的枚举类
     *
     * @param type 类型值
     * @return 枚举类
     */
    public static ChatArchiveMsgTypeEnum getChatArchiveMsgTypeEnum(Integer code) {
        for (ChatArchiveMsgTypeEnum value : ChatArchiveMsgTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    /**
     * 根据类型获取对应的枚举类
     *
     * @param type 类型值
     * @return 枚举类
     */
    public static ChatArchiveMsgTypeEnum getChatArchiveMsgTypeEnum(String type) {
        if(StrUtil.isBlank(type)){
            return null;
        }
        for (ChatArchiveMsgTypeEnum value : ChatArchiveMsgTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
