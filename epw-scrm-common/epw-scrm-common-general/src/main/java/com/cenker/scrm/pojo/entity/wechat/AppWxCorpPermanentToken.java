package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/15
 * @Description 代开发自建应用永久授权记录
 */
@Data
@TableName("app_corp_permanent_token")
public class AppWxCorpPermanentToken {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 企业id
     */
    private String corpId;

    /**
     * 企业名
     */
    private String corpName;

    /**
     * 授权方企业类型
     */
    private String corpType;

    /**
     * 状态-1 有效 2 无效
     */
    private Integer status;

    /**
     * 授权用户id
     */
    private String userId;

    /**
     * 授权人名称
     */
    private String name;

    /**
     * 永久授权码(secret)
     */
    private String permanentCode;

    /**
     * 授权应用id(模板id)
     */
    private int agentId;

    /**
     * token
     */
    private String messageToken;
    /**
     * aesKey
     */
    private String messageSecret;
    /**
     * 回调地址
     */
    private String messageCallbackUrl;
}
