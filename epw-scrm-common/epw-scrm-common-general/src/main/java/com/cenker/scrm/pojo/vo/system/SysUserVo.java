package com.cenker.scrm.pojo.vo.system;

import com.cenker.scrm.annotation.Excel;
import com.cenker.scrm.pojo.entity.system.SysUser;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @title SysUserVo
 * @date 2024/12/10 10:42
 * @description TODO
 */
@Data
public class SysUserVo implements Serializable {
    private static final long serialVersionUID = -3762702192647880881L;

    private String userid;

    /**
     * 昵称+用户账号
     */
    private String name;
    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 企业ID
     */
    @Excel(name = "企业ID")
    private String corpId;

    /**
     * 企业授权关联ID
     */
    private String corpUserId;

    /**
     * 域账号
     */
    private String domainAccount;

    /**
     * 是否企业管理员
     */
    private Boolean administrator;

    public SysUserVo(){}

    public SysUserVo(SysUser sysUser) {
        this.userid = sysUser.getUserId();
        this.name = sysUser.getNickName() + "(" + sysUser.getUserName() + ")";
        this.avatar = sysUser.getAvatar();
        this.userName = sysUser.getUserName();
        this.nickName = sysUser.getNickName();
        this.corpId = sysUser.getCorpId();
        this.corpUserId = sysUser.getCorpUserId();
        this.domainAccount = sysUser.getDomainAccount();
        this.administrator = sysUser.getAdministrator();
    }

}
