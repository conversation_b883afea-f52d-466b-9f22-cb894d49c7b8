package com.cenker.scrm.pojo.vo.group;

import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/25
 * @Description 数据概览视图
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroupCodeDataStatisticsVO extends GroupCodeInfoVO{
    /**
     * 扫码次数
     */
    private Integer scanCodeCnt;

    /**
     * 扫码入群总客户数
     */
    @Excel(name = "扫码入群客户数",sort = 4)
    private Integer totalAddGroupCnt;

    /**
     * 扫码退群总客户数
     */
    @Excel(name = "退群客户数",sort = 5)
    private Integer totalQuitGroupCnt;

    /**
     * 扫码入群净增客户数 = 扫码入群总客户数 - 扫码退群总客户数
     */
    @Excel(name = "净增客户数",sort = 6)
    private Integer totalNetAddGroupCnt;

    /**
     * 分日/时数据
     */
    List<GroupCodeDataStatisticsDailyVO> data;
}
