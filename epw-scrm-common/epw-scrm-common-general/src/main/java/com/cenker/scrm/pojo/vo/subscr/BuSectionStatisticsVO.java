package com.cenker.scrm.pojo.vo.subscr;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 栏目统计视图类
 * 用于传输栏目统计的相关信息
 */
@Data
public class BuSectionStatisticsVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 栏目ID，关联订阅栏目表的id
     */
    private String sectionId;

    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date statisticDate;

    /**
     * 本栏目订阅人数
     */
    private Integer subscriptionCount;

    /**
     * 总订阅人数
     */
    private Integer subscriptionCountTotal;

    /**
     * 订阅比率
     */
    private BigDecimal subscriptionRatio;

    /**
     * 新增订阅人数
     */
    private Integer newSubscriptionCount;

    /**
     * 取消订阅人数
     */
    private Integer cancelSubscriptionCount;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private List<BuSectionStatisticsVO> detailList;
}