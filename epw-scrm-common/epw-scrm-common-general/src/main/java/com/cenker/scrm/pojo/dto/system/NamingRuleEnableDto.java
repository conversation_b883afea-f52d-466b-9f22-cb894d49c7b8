package com.cenker.scrm.pojo.dto.system;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class NamingRuleEnableDto {

    /**
     * 规则ID
     */
    @NotBlank(message = "规则ID不能为空")
    private String ruleId;


    /**
     * 规则状态, 0-禁用，1-启用
     */
    @NotBlank(message = "规则状态不能为空")
    private String ruleStatus;

    /**
     * 是否应用到已有智能物料/营销素材，0-否，1-是
     */
    @NotNull(message = "是否更新状态不能为空")
    private Integer applyToOld;

    /**
     * 登录用户ID
     */
    private String loginUserId;
}
