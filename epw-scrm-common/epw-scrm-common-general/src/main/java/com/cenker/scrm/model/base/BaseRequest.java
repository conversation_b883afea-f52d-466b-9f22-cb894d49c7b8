package com.cenker.scrm.model.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cenker.scrm.pojo.valid.SelectGroup;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/29 11:59
 */
@Data
public class BaseRequest {
    @TableField(exist = false)
    @NotNull(message = "需要分页页码",groups = {SelectGroup.class})
    @Min(message = "分页限制",value = 1)
    private Integer pageNum;

    @TableField(exist = false)
    @NotNull(message = "需要分页条数",groups = {SelectGroup.class})
    @Max(message = "分页限制",value = 100)
    @Min(message = "分页限制",value = 1)
    private Integer pageSize;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 企业微信用户id（员工企微账号）
     */
    private String wxUserId;

    /**
     * 主部门ID
     */
    private Integer deptId;

    /**
     * 数据权限类型（1全部数据权限 2自定义数据权限 3本部门数据权限 4本部门及以下数据权限 5仅本人数据权限）
     * 当用户拥有全部数据权限或仅本人数据权限时，该字段有值，其他情况该字段为空
     */
    private String dataScope;

    /**
     * 数据权限列表（部门ID集合）
     * 仅当数据权限类型为自定义数据权限、本部门数据权限、本部门及以下数据权限时，该字段有值，其他情况该字段为空
     */
    private List<Integer> permissionDeptIds;



    /**
     * 所拥有可见的创建人id权限
     */
    @TableField(exist = false)
    private List<Long> sysUserIds;

    /**
     * 所拥有可见的企微userId权限
     */
    @TableField(exist = false)
    private List<String> wkUserIds;
}
