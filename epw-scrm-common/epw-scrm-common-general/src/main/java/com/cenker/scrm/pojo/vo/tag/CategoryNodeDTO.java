package com.cenker.scrm.pojo.vo.tag;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class CategoryNodeDTO implements Serializable {

    /**
     * 标签名
     */
    private String tagName;

    /**
     * 标签值
     */
    private String tagValue;


    @Data
    @NoArgsConstructor
    public static class SecondaryCategoryDTO implements Serializable {
        private String categoryName;
        private List<CategoryNodeDTO> tags;

        // 构造方法、Getter和Setter省略
    }


    @Data
    @NoArgsConstructor
    public static class PrimaryCategoryDTO implements Serializable {
        private String categoryName;
        private List<SecondaryCategoryDTO> secondaryCategories;

        // 构造方法、Getter和Setter省略
    }

}
