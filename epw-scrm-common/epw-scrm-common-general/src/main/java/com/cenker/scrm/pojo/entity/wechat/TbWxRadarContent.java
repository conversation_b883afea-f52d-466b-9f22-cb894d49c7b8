package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 智能物料内容表
 */
@TableName("tb_wx_radar_content")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TbWxRadarContent {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 智能物料id
   */
  private String radarId;
  /**
   * 类型 1 新建文章 2 公众号文章
   */
  private Integer type;
  /**
   * 封面
   */
  private String cover;
  /**
   * 摘要
   */
  private String digest;
  /**
   * 标题
   */
  private String title;
  /**
   * 内容
   */
  private String content;
  /**
   * 备注
   */
  private String remark;
  /**
   * 0 未删除 1已删除
   */
  private String delFlag;
  /**
   * 作者
   */
  private String author;
  /**
   * 基础阅读量
   */
  private Integer baseReadNum;
  /**
   * 原链接
   */
  private String baseUrl;

  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date createTime;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date updateTime;
  private String createBy;
  private String updateBy;

  /**
   * pdf转换图片
   */
  private String pdfImage;

  /**
   * 是否展示
   */
  private Boolean showStatus;

  /**
   * 链接
   */
  @TableField(exist = false)
  private String url;

  /**
   * 文末提示
   */
  private String tips;
}
