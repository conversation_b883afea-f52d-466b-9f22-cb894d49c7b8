package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbWxMassMessageDetail {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private String msgId;
  /**
   *  发送状态 0 失败 1 成功 2 待发送
   */
  private Integer checkStatus;
  private Long expectSend;
  private Long actualSend;
  private Long corpConfigId;
  private String sender;
  private String errCode;
  /**
   * 企微报错原文（若有）
   */
  private String errJson;
  private String failList;
  private String remark;
  private Long messageInfoId;
  private Integer delFlag;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  @TableField(fill = FieldFill.UPDATE)
  private Date updateTime;

}
