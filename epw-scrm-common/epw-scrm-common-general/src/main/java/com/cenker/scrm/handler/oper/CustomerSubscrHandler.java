package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.util.BuOperTrackUtils;

import java.util.List;

/**
 * @description 订阅栏目处理器
 */
public class CustomerSubscrHandler extends DefaultBuOperTrackHandler {
    public CustomerSubscrHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        RelatedResource relatedResource = operTrackParams.getRelatedResource();
        List<String> addTagList = relatedResource.getSubscrList();
        String contentStr = eventType.getContent();
        String value;
        switch (eventType) {
            case CUSTOMER_SUBSCR_EDIT_REMOVE:
            case CUSTOMER_SUBSCR_EDIT_ADD:
                value = BuOperTrackUtils.buildHtml(addTagList, "tag", null);
                // 标签
                contentStr = contentStr.replace("{0}", value);
                break;
            default:
                break;
        }
        return contentStr;
    }
}
