package com.cenker.scrm.pojo.dto.message;

import com.cenker.scrm.pojo.dto.condition.MessageConditionDTO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/30
 * @Description 群发消息参数传递 参照MassMessageChainDTO
 */
@Data
public class MassMessageChainWebDTO {

    private Long id;

    /**
     * 群发任务名
     */
    @Size(max = 30, message = "群发任务名称字数受限")
    private String massName;
    /**
     * 群发消息场景 1 企业群发 2 个人群发 3 sop企业群发... 暂时默认企业群发
     */
    private Integer massScene = 1;
    /**
     * 是否员工去重 是-单条走企微去重 否-多条指定员工
     */
    private Boolean userDistinct;
    /**
     * 发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settingTime;

    /**
     * 群发任务的类型，默认为single，表示发送给客户，group表示发送给客户群
     */
    private String chatType;

    /**
     * 是否定时任务 0 立即发送 1 定时发送
     */
    @NotNull(message = "请选择群发时间")
    @DecimalMax(value = "1", message = "非法请求-请选择群发时间")
    @DecimalMin(value = "0", message = "非法请求-请选择群发时间")
    private Integer timedTask;

    /**
     * 消息范围 0 全部客户/群  1 指定客户/群主 重构后转为逻辑字段
     */
    private Integer pushRange;

    /**
     * 群发消息文本
     */
    @Size(max = 1000, message = "群发消息文本字数受限")
    private String massContent;
    /**
     * 群发消息附件json 2023-05-16 重构更新统一 massAttachment改为attachments
     */
    @Valid
    @Size(max = 9, message = "最多支持9个附件")
    private List<WelcomeAttachmentVo> attachments;

    /**
     * 筛选发送条件json
     */
    private MessageConditionDTO messageCondition;
    /**
     * 企业主键id
     */
    private Long corpConfigId;
    private String corpId;
    private Long createBy;

    /**
     * 重构新增 标签、客户阶段、添加时间是否筛选 0 否 1 是
     */
    private Integer tagSelectType;
    private Integer stageSelectType;
    private Integer addSelectType;
    /**
     * 重构新增 员工是否筛选 0 否 1 是
     */
    //@NotNull(message = "请选择是否筛选客户")
    private Integer userSelectType;

    private String condition;

}
