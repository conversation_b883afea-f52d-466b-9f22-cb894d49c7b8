package com.cenker.scrm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/8/30
 * @Description 群聊相关配置
 */
@Component
@ConfigurationProperties(prefix = "wechat.group")
public class GroupConfig {

    /**
     * 社群活码扫码回调地址
     */
    private static String codeScanHandlerUrl;
    /**
     * 切换群的人数条件
     */
    private static Integer limitMemberCnt;
    /**
     * 社群活码微信登录回调地址
     */
    private static String codeScanWxRedirectUrl;

    public static String getCodeScanWxRedirectUrl() {
        return codeScanWxRedirectUrl;
    }

    public static String getCodeScanHandlerUrl() {
        return codeScanHandlerUrl;
    }

    public static Integer getLimitMemberCnt() {
        return limitMemberCnt;
    }

    public void setCodeScanWxRedirectUrl(String codeScanWxRedirectUrl) {
        GroupConfig.codeScanWxRedirectUrl = codeScanWxRedirectUrl;
    }

    public void setCodeScanHandlerUrl(String codeScanHandlerUrl) {
        GroupConfig.codeScanHandlerUrl = codeScanHandlerUrl;
    }

    public void setLimitMemberCnt(Integer limitMemberCnt) {
        GroupConfig.limitMemberCnt = limitMemberCnt;
    }
}
