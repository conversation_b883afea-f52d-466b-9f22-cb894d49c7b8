package com.cenker.scrm.util.http;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 微信公众号文章提取
 * <AUTHOR>
 */
public class WechatArticleInfoExtractor {

    /**
     * 微信文章URL
     */
    public static final String MP_ARTICLE_URL = "https://mp.weixin.qq.com/s/";
    /**
     * 文章选择器
     */
    public static final String ARTICLE_SELECTOR = "div#js_article";
    /**
     * 文章标题选择器
     */
    public static final String ARTICLE_TITLE_SELECTOR = "meta[property='og:title']";
    /**
     * 文章作者选择器
     */
    public static final String ARTICLE_AUTHOR_SELECTOR = "meta[property='og:article:author']";
    /**
     * 文章封面选择器
     */
    public static final String ARTICLE_COVER_SELECTOR = "meta[property='og:image']";
    /**
     * 文章摘要选择器
     */
    public static final String ARTICLE_ABSTRACT_SELECTOR = "meta[property^='og:description']";
    /**
     * 文章内容选择器
     */
    public static final String ARTICLE_CONTENT_SELECTOR = "div.rich_media_content";
    /**
     * 公众号名称选择器
     */
    public static final String ACCOUNT_NAME_SELECTOR = "#js_name";
    /**
     * 公众号名称选择器
     */
    public static final String PROFILE_SELECTOR = ".profile_nickname";
    /**
     * 内容属性
     */
    public static final String CONTENT_ATTR = "content";

    /**
     * 判断是否为微信文章URL
     * @param url
     * @return
     */
    public static boolean isWechatArticleUrl(String url) {
        return null != url && url.startsWith(MP_ARTICLE_URL);
    }

    /**
     * 获取文章标题
     * @param doc
     * @return
     */
    public static String getTitle(Document doc) {
        return getElementAttr(doc, ARTICLE_TITLE_SELECTOR, CONTENT_ATTR);
    }

    /**
     * 获取文章作者
     * @param doc
     * @return
     */
    public static String getAuthor(Document doc) {
        return getElementAttr(doc, ARTICLE_AUTHOR_SELECTOR, CONTENT_ATTR);
    }

    /**
     * 获取文章封面
     * @param doc
     * @return
     */
    public static String getCover(Document doc) {
        return getElementAttr(doc, ARTICLE_COVER_SELECTOR, CONTENT_ATTR);
    }

    /**
     * 获取文章摘要
     * @param doc
     * @return
     */
    public static String getAbstract(Document doc) {
        return getElementAttr(doc, ARTICLE_ABSTRACT_SELECTOR, CONTENT_ATTR);
    }

    /**
     * 获取文章内容
     * @param doc
     * @return
     */
    public static String getContent(Document doc) {
        Element contentElement = doc.selectFirst(ARTICLE_CONTENT_SELECTOR);

        if (contentElement == null) {
            contentElement = doc.selectFirst(ARTICLE_SELECTOR);
        }

        if (contentElement != null) {
            String contentText = contentElement.html();
            contentText = contentText.replace("data-src", "src");
            return contentText;
        }

        return null;
    }

    /**
     * 获取公众号名称
     * @param doc
     * @return
     */
    public static String getAccountName(Document doc) {
        // 提取公众号名称
        Element accountNameElement = doc.selectFirst(ACCOUNT_NAME_SELECTOR);
        if (accountNameElement == null) {
            // 如果没有找到带有id为'js_name' 的a标签，尝试其他选择器
            accountNameElement = doc.selectFirst(PROFILE_SELECTOR);
        }

        return accountNameElement != null ? accountNameElement.text() : "";
    }

    /**
     * 获取元素属性值
     * @param doc
     * @param selector
     * @param attr
     * @return
     */
    private static String getElementAttr(Document doc, String selector, String attr) {
        Elements metaElements = doc.select(selector);
        if (Objects.isNull(metaElements) || metaElements.isEmpty()) {
            return "";
        }

        for (Element element : metaElements) {
            if (element.hasAttr(attr)) {
                return element.attr(attr);
            }
        }

        return "";
    }


    /**
     * 模拟浏览器请求头
     *
     * @return
     */
    private Map<String, Object> buildHeaders() {
        Map<String, Object> map = new HashMap<>(new LinkedHashMap<>());
        map.put("Accept", "text/html, application/xhtml+xml, image/jxr, */*");
        map.put("Accept-Encoding", "gzip, deflate");
        map.put("Accept-Language", "zh-Hans-CN, zh-Hans; q=0.8, en-US; q=0.5, en; q=0.3");
        map.put("Host", "mp.weixin.qq.com");
        map.put("If-Modified-Since", "Sat, 04 Jan 2020 12:23:43 GMT");
        map.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
        return map;
    }

    public static void main(String[] args) {
        try {
            // 微信文章的URL
            String url = "https://mp.weixin.qq.com/s/XSqCvUohkfNo2WJkvTDr1g";

            if (isWechatArticleUrl(url)) {
                // 使用Jsoup连接到页面
                Document doc = Jsoup.connect(url).get();

                // 获取公众号名称
                String accountName = getAccountName(doc);
                System.out.println("公众号名称: " + accountName);

                // 提取标题
                String title = getTitle(doc);
                System.out.println("标题: " + title);

                // 提取作者
                String author = getAuthor(doc);
                System.out.println("作者: " + author);

                // 提取封面
                String coverUrl = getCover(doc);
                System.out.println("封面: " + coverUrl);

                // 提取摘要
                String abstractText = getAbstract(doc);
                System.out.println("摘要: " + abstractText);

                // 提取文章内容
                String content = getContent(doc);
                System.out.println("内容: " + content);
            } else {
                System.out.println("不是微信文章URL");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
