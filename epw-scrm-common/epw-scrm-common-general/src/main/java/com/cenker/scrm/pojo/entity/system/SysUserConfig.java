package com.cenker.scrm.pojo.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <p>
 * 用户个性化配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_user_config")
public class SysUserConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * sys_user表的user_id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 配置类型，sidebarSwitch: 侧边栏开关
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

}
