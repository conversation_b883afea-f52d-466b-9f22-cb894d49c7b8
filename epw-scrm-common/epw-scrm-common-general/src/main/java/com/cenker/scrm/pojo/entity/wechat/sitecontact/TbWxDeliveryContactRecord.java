package com.cenker.scrm.pojo.entity.wechat.sitecontact;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_delivery_contact_record")
public class TbWxDeliveryContactRecord {

  @TableId(type = IdType.AUTO)
  private Long id;
  private String signState;
  private Long contactId;
  private Long deliveryContactId;
  private Long siteId;
  private Long storeId;
  private Long userPriId;
  private Long deliveryUserId;
  private String userId;
  private String externalUserId;
  private Long extCustomerId;
  /*
   * 是否拉新
   */
  private Boolean newFlag;
  private String remark;
  private Boolean delFlag;
  private Long createBy;
  private Date createTime;
  private Long updateBy;
  private Date updateTime;
}
