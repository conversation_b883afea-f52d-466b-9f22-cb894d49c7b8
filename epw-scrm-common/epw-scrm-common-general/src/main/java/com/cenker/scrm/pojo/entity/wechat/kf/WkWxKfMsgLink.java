package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 link 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_link")
public class WkWxKfMsgLink extends WkWxKfMsgItemCommonResp {

    @TableField("`title`")
    private String title;
    private String desc;
    @TableField("`url`")
    private String url;
    private String thumbMediaId;
    private String picUrl;

    @Override
    public WkWxKfMsgLink init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.title = msgItem.getLink().getTitle();
        this.desc = msgItem.getLink().getDesc();
        this.url = msgItem.getLink().getUrl();
        this.thumbMediaId = msgItem.getLink().getThumb_media_id();
        this.picUrl = msgItem.getLink().getPicUrl();

        return this;
    }
}
