package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信客户群对象 tb_wx_customer_group
 *
 * <AUTHOR>
 * @date 2021-01-26
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
 // @ApiModel("客户群实体")
public class TbWxCustomerGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
     // @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
     // @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
     // @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
     // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * chatId
     */
     // @ApiModelProperty("微信群ID")
    @TableId
    private String chatId;

    /**
     * 群名
     */
     // @ApiModelProperty("群名")
     // @Excel(name = "群名")
    private String groupName;

    /**
     * 群公告
     */
     // @ApiModelProperty("群公告")
     // @Excel(name = "群公告")
    private String notice;

    /**
     * 群主userId
     */
     // @ApiModelProperty("群主userId")
     // @Excel(name = "群主userId")
    private String owner;

    /**
     * 0 - 正常;1 - 跟进人离职;2 - 离职继承中;3 - 离职继承完成
     */
     // @ApiModelProperty("状态")
     // @Excel(name = "0 - 正常;1 - 跟进人离职;2 - 离职继承中;3 - 离职继承完成")
    private Integer status;

    /**
     * 企业id
     */
     // @ApiModelProperty("企业id")
     // @Excel(name = "企业id")
    private String corpId;

    /**
     * 群解散时间
     */
     // @ApiModelProperty("群解散时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
     // @Excel(name = "群解散时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dismissDate;

     // @ApiModelProperty("解散状态")
     // @Excel(name = "0 - 未解散;1 - 解散")
    private Integer dismissStatus;

}