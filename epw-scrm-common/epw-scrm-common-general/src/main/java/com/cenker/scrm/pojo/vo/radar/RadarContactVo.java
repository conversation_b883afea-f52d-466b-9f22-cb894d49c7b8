package com.cenker.scrm.pojo.vo.radar;

import lombok.Data;

@Data
public class RadarContactVo {

    /**
     * 成员名片 0 未开启 1 已开启
     */
    private Integer contactStatus;

    /**
     * 员工名
     */
    private String staffName;
    /**
     * 员工头像
     */
    private String staffImg;

    /**
     * 员工活码地址
     */
    private String contactCode;

    /**
     * 当前登录微信用户是否为企微好友，0 不是 1 是
     */
    private Integer addedEnterpriseWeChat;
    /**
     * 正常好友数量
     */
    private Integer friends;
    /**
     * 是否已认证
     */
    private Boolean isAuth;
    /**
     * 客户认证链接
     */
    private String authLink;
}
