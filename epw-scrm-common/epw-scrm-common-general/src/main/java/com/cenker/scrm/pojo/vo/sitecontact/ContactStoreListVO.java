package com.cenker.scrm.pojo.vo.sitecontact;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/12/26
 * @Description 门店列表视图
 */
@Data
public class ContactStoreListVO {

    private String storeId;
    /**
     * 门店名
     */
    private String storeName;
    /**
     * 站点名
     */
    private String siteName;
    /**
     * 所属地区名
     */
    private String areaName;
    /**
     * 门店状态
     */
    private Integer areaStatus;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String remark;
}
