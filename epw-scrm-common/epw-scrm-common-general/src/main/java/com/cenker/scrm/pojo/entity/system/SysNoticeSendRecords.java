package com.cenker.scrm.pojo.entity.system;

import java.util.Date;

import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 系统通知发送记录对象 sys_notice_send_records
 * 
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
public class SysNoticeSendRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 公告ID */
    private Long noticeId;
    /**
     * 公告类型，1-公告，2-站内信
     */
    private Integer noticeType;

    /** 接收通知的用户Id，记录sys_user表的user_id */
    private String userId;

    /** 通知方式，1-管理后台，2-企微端 */
    private Integer noticeMode;

    /** 是否已读，0-未读，1-已读 */
    private Integer readStatus;

    /** 已读时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /** 应用消息id */
    private String msgId;

    /** 是否撤回，0-标识未撤回 1-标识撤回 */
    private Integer delFlag;

    /** 撤回时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date delTime;

    /** 是否已提醒，0-未提醒，1-已提醒 */
    private String hasReminder;

}
