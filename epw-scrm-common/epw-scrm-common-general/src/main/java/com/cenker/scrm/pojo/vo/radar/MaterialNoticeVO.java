package com.cenker.scrm.pojo.vo.radar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料发布通知VO类
 *
 * <AUTHOR>
 */
@Data
public class MaterialNoticeVO {

    /** 消息编号，发送方全局唯一 */
    @JsonProperty("msg_id")
    private String msgId;

    /** 消息类型，固定填写"001" */
    @JsonProperty("msg_type")
    private String msgType;

    /** 消息版本号，当前填写"V1.3" */
    @JsonProperty("msg_version")
    private String msgVersion;

    /** 消息创建时间 */
    @JsonProperty("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 物料名称 */
    @JsonProperty("material_name")
    private String materialName;

    /** 物料唯一性标识 */
    @JsonProperty("material_id")
    private String materialId;

    /** 物料简要说明 */
    @JsonProperty("material_desc")
    private String materialDesc;

    /** 物料最后修改时间 */
    @JsonProperty("last_modify_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModifyTime;

    /** 物料存储路径 */
    @JsonProperty("storage_path")
    private String storagePath;

    /** 物料版本 */
    @JsonProperty("material_version")
    private String materialVersion;

    /** 所属部门 */
    @JsonProperty("department")
    private String department;

    /** 文件类型 */
    @JsonProperty("file_type")
    private String fileType;

    /** 上传人员 */
    @JsonProperty("uploader")
    private String uploader;

    /** 作者列表 */
    @JsonProperty("owner")
    private String owner;

    /** 物料标签列表 */
    @JsonProperty("tag_list")
    private String tagList;

    /** 元数据列表 */
    @JsonProperty("meta_data_list")
    private String metaDataList;

    /** 发布模块名称 */
    @JsonProperty("publish_module_name")
    private String publishModuleName;

    /** 自定义发布规则 */
    @JsonProperty("publish_rule")
    private String publishRule;

    /** 外网长链接URL */
    @JsonProperty("outer_preview_url")
    private String outerPreviewUrl;

    /** 外网CDN加速URL */
    @JsonProperty("outer_cdn_url")
    private String outerCdnUrl;

    /** 外网短链接URL */
    @JsonProperty("outer_short_url")
    private String outerShortUrl;
}
