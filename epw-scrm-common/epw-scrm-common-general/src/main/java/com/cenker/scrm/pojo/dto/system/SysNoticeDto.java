package com.cenker.scrm.pojo.dto.system;

import com.cenker.scrm.pojo.entity.system.SysNotice;
import com.cenker.scrm.pojo.entity.system.SysNoticeSendRecords;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 系统通知的MQ消息对象
 * <AUTHOR>
 * @title SysNoticeMqDto
 * @date 2024/12/5 16:35
 * @description TODO
 */
@Data
public class SysNoticeDto implements Serializable {
    private static final long serialVersionUID = -2503249117964378855L;

    /**
     * 系统公告
     */
    private SysNotice sysNotice;

    /**
     * 通知到企微应用的消息集合
     */
    private Map<String, SysNoticeSendRecords> sendQwWorkNoticeMap;

    /**
     * 通知到管理后台的消息集合
     */
    private List<SysNoticeSendRecords> sendAdminNoticeList;

    /**
     * 通知企微员工Id列表
     */
    private List<String> corpUserIds;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 消息标题
     */
    private String noticeTitle;

    /**
     * 通知人
     */
    private String noticeUser;

    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 通知模式
     */
    private String noticeMode;
}
