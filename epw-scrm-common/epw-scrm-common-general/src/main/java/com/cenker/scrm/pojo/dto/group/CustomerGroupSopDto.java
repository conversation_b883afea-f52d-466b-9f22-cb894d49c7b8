package com.cenker.scrm.pojo.dto.group;


import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 客户群查询dto
 */
@Data
 // @ApiModel("客户群查询实体")
public class CustomerGroupSopDto extends BaseRequest {
    /**
     * 群名
     */
     // @ApiModelProperty("群名")
    private String groupName;
    /**
     * 群主
     */
    // @ApiModelProperty("群主")
    private String ownerName;

    /**
     * 开始时间
     */
     // @ApiModelProperty("开始时间")
    private String beginTime;
    /**
     * 结束时间
     */
     // @ApiModelProperty("结束时间")
    private String endTime;


}
