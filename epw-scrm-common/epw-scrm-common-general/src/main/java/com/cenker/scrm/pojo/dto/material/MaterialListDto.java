package com.cenker.scrm.pojo.dto.material;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;

@Data
public class MaterialListDto extends BaseRequest {

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 本地资源文件地址/海报时是背景图片
     */
    private String materialUrl;
    /**
     * 图片名称
     */
    private String materialName;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 音频时长
     */
    private String audioTime;
    /**
     * 企业id
     */
    private String corpId;

    /**
     * 素材类型
     */
    private Integer mediaType;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 侧边栏id 用来获取已抓取的素材
     */
    private String sideId;
    /**
     * 侧边栏id 用来排除已抓取的素材
     */
    private String sideIds;

    /**
     * 状态 PENDING_APPROVAL 待审核  REVOKED 已撤回 REJECTED 审核驳回 EXECUTING 使用中
     */
    private String status;

    /**
     * 审批人
     */
    private String approvalUser;

}
