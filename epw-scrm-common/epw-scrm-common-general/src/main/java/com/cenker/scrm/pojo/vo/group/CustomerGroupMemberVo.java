package com.cenker.scrm.pojo.vo.group;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 企业微信客户群对象 tb_wx_customer_group
 *
 * <AUTHOR>
 * @date 2021-01-26
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CustomerGroupMemberVo implements Serializable {
    private static final long serialVersionUID = 1L;

     // @Excel(name = "客户名称")
    private String userName;

     // @Excel(name = "客户名称", readConverterExp = "1=企业成员, 2=外部联系人)")
    private String type;

     // @Excel(name = "入群方式", readConverterExp = "1=由成员邀请入群（直接邀请入群）, 2=由成员邀请入群（通过邀请链接入群）, 3=通过扫描群二维码入群)")
    private String joinScene;

     // @Excel(name = "入群时间")
    private String joinTime;

     // @Excel(name = "群主")
    private String owner;
}