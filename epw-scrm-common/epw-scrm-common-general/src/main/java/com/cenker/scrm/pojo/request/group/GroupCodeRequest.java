package com.cenker.scrm.pojo.request.group;

import com.cenker.scrm.model.base.BaseWebRequest;
import com.cenker.scrm.pojo.dto.group.GroupCodeConfigDTO;
import com.cenker.scrm.pojo.valid.DeleteGroup;
import com.cenker.scrm.pojo.valid.DetailGroup;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.TagVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupCodeRequest extends BaseWebRequest {

    @Null(message = "无需该参数codeId", groups = InsertGroup.class)
    @NotNull(message = "缺失codeId", groups = {UpdateGroup.class, DetailGroup.class})
    private Long codeId;
    /**
     * 活码名称
     */
    @NotBlank(message = "活码名称不能为空", groups = {InsertGroup.class, UpdateGroup.class})
    @Size(max = 30, message = "活码名称字数受限", groups = {InsertGroup.class, UpdateGroup.class})
    private String codeName;

    /**
     * 活码类型 1 顺序入群 2 分类入群
     */
    @NotNull(message = "缺失活码类型", groups = {InsertGroup.class})
    @Max(value = 2, groups = {InsertGroup.class, UpdateGroup.class})
    @Min(value = 1, groups = {InsertGroup.class, UpdateGroup.class})
    private Integer codeType;

    /**
     * 是否自动创建新群 0 否 1 是
     */
    @NotNull(message = "缺失入群设置状态", groups = {InsertGroup.class, UpdateGroup.class})
    private Boolean autoCreateRoom;

    /**
     * 自动建群新群名称 当is_auto_create_room为1时有效 最长40个utf8字符
     */
    @Size(max = 10, message = "新群名称字数受限", groups = {InsertGroup.class, UpdateGroup.class})
    private String roomBaseName;

    /**
     * 自动建群的群起始序号
     */
    @Max(message = "群起始序号受限", value = 99, groups = {InsertGroup.class, UpdateGroup.class})
    private Long roomBaseId;

    /**
     * 备用员工关联活码id
     */
    private Long spareCodeId;

    /**
     * 备用员工活码/群码地址
     */
    private String spareCodeUrl;

    /**
     * 社群列表
     */
    @NotNull(message = "请选择社群", groups = {InsertGroup.class, UpdateGroup.class})
    @Size(min = 1, max = 200, message = "请至少选择一个社群", groups = {InsertGroup.class, UpdateGroup.class})
    @Valid
    private List<GroupCodeConfigDTO> groupList;

    /**
     * 创建人（列表查询参数）
     */
    private String createUserName;

    /**
     * 活码id （删除参数）
     */
    @NotNull(message = "请选择删除社群活码", groups = {DeleteGroup.class})
    @Size(min = 1, max = 200, message = "请至少选择一个社群活码", groups = {DeleteGroup.class})
    private List<Long> codeIdList;

    /**
     * 数据区趋势查询参数
     */
    private Date queryBeginTime;
    private Date queryEndTime;
    /**
     * 1 进群 2 退群
     */
    private Integer changeType;

    /**
     * 标签列表
     */
    private List<TagVO> tagList;

    /**
     * 使用状态，1：使用中，0：已废弃
     */
    private Integer useStatus;


}
