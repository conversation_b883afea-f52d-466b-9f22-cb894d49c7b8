package com.cenker.scrm.pojo.vo.external;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WxCustomerBindingVO implements Serializable {

    /**
     * 是否认证
     */
    private Boolean isAuth;

    /**
     * 绑定的UnionId
     */
    private String unionId;

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 客户名
     */
    private String custName;


    public WxCustomerBindingVO(Boolean isAuth){
        this.isAuth=isAuth;
    }


}
