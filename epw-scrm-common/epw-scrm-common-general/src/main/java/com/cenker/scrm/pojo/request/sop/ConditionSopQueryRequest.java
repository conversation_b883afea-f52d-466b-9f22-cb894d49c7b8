package com.cenker.scrm.pojo.request.sop;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/7/19
 * @Description 条件sop查询
 */
@Data
public class ConditionSopQueryRequest extends BaseSopRequest {
    /**
     * 执行状态 1 已完成 2 未完成 3 已过期
     */
    private Integer executeStatus;

    /**
     * 任务版本号
     */
    private String contentVersion;

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 员工id
     */
    private String userId;

    /**
     * 阶段id-旅程sop
     */
    private Long stageId;

    /**
     * 是否需要计算任务开始时间（条件、旅程sop需要计算）
     */
    private Boolean calculateDay = true;
    private String taskExecuteDayTime;
}
