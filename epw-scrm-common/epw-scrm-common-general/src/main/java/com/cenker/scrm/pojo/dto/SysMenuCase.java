package com.cenker.scrm.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/12/29
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysMenuCase {
    /**
     * 菜单ID
     */
    private String menuId;
    /**
     * 案例链接
     */
    private String caseLink;

    /**
     * 使用手册链接
     */
    private String helpLink;

    /**
     * 简介
     */
    private String remark;

    /**
     * 简介说明标题
     */
    private String helpTitle;

    /**
     * 简介说明图标
     */
    private String helpIcon;
    /**
     * 角标
     */
    private String cornerMark;
    private String cornerMarkBackGround;
    private String cornerMarkColor;
}
