package com.cenker.scrm.pojo.vo.statistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * 数据统计-客户群数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
public class StatisticCustomerGroupSummaryVo {

    /**
     * 统计日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String statisticDate;
    /**
     *群聊总数
     */
    private Integer groupTotal;
    /**
     *客户群成员总数
     */
    private Integer groupMemberTotal;
    /**
     *客户群员工总数
     */
    private Integer groupStaffTotal;
    /**
     *新增群聊数
     */
    private Integer newTotal;
    /**
     *解散群聊数
     */
    private BigDecimal dissTotal;
    /**
     *活跃群聊数
     */
    private Integer groupActiveTotal;
    /**
     *群聊消息数
     */
    private Integer chatTotal;
    /**
     *入群人数
     */
    private Integer memberInTotal;
    /**
     *退群人数
     */
    private Integer memberOutTotal;
    /**
     *活跃客户数
     */
    private Integer customerActiveTotal;
    /**
     *净增人数
     */
    private Integer netNewMemberTotal;

}
