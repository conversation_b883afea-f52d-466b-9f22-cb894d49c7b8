package com.cenker.scrm.handler.oper;

import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.external.CustomerPortraitVo;
import com.cenker.scrm.util.DateUtils;

import java.util.Objects;

/**
 * 客户信息变更
 */
public class CustomerInfoEditHandler extends DefaultBuOperTrackHandler {
    public CustomerInfoEditHandler(TrackEventTypeEnum typeEnum) {
        this.eventType = typeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        TbWxExtCustomer extCustomer = operTrackParams.getCustomer();
        CustomerPortraitVo weCustomerPortrait = operTrackParams.getWeCustomerPortrait();
        String fieldName = weCustomerPortrait.getFieldName();
        String oldSource = "";
        String newSource = "";
        if ("name".equals(fieldName) && !Objects.equals(weCustomerPortrait.getName(),extCustomer.getName())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getName()) ? "昵称":"昵称：" + extCustomer.getName() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getName()) ? "昵称" : "昵称：" + weCustomerPortrait.getName() + "";
        } else if ("gender".equals(fieldName) && !Objects.equals(weCustomerPortrait.getGender(),extCustomer.getGender())) {
            // 0 未知 1 男 2女
            oldSource = extCustomer.getGender() == 0 ? "性别：未知" : extCustomer.getGender() == 1 ? "性别：男" : "性别：女";
            newSource = weCustomerPortrait.getGender() == 0 ? "性别：未知" : weCustomerPortrait.getGender() == 1 ? "性别：男" : "性别：女";
        } else if ("birthday".equals(fieldName) && !Objects.equals(weCustomerPortrait.getBirthday(),extCustomer.getBirthday())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getBirthday()) ? "生日":"生日：" + DateUtils.getDate(extCustomer.getBirthday()) + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getBirthday()) ? "生日" : "生日：" + DateUtils.getDate(weCustomerPortrait.getBirthday()) + "";
        } else if ("remarkMobiles".equals(fieldName) && !Objects.equals(weCustomerPortrait.getRemarkMobiles(),extCustomer.getMobiles())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getMobiles()) ? "手机号":"手机号：" + extCustomer.getMobiles() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getRemarkMobiles()) ? "手机号" : "手机号：" + weCustomerPortrait.getRemarkMobiles() + "";
        } else if ("address".equals(fieldName) && !Objects.equals(weCustomerPortrait.getAddress(),extCustomer.getAddress())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getAddress()) ? "地址":"地址：" + extCustomer.getAddress() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getAddress()) ? "地址" : "地址：" + weCustomerPortrait.getAddress() + "";
        } else if ("qq".equals(fieldName) && !Objects.equals(weCustomerPortrait.getQq(),extCustomer.getQq())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getQq()) ? "QQ":"QQ：" + extCustomer.getQq() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getQq()) ? "QQ" : "QQ：" + weCustomerPortrait.getQq() + "";
        } else if ("email".equals(fieldName) && !Objects.equals(weCustomerPortrait.getEmail(),extCustomer.getEmail())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getEmail()) ? "邮箱":"邮箱：" + extCustomer.getEmail() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getEmail()) ? "邮箱" : "邮箱：" + weCustomerPortrait.getEmail() + "";
        } else if ("position".equals(fieldName) && !Objects.equals(weCustomerPortrait.getPosition(),extCustomer.getPosition())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getPosition()) ? "职位":"职位：" + extCustomer.getPosition() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getPosition()) ? "职位" : "职位：" + weCustomerPortrait.getPosition() + "";
        } else if ("corpName".equals(fieldName) && !Objects.equals(weCustomerPortrait.getCorpName(),extCustomer.getCorpName())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getCorpName()) ? "企业简称":"企业简称：" + extCustomer.getCorpName() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getCorpName()) ? "企业简称" : "企业简称：" + weCustomerPortrait.getCorpName() + "";
        } else if ("corpFullName".equals(fieldName) && !Objects.equals(weCustomerPortrait.getCorpFullName(),extCustomer.getCorpFullName())) {
            oldSource = ObjectUtil.isEmpty(extCustomer.getCorpFullName()) ? "所在企业":"所在企业：" + extCustomer.getCorpFullName() + "";
            newSource = ObjectUtil.isEmpty(weCustomerPortrait.getCorpFullName()) ? "所在企业" : "所在企业：" + weCustomerPortrait.getCorpFullName() + "";
        }

        String contentStr = eventType.getContent();
        // oldValue
        contentStr = contentStr.replace("{0}", oldSource);
        // newValue
        contentStr = contentStr.replace("{1}", newSource);
        return contentStr;
    }

}
