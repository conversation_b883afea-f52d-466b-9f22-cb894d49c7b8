package com.cenker.scrm.pojo.vo.sitecontact;

import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/23
 * @Description 站点活码信息
 */
@Data
public class ContactSiteInfoVO {

    private String id;

    /**
     * 执行者用户列表 最多配置100个使用成员（包含部门展开后的成员）
     */
    private List<UserVO> userList;

    /**
     * 联系我添加标签 添加客户时自动添加
     */
    private List<TagVO> tagList;

    /**
     * 外部客户添加时是否无需验证，默认为true(1)
     */
    private Boolean skipVerify;

    /**
     * 联系方式的备注信息，用于助记，不超过30个字符
     */
    private String remark;

    /**
     * 欢迎语
     */
    private String welContent;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 添加客户数
     */
    private Integer addCount;

    /**
     * 使用成员
     */
    private String userName;

    /**
     * 使用成员查询
     */
    private List<String> corpUserId;

    /**
     * 2022-05-31 新增渠道活码创建来源标识 1 web  2 侧边栏 3 工作台
     */
    private Integer since;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点区域id
     */
    private Long siteParentId;

    /**
     * 所属地区
     */
    private String areaName;
    /**
     * 站点名
     */
    private String siteName;

    /**
     * 1 省 2 市 3 门店
     */
    private Integer areaLevel;

    /**
     * 前端所需回显 格式[省id,市id]
     */
    private String [] areaIds;
}
