package com.cenker.scrm.pojo.dto.sop;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description 条件sop内容
 */
@Data
public class SopConditionContentDTO extends SopContentDTO {
    /**
     * 任务开始时间 条件sop限定格式HH:mm:ss
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private Date startTime;
}
