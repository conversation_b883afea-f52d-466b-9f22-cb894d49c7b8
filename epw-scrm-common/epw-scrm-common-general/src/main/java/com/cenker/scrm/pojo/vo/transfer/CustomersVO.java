package com.cenker.scrm.pojo.vo.transfer;

import com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecordItem;
import lombok.Data;

import java.util.List;

import static com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecordItem.filterItemsByTypeAndStatus;

@Data
public class CustomersVO implements java.io.Serializable{
    /**
     * 继承客户总数。
     * 表示在此次继承中涉及的客户数量。
     */
    private int totalCustomers;

    /**
     * 继承成功总数。
     * 表示此次继承成功转移的客户数量。
     */
    private int successTransferredCustomers;

    /**
     * 继承中的客户总数。
     */
    private int totalTransferringCustomers;

    /**
     * 继承失败总数。
     * 表示此次继承失败转移的客户数量。
     */
    private int failedTransferredCustomers;

    /**
     * 继承过于频繁的总数。
     */
    private int tooFrequentTransferredCustomers;

    /**
     * 已拒绝总数
     */
    private int refusedTransferredCustomers;

    /**
     * 达到上限
     */
    private int reachLimitTransferredCustomers;

    public static CustomersVO calculateCustomersDetails(List<TbWxTransferRecordItem> items) {
        CustomersVO customers = new CustomersVO();
        customers.setTotalCustomers(filterItemsByTypeAndStatus(items, 0, null).size());
        customers.setSuccessTransferredCustomers(filterItemsByTypeAndStatus(items, 0, "1").size());
        customers.setTotalTransferringCustomers(filterItemsByTypeAndStatus(items, 0, "2").size());
        customers.setRefusedTransferredCustomers(filterItemsByTypeAndStatus(items, 0, "3").size());
        customers.setFailedTransferredCustomers(filterItemsByTypeAndStatus(items, 0, "5").size());
        customers.setTooFrequentTransferredCustomers(filterItemsByTypeAndStatus(items, 0, "4").size());
        customers.setReachLimitTransferredCustomers(filterItemsByTypeAndStatus(items, 0, "4").size());
        return customers;

    }

}
