package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentTaskInfo {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 1 标识企业发表  2 标识个人发表
     */
    private Integer momentType;
    /**
     * 是否定时任务 0 常规 1 定时发送
     */
    private Integer timedTask;
    /**
     * 发送时间
     */
    private Date settingTime;
    /**
     * 消息发送状态
     * PENDING_EXEC 待执行 FINISHED 已完成 EXECUTING 执行中
     * EXEC_EXCEPTION 执行异常 PENDING_APPROVAL 待审核 REJECTED 已退回
     * CANCELED 已取消 REVOKED 已撤回
     */
    private String checkStatus;
    /**
     * 0 标识未删除 1 标识删除
     */
    private Integer delFlag;
    /**
     * 企业id
     */
    private String corpId;
    /**
     * 异步任务id 以获取创建的结果
     */
    private String jobId;
    /**
     * 朋友圈id 存在则代表创建成功
     */
    private String momentId;
    /**
     * 消息文本内容
     */
    private String content;
    /**
     * 附件类型 可选image、link或者video
     */
    private String msgType;
    /**
     * 附件内容 json格式
     */
    private String msgContent;
    /**
     * 条件内容
     */
    private String sendCondition;

    private String createBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String updateBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 部门id
     */
    private Integer deptId;
    /**
     * 启用了审批
     * true:启用审批
     * false:未启用审批
     */
    private boolean enableApproval;
    /**
     * 审批人账号
     */
    private String approvalUser;
    /**
     * 意见
     */
    private String approvalRemark;
    /**
     * EXEC_EXCEPTION 执行异常时记录异常信息
     */
    private String errorMsg;
}
