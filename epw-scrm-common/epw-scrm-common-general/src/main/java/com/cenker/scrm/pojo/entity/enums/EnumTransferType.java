package com.cenker.scrm.pojo.entity.enums;

/**
 * 客户转移类型枚举类。
 */
public enum EnumTransferType {
    ON_JOB(0, "在职继承"),
    OFF_JOB(1, "离职继承");

    private final int value;
    private final String desc;

    EnumTransferType(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }


}
