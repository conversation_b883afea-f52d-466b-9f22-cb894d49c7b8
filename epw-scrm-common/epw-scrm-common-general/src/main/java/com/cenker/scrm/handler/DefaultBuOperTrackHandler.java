package com.cenker.scrm.handler;

import cn.hutool.json.JSONUtil;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.enums.TrackTypeEnum;
import com.cenker.scrm.handler.oper.*;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.util.BuOperTrackUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public abstract class DefaultBuOperTrackHandler {

    public static final Map<String, DefaultBuOperTrackHandler> HANDLER = new HashMap<>();
    public TrackEventTypeEnum eventType;
    static {
        HANDLER.put(TrackEventTypeEnum.CUSTOMER_INFO_EDIT.getSubEventType(), new CustomerInfoEditHandler(TrackEventTypeEnum.CUSTOMER_INFO_EDIT));

        HANDLER.put(TrackEventTypeEnum.CORPORATE_TAG_EDIT_ADD.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.CORPORATE_TAG_EDIT_ADD));
        HANDLER.put(TrackEventTypeEnum.CORPORATE_TAG_EDIT_REMOVE.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.CORPORATE_TAG_EDIT_REMOVE));

        HANDLER.put(TrackEventTypeEnum.CORPORATE_TAG_EDIT_SYNC_ADD.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.CORPORATE_TAG_EDIT_SYNC_ADD));
        HANDLER.put(TrackEventTypeEnum.CORPORATE_TAG_EDIT_SYNC_REMOVE.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.CORPORATE_TAG_EDIT_SYNC_REMOVE));

        HANDLER.put(TrackEventTypeEnum.PERSONAL_TAG_EDIT_ADD.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.PERSONAL_TAG_EDIT_ADD));
        HANDLER.put(TrackEventTypeEnum.PERSONAL_TAG_EDIT_REMOVE.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.PERSONAL_TAG_EDIT_REMOVE));

        HANDLER.put(TrackEventTypeEnum.BULK_EDIT_TAG_ADD.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.BULK_EDIT_TAG_ADD));
        HANDLER.put(TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE.getSubEventType(), new CustomerTagChangeHandler(TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE));

        HANDLER.put(TrackEventTypeEnum.CUSTOMER_CHURN_CUST.getSubEventType(), new CustomerChurnHandler(TrackEventTypeEnum.CUSTOMER_CHURN_CUST));
        HANDLER.put(TrackEventTypeEnum.CUSTOMER_CHURN_EMPE.getSubEventType(), new CustomerChurnHandler(TrackEventTypeEnum.CUSTOMER_CHURN_EMPE));

        HANDLER.put(TrackEventTypeEnum.GROUP_JOIN_INVITE.getSubEventType(), new CustomerGroupHandler(TrackEventTypeEnum.GROUP_JOIN_INVITE));
        HANDLER.put(TrackEventTypeEnum.GROUP_JOIN_SCAN.getSubEventType(), new CustomerGroupHandler(TrackEventTypeEnum.GROUP_JOIN_SCAN));
        HANDLER.put(TrackEventTypeEnum.GROUP_LEAVE_REMOVED.getSubEventType(), new CustomerGroupHandler(TrackEventTypeEnum.GROUP_LEAVE_REMOVED));
        HANDLER.put(TrackEventTypeEnum.GROUP_LEAVE_VOLUNTARY.getSubEventType(), new CustomerGroupHandler(TrackEventTypeEnum.GROUP_LEAVE_VOLUNTARY));

        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_CONDI_ADD.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_CONDI_ADD));
        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_CONDI_REMOVE.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_CONDI_REMOVE));
        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_RADAR.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_RADAR));
        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_GROUP.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_GROUP));
        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_SMART_MATERIAL.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_SMART_MATERIAL));
        HANDLER.put(TrackEventTypeEnum.AUTO_TAGGING_LINK.getSubEventType(), new AutoTaggingHandler(TrackEventTypeEnum.AUTO_TAGGING_LINK));

        HANDLER.put(TrackEventTypeEnum.CUSTOMER_ADD_RADAR.getSubEventType(), new CustomerAddHandler(TrackEventTypeEnum.CUSTOMER_ADD_RADAR));
        HANDLER.put(TrackEventTypeEnum.CUSTOMER_ADD_LINK.getSubEventType(), new CustomerAddHandler(TrackEventTypeEnum.CUSTOMER_ADD_LINK));
        HANDLER.put(TrackEventTypeEnum.CUSTOMER_ADD_OTHER.getSubEventType(), new CustomerAddHandler(TrackEventTypeEnum.CUSTOMER_ADD_OTHER));

        HANDLER.put(TrackEventTypeEnum.SMART_MATERIAL_ACTION_VIEW.getSubEventType(), new SmartMaterialActionHandler(TrackEventTypeEnum.SMART_MATERIAL_ACTION_VIEW));
        HANDLER.put(TrackEventTypeEnum.SMART_MATERIAL_ACTION_SHARE.getSubEventType(), new SmartMaterialActionHandler(TrackEventTypeEnum.SMART_MATERIAL_ACTION_SHARE));
        HANDLER.put(TrackEventTypeEnum.SMART_MATERIAL_AUTO_TAG.getSubEventType(), new SmartMaterialActionHandler(TrackEventTypeEnum.SMART_MATERIAL_AUTO_TAG));

        HANDLER.put(TrackEventTypeEnum.CUSTOMER_AUTH.getSubEventType(), new CustomerAuthHandler(TrackEventTypeEnum.CUSTOMER_AUTH));

        HANDLER.put(TrackEventTypeEnum.MOMENT_INTERACTION_COMMENT.getSubEventType(), new MomentInteractionHandler(TrackEventTypeEnum.MOMENT_INTERACTION_COMMENT));
        HANDLER.put(TrackEventTypeEnum.MOMENT_INTERACTION_LIKE.getSubEventType(), new MomentInteractionHandler(TrackEventTypeEnum.MOMENT_INTERACTION_LIKE));

        HANDLER.put(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_REMOVE.getSubEventType(), new CustomerSubscrHandler(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_REMOVE));
        HANDLER.put(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_ADD.getSubEventType(), new CustomerSubscrHandler(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_ADD));
    }

    public static DefaultBuOperTrackHandler getEventHandler(String eventType) {
        return HANDLER.get(eventType);
    }

    protected abstract String getContent(OperTrackParams operTrackParams);

    public BuOperTrack genBuOperTrack(OperTrackParams operTrackParams) {
        // 先获取相关资源对象，避免重复调用 getRelatedResource()
        RelatedResource relatedResource = operTrackParams.getRelatedResource();
        // 使用 null-safe 的方式获取 fromWechat 值
        boolean fromWechat = relatedResource != null && relatedResource.getFromWechat() != null && relatedResource.getFromWechat();
        String jsonStr = relatedResource == null ? "{}" : JSONUtil.toJsonStr(relatedResource);
        TrackTypeEnum trackTypeEnum = BuOperTrackUtils.getTrackTypeEnum(eventType.getSubEventType(), fromWechat);
        BuOperTrack buOperTrack = new BuOperTrack();
        buOperTrack.setExternalUserId(operTrackParams.getExternalUserId());
        buOperTrack.setUserId(operTrackParams.getUserId());
        buOperTrack.setCorpId(operTrackParams.getCorpId());
        buOperTrack.setTrackType(trackTypeEnum.getCode());
        buOperTrack.setOperId(operTrackParams.getOperId());
        buOperTrack.setEventType(eventType.getEventType());
        buOperTrack.setSubEventType(eventType.getSubEventType());
        buOperTrack.setTitle(eventType.getTitle());
        buOperTrack.setContent(getContent(operTrackParams));
        buOperTrack.setRelatedResource(jsonStr);
        buOperTrack.setCreateTime(operTrackParams.getCreateTime() == null ? new Date() : operTrackParams.getCreateTime());
        return buOperTrack;
    }
}