package com.cenker.scrm.model.enummodel;

/**
 * <AUTHOR>
 * @create 2021/6/28 14:03
 * 响应码枚举值
 */
public enum ResponseCodeEnum {
    /**
     * 响应码枚举值
     */
    ResponseCode_0000("请求成功", "0000"),

    ResponseCode_1001("请求失败", "1001"),

    ResponseCode_1002("无效的参数", "1002"),

    ResponseCode_1003("操作过快，稍等5秒之后再操作", "1003"),

    ResponseCode_1004("内部错误", "1004"),

    ResponseCode_1005("参数校验失败", "1005"),

    ResponseCode_1006("签名错误", "1006"),

    ResponseCode_1007("未查询到符合条件的数据", "1007"),

    ResponseCode_1008("获取配置数据错误", "1008"),

    ResponseCode_1009("获取返回异常", "1009"),

    ResponseCode_1010("反序列化为空", "1010"),

    ResponseCode_1011("数据已存在", "1011"),

    ResponseCode_2001("未查询到符合条件的交通方案", "2001"),

    ResponseCode_2100("需要添加企微才能参与游戏哦~", "2100"),

    ResponseCode_9000("系统正在开小差", "9000"),

    ResponseCode_9001("访问接口授权失败", "9001"),

    ResponseCode_9002("活动已下线", "9002"),

    ResponseCode_9003("活动未开始", "9003"),

    ResponseCode_9005("兑换失败，请联系客服", "9005"),

    ResponseCode_9006("无权限操作", "9006"),

    ResponseCode_9007("发放奖品出了点小故障，请联系客服", "9007"),
    ;



    /**
     * 状态码描述
     */
    private String description;

    /**
     * 状态码
     */
    private String code;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    private ResponseCodeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }

    public static String getDescription(String code) {
        for (ResponseCodeEnum rce : ResponseCodeEnum.values()) {
            if (rce.getCode().equals(code)) {
                return rce.description;
            }
        }
        return null;
    }
}
