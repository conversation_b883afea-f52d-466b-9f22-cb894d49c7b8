package com.cenker.scrm.pojo.entity.wechat.sop;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.cenker.scrm.pojo.dto.condition.SopConditionDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description sop条件信息表
 * <AUTHOR> @Date 2023-07-25 
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("ck_sop_condition_info")
public class SopConditionInfo implements Serializable {
	private static final long serialVersionUID =  6372228995264643610L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * sop唯一标识
	 */
	private Long sopId;

	/**
	 * 旅程id
	 */
	private Long journeyId;

	/**
	 * 旅程阶段id
	 */
	private Long stageId;

	/**
	 * 筛选发送条件
	 */
//	@Deprecated
//	@TableField(typeHandler = JacksonTypeHandler.class)
	private String sendCondition;

	private String viewSendCondition;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业id
	 */
	private String corpId;

	/**
	 * 逻辑删除 0 表示未删除，1 表示删除
	 */
	@TableField(value = "is_deleted")
	@TableLogic
	private Boolean deleted;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}

