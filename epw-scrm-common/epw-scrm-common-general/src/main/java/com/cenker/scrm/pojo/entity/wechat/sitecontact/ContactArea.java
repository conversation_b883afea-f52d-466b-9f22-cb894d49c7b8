package com.cenker.scrm.pojo.entity.wechat.sitecontact;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("contact_area")
public class ContactArea {

  @TableId(type = IdType.AUTO)
  private Long id;

  /**
   * 唯一id
   */
  private String signId;
  /**
   * 省份名/城市/地区名
   */
  private String areaName;
  /**
   * 1:省份province,2:市city
   */
  private Integer areaLevel;
  /**
   * 地区父节点
   */
  private Long parentId;
  /**
   * 启用状态 0 未启用 1 启用
   */
  private Integer areaStatus;
  /**
   * 排序号
   */
  private Integer orderNum;
  private String remark;
  @TableLogic
  private Integer delFlag;
  /**
   * 企业ID
   */
  private Long corpId;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  private Long updateBy;
  private Date updateTime;

  /**
   * 子分类
   */
  @TableField(exist = false)
  private List<ContactArea> children = new ArrayList<>();

  /**
   * 配送员数
   */
  @TableField(exist = false)
  private Integer deliveryCnt;
}
