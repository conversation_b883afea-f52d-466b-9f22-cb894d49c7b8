package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 裂变任务员工列对象 we_task_fission_staff
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Data
public class TbWxFissionStaff {
    private static final long serialVersionUID = -8615355081305655759L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务裂变表id
     */
    private String fissionId;

    /**
     * 员工或机构，1 组织机构 2 成员 3 全部
     */
    private Integer staffType;

    /**
     * 员工或组织机构id,为全部时为空
     */
    private String staffId;

    /**
     * 员工或组织机构姓名，类型为全部时，为空
     */
    private String staffName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("fissionId", getFissionId())
                .append("staffType", getStaffType())
                .append("staffId", getStaffId())
                .append("staffName", getStaffName())
                .toString();
    }
}
