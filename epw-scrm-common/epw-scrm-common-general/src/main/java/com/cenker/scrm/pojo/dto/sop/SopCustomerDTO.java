package com.cenker.scrm.pojo.dto.sop;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/7/17
 * @Description
 */
@Data
public class SopCustomerDTO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 添加了此外部联系人的企业成员userid
     */
    private String userId;

    /**
     * 客户id
     */
    private String externalUserId;

    /**
     * 客户昵称
     */
    private String externalUserName;
}
