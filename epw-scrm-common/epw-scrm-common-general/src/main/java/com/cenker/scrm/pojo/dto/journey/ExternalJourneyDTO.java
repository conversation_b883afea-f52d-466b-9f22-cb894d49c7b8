package com.cenker.scrm.pojo.dto.journey;


import com.cenker.scrm.pojo.valid.DeleteGroup;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.SelectGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/20
 * @Description
 */
@Data
public class ExternalJourneyDTO{
    private static final long serialVersionUID = -2776724100003618796L;

    /**
     * 旅程id
     */
    @Null(message = "无需该参数journeyId",groups = InsertGroup.class)
    @NotNull(message = "缺失journeyId",groups = {DeleteGroup.class, UpdateGroup.class, SelectGroup.class})
    private Long journeyId;


    /**
     * 旅程名
     */
    @NotBlank(message = "旅程名不能为空",groups = {InsertGroup.class,UpdateGroup.class})
    @Size(max = 10,message = "旅程字数受限",groups = {InsertGroup.class,UpdateGroup.class})
    private String journeyName;

    /**
     * 企业id
     */
    @Null(message = "无需该参数")
    private Long corpId;
    @Null(message = "无需该参数createBy")
    private Long createBy;
    @Null(message = "无需该参数updateBy")
    private Long updateBy;

    /**
     * 旅程阶段
     */
    @Valid
    @NotEmpty(message = "旅程阶段不能为空",groups = {InsertGroup.class,UpdateGroup.class})
    @Size(max = 10,message = "阶段数受限",groups = {InsertGroup.class,UpdateGroup.class})
    private List<ExternalJourneyStageDTO> stages;

    /**
     * 排序
     */
    @Null(message = "无需该参数orderNum")
    private Integer orderNum;

    /**
     * 组排序
     */
    private List<ExternalJourneyDTO> orderList;

    private Integer pageNum;
    private Integer pageSize;
    /**
     * 查询公司或客户名
     */
    private String name;

    /**
     * 是否自动化 0 否 1是
     */
    private Boolean automation;
}
