package com.cenker.scrm.pojo.entity.statistic;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-热词统计
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_hot_word")
public class TbStatisticHotWord {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 热词名称
     */
    @Excel(name = "热词", sort = 2)
    private String hotWord;

    /**
     * 近似词，多个逗号分隔
     */
    @Excel(name = "近似词", sort = 3)
    private String synonWords;

    /**
     * 员工触发次数
     */
    @Excel(name = "员工触发次数", sort = 4)
    private Integer staffTriggerTimes;

    /**
     * 员工触发人数
     */
    @Excel(name = "触发员工人数", sort = 6)
    private Integer staffTriggerNum;

    /**
     * 客户触发次数
     */
    @Excel(name = "客户触发次数", sort = 5)
    private Integer customerTriggerTimes;

    /**
     * 客户触发人数
     */
    @Excel(name = "触发客户人数", sort = 7)
    private Integer customerTriggerNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 热词id
     */
    private Long hotId;

}
