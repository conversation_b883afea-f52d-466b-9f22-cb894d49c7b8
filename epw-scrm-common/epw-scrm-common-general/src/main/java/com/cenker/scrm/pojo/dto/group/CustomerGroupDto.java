package com.cenker.scrm.pojo.dto.group;


import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 客户群查询dto
 */
@Data
 // @ApiModel("客户群查询实体")
public class CustomerGroupDto extends BaseRequest implements Serializable {
    /**
     * 群名
     */
     // @ApiModelProperty("群名")
    private String groupName;
    /**
     * 群主
     */
    // @ApiModelProperty("群主列表")
    private String ownerName;
    /**
     * 群主列表
     */
     // @ApiModelProperty("群主列表")
    private List<String> owner;
    /**
     * 开始时间
     */
     // @ApiModelProperty("开始时间")
    private String beginTime;
    /**
     * 结束时间
     */
     // @ApiModelProperty("结束时间")
    private String endTime;

     // @ApiModelProperty("企业ID")
    private String corpId;

     // @ApiModelProperty("微信群ID")
    private String groupId;

    private String userId;

     // @ApiModelProperty("查询参数")
    private Map<String, String> params;

    /**
     * 企业管理员
     */
    private Boolean administrator;
    /**
     * 群成员名
     */
    private String memberName;
    /**
     * 成员属性 1 客户 2 员工 3 非客户
     */
    private Integer memberType;

    // 获取企业进退群成员数据
    /**
     * 1 入群客户 2 退群客户
     */
    private Integer behaviorType;
    /**
     * 查询范围 1 今天 2 昨天 3 最近7天
     */
    private Integer timeScope;
    /**
     * 外部联系人默认头像
     */
    private String avatar;
}
