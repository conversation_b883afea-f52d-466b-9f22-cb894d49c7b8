package com.cenker.scrm.pojo.entity.subscr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 订阅实体类
 * 用于存储订阅的相关信息
 */
@Data
@TableName("bu_subscription")
public class BuSubscription {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 客户ID
     */
    private String externalUserId;

    /**
     * 关联菜单ID
     */
    private String subMenuId;

    /**
     * 一级菜单ID
     */
    private String firstSubMenuId;

    /**
     * 栏目ID，关联订阅栏目表的id
     */
    private String sectionId;

    /**
     * 订阅时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date subscrTime;

    /**
     * 取消订阅时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelSubscrTime;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;

}
