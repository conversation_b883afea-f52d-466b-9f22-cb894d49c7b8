package com.cenker.scrm.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@TableName("tb_wx_customer_tag_log")
@Builder
public class TbWxCustomerTagLog {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String externalUserId;

    private String corpId;

    private String tag;

    private String tagId;

    private String type;

    private String groupName;

    private String operId;
    /**
     * add 添加 del 删除
     */
    private String operType;

    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}