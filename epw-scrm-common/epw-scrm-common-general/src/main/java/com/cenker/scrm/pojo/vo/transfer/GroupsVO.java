package com.cenker.scrm.pojo.vo.transfer;

import com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecordItem;
import lombok.Data;

import java.util.List;

import static com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecordItem.filterItemsByTypeAndStatus;

@Data
public class GroupsVO implements java.io.Serializable{

    /**
     * 继承社群总数。
     * 表示此次继承中涉及的社群数量。
     */
    private int totalGroups;

    /**
     * 继承成功总数。
     */
    private int successTransferredGroups;

    /**
     * 继承失败
     */
    private int failedTransferredGroups;

    /**
     * 继承过于频繁
     */
    private int tooFrequentTransferredGroups;

    public static GroupsVO calculateGroupsDetails(List<TbWxTransferRecordItem> items) {
        GroupsVO groups = new GroupsVO();
        groups.setTotalGroups(filterItemsByTypeAndStatus(items, 1, null).size());
        groups.setSuccessTransferredGroups(filterItemsByTypeAndStatus(items, 1, "1").size());
        groups.setFailedTransferredGroups(filterItemsByTypeAndStatus(items, 1, "2").size());
        groups.setTooFrequentTransferredGroups(filterItemsByTypeAndStatus(items, 1, "3").size());
        return groups;
    }


}