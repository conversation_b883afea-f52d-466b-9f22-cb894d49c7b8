package com.cenker.scrm.pojo.vo.transfer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 每个客户转移详情视图对象
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TransferDetailsPerCustomerVO implements java.io.Serializable {
    /**
     * 客户Id。
     */
    private String customerId;

    /**
     * 客户名称。
     */
    private String customerName;

    /**
     * 客户头像
     */
    private String customerAvatar;

    /**
     * 原添加人
     */
    private String originalAdder;

    /**
     * 接替状态： 1-接替完毕 2-等待接替 3-客户拒绝 4-接替成员客户达到上限
     */
    private int takeoverStatus;

    /**
     * 离职时间
     */
    private String leaveTime;

    /**
     * 接替员工姓名
     */
    private String takeoverUserName;
}
