package com.cenker.scrm.pojo.vo.transfer;

import com.cenker.scrm.pojo.entity.enums.EnumTransferType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户转移详情视图对象。
 * 用于展示员工客户转移的相关信息。
 */
@Data
public class TransferDetailsVO implements Serializable {

    /**
     * 转移类型。
     */
    private EnumTransferType transferType;

    /**
     * 移交人姓名或标识。
     */
    private String handoverUserName;

    /**
     * 接手人姓名或标识。
     */
    private String takeoverUserName;

    /**
     * 离职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String dimissionTime;

    /**
     * 所属部门名
     */
    private String department;

    /**
     * 创建时间。
     * 记录继承记录创建的时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人姓名或标识。
     * 指启动客户继承流程的用户。
     */
    private String creatorUserName;

    /**
     * 转移成功后的消息。
     * 在客户转移操作成功完成后，可以向相关人员发送的自定义消息。
     */
    private String transferSuccessMsg;

    /**
     * 客户转移详情。
     * 包含此次继承中的客户转移详情。
     */
    private CustomersVO customers;

    /**
     * 社群转移详情。
     * 包含此次继承中的社群转移详情。
     */
    private GroupsVO groups;

    /**
     * 预计完成时间
      */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedCompletionTime;

    public Date getExpectedCompletionTime() {

        if(createTime!=null){
            //创建时间，24小时内完成
            expectedCompletionTime = new Date(createTime.getTime() + 24 * 60 * 60 * 1000);
        }
        return expectedCompletionTime;
    }

}


