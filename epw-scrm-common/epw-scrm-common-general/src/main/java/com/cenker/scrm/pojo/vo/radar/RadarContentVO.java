package com.cenker.scrm.pojo.vo.radar;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/1
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RadarContentVO {

    private String id;
    /**
     * 成员名片 0 未开启 1 已开启
     */
    private Integer contactStatus;
    /**
     * 智能物料类型 1 图文 2 链接 3 PDF
     */
    private Integer radarType;
    /**
     * 封面
     */
    private String cover;
    /**
     * 摘要
     */
    private String digest;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 员工活码地址
     */
    private String contactCode;
    /**
     * 文章创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /**
     * 员工名
     */
    private String staffName;
    /**
     * 员工头像
     */
    private String staffImg;
    /**
     * 当前登录人
     */
    private String forwardUser;
    /**
     * 作者
     */
    private String author;
    /**
     * 公众号
     */
    private String remark;
    /**
     * 原链接
     */
    private String baseUrl;
    /**
     * 基础阅读量
     */
    private Integer baseReadNum;

    /**
     * pdf图片
     */
    private String pdfImageSource;
    private List<String> pdfImage;

    /**
     * 文末提示
     */
    private String tips;
    /**
     * 查看权限 onlyWeComFri:仅企微好友; onlyWeComAuthFri:仅企微认证好友; none:不限制
     */
    private String viewPerm;
}
