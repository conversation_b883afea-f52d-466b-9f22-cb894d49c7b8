package com.cenker.scrm.pojo.vo.message;


import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/9/15
 * @Description
 */
@Data
public class MassMessageDetailVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 群发任务名
     */
    private String massName;

    /**
     * 任务状态:PENDING_EXEC 未开始  EXECUTING 执行中
     * CANCELED 已取消 EXEC_EXCEPTION 创建失败
     * FINISHED 已完成 PENDING_APPROVAL 待审核
     * REJECTED 已退回 REVOKED 已撤回
     */
    private String checkStatus;

    /**
     * 发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private Date settingTime;
    /**
     * 是否员工去重 是-单条走企微去重 否-多条指定员工
     */
    private Boolean userDistinct;
    /**
     * 是否定时任务 0 立即发送 1 定时发送
     */
    private Integer timedTask;
    /**
     * 消息范围 0 全部客户  1 指定客户
     */
    private Integer pushRange;
    /**
     * 预计发送客户数
     */
    private Integer expectSendCnt;

    /**
     * 筛选发送条件json
     */
    private String condition;
    /**
     * 筛选发送条件json
     */
//    private MessageConditionDTO messageCondition;
    /**
     * 群发消息文本
     */
    private String massContent;
    /**
     * 群发消息附件json
     */
    private List<WelcomeAttachmentVo> attachments;

    private String sendUsers;

    private String createBy;
    private String createByUser;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private Date updateTime;
    /**
     * 启用了审批
     * true:启用审批
     * false:未启用审批
     */
    private boolean enableApproval;
    /**
     * 审批权限
     * true:有审批权限
     * false:无审批权限
     */
    private boolean canApproval;
    /**
     * 审批人账号
     */
    private String approvalUser;
    /**
     * 审批人姓名
     */
    private String approvalUserName;
    /**
     * 意见
     */
    private String approvalRemark;
    /**
     * 执行异常时的错误信息
     */
    private String errorMsg;
}
