package com.cenker.scrm.pojo.request.sop;

import com.cenker.scrm.pojo.dto.condition.SopMassSelectDTO;
import com.cenker.scrm.pojo.dto.sop.SopMassCustomerContentDTO;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description 1V1sop请求实体
 */
@Data
public class MassCustomerSopRequest extends BaseSopRequest {
    /**
     * 筛选条件
     */
    @Deprecated
    private SopMassSelectDTO sopMassSelectCondition;

    /**
     * 前端存储条件json
     */
    @Deprecated
    private String viewSendCondition;

    /**
     * 新版tj筛选发送条件json，新版筛选发送条件json字符串，值与查询发送客户列表接口的condition值保持一致
     */
    @NotNull(message = "请选择筛选条件",groups = {InsertGroup.class})
    @Valid
    private String sendCondition;

    /**
     * 内容序列
     */
    @NotNull(message = "请填写内容",groups = {InsertGroup.class, UpdateGroup.class})
    @Size(min = 1, message = "请至少填写一项内容",groups = {InsertGroup.class, UpdateGroup.class})
    @Valid
    private List<SopMassCustomerContentDTO> sopContentList;
}
