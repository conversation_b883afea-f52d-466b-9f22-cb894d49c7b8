package com.cenker.scrm.pojo.entity.enums;

public enum OperRelEnum {

    OPER_REL_EQ("EQ", "等于","="),
    OPER_REL_GT("GT", "大于",">"),
    OPER_REL_LT("LT", "小于","<"),
    OPER_REL_GE("GE", "大于等于",">="),
    OPER_REL_LE("LE", "小于等于","<="),
    OPER_REL_NE("NE", "不等于","!="),
    OPER_REL_IN("IN", "包含","in"),
    OPER_REL_NOTIN("NOTIN", "不包含","not in"),
    OPER_REL_LIKE("LIKE", "匹配","like"),
    OPER_REL_NOTLIKE("NOTLIKE", "不匹配","not like");

    private final String value;
    private final String desc;

    private final String operStr;

    OperRelEnum(String value, String desc,String operStr) {
        this.value = value;
        this.desc = desc;
        this.operStr =operStr;
    }

    public String getValue() {
        return value;
    }
    public String getOperStr() {
        return operStr;
    }

    public String getDesc() {
        return desc;
    }

    public static String getValueByDesc(String desc) {
        for (OperRelEnum e : OperRelEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.getValue();
            }
        }
        return "";
    }

    public static String getOperStrByValue(String value) {
        for (OperRelEnum e : OperRelEnum.values()) {
            if (e.getValue().equals(value)) {
                return e.getOperStr();
            }
        }
        return "";
    }
}
