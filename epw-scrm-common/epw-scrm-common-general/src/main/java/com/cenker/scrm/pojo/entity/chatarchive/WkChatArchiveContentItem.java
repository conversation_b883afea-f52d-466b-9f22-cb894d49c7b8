package com.cenker.scrm.pojo.entity.chatarchive;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 会话记录详细内容对象 wk_chat_archive_content_item
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@TableName(value = "wk_chat_archive_content_item",autoResultMap = true)
public class WkChatArchiveContentItem implements Serializable {

    private static final long serialVersionUID = 3937214356976470988L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 消息Id，消息的唯一标识 */
    private String msgId;

    /** 消息类型，text：文本，image：图片，类型较多不一一介绍 */
    private Integer msgType;

    /** 会话记录ID，关联wx_chat_archive_info表的id */
    private Long archiveId;

    /** 消息内容 */
    private String msgContent;

    /** 资源文件链接 */
    private String fileUrl;

    /** 撤回消息Id，消息的唯一标识 */
    private String preMsgId;

    /** 消息内容JSON */
    private String contentData;
}
