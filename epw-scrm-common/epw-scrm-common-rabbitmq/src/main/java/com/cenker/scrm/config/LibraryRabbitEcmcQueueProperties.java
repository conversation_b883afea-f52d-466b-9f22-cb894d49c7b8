package com.cenker.scrm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应答消息队列配置
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "library.ecmc.publish.reply")
@Component
@Data
public class LibraryRabbitEcmcQueueProperties {
    /**
     * 应答消息队列名称
     */
    private String queue;
    /**
     * 应答消息路由键
     */
    private String routingKey;
    /**
     * 应答消息绑定键
     */
    private String bindingKey;
}
