package com.cenker.scrm.config;

import com.cenker.scrm.constants.LibraryRabbitMqQueuesConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Library RabbitMQ 配置类
 * 用于配置第二个 RabbitMQ 实例的连接工厂、交换机、队列等
 */
@Configuration
@EnableConfigurationProperties({LibraryRabbitMQProperties.class, LibraryRabbitCmcQueueProperties.class, LibraryRabbitEcmcQueueProperties.class})
@RequiredArgsConstructor
@Slf4j
public class LibraryRabbitMQConfig {

    private final LibraryRabbitMQProperties libraryRabbitMQProperties;
    private final LibraryRabbitCmcQueueProperties libraryRabbitCmcQueueProperties;
    private final LibraryRabbitEcmcQueueProperties libraryRabbitEcmcQueueProperties;

    /**
     * Library RabbitMQ 连接工厂
     */
    @Bean("libraryConnectionFactory")
    public ConnectionFactory libraryConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(libraryRabbitMQProperties.getHost());
        connectionFactory.setPort(libraryRabbitMQProperties.getPort());
        connectionFactory.setUsername(libraryRabbitMQProperties.getUsername());
        connectionFactory.setPassword(libraryRabbitMQProperties.getPassword());
        connectionFactory.setVirtualHost(libraryRabbitMQProperties.getVirtualHost());
        connectionFactory.setAddresses(libraryRabbitMQProperties.getHost() + ":" + libraryRabbitMQProperties.getPort());
        connectionFactory.setRequestedHeartBeat(0);
        
        log.info("初始化 Library RabbitMQ 连接工厂: {}:{}", 
                libraryRabbitMQProperties.getHost(), libraryRabbitMQProperties.getPort());
        
        return connectionFactory;
    }

    /**
     * Library RabbitMQ 管理器
     */
    @Bean("libraryRabbitAdmin")
    public RabbitAdmin libraryRabbitAdmin(@Qualifier("libraryConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitAdmin admin = new RabbitAdmin(connectionFactory);
        admin.setAutoStartup(true);
        admin.setIgnoreDeclarationExceptions(true);
        return admin;
    }

    /**
     * Library RabbitMQ 模板
     */
    @Bean("libraryRabbitTemplate")
    public RabbitTemplate libraryRabbitTemplate(@Qualifier("libraryConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(libraryStringMessageConverter());
        // 设置确认回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                System.out.println("消息成功发送到交换机");
            } else {
                System.err.println("消息发送失败: " + cause);
            }
        });
        return rabbitTemplate;
    }

    /**
     * Library RabbitMQ 消息转换器
     */
    @Bean("libraryStringMessageConverter")
    public MessageConverter libraryStringMessageConverter() {
        return new MessageConverter() {
            @Override
            public Message toMessage(Object object, MessageProperties messageProperties) {
                if (object instanceof String) {
                    byte[] body = ((String) object).getBytes(StandardCharsets.UTF_8);
                    return new Message(body, messageProperties);
                }
                return null;
            }

            @Override
            public Object fromMessage(Message message) {
                byte[] body = message.getBody();
                return new String(body, StandardCharsets.UTF_8);
            }
        };
    }

    /**
     * Library RabbitMQ 监听器容器工厂 - 手动确认模式
     */
    @Bean(LibraryRabbitMqQueuesConstant.LIBRARY_MQ_FACTORY_NAME_MANUAL_ACK)
    public SimpleRabbitListenerContainerFactory libraryRabbitListenerContainerFactory(
            @Qualifier("libraryConnectionFactory") ConnectionFactory connectionFactory,
            @Qualifier("libraryStringMessageConverter") MessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(1);
        factory.setBatchSize(1);
        factory.setMessageConverter(messageConverter);
        // 设置手动确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    /**
     * Library RabbitMQ 直连交换机
     */
    @Bean("libraryDirectExchange")
    public DirectExchange libraryDirectExchange() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new DirectExchange(LibraryRabbitMqQueuesConstant.LIBRARY_EXCHANGE_DIRECT, true, false, arguments);
    }

    /**
     * 通知消息队列
     */
    @Bean("cmcPublishReqQueue")
    public Queue cmcPublishReqQueue() {
        String queueName = getNotificationQueueName();
        Map<String, Object> arguments = new HashMap<>(4);
        log.info("创建通知消息队列: {}", queueName);
        return new Queue(queueName, true, false, false, arguments);
    }

    /**
     * 应答消息队列
     */
    @Bean("ecmcPublishReplyQueue")
    public Queue ecmcPublishReplyQueue() {
        String queueName = getReplyQueueName();
        Map<String, Object> arguments = new HashMap<>(4);
        log.info("创建应答消息队列: {}", queueName);
        return new Queue(queueName, true, false, false, arguments);
    }

    /**
     * 通知消息队列绑定
     */
    @Bean("cmcPublishReqBinding")
    public Binding cmcPublishReqBinding(@Qualifier("cmcPublishReqQueue") Queue queue,
                                       @Qualifier("libraryDirectExchange") DirectExchange exchange) {
        String bindingKey = getNotificationBindingKey();
        log.info("绑定通知消息队列: {} -> {}", queue.getName(), bindingKey);
        return BindingBuilder.bind(queue).to(exchange).with(bindingKey);
    }

    /**
     * 应答消息队列绑定
     */
    @Bean("ecmcPublishReplyBinding")
    public Binding ecmcPublishReplyBinding(@Qualifier("ecmcPublishReplyQueue") Queue queue,
                                          @Qualifier("libraryDirectExchange") DirectExchange exchange) {
        String bindingKey = getReplyBindingKey();
        log.info("绑定应答消息队列: {} -> {}", queue.getName(), bindingKey);
        return BindingBuilder.bind(queue).to(exchange).with(bindingKey);
    }

    /**
     * 获取通知消息队列名称
     */
    public String getNotificationQueueName() {
        String queueName = libraryRabbitCmcQueueProperties.getQueue();
        return StringUtils.hasText(queueName) ? queueName : LibraryRabbitMqQueuesConstant.CMC_PUBLISH_REQ_QUEUE_DEFAULT;
    }

    /**
     * 获取通知消息路由键
     */
    public String getNotificationRoutingKey() {
        String routingKey = libraryRabbitCmcQueueProperties.getRoutingKey();
        return StringUtils.hasText(routingKey) ? routingKey : LibraryRabbitMqQueuesConstant.CMC_PUBLISH_REQ_ROUTING_KEY_DEFAULT;
    }

    /**
     * 获取通知消息绑定键
     */
    public String getNotificationBindingKey() {
        String bindingKey = libraryRabbitCmcQueueProperties.getBindingKey();
        return StringUtils.hasText(bindingKey) ? bindingKey : LibraryRabbitMqQueuesConstant.CMC_PUBLISH_REQ_BINDING_KEY_DEFAULT;
    }

    /**
     * 获取应答消息队列名称
     */
    public String getReplyQueueName() {
        String queueName = libraryRabbitEcmcQueueProperties.getQueue();
        return StringUtils.hasText(queueName) ? queueName : LibraryRabbitMqQueuesConstant.ECMC_PUBLISH_REPLY_QUEUE_DEFAULT;
    }

    /**
     * 获取应答消息路由键
     */
    public String getReplyRoutingKey() {
        String routingKey = libraryRabbitCmcQueueProperties.getRoutingKey();
        return StringUtils.hasText(routingKey) ? routingKey : LibraryRabbitMqQueuesConstant.ECMC_PUBLISH_REPLY_ROUTING_KEY_DEFAULT;
    }

    /**
     * 获取应答消息绑定键
     */
    public String getReplyBindingKey() {
        String bindingKey = libraryRabbitCmcQueueProperties.getBindingKey();
        return StringUtils.hasText(bindingKey) ? bindingKey : LibraryRabbitMqQueuesConstant.ECMC_PUBLISH_REPLY_BINDING_KEY_DEFAULT;
    }
}
