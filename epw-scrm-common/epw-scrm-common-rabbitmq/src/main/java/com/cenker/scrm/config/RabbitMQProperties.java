package com.cenker.scrm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 自定义配置
 * 部分字段设置有默认值，也可以自定义配置
 */
@ConfigurationProperties(prefix = "rabbitmq.producer")
@Data
public class RabbitMQProperties {
    /**
     * mq host
     */
    private String host;

    /**
     * mq 端口号
     */
    private Integer port;

    /**
     * mq 用户名
     */
    private String username;

    /**
     * mq 密码
     */
    private String password;

    /**
     * mq vhost
     */
    private String virtualHost;

    /**
     * 消息发送超时时间
     */
    private static Integer sendMessageTimeOut;

    /**
     * 同步发送消息失败重试次数，默认3
     */
    private static Integer retryTimesWhenSendFailed;

    public void setRetryTimesWhenSendFailed(Integer retryTimesWhenSendFailed) {
        RabbitMQProperties.retryTimesWhenSendFailed = retryTimesWhenSendFailed;
    }

    public static Integer getRetryTimesWhenSendFailed(){
        return retryTimesWhenSendFailed;
    }

    public static Integer getSendMessageTimeOut() {
        return sendMessageTimeOut;
    }

    public void setSendMessageTimeOut(Integer sendMessageTimeOut) {
        RabbitMQProperties.sendMessageTimeOut = sendMessageTimeOut;
    }
}
