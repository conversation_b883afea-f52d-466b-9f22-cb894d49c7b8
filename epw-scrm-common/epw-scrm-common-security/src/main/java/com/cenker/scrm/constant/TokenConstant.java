package com.cenker.scrm.constant;

import com.cenker.scrm.constants.CacheKeyConstants;

public interface TokenConstant {

    /**
     * token前缀
     */
    String BEARER = "Bearer ";

    /**
     * 管理后台接口token请求头
     */
    String HEADER_AUTHORIZATION = "Authorization";

    /**
     * H5端接口token请求头
     */
    String HEADER_ACCESS_TOKEN = "X-Access-Token";

    /**
     * 域账号登录token请求头
     */
    String HEADER_USER_TOKEN = "User-Token";

    /**
     * token中存储租户id
     */
    String CLAIM_KEY_TENANT_ID = "tenantId";

    /**
     * token的唯一ID，用于换取用户信息，后续建议改为统一的uuid，目前暂时用于pc端登录
     */
    String CLAIM_KEY_UUID = CacheKeyConstants.LOGIN_USER_KEY;

    /**
     * token中存储用户id
     */
    String CLAIM_KEY_USER_ID = "userId";

    /**
     * token中存储用户名
     */
    String CLAIM_KEY_USER_NAME = "userName";

    /**
     * token中存储登录类型: 1-管理后台 2-企微端 3-H5端 4-域账号登录
     */
    String CLAIM_KEY_LOGIN_TYPE = "loginType";

    /**
     * token中存储企业微信用户id（员工企微账号）
     */
    String CLAIM_KEY_WX_USER_ID = "wxUserId";

    /**
     * token中存储主部门ID
     */
    String CLAIM_KEY_DEPT_ID = "deptId";

}
