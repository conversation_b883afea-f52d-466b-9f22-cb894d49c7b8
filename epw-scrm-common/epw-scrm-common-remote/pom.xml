<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cenker.scrm</groupId>
        <artifactId>epw-scrm-common</artifactId>
        <version>2.6.0</version>
    </parent>

    <artifactId>epw-scrm-common-remote</artifactId>
    <description>远程调用接口服务</description>

    <dependencies>
        <!--服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- 公共模块 -->
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-general</artifactId>
        </dependency>

    </dependencies>

</project>