package com.cenker.scrm.wx;

import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, contextId = "groupCodeFeign", path = "/code/group")
public interface GroupCodeFeign {

    @RequestMapping("/getGroupCodeByState")
    RemoteResult<GroupCodeInfo> getGroupCodeByState(@RequestParam("state") String state);
}
