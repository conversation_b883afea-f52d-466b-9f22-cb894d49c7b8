package com.cenker.scrm.auth;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

@FeignClient(value = ServiceNameConstants.OAUTH_SERVICE, contextId = "remoteOauthFeign", fallback = OauthFeignCallback.class)
public interface OauthFeign {

    @PostMapping(value = "/oauth/getToken")
    String getToken(@RequestBody H5LoginUser loginUser);

    @GetMapping("/wx/getToken")
    public AjaxResult getToken(@RequestParam("code") String code,
                               @RequestParam("agentId") Integer agentId,
                               @RequestParam("corpId") String corpId);

    @GetMapping("/wx/getSideAble")
    AjaxResult getSideAble(@RequestParam("userId") String userId);
}
