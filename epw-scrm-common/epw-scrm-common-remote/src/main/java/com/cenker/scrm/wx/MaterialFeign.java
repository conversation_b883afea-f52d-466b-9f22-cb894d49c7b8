package com.cenker.scrm.wx;

import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.ServiceNameConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, contextId = "materialFeign", path = "/tp/material")
public interface MaterialFeign {

    @RequestMapping("/updateNamingRule")
    RemoteResult updateNamingRule(@RequestBody Map<String, String> params);
}
