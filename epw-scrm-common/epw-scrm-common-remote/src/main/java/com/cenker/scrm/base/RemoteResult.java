package com.cenker.scrm.base;

/**
 * feign调用返回结果
 * @param <T>
 */
public class RemoteResult<T> {

    private static final String DEFAULT_SUCCESS_CODE = "0000";
    private static final String DEFAULT_SUCCESS_MESSAGE = "success";
    private boolean success;
    private String code;
    private String message;
    private T data;

    public RemoteResult() {
        this.success = true;
        this.message = DEFAULT_SUCCESS_MESSAGE;
    }

    public RemoteResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public RemoteResult(boolean success, T data) {
        this.success = success;
        this.data = data;
    }

    public RemoteResult(boolean success, String code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }

    public RemoteResult(boolean success, String code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static RemoteResult success() {
        return new RemoteResult(true, DEFAULT_SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE);
    }

    public static RemoteResult success(String message) {
        return new RemoteResult(true, DEFAULT_SUCCESS_CODE, message);
    }

    public static <T> RemoteResult<T> success(String message, T data) {
        return new RemoteResult(true, DEFAULT_SUCCESS_CODE, message, data);
    }

    public static <T> RemoteResult<T> data(T data) {
        return new RemoteResult(true, DEFAULT_SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE, data);
    }

    public static <T> RemoteResult<T> error(String code, String message) {
        return new RemoteResult(false, code, message, (Object)null);
    }

    public static <T> RemoteResult<T> error(String code, String message, T data) {
        return new RemoteResult(false, code, message, data);
    }

    public boolean isSuccess() {
        return this.success && DEFAULT_SUCCESS_CODE.equals(this.code);
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setData(T data) {
        this.data = data;
    }
}
