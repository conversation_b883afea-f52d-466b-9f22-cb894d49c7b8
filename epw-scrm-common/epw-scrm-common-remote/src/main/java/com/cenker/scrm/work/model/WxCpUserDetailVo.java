package com.cenker.scrm.work.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class WxCpUserDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer agentId;
    private String corpId;

    /**
     * 企微用户信息(来自WxCpOauth2UserInfo)
     */
    private String openId;
    private String deviceId;
    private String userId;
    private String userTicket;
    private String expiresIn;
    private String externalUserId;
    private String parentUserId;
    private String studentUserId;

    /**
     * 企微用户详情信息（来自WxCpUserDetail）
     */
    private String name;
    private String mobile;
    private String gender;
    private String email;
    private String avatar;
    @SerializedName("qr_code")
    private String qrCode;
    @SerializedName("biz_mail")
    private String bizMail;
    private String address;
}
