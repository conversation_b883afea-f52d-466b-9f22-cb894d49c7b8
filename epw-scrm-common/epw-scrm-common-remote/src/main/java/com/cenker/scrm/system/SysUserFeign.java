package com.cenker.scrm.system;

import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.system.model.UserCategoryVo;
import com.cenker.scrm.system.model.UserPermissionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/25
 * @Description 用户管理
 */
@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE, contextId = "remoteSysUserFeign", fallback = SysUserFeignCallback.class, path = "/system/user")
public interface SysUserFeign {

    @RequestMapping("/list")
    TableDataInfo list(@RequestBody SysUser user);

    @RequestMapping("/export")
    List<SysUser> export(@RequestBody SysUser user);

    //@RequestMapping("/importData")
    //AjaxResult importData(MultipartFile file, boolean updateSupport);

    @RequestMapping("/importTemplate")
    AjaxResult importTemplate();

    /**
     * 根据用户编号获取详细信息
     */
    @RequestMapping("/{userId}")
    AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId);

    /**
     * 根据用户编号获取详细信息
     */
    @RequestMapping("/")
    AjaxResult getInfo();

    /**
     * 新增用户
     */
    @RequestMapping("/add")
    AjaxResult add(@Validated @RequestBody SysUser user);

    /**
     * 修改用户
     */
    @RequestMapping("/edit")
    AjaxResult edit(@Validated @RequestBody SysUser user);

    /**
     * 删除用户
     */
    @RequestMapping("/remove/{userIds}")
    AjaxResult remove(@PathVariable("userIds") Long[] userIds);

    /**
     * 重置密码
     */
    @RequestMapping("/resetPwd")
    AjaxResult resetPwd(@RequestBody SysUser user);

    /**
     * 状态修改
     */
    @RequestMapping("/changeStatus")
    AjaxResult changeStatus(@RequestBody SysUser user);

    @RequestMapping("/selectUserByDomainAccount")
    SysUser selectUserByDomainAccount(@RequestParam("domainAccount") String domainAccount);

    @RequestMapping("/getUserPermissionByUserId")
    RemoteResult<UserPermissionVo> getUserPermissionByUserId(@RequestParam("userId") Long userId);

    @RequestMapping("/selectUserByWxUserId")
    RemoteResult<SysUser> selectUserByWxUserId(@RequestParam("wxUserId") String wxUserId);

    @RequestMapping("/getUserPermissionCategory")
    RemoteResult<UserCategoryVo> getUserPermissionCategory(@RequestParam("userId")String userId, @RequestParam("categoryType")String categoryType);
}
