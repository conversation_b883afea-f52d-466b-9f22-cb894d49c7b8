package com.cenker.scrm.wx;


import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.ServiceNameConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(value = ServiceNameConstants.WX_CENTER_SERVICE, contextId = "tagGroupFeign", path = "/tp/groupTag")
public interface TagGroupFeign {

    @GetMapping("/countTagByCategoryId")
    RemoteResult<Integer> countTagByCategoryId(@RequestParam("categoryId") String categoryId);

}
