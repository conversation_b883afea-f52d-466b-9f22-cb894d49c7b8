package com.cenker.scrm.efunds.model;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@NoArgsConstructor
public abstract class WxEngineReq {

    public WxEngineReq(String url, String _md, String serviceAccount, String signatureKey) {
        this.url = url + "/wechat.do";
        this._md = _md;

        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.nonce = IdUtil.fastSimpleUUID();
        this.serviceAccount = serviceAccount;
        this.signatureKey = signatureKey;
        this.signature = this.generateSignature(serviceAccount, nonce, timestamp, signatureKey);
    }

    private String url;

    /**
     * 方法名
     */
    private String _md;

    /**
     * 公众号
     */
    private String serviceAccount;

    /**
     * 随机字符串
     */
    private String nonce;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 签名
     */
    private String signature;

    /**
     * 签名密钥
     */
    private String signatureKey;

    /**
     * 调用方系统名称
     */
    private String appCode;

    public Map<String, Object> getParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("_md", _md);
        params.put("serviceAccount", serviceAccount);
        params.put("nonce", nonce);
        params.put("timestamp", timestamp);
        params.put("signature", signature);
        params.put("appCode", appCode);
        return params;
    }

    /**
     * 生成签名
     * @param serviceAccount
     * @param nonce
     * @param timestamp
     * @param signatureKey
     * @return
     */
    private String generateSignature(String serviceAccount, String nonce, String timestamp, String signatureKey) {
        try {
            String encodeStr = URLEncoder.encode(serviceAccount + nonce + timestamp + signatureKey, "UTF-8");
            return DigestUtil.md5Hex(encodeStr.toUpperCase());
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return null;
        }
    }

}
