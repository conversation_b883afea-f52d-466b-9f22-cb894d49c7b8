package com.cenker.scrm.efunds;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.efunds.model.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 易方达微信引擎/微信接入层客户端
 * <AUTHOR>
 */
@Slf4j
public class WechatEngineClient {

    /**
     * 获取微信授权信息
     * @param wxAuthInfoGetReq
     * @return
     */
    public static WxAuthInfoGetRsp getAuthInfo(WxAuthInfoGetReq wxAuthInfoGetReq) {
        return executeWechatEngineApi(wxAuthInfoGetReq, WxAuthInfoGetRsp.class);
    }

    /**
     * 获取微信用户信息
     * @param wxUserInfoGetReq
     * @return
     */
    public static WxUserInfoGetRsp getUserInfo(WxUserInfoGetReq wxUserInfoGetReq) {
        return executeWechatEngineApi(wxUserInfoGetReq, WxUserInfoGetRsp.class);
    }

    /**
     * 获取微信公众号 Token
     * @param wxMpTokenGetReq
     * @return
     */
    public static WxMpTokenGetRsp getWxMpToken(WxMpTokenGetReq wxMpTokenGetReq) {
        return executeWechatAccessApi(wxMpTokenGetReq, WxMpTokenGetRsp.class);
    }

    /**
     * 公共方法：执行微信引擎接口请求并返回响应对象
     * @param request 微信请求对象，继承自WxEngineReq
     * @param clazz 返回类型Class对象
     * @param <T> 返回类型泛型，必须继承自WxEngineRsp
     * @param <E> 请求类型泛型，必须继承自WxEngineReq
     * @return 接口响应对象
     */
    private static <T extends WxEngineRsp, E extends WxEngineReq> T executeWechatEngineApi(E request, Class<T> clazz) {
        log.info("【微信引擎】调用接口：{}，请求参数：{}", request.get_md(), request.getParams());
        String responseStr = HttpUtil.get(request.getUrl(), request.getParams());
        log.info("【微信引擎】调用接口：{}，响应结果：{}", request.get_md(), responseStr);
        return JSON.parseObject(responseStr, clazz);
    }

    /**
     * 公共方法：执行微信接入层接口请求并返回响应对象
     * @param request 微信请求对象，继承自WxEngineReq
     * @param clazz 返回类型Class对象
     * @param <T> 返回类型泛型，必须继承自WxEngineRsp
     * @param <E> 请求类型泛型，必须继承自WxEngineReq
     * @return 接口响应对象
     */
    private static <T extends WxAccessRsp, E extends WxAccessReq> T executeWechatAccessApi(E request, Class<T> clazz) {
        log.info("【微信引擎】调用接口：{}，请求参数：{}", request.getMethod(), request.getParams());
        String responseStr = HttpUtil.get(request.getUrl(), request.getParams());
        log.info("【微信引擎】调用接口：{}，响应结果：{}", request.getMethod(), responseStr);

        return JSON.parseObject(responseStr, clazz);
    }


}
