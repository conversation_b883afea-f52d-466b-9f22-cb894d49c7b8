package com.cenker.scrm.work;

import com.cenker.scrm.constants.ServiceNameConstants;
import me.chanjar.weixin.cp.bean.WxCpUserLoginInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 暂时罗列wx-cenker所调用的企微接口 消除work注入依赖
 *
 * <AUTHOR> 直接获取api对象存在限制  还是通过调用实际功能如添加渠道码来实现调用企微功能
 */
@FeignClient(value = ServiceNameConstants.WORK_API_SERVICE, contextId = "remoteWxCpFeign",  path = "/service/cp")
public interface WxCpFeign {
    /**
     * 获取用户登录身份 新版 https://developer.work.weixin.qq.com/document/path/98176
     *
     * @param code 通过成员授权获取到的code，最大为512字节。每次成员授权带上的code将不一样，code只能使用一次，5分钟未被使用自动过期。
     * @param corpId 企业id
     * @return 成员id信息
     */
    @PostMapping("/getUserInfo4Auth")
    WxCpUserLoginInfo getUserInfo4Auth(@RequestParam("code") String code, @RequestParam("corpId") String corpId);
}
