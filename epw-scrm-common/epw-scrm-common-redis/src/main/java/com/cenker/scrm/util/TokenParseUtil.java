package com.cenker.scrm.util;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.config.OauthTokenConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.model.login.BaseLoginInfo;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.model.login.MobileUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Slf4j
public class TokenParseUtil {

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isBlank(token)) {
            return null;
        }

        try {
            Claims claims = parseToken(token);
            String uuid = (String) claims.get(CacheKeyConstants.LOGIN_USER_KEY);
            if (StrUtil.isBlank(uuid)) {
                return null;
            }
            String userKey = getTokenKey(uuid);
            return redisCache.getCacheObject(userKey);
        } catch (Exception e) {
            log.error("获取用户身份信息失败！",e);
            return null;
        }
    }

    /**
     * 获取用户身份信息(工作台)
     */
    public H5LoginUser getLoginUser(HttpServletRequest request, String header) {
        // 获取请求携带的令牌
        try {
            String token = getToken(request,header);
            if (StringUtils.isNotEmpty(token)) {
                Claims claims = parseToken(token);
                // 解析对应的权限以及用户信息
                String uuid = (String) claims.get(CacheKeyConstants.WORKBENCH_LOGIN_USER_KEY);
                String userKey = getTokenKeyH5(uuid);
                return redisCache.getCacheObject(userKey);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser
     * @return 令牌
     */
    public void verifyToken(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(BaseLoginInfo loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + OauthTokenConfig.getExpireTime() * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, OauthTokenConfig.getExpireTime(), TimeUnit.MINUTES);
        String userName = null;
        if (loginUser instanceof LoginUser) {
            LoginUser loginUser1 = (LoginUser) loginUser;
            userName = loginUser1.getUser() != null ? loginUser1.getUser().getUserName() : loginUser1.getUserName();
        } else if (loginUser instanceof H5LoginUser) {
            H5LoginUser loginUser1 = (H5LoginUser) loginUser;
            userName = loginUser1.getMobileUser() != null ? loginUser1.getMobileUser().getUserId() : (loginUser1.getMpWxUser() != null ? loginUser1.getMpWxUser().getNickName() : loginUser1.getUserName());
        }
        log.info("【token续期】登录用户：{}, 续期时间：{} 分", userName, OauthTokenConfig.getExpireTime());
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        String token = Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, OauthTokenConfig.getSecret()).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(OauthTokenConfig.getSecret())
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(OauthTokenConfig.getHeader());
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }
    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    public String getToken(HttpServletRequest request,String header) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return CacheKeyConstants.LOGIN_TOKEN_KEY + uuid;
    }

    private String getTokenKeyH5(String uuid) {
        return CacheKeyConstants.WORKBENCH_LOGIN_TOKEN_KEY + uuid;
    }

    public H5LoginUser getWorkLoginUser(HttpServletRequest request) {
//        return getLoginUser(request,OauthTokenConfig.getHeader());
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isBlank(token)) {
            return null;
        }

        try {
            Claims claims = parseToken(token);
            String uuid = (String) claims.get(CacheKeyConstants.LOGIN_USER_KEY);
            if (StrUtil.isBlank(uuid)) {
                return null;
            }
            String userKey = getTokenKey(uuid);
            return redisCache.getCacheObject(userKey);
        } catch (Exception e) {
            log.error("获取用户身份信息失败！",e);
            return null;
        }
    }

    public H5LoginUser getH5LoginUser(HttpServletRequest request) {
        return getLoginUser(request,OauthTokenConfig.getWxHeader());
    }

    public H5LoginUser getH5LoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = parseToken(token);
            // 解析对应的权限以及用户信息
            String uuid = (String) claims.get(CacheKeyConstants.WORKBENCH_LOGIN_USER_KEY);
            String userKey = getTokenKeyH5(uuid);
            log.info("【微信token解析】缓存key：{}",userKey);
            return redisCache.getCacheObject(userKey);
        }
        return null;
    }

    /**
     * 获取用户身份信息
     * @return 用户信息
     */
    public H5LoginUser getLoginUserH5(HttpServletRequest request) {
        // 获取请求携带的令牌
        /*String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = parseToken(token);
            // 解析对应的权限以及用户信息
            String uuid = (String) claims.get(CacheKeyConstants.WORKBENCH_LOGIN_USER_KEY);
            String userKey = getTokenKeyH5(uuid);
            return redisCache.getCacheObject(userKey);
        }
        return null;*/
        return getWorkLoginUser(request);
    }

    public H5LoginUser getLoginUserH5Wx(HttpServletRequest request) {
        String token = request.getHeader(OauthTokenConfig.getWxHeader());
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = parseToken(token);
            // 解析对应的权限以及用户信息
            String uuid = (String) claims.get(CacheKeyConstants.WORKBENCH_LOGIN_USER_KEY);
            String userKey = getTokenKeyH5(uuid);
            return redisCache.getCacheObject(userKey);
        }
        return null;
    }

    public boolean delLoginUserToken(String token, boolean weComLogin) {
        if (StringUtils.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);
                String uuid;
                String userKey;
                if (weComLogin) {
                    uuid = (String) claims.get(CacheKeyConstants.LOGIN_USER_KEY);
                    userKey = getTokenKey(uuid);
                } else {
                    uuid = (String) claims.get(CacheKeyConstants.WORKBENCH_LOGIN_USER_KEY);
                    userKey = getTokenKeyH5(uuid);
                }
                if (StrUtil.isBlank(uuid)) {
                    return false;
                }
                return redisCache.deleteObject(userKey);
            } catch (Exception e) {
                log.error("获取用户身份信息失败！", e);
                return false;
            }
        }
        return false;
    }
}
