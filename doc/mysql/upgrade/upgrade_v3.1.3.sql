ALTER TABLE `sys_role` ADD COLUMN `side_able`  tinyint(1) NULL DEFAULT 1 COMMENT '企微端是否可用 0否 1是' AFTER `status`;

-- 2.3.1.6 SCRM-渠道活码/社群活码新增废弃/删除按钮
ALTER TABLE `tb_wx_contact` DROP COLUMN `config_status`;
ALTER TABLE `tb_wx_contact` ADD COLUMN `use_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '使用状态，1：使用中，0：已废弃' AFTER `config_id`;
ALTER TABLE `ck_group_code_info` ADD COLUMN `use_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '使用状态，1：使用中，0：已废弃' AFTER `corp_id`;

UPDATE `tb_wx_contact` SET use_status = 1 WHERE del_flag = '0';
UPDATE `ck_group_code_info` SET use_status = 1 WHERE is_deleted = 0;

INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, update_by, create_time, update_time, del_flag, remark, case_link, help_title, help_icon, help_link, icon1, icon2, corner_mark, corner_mark_back_ground, corner_mark_color)
VALUES(1433699711154642096, '废弃', 1433699711154641705, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:channel:abandon', '#', '1', NULL, '2024-11-12 14:48:38', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');

INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, update_by, create_time, update_time, del_flag, remark, case_link, help_title, help_icon, help_link, icon1, icon2, corner_mark, corner_mark_back_ground, corner_mark_color)
VALUES(1433699711154642097, '废弃', 1433699711154641706, 7, '', NULL, 1, 0, 'F', '0', '0', 'modules:communityCode:abandon', '#', '1', NULL, '2024-11-12 14:49:11', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641745, '删除', 1433699711154641706, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:communityCode:delete', '#', '1', '', '2023-06-16 17:29:58', '2024-03-13 15:19:49', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641823, '取消', 1433699711154641819, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:groupToGroup:cancelTask', '#', '1', 'admin', '2023-06-30 14:37:45', '2024-03-13 15:19:49', 0, '', '', '', '', '', '#', '#', '', '', '');

-- 2.1.11 SCRM-客户详情查看权限统一
DELETE FROM sys_menu WHERE menu_id = 1433699711154642011;
DELETE FROM sys_menu WHERE menu_id = 1433699711154642062;
DELETE FROM sys_menu WHERE menu_id = 1433699711154642025;
DELETE FROM sys_menu WHERE menu_id = 1433699711154642026;
DELETE FROM sys_menu WHERE menu_id = 1433699711154642027;
DELETE FROM sys_menu WHERE menu_id = 1433699711154642028;
DELETE FROM sys_menu WHERE menu_id = 1433699711154641781;

DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642011;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642062;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642025;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642026;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642027;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154642028;
DELETE FROM sys_role_menu WHERE menu_id = 1433699711154641781;


-- SCRM-打标签人显示优化
ALTER TABLE tb_wx_ext_follow_user_tag
    ADD COLUMN remark varchar(500) NULL COMMENT '标签添加来源描述' AFTER group_name;

-- 2.3.1.2 会话存档数据改由企微官方接口获取
CREATE TABLE `wk_chat_archive_info_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `msg_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息id',
    `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工id',
    `user_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '成员名称',
    `dept_id` bigint DEFAULT NULL COMMENT '部门id',
    `dept_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门名称',
    `room_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群ID',
    `room_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群名称',
    `msg_time` datetime NOT NULL COMMENT '消息时间',
    `corp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会话记录日记表';

UPDATE `xxl_job_info` SET trigger_status = 1 WHERE executor_handler = 'pullChatArchive';

INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('物料发送渠道', 'material_delivery_channel', '0', 'admin', NULL, '2024-11-19 10:43:28', '2024-11-19 10:49:02', 0, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (1, '侧边栏', '1', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:44:59', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (2, '朋友圈', '2', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:45:10', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (3, '工作台', '3', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:45:19', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (4, '渠道活码', '4', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:46:10', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (5, '企业话术', '5', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:46:26', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (6, '群发客户', '6', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:46:36', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (7, '获客链接', '7', 'material_delivery_channel', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-11-19 10:46:44', NULL, NULL);

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641789, '会话存档', 1433699711154641904, 1, 'session', NULL, 1, 0, 'M', '0', '0', '', '#', '1', 'admin', '2023-06-20 14:05:05', '2024-03-26 16:03:44', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641909, '敏感管控', 1433699711154641904, 2, 'sensitive-monitoring', NULL, 1, 0, 'M', '0', '0', '', '#', '1', 'admin', '2024-03-15 15:50:36', '2024-03-16 11:16:55', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641913, '服务管控', 1433699711154641904, 3, 'service-control', NULL, 1, 0, 'M', '0', '0', '', '#', '1', 'admin', '2024-03-16 17:32:36', '2024-03-26 16:04:03', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641790, '聊天存档', 1433699711154641789, 1, 'chatarchive', 'waitonline/chatArchive/index', 1, 0, 'C', '0', '0', 'waitonline:chatArchive:list', '#', '1', 'admin', '2023-06-20 14:32:10', '2024-03-13 15:19:49', 0, '会话存档功能在客户和员工同意的情况下，可以合规记录文字、图片、视频、语音等类型聊天记录，并支持以员工、关键词、时间段、消息类型等多维度快速检索消息，当员工与客户聊天触发敏感内容规则时，可立即通知风控审计人，满足员工服务过程合规性审查和风控需求', '', '聊天存档', 'https://scrm.efundsdemo.com/wecom-crm-em/scrm/common/f9a3fc27-d073-49e8-a9e7-49c83b985594.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641910, '敏感规则', 1433699711154641909, 1, 'sensitive-rules', 'modules/sensitiveRules/index', 1, 0, 'C', '0', '0', 'modules:sensitiveRules:index', '#', '1', 'admin', '2024-03-15 15:52:57', '2024-04-09 14:11:14', 0, '设置敏感词和敏感行为拦截规则，实时质检服务质量，及时排除舆情风险', '', '敏感规则', 'https://scrm.efundsdemo.com/wecom-crm-em/scrm/common/d6e3416e-b2f2-49f9-8026-b45cee38461b.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641915, '新增规则', 1433699711154641910, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:add', '#', '1', NULL, '2024-03-20 10:33:59', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641916, '废弃', 1433699711154641910, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:stop', '#', '1', 'admin', '2024-03-20 10:35:32', '2024-05-22 16:48:15', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641917, '编辑', 1433699711154641910, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:edit', '#', '1', NULL, '2024-03-20 10:36:04', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641918, '导入规则', 1433699711154641910, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:export', '#', '1', NULL, '2024-03-20 10:38:23', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641911, '敏感词警告', 1433699711154641909, 2, 'sensitive-alert-log', 'modules/sensitiveAlertLog/index', 1, 0, 'C', '0', '0', 'modules:sensitiveAlertLog:index', '#', '1', 'admin', '2024-03-15 15:55:18', '2024-04-09 14:14:08', 0, '查看触发敏感词警告的记录', '', '敏感词警告', 'https://scrm.efundsdemo.com/wecom-crm-em/scrm/common/bcb39882-f6a7-4aff-8756-390d84f42602.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641994, '导出', 1433699711154641911, 1, 'modules:sensitiveAlertLog:export', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveAlertLog:export', '#', '1', 'admin', '2024-05-22 16:41:37', '2024-05-22 16:42:15', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641912, '敏感行为', 1433699711154641909, 3, 'sensitive-behavior-warningLog', 'modules/sensitiveBehaviorWarningLog/index', 1, 0, 'C', '0', '0', 'modules:sensitiveBehaviorWarningLog:index', '#', '1', 'admin', '2024-03-15 16:00:28', '2024-03-22 09:19:31', 0, '查看触发敏感行为警告的记录', '', ' 敏感行为警告记录 ', 'https://scrm.efundsdemo.com/wecom-crm-em/scrm/common/b58a88f6-9482-4006-a65c-777403791120.png', '12414', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641995, '导出', 1433699711154641912, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveBehaviorWarningLog:export', '#', '1', NULL, '2024-05-22 16:43:01', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641914, '回复超时', 1433699711154641913, 1, 'reply-timeout-setting', 'modules/replyTimeoutSetting/index', 1, 0, 'C', '0', '0', 'module:replyTimeoutSetting:index', '#', '1', 'admin', '2024-03-16 17:33:39', '2024-04-09 14:11:36', 0, '回复超时功能旨在设定回复时间的上限，统计员工沟通时超过此时间的回复次数，进而督促员工，优化服务质量。', '', '回复超时', 'https://scrm.efundsdemo.com/wecom-crm-em/scrm/common/f6c502ce-9755-462f-b9d1-47d4ea64ea48.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641919, '保存', 1433699711154641914, 1, '', NULL, 1, 0, 'F', '0', '0', 'module:replyTimeoutSetting:edit', '#', '1', NULL, '2024-03-25 17:50:29', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');

-- 触发记录表
CREATE OR REPLACE VIEW v_sens_record AS
SELECT
    r.id,
    t.rule_name,              -- 敏感词组名称
    t.sensitive_words,        -- 敏感词，多个逗号分开
    r.send_user_id,           -- 发送方id，客户企微id/员工id
    r.send_user_type,         -- 发送方类型：1-微信用户 2-企业员工
    (SELECT d.`name` FROM tb_wx_user u LEFT JOIN tb_wx_department d ON u.main_department = d.id WHERE u.userid = r.send_user_id LIMIT 1) AS send_user_department,     -- 部门名称
    r.accept_user_id,         -- 接收方id，客户企微id/员工id/群聊id
    r.accept_user_type,       -- 接收方类型：1-微信用户 2-企业员工 3-群聊
    (SELECT d.`name` FROM tb_wx_user u LEFT JOIN tb_wx_department d ON u.main_department = d.id WHERE u.userid = r.accept_user_id LIMIT 1) AS accept_user_department, -- 部门名称
    r.trigger_time            -- 触发时间
FROM ck_session_sens_word_alarm_record r
         LEFT JOIN ck_session_sens_rule_info t ON r.rule_id = t.rule_id
ORDER BY r.trigger_time DESC;

-- 敏感内容拦截规则表
CREATE OR REPLACE VIEW v_sens_rule AS
SELECT
    t.rule_id,
    t.rule_name,         -- 敏感词组名称
    t.sensitive_words,   -- 敏感词，多个逗号分开
    t.act_types,         -- 敏感行为，多选，逗号分隔,1.发送手机号,  2.发送邮箱地址  3.发送和接收红包
    GROUP_CONCAT(DISTINCT m.check_user_id) AS userIds,    -- 员工Id，多个逗号分开
    GROUP_CONCAT(DISTINCT u.`name`) AS userNames,         -- 员工名称，多个逗号分开
    t.intercept_type,    -- 拦截方式：1.警告并拦截 2.警告且事后审计 3.仅事后审计
    t.create_time        -- 创建时间
FROM ck_session_sens_rule_info t
         LEFT JOIN ck_session_sens_check_mapping m ON t.rule_id = m.rule_id
         LEFT JOIN tb_wx_user u ON m.check_user_id = u.userid
WHERE t.`status` = 1
GROUP BY t.rule_id
ORDER BY t.rule_id DESC;

-- 触发记录表
CREATE OR REPLACE VIEW v_sens_act_record AS
SELECT
    r.id,
    r.act_type,           -- 敏感行为，多选，逗号分隔,字典：1.发送手机号,  2.发送邮箱地址  3.发送和接收红包
    r.send_user_id,       -- 发送方id，客户企微id/员工id
    r.send_user_type,     -- 发送方类型：1-微信用户 2-企业员工
    (SELECT d.`name` FROM tb_wx_user u LEFT JOIN tb_wx_department d ON u.main_department = d.id WHERE u.userid = r.send_user_id LIMIT 1) AS send_user_department,     -- 部门名称，非员工则为空
    r.accept_user_id,     -- 接收方id，客户企微id/员工id/群聊id
    r.accept_user_type,   -- 接收方类型：1-微信用户 2-企业员工 3-群聊
    (SELECT d.`name` FROM tb_wx_user u LEFT JOIN tb_wx_department d ON u.main_department = d.id WHERE u.userid = r.accept_user_id LIMIT 1) AS accept_user_department, -- 部门名称，非员工则为空
    r.trigger_time        -- 触发时间
FROM ck_session_sens_act_alarm_record r
ORDER BY r.trigger_time DESC;

-- 回复超时规则表
CREATE OR REPLACE VIEW v_sens_reply_timeout_rule AS
SELECT
    s.set_id,
    s.time_num,      -- 超时提醒时长，单位分钟
    s.create_time,   -- 创建时间
    GROUP_CONCAT(DISTINCT m.check_user_id) AS userIds,  -- 员工范围，多个逗号分开
    s.start_time,    -- 监控开始时间,格式：HH:mm
    s.end_time       -- 监控结束时间,格式：HH:mm
FROM ck_session_timeout_set s
         LEFT JOIN ck_session_timeset_check_mapping m ON s.set_id = m.set_id;

-- 回复超时记录表
CREATE OR REPLACE VIEW v_sens_reply_timeout_record AS
SELECT
    c.id,
    u.userid,              -- 触发员工id
    d.NAME AS dept_name,   -- 触发员工部门名称
    c.origin_msg_time      -- 触发时间
FROM wk_chat_conversation c
         LEFT JOIN tb_wx_user u ON u.userid = c.staff_id
         LEFT JOIN tb_wx_department d ON d.id = u.main_department
WHERE c.timeout = 1
ORDER BY c.origin_msg_time DESC;


-- --------------------
-- 角色支持自定义部门数据权限
-- --------------------
-- 渠道活码
ALTER TABLE `tb_wx_contact` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_contact` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 社群活码
ALTER TABLE `ck_group_code_info` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `ck_group_code_info` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 获客链接
ALTER TABLE `tb_wx_custlink` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_custlink` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 条件SOP/旅程SOP/1v1SOP
ALTER TABLE `ck_sop_info` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `ck_sop_info` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 1v1群发/社群群发
ALTER TABLE `tb_wx_mass_message_info` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_mass_message_info` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 发朋友圈
ALTER TABLE `tb_wx_moment_task_info` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_moment_task_info` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 智能物料
ALTER TABLE `tb_wx_radar_interact` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_radar_interact` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 营销素材
ALTER TABLE `tb_wx_material` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_material` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 企业话术
ALTER TABLE `tb_wx_quick_reply` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_wx_quick_reply` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 智能表单
ALTER TABLE `pr_user_project` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `pr_user_project` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 自动打标签
ALTER TABLE `tb_wx_auto_tag` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 热词设置
ALTER TABLE `ck_session_hot_word_info` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `ck_session_hot_word_info` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 员工数据
ALTER TABLE `tb_statistic_staff` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_statistic_staff` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 客户数据
ALTER TABLE `tb_statistic_customer_detail` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_statistic_customer_detail` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 社群数据
ALTER TABLE `tb_statistic_customer_group` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_statistic_customer_group` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 物料数据
ALTER TABLE `tb_statistic_radar` ADD COLUMN `create_by` varchar(128) DEFAULT NULL COMMENT '创建人';
ALTER TABLE `tb_statistic_radar` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_statistic_radar` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;
-- 社群数据
ALTER TABLE `tb_statistic_hot_word` ADD COLUMN `create_by` bigint DEFAULT NULL COMMENT '创建人';
ALTER TABLE `tb_statistic_hot_word` ADD COLUMN `dept_id` bigint  DEFAULT NULL COMMENT '部门id';
ALTER TABLE `tb_statistic_hot_word` ADD COLUMN `hot_id` bigint DEFAULT NULL COMMENT '热词id';
ALTER TABLE `tb_statistic_hot_word` ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;

UPDATE `tb_wx_contact` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `ck_group_code_info` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_custlink` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `ck_sop_info` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_mass_message_info` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_moment_task_info` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_radar_interact` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_material` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_quick_reply` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `pr_user_project` c LEFT JOIN sys_user u ON c.user_id = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_wx_auto_tag` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `ck_session_hot_word_info` c LEFT JOIN sys_user u ON c.create_by = u.user_id LEFT JOIN tb_wx_user wu ON wu.userid = u.corp_user_id SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_statistic_staff` c LEFT JOIN tb_wx_user wu ON wu.userid = c.userid SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_statistic_customer_detail` c LEFT JOIN tb_wx_user wu ON wu.userid = c.userid SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_statistic_customer_group` c LEFT JOIN tb_wx_user wu ON wu.userid = c.owner SET c.`dept_id` = wu.main_department WHERE c.`dept_id` IS NULL;
UPDATE `tb_statistic_radar` c LEFT JOIN tb_wx_radar_interact r ON r.id = c.radar_id SET c.`create_by` = r.`create_by`, c.`dept_id` = r.dept_id WHERE c.`dept_id` IS NULL;
UPDATE `tb_statistic_hot_word` c LEFT JOIN ck_session_hot_word_info hw ON hw.hot_word = c.hot_word SET c.`hot_id` = hw.`hot_id`, c.`create_by` = hw.`create_by`, c.`dept_id` = hw.dept_id WHERE c.`dept_id` IS NULL;

-- 菜单按钮权限导出
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642098, '导出', 1433699711154641712, 4, '', NULL, 1, 0, 'F', '0', '0', 'system:menu:export', '#', '1', NULL, '2024-11-18 09:53:06', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
-- 删除重复记录
DELETE t1
FROM tb_dc_user_tags_operrel_mapping t1
         JOIN (
    SELECT oper_rel, tag_type_id, MIN(id) as min_id
    FROM tb_dc_user_tags_operrel_mapping
    GROUP BY oper_rel, tag_type_id
    HAVING COUNT(*) > 1
) t2 ON t1.oper_rel = t2.oper_rel AND t1.tag_type_id = t2.tag_type_id
WHERE t1.id > t2.min_id;

DELETE FROM `xxl_job_info` WHERE executor_handler = 'synchronizeMoment';
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '同步近期朋友圈数据', now(), now(), '管理员', '', 'CRON', ' 0 0 */1 * * ?', 'DO_NOTHING', 'FIRST', 'syncRecentMoment', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', now(), '', 1, 1734159600000, 1734170400000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '同步历史朋友圈数据', now(), now(), '管理员', '', 'CRON', ' 0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'syncHistoryMoment', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', now(), '', 1, 1734159600000, 1734170400000);

ALTER TABLE `tb_wx_moment_send_user` ADD INDEX `idx_moment_task_id`(`moment_task_id`) USING BTREE;
ALTER TABLE `tb_wx_moment_interact` ADD INDEX `idx_moment_task_id`(`moment_task_id`, `user_id`) USING BTREE;
ALTER TABLE `tb_wx_moment_interact` ADD INDEX `idx_external_user_id`(`external_user_id`) USING BTREE;
ALTER TABLE `tb_wx_moment_customer` ADD INDEX `idx_moment_id`(`moment_id`) USING BTREE;
ALTER TABLE `tb_wx_moment_like_and_comment` ADD INDEX `idx_moment_id`(`moment_id`) USING BTREE;
