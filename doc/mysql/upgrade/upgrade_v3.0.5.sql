DROP TABLE IF EXISTS `tb_wx_business_tag`;
CREATE TABLE `tb_wx_business_tag`
(
    `external_user_id`       varchar(64) NOT NULL COMMENT '外部联系人的userid',
    `statistic_date`         date DEFAULT NULL COMMENT '统计时间',
    `message_sent_cnt`       int  DEFAULT NULL COMMENT '加入企微后发送消息数',
    `l7d_message_sent_cnt`   int  DEFAULT NULL COMMENT '近7天发送企微消息数',
    `l30d_message_sent_cnt`  int  DEFAULT NULL COMMENT '近30天发送企微消息数',
    `l7d_active_days`        int  DEFAULT NULL COMMENT '近7天企微活跃天数',
    `l30d_active_days`       int  DEFAULT NULL COMMENT '近30天企微活跃天数',
    `l7d_material_read_cnt`  int  DEFAULT NULL COMMENT '近7天物料阅读次数',
    `l30d_material_read_cnt` int  DEFAULT NULL COMMENT '近30天物料阅读次数',
    PRIMARY KEY (`external_user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='客户企微业务标签表';

ALTER TABLE `tb_dc_user_tags`
    ADD COLUMN `tag_source` tinyint(1) NULL COMMENT '标签来源：1 企微，2 数据中心' AFTER `dict_type`;
ALTER TABLE `tb_dc_user_tags`
    MODIFY COLUMN `display_order` varchar(10) DEFAULT '' COMMENT '排序字段，用于前端页面展示时的顺序' AFTER `update_time`;

INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '性别', NULL, '2024-03-13 15:33:39', NULL, '2024-06-06 14:23:04', '010101', 0,
        'c_gen_name', 'MULTI_SELECT', 'user_tag_sex', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '年龄段', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:02', '010102', 1,
        'c_age_stra', 'MULTI_SELECT', 'user_tag_age', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '生日', NULL, '2024-03-13 15:33:39', NULL, '2024-06-03 10:25:03', '010103', 1,
        'c_bth_date', 'DATE', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '城市', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:03', '010104', 1,
        'c_city_name', 'TEXT', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '省份', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:04', '010105', 1,
        'c_prov_name', 'TEXT', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('基本信息', '基本信息', '职业', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:05', '010106', 1,
        'c_occo_name', 'TEXT', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '是否直销', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:06', '020101', 1,
        'c_if_ds_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '是否代销', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:07', '020102', 1,
        'c_if_cs_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '是否投顾', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:08', '020103', 1,
        'c_if_ia_cust_rul', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '当日资产金额', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:09', '020104', 1,
        'c_day_hld_ast_stra', 'MULTI_SELECT', 'user_tag_asset_level', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '近一年日均资产', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:10', '020105', 1,
        'c_l1y_avg_hld_ast_stra', 'MULTI_SELECT', 'user_tag_asset_level', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '近三年日均资产', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:11', '020106', 1,
        'c_l3y_avg_hld_ast_stra', 'MULTI_SELECT', 'user_tag_asset_level', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '开户日期', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:11', '020107', 1,
        'c_cust_open_date', 'DATE', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '风险测评等级', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:12', '020108', 1,
        'c_risk_type_name', 'MULTI_SELECT', 'user_tage_pg_level', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('账户概况', '账户概况', '持仓产品', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:28', '020109', 1,
        'c_day_ttl_hld_fund', 'TEXT', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '交易频率', '近一年内平均一支基金交易次数', NULL, '2024-03-13 15:33:39', NULL,
        '2024-05-28 16:19:14', '030101', 1, 'f_l1y_tracnt', 'INTEGER', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '定投', '是否定投', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:14', '030102', 1,
        'c_l3m_if_rfap', 'MULTI_SELECT', 'user_tage_is_dt', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '定投', '最近一次定投行为的时间', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:15',
        '030103', 1, 'c_last_rfap_cfm_date', 'DATE', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('投资收益', '账户收益（数值）', '历史以来累计收益', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:16',
        '040201', 1, 'f_his_ttl_cf_prft', 'CURRENCY', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('投资收益', '账户收益（数值）', '近三年累计收益', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:17',
        '040202', 1, 'f_l3y_ttl_cf_prft', 'CURRENCY', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', 'R1仓位比例', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:18', '050101', 1,
        'f_r1_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', 'R2+R3仓位比例', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:21', '050102', 1,
        'f_r23_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', 'R4+R5仓位比例', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:21', '050103', 1,
        'f_r45_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '仓位比例标签', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:22', '050104', 1,
        'c_astr_ratio_label', 'MULTI_SELECT', 'user_tage_ratio_label', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客服标签', '贵宾', '是否贵宾客户', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:22', '060101', 1,
        'c_if_vip_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客服标签', '投诉', '是否投诉客户', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:23', '060201', 1,
        'c_if_complaint_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客服标签', '免打扰', '是否免打扰客户', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:24', '060301', 1,
        'c_if_notdisturb_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('投资收益', '账户收益（分类）', '历史以来盈利情况', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:25',
        '040101', 1, 'c_his_ttl_cf_prft_label', 'MULTI_SELECT', 'user_tag_his_prft', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('投资收益', '账户收益（分类）', '近三年盈利情况', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:26',
        '040102', 1, 'c_l3y_ttl_cf_prft_label', 'MULTI_SELECT', 'user_tag_l3y_prft', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客服接触', '客服接触', '电话客服话务次数', NULL, '2024-03-13 15:33:39', NULL, '2024-05-28 16:19:27', '070101',
        1, 'f_mobile_cnt', 'INTEGER', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '货币型基金占比', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:29', '050105', 1,
        'f_mmf_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '债券型基金占比', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:30', '050106', 1,
        'f_bond_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '股票型基金占比', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:30', '050107', 1,
        'f_equity_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '混合型基金占比', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:31', '050108', 1,
        'f_blend_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', 'FOF基金占比', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:32', '050109', 1,
        'f_fof_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('风险承担', '持仓倾向', '其他类型基金占比', NULL, '2024-05-31 09:36:29', NULL, '2024-05-31 09:36:29', '050110',
        0, 'f_other_ast_ratio', 'RATIO', NULL, 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '养老', '是否有过Y份额持仓', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:33', '030104', 1,
        'c_if_y_share_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '养老', '是否有过非Y份额养老基金持仓', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:34',
        '030105', 1, 'c_if_not_y_pens_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '指数', '是否有过ETF基金持仓', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:34', '030106',
        1, 'c_if_etf_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '指数', '是否有过ETF联接基金持仓', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:36',
        '030107', 1, 'c_if_etf_cnet_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('交易记录', '指数', '是否有过普通指数基金持仓', NULL, '2024-05-23 14:16:44', NULL, '2024-05-28 16:19:36',
        '030108', 1, 'c_if_index_cust', 'MULTI_SELECT', 'user_tage_y_n', 2);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微沟通', '加入企微后发送消息数', NULL, now(), NULL, now(), '080101', 1, 'message_sent_cnt',
        'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微沟通', '近7天发送企微消息数', NULL, now(), NULL, now(), '080102', 1,
        'l7d_message_sent_cnt', 'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微沟通', '近30天发送企微消息数', NULL, now(), NULL, now(), '080103', 1,
        'l30d_message_sent_cnt', 'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微沟通', '近7天企微活跃天数', NULL, now(), NULL, now(), '080104', 1, 'l7d_active_days',
        'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微沟通', '近30天企微活跃天数', NULL, now(), NULL, now(), '080105', 1, 'l30d_active_days',
        'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微物料', '近7天物料阅读次数', NULL, now(), NULL, now(), '080106', 1, 'l7d_material_read_cnt',
        'INTEGER', NULL, 1);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`,
                              `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`,
                              `data_format_type`, `dict_type`, `tag_source`)
VALUES ('客户活跃', '企微物料', '近30天物料阅读次数', NULL, now(), NULL, now(), '080107', 1,
        'l30d_material_read_cnt', 'INTEGER', NULL, 1);



INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'message_sent_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'message_sent_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'message_sent_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'message_sent_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'message_sent_cnt', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l7d_message_sent_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l7d_message_sent_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l7d_message_sent_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l7d_message_sent_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l7d_message_sent_cnt', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l30d_message_sent_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l30d_message_sent_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l30d_message_sent_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l30d_message_sent_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l30d_message_sent_cnt', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l7d_active_days', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l7d_active_days', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l7d_active_days', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l7d_active_days', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l7d_active_days', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l30d_active_days', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l30d_active_days', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l30d_active_days', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l30d_active_days', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l30d_active_days', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l7d_material_read_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l7d_material_read_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l7d_material_read_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l7d_material_read_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l7d_material_read_cnt', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('EQ', '等于', '2024-03-25 11:29:43', 1, 'l30d_material_read_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GT', '大于', '2024-03-25 11:29:43', 1, 'l30d_material_read_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LT', '小于', '2024-03-25 11:29:43', 1, 'l30d_material_read_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('GE', '大于等于', '2024-03-25 11:29:43', 1, 'l30d_material_read_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('LE', '小于等于', '2024-03-25 11:29:43', 1, 'l30d_material_read_cnt', 5);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('IN', '包含', '2024-03-26 16:12:32', 1, 'SEL_AUTH_STATUS', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`,
                                              `display_order`)
VALUES ('NOTIN', '不包含', '2024-03-26 16:12:32', 1, 'SEL_AUTH_STATUS', 2);



INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`,
                           `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`,
                           `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`,
                           `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`,
                           `trigger_status`, `trigger_last_time`, `trigger_next_time`)
VALUES (2, '统计客户企微标签数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */1 * * ?',
        'DO_NOTHING', 'FIRST', 'statWxBusinessTagData', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化',
        '2023-08-09 18:50:36', '', 1, 1716209940000, 1716217140000);


DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`
(
    `oper_id`        bigint   NOT NULL AUTO_INCREMENT COMMENT '日志主键',
    `trace_id`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求追踪ID',
    `module_key`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '模块key',
    `title`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '模块标题',
    `oper_desc`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '操作行为',
    `business_type`  tinyint(1)                                                    DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除 4导入 5导出 6同步 7强退）',
    `method`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '方法名称',
    `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '请求方式',
    `operator_type`  tinyint(1)                                                    DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2企微端用户）',
    `oper_user_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '操作人员ID',
    `oper_name`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '操作人员名称',
    `dept_name`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '部门名称',
    `oper_url`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
    `oper_ip`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '主机地址',
    `oper_location`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作地点',
    `oper_param`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数',
    `json_result`    longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '返回参数',
    `status`         tinyint(1)                                                    DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
    `error_msg`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误消息',
    `oper_time`      datetime                                                      DEFAULT NULL COMMENT '操作时间',
    `create_time`    datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`       tinyint(1)                                                    DEFAULT '0' COMMENT '0 标识未删除 1 标识删除 ',
    `create_by`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci        DEFAULT NULL COMMENT '创建者',
    PRIMARY KEY (`oper_id`) USING BTREE,
    KEY `idx_module_key` (`module_key`) USING BTREE,
    KEY `idx_business_type` (`business_type`) USING BTREE,
    KEY `idx_oper_time` (`oper_time`) USING BTREE,
    KEY `idx_oper_name` (`oper_name`) USING BTREE,
    KEY `idx_dept_name` (`dept_name`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='操作日志记录';

ALTER TABLE `tb_wx_cache_content`
    MODIFY COLUMN `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细信息' AFTER `type`;

ALTER TABLE `sys_dict_data` ADD INDEX `idx_dict_type`(`dict_type`) USING BTREE;

INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('操作类型', 'sys_oper_type', '0', 'admin', 'admin', '2024-06-12 10:26:26', '2024-06-12 15:01:31', 0, NULL);
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('模块名称', 'module_name', '0', 'admin', NULL, '2024-06-12 10:32:45', NULL, 0, NULL);
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('客户类型', 'customer_type', '0', 'admin', NULL, '2024-06-12 15:19:02', NULL, 0, NULL);

DELETE FROM `sys_dict_data` WHERE  dict_type = 'customer_type';
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (1, '微信客户', '1', 'customer_type', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 15:19:27', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (2, '企业客户', '2', 'customer_type', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 15:19:39', NULL, NULL);

DELETE FROM `sys_dict_data` WHERE  dict_type = 'sys_oper_type';
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 0, 'admin', '', '2021-03-18 00:23:12', NULL, '新增操作');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 0, 'admin', '', '2021-03-18 00:23:12', NULL, '修改操作');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 0, 'admin', '', '2021-03-18 00:23:12', NULL, '删除操作');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (4, '查询', '4', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:28:00', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (5, '导入', '5', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:28:21', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (6, '导出/下载', '6', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:28:34', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (7, '同步', '7', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:28:58', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (8, '强退', '8', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:30:08', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (9, '清空', '9', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:30:48', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (99, '其他', '0', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', 'admin', '2024-06-12 10:31:40', '2024-06-12 15:01:31', NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (10, '授权', '10', 'sys_oper_type', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 13:01:57', '2024-06-12 15:01:31', NULL);


DELETE FROM `sys_dict_data` WHERE  dict_type = 'module_name';
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (1, '工作台', 'workbench', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (2, '渠道活码', 'channel', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (3, '社群活码', 'community-code', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (4, '获客链接', 'customer-acquisition-link', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (5, '条件SOP', 'condition-sop', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (6, '旅程SOP', 'journey-sop', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (7, '1V1SOP', 'one-to-one-sop', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (8, '1V1群发','mass-customer', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (9, '发朋友圈','sendmoments', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (10, '社群群发','mass-group', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (11, '服务话术', 'enterprisescript', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (12, '智能物料', 'intelligent-material', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (13, '营销素材', 'normal-material', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (14, '智能表单','smartForm', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (15, '客户列表', 'customerList', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (16, '客户标签', 'customerTag', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (17, '流失提醒', 'churn', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (18, '社群管理', 'community', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (19, '客户旅程', 'journey', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (20, '业务标签', 'businesstag', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (21, '在职继承', 'incumbency-succession', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (22, '离职继承', 'dimission', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (23, '通讯录', 'department', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (24, '权限管理', 'permission-management', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (25, '聊天存档', 'chatArchive', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (26,'敏感规则', 'sensitive-rules', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (27, '敏感词警告', 'sensitive-alert-log', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (28,'敏感行为', 'sensitive-behavior', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (29,'回复超时', 'reply-timeout-setting', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (30, '热词设置', 'hot-work-setting', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (31, '员工数据', 'staff-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (32, '客户数据', 'customer-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (33, '客户群数据', 'customer-group-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (34, '物料统计', 'radar-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (35, '热词统计', 'hot-word-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (36, '敏感词统计', 'sensitive-word-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (37, '敏感行为统计', 'sensitive-behavior-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (38, '回复超时统计', 'reply-timeout-statistics', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (39, '企业设置', 'enterpriseConfiguration', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (40, '菜单管理', 'menu', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (41, '用户管理', 'user', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (42, '角色管理', 'role', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (43, '参数管理', 'config', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (44, '字典管理', 'dict', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (45, '操作日志', 'operationLog', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25',NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (46, '在线用户', 'online', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES (47, '登录日志', 'login_info', 'module_name', NULL, NULL, 'N', '0', 0, 'admin', NULL, '2024-06-12 10:33:25', NULL, NULL);

DELETE FROM `sys_menu` WHERE `menu_id` = 1433699711154641832;
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642012, '操作日志', 1433699711154641830, 2, 'operation-log', 'monitor/operation-log/index', 1, 0, 'C', '0', '0', 'monitor:operationLog:list', '#', '1', 'admin', '2024-06-06 00:55:31', '2024-06-06 00:56:43', 0, '操作日志的作用主要侧重于合规人员的审计和违规操作的监测。它详细记录了系统中所有的用户操作行为，使得合规人员能够轻松追踪和审计用户的操作历史', '', '操作日志', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/b0ca54bb-0b61-4b02-b627-157c0a082bb7.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642013, '导出', 1433699711154642012, 1, '', NULL, 1, 0, 'F', '0', '0', 'monitor:operationLog:export', '#', '1', NULL, '2024-06-06 00:56:59', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
