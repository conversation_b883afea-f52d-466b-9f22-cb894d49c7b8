-- 员工维表
CREATE OR REPLACE VIEW v_staff AS
SELECT
    u.`userid` AS staff_id,
    u.`NAME`,
    u.`main_department `,
    count( DISTINCT CASE WHEN efu.STATUS = 0 THEN efu.external_user_id END ) AS customer_cnt,
    count( DISTINCT efu.external_user_id ) AS his_customer_cnt,
    count( DISTINCT CASE WHEN efu.STATUS != 0 THEN efu.external_user_id END ) AS his_lost_customer_cnt,
    count( DISTINCT g.chat_id ) AS manage_group_cnt,
    count( DISTINCT gm.group_id) AS join_group_cnt,
    r1.role
FROM
    tb_wx_user u
        LEFT JOIN tb_wx_ext_follow_user efu ON efu.corp_id = u.corp_id AND efu.user_id = u.userid
        LEFT JOIN tb_wx_customer_group g ON g.`owner` = u.userid AND g.corp_id = u.corp_id AND g.STATUS = 0 AND g.dismiss_status = 0
        LEFT JOIN (
        SELECT
            su.corp_user_id,
            GROUP_CONCAT( r.role_name ) AS role
        FROM
            sys_user su
                LEFT JOIN sys_user_role ur ON ur.user_id = su.user_id
                LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE
            su.corp_user_id IS NOT NULL
          AND su.del_flag = 0
        GROUP BY su.corp_user_id
    ) r1 ON r1.corp_user_id = u.userid
        LEFT JOIN tb_wx_customer_group_member gm ON gm.user_id = u.userid AND gm.STATUS = 0
WHERE u.status = 1 AND u.del_flag = '1'
GROUP BY u.userid;

-- 1. 客户维表
CREATE OR REPLACE VIEW v_customer AS
SELECT
    c.external_user_id,
    c.union_id,
    c.first_add_date,
    c.is_auth,
    ar.first_auth_time,
    IF(efu.id is not null, c.`status`, '2') AS status,
    IF(s.custno IS NULL, 'N', 'Y') AS internal_employee,
    IF(c.corp_name IS NULL, 'N', 'Y') AS corp_customer,
    ut.wx_tag,
    IF(s.custno IS NULL, l.c_gen_name, '--') as c_gen_name,
    IF(s.custno IS NULL, l.c_age_stra, '--') as c_age_stra,
    IF(s.custno IS NULL, l.c_bth_date, '--') as c_bth_date,
    IF(s.custno IS NULL, l.c_city_name, '--') as c_city_name,
    IF(s.custno IS NULL, l.c_prov_name, '--') as c_prov_name,
    IF(s.custno IS NULL, l.c_occo_name, '--') as c_occo_name,
    IF(s.custno IS NULL, l.c_if_ds_cust, '--') as c_if_ds_cust,
    IF(s.custno IS NULL, l.c_if_cs_cust, '--') as c_if_cs_cust,
    IF(s.custno IS NULL, l.c_if_ia_cust_rul, '--') as c_if_ia_cust_rul,
    IF(s.custno IS NULL, l.c_day_hld_ast_stra, '--') as c_day_hld_ast_stra,
    IF(s.custno IS NULL, l.c_l1y_avg_hld_ast_stra, '--') as c_l1y_avg_hld_ast_stra,
    IF(s.custno IS NULL, l.c_l3y_avg_hld_ast_stra, '--') as c_l3y_avg_hld_ast_stra,
    IF(s.custno IS NULL, l.c_cust_open_date, '--') as c_cust_open_date,
    IF(s.custno IS NULL, l.c_risk_type_name, '--') as c_risk_type_name,
    IF(s.custno IS NULL, l.c_day_ttl_hld_fund, '--') as c_day_ttl_hld_fund,
    IF(s.custno IS NULL, l.f_l1y_tracnt, '--') as f_l1y_tracnt,
    IF(s.custno IS NULL, l.c_l3m_if_rfap, '--') as c_l3m_if_rfap,
    IF(s.custno IS NULL, l.c_last_rfap_cfm_date, '--') as c_last_rfap_cfm_date,
    IF(s.custno IS NULL, l.f_his_ttl_cf_prft, '--') as f_his_ttl_cf_prft,
    IF(s.custno IS NULL, l.f_l3y_ttl_cf_prft, '--') as f_l3y_ttl_cf_prft,
    IF(s.custno IS NULL, l.f_r1_ast_ratio, '--') as f_r1_ast_ratio,
    IF(s.custno IS NULL, l.f_r23_ast_ratio, '--') as f_r23_ast_ratio,
    IF(s.custno IS NULL, l.f_r45_ast_ratio, '--') as f_r45_ast_ratio,
    IF(s.custno IS NULL, l.c_astr_ratio_label, '--') as c_astr_ratio_label,
    IF(s.custno IS NULL, l.c_if_vip_cust, '--') as c_if_vip_cust,
    IF(s.custno IS NULL, l.c_if_complaint_cust, '--') as c_if_complaint_cust,
    IF(s.custno IS NULL, l.c_if_notdisturb_cust, '--') as c_if_notdisturb_cust,
    IF(s.custno IS NULL, l.c_his_ttl_cf_prft_label, '--') as c_his_ttl_cf_prft_label,
    IF(s.custno IS NULL, l.c_l3y_ttl_cf_prft_label, '--') as c_l3y_ttl_cf_prft_label,
    IF(s.custno IS NULL, l.f_mobile_cnt, '--') as f_mobile_cnt,
    IF(s.custno IS NULL, l.f_mmf_ast_ratio, '--') as f_mmf_ast_ratio,
    IF(s.custno IS NULL, l.f_bond_ast_ratio, '--') as f_bond_ast_ratio,
    IF(s.custno IS NULL, l.f_equity_ast_ratio, '--') as f_equity_ast_ratio,
    IF(s.custno IS NULL, l.f_blend_ast_ratio, '--') as f_blend_ast_ratio,
    IF(s.custno IS NULL, l.f_fof_ast_ratio, '--') as f_fof_ast_ratio,
    IF(s.custno IS NULL, l.f_other_ast_ratio, '--') as f_other_ast_ratio,
    IF(s.custno IS NULL, l.c_if_y_share_cust, '--') as c_if_y_share_cust,
    IF(s.custno IS NULL, l.c_if_not_y_pens_cust, '--') as c_if_not_y_pens_cust,
    IF(s.custno IS NULL, l.c_if_etf_cust, '--') as c_if_etf_cust,
    IF(s.custno IS NULL, l.c_if_etf_cnet_cust, '--') as c_if_etf_cnet_cust,
    IF(s.custno IS NULL, l.c_if_index_cust, '--') as c_if_index_cust,
    bt.message_sent_cnt,
    bt.l7d_message_sent_cnt,
    bt.l30d_message_sent_cnt,
    bt.l7d_active_days,
    bt.l30d_active_days,
    bt.l7d_material_read_cnt,
    bt.l30d_material_read_cnt
FROM
    tb_wx_ext_customer c
        LEFT JOIN ( SELECT custno, min( bind_time ) AS first_auth_time FROM tb_wx_customer_auth_record GROUP BY custno ) ar ON ar.custno = c.custno
        LEFT JOIN t_staff s ON s.custno = c.custno
        LEFT JOIN v_f_scrm_label l ON l.custno = c.custno
        LEFT JOIN tb_wx_business_tag bt ON bt.external_user_id = c.external_user_id
        LEFT JOIN (
        SELECT
            external_user_id,
            JSON_OBJECTAGG( group_name, tags_list ) AS wx_tag
        FROM
            ( SELECT external_user_id, group_name, GROUP_CONCAT( tag ) AS tags_list FROM tb_wx_ext_follow_user_tag WHERE type = 1 AND del_flag = 0 GROUP BY external_user_id, group_name ) AS subquery
        GROUP BY external_user_id
    ) ut ON ut.external_user_id = c.external_user_id
        LEFT JOIN tb_wx_ext_follow_user efu ON efu.external_user_id = c.external_user_id AND efu.STATUS = 0
GROUP BY c.external_user_id;

-- 物料维度统计
CREATE OR REPLACE VIEW v_radar_stat_data AS
SELECT r.statistic_date,
       r.radar_id,
       r.title,
       r.radar_type,
       r.send_times,
       r.click_times,
       r.forward_times,
       r.new_flag,
       r.create_time,
       count(DISTINCT CASE WHEN cr.forward_to = 1 THEN cr.customer_id END) AS forward_num,
       sum(cr.read_time)                                                   AS total_read_duration,
       count(DISTINCT cr.customer_id)                                      AS click_num
FROM tb_statistic_radar r
         LEFT JOIN tb_wx_radar_content c ON c.radar_id = r.radar_id
         LEFT JOIN tb_wx_radar_content_record cr ON cr.content_id = c.id AND date(cr.create_time) = r.statistic_date
GROUP BY r.statistic_date,
         r.radar_id;


-- 删除sys_role_dept表中与特定角色相关的记录
DELETE sr FROM `sys_role_dept` sr JOIN `sys_role` r ON sr.role_id = r.role_id WHERE r.role_name IN ('权限管理员', '部门管理员', '超级管理员');
-- 删除sys_role_menu表中与特定角色相关的记录
DELETE sm FROM `sys_role_menu` sm JOIN `sys_role` r ON sm.role_id = r.role_id WHERE r.role_name IN ('权限管理员', '部门管理员', '超级管理员');
-- 删除sys_user_role表中与特定角色相关的记录
DELETE su FROM `sys_user_role` su JOIN `sys_role` r ON su.role_id = r.role_id WHERE r.role_name IN ('权限管理员', '部门管理员', '超级管理员');
-- sys_role表中删除特定角色
ALTER TABLE `sys_role` ADD INDEX `idx_role_name`(`role_name`) USING BTREE;
DELETE FROM `sys_role` WHERE role_name in ('权限管理员', '部门管理员', '超级管理员');

ALTER TABLE `tb_wx_moment_task_info`
    ADD COLUMN `task_name` varchar(255) NULL COMMENT '任务名称' AFTER `id`;

DROP TABLE IF EXISTS `bu_category`;
CREATE TABLE `bu_category`
(
    `category_id`   bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称',
    `category_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '分组类型',
    `parent_id`     bigint                                                        NOT NULL COMMENT '父级分组id',
    `sort`          int                                                                    DEFAULT NULL COMMENT '排序',
    `dept_id`       bigint                                                                 DEFAULT NULL COMMENT '部门ID',
    `create_by`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '创建者',
    `update_by`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '更新者',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`      tinyint(1)                                                             DEFAULT 0 COMMENT '0 标识未删除 1 标识删除 ',
    PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE utf8mb4_general_ci COMMENT ='业务分组表';

DROP TABLE IF EXISTS `sys_role_category`;
CREATE TABLE `sys_role_category`
(
    `role_id`     bigint   NOT NULL COMMENT '角色ID',
    `category_id` bigint   NOT NULL COMMENT '分组ID',
    `category_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '分组类型',
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NOT NULL                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`    tinyint(1)                                                   DEFAULT '0' COMMENT '0 标识未删除 1 标识删除 ',
    PRIMARY KEY (`role_id`, `category_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='角色和分组关联表';

ALTER TABLE `tb_wx_corp_tag_group`
    ADD COLUMN `category_id` bigint NULL COMMENT '分组ID' AFTER `type`;

ALTER TABLE `sys_role`
    ADD COLUMN `tag_category_scope` varchar(255) NULL COMMENT '标签分组权限' AFTER `dept_check_strictly`;

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642063, '新增分组', 1433699711154641729, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:customerTag:group:add', '#', '1', NULL, '2024-07-30 16:56:30', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642064, '编辑分组', 1433699711154641729, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:customerTag:group:edit', '#', '1', NULL, '2024-07-30 16:56:44', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642065, '删除分组', 1433699711154641729, 7, '', NULL, 1, 0, 'F', '0', '0', 'modules:customerTag:group:delete', '#', '1', NULL, '2024-07-30 16:57:13', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642067, '拖动分组', 1433699711154641729, 8, '', NULL, 1, 0, 'F', '0', '0', 'modules:customerTag:group:drop', '#', '1', NULL, '2024-08-07 11:03:16', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');

