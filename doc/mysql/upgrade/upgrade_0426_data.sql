INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`, `remark`) VALUES ( 2, '警告且事后审计', '2', 'sens_rule_cept_type', '', '', 'Y', '0', 0, 'admin', '', '2024-03-18 08:51:32', '2024-03-22 09:59:43', '敏感词拦截方式');

INSERT INTO `tb_wx_mass_message_query_type` (`group_name`, `qry_type`, `qry_type_name`, `qry_type_icon`, `show_flag`, `display_order`, `create_by`, `create_time`, `update_by`, `update_time`)
VALUES ('客户行为', 'AUTH_STATUS', '认证状态', 'userDefined', '1', 5, 1, '2024-03-26 15:55:06', 1, '2024-03-26 15:55:10');

INSERT INTO `tb_wx_mass_message_query_type_tag` ( `qry_type`, `qry_tag_id`, `qry_tag_name`, `dict_type`, `data_format_type`, `show_flag`, `display_order`, `qry_class_name`, `qry_tab_column`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ( 'AUTH_STATUS', 'SEL_AUTH_STATUS', '认证标签', 'user_tag_auth_status', 'MULTI_SELECT', '1', 1, NULL, 'is_auth', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签认证状态', 'user_tag_auth_status', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '未认证', '0', 'user_tag_auth_status', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '已认证', '1', 'user_tag_auth_status', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`, `display_order`) VALUES ('IN', '包含', '2024-03-26 16:17:53', 1, 'SEL_AUTH_STATUS', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`, `display_order`) VALUES ('NOTIN', '不包含', '2024-03-26 16:17:53', 1, 'SEL_AUTH_STATUS', 2);

INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '记录热词触发', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 */10 * * * ?', 'DO_NOTHING', 'FIRST', 'startHotWordTrigger', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '统计客户数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */2 * * ?', 'DO_NOTHING', 'FIRST', 'statCustomerByDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '统计敏感词触发数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */2 * * ?', 'DO_NOTHING', 'FIRST', 'statSensWordByDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '统计敏感行为触发数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */2 * * ?', 'DO_NOTHING', 'FIRST', 'statSensActByDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '统计物料数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */2 * * ?', 'DO_NOTHING', 'FIRST', 'statRadarByDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '统计热词数据', '2023-08-09 18:50:36', '2023-08-09 18:50:52', '管理员', '', 'CRON', '0 59 */2 * * ?', 'DO_NOTHING', 'FIRST', 'statHotWordByDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-08-09 18:50:36', '', 1, 1713718500000, 1713718800000);

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641948, '数据统计', 0, 8, 'statistic', NULL, 1, 0, 'M', '0', '0', '', 'system', '1', 'admin', '2024-04-21 15:24:02', '2024-04-22 12:52:36', 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', 'icon-svg-dataStatistics-active', 'icon-svg-dataStatistics', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641949, '基础统计', 1433699711154641948, 1, 'basic', NULL, 1, 0, 'M', '0', '0', NULL, '#', '1', NULL, '2024-04-21 15:25:44', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641950, '风控统计', 1433699711154641948, 2, 'risk-control', NULL, 1, 0, 'M', '0', '0', NULL, '#', '1', NULL, '2024-04-21 15:26:45', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641951, '员工数据', 1433699711154641949, 1, 'staff', 'modules/statistic/staff/index', 1, 0, 'C', '0', '0', 'statistic:staff:list', '#', '1', 'admin', '2024-04-21 15:29:03', '2024-04-22 15:27:54', 0, '帮助企业全面把握员工表现，优化资源配置，为制定精准销售策略提供有力数据支持，从而推动业务增长与客户关系优化。', '', '员工数据', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/4eaf7556-a451-4c1f-9396-7152e0c3f44a.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641952, '客户数据', 1433699711154641949, 2, 'customer', 'modules/statistic/customer/index', 1, 0, 'C', '0', '0', 'statistic:customer:list', '#', '1', 'admin', '2024-04-21 15:36:53', '2024-04-22 15:28:11', 0, '帮助企业深入洞察客户需求与市场趋势，为精准营销和个性化服务提供数据支持，进而促进业务增长与客户关系优化。', '', '客户数据', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/301c294a-95cd-4614-b90f-d008b21de7cf.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641953, '客户群数据', 1433699711154641949, 3, 'customer-group', 'modules/statistic/customer-group/index', 1, 0, 'C', '0', '0', 'statistic:customerGroup:list', '#', '1', 'admin', '2024-04-21 15:38:52', '2024-04-22 15:28:29', 0, '精准揭示不同客户群体的特征与需求，为个性化营销与资源优化提供数据支撑，推动企业高效增长。', '', '客户群数据', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/41b878c5-5cdd-4497-b0d1-c8c30ed3ee29.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641954, '物料统计', 1433699711154641949, 4, 'radar', 'modules/statistic/radar/index', 1, 0, 'C', '0', '0', 'statistic:radar:list', '#', '1', 'admin', '2024-04-21 15:39:58', '2024-04-22 15:28:54', 0, '实时追踪和管理物料流动情况，确保物料供应与需求之间的平衡，从而优化资源配置，提升运营效率，并为企业决策提供有力支持。', '', '物料统计', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/1dcb8356-eebf-4032-8d7e-5202325aab18.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641955, '热词统计', 1433699711154641949, 5, 'hot-word', 'modules/statistic/hot-word/index', 1, 0, 'C', '0', '0', 'statistic:hotWord:list', '#', '1', 'admin', '2024-04-21 15:41:00', '2024-04-22 15:29:19', 0, '实时捕捉和分析市场与客户关注的热点话题，为企业把握市场动态、优化营销策略以及提升品牌形象提供有力的数据支持。', '', '热词统计', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/6e86a4b0-ba49-43fd-81bc-af2da3d4c5cc.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641956, '敏感词统计', 1433699711154641950, 1, 'sens-word', 'modules/statistic/sens-word/index', 1, 0, 'C', '0', '0', 'statistic:sensWord:list', '#', '1', 'admin', '2024-04-21 15:42:09', '2024-04-22 15:29:30', 0, '帮助企业预防风险、优化销售策略、提升客户满意度，从而实现更好的销售管理和客户关系维护。', '', '敏感词统计', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/47ea5f63-1fd8-4ee4-b5ce-a064c54157c5.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641957, '敏感行为', 1433699711154641950, 2, 'sens-act', 'modules/statistic/sens-act/index', 1, 0, 'C', '0', '0', 'statistic:sensAct:list', '#', '1', 'admin', '2024-04-21 15:43:23', '2024-04-22 15:29:43', 0, '识别风险、处理危机、优化客户服务，并提供市场洞察，帮助企业保持品牌形象和竞争力。', '', '敏感行为', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/da751fcf-deaa-450e-9621-2e93d8a8af03.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641958, '回复超时', 1433699711154641950, 3, 'reply-timeout', 'modules/statistic/reply-timeout/index', 1, 0, 'C', '0', '0', 'statistic:replyTimeout:list', '#', '1', 'admin', '2024-04-21 15:44:16', '2024-04-22 15:29:50', 0, '评估客户服务效率的关键指标，有助于企业及时发现并改进服务流程，提升客户满意度和整体业务表现。', '', '回复超时', 'https://file-1302750106.cos.ap-guangzhou.myqcloud.com/efunds-aimiscrm/scrm/common/3cd09207-10e0-448b-b843-42e790acbf3e.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641959, '导出', 1433699711154641956, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:sensWord:export', '#', '1', 'admin', '2024-04-21 15:44:51', '2024-04-21 15:46:21', 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641960, '导出', 1433699711154641957, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:sensAct:export', '#', '1', NULL, '2024-04-21 15:49:38', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641961, '导出', 1433699711154641958, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:replyTimeout:export', '#', '1', NULL, '2024-04-21 15:50:03', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641962, '导出', 1433699711154641955, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:hotWord:export', '#', '1', NULL, '2024-04-21 15:50:19', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641963, '导出', 1433699711154641954, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:radar:export', '#', '1', NULL, '2024-04-21 15:50:36', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641964, '导出', 1433699711154641953, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:customerGroup:export', '#', '1', NULL, '2024-04-21 15:50:55', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641965, '导出', 1433699711154641952, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:customer:export', '#', '1', NULL, '2024-04-21 15:51:11', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641966, '导出', 1433699711154641951, 1, '', NULL, 1, 0, 'F', '0', '0', 'statistic:staff:export', '#', '1', NULL, '2024-04-21 15:51:27', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641967, '查看详情', 1433699711154641957, 2, '', NULL, 1, 0, 'F', '0', '0', 'statistic:sensAct:data', '#', '1', NULL, '2024-04-21 16:00:58', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641968, '查看详情', 1433699711154641956, 2, '', NULL, 1, 0, 'F', '0', '0', 'statistic:sensWord:data', '#', '1', NULL, '2024-04-21 16:01:17', NULL, 0, '', '', '', 'https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png', '', '#', '#', '', '', '');

INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('账户概况', '账户概况', '持仓产品', NULL, '2024-03-13 15:33:39', NULL, '2024-04-10 16:25:01', 50, 0, 'c_day_ttl_hld_fund', 'TEXT', NULL);

INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`, `display_order`) VALUES ('LIKE', '匹配', '2024-03-25 11:29:43', 1, 'c_day_ttl_hld_fund', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping`(`oper_rel`, `oper_rel_name`, `create_time`, `create_by`, `tag_type_id`, `display_order`)  VALUES ('NOTLIKE', '不匹配', '2024-03-25 11:29:43', 1, 'c_day_ttl_hld_fund', 2);
