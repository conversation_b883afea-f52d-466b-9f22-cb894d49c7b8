INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641969, '详情', 1433699711154641706, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:communityCode:detail', '#', '1', NULL, '2024-07-04 13:06:41', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641970, '导出列表', 1433699711154641706, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:communityCode:downloadList', '#', '1', NULL, '2024-07-4 13:10:20', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642011, '客户详情', 1433699711154641941, 7, '', NULL, 1, 0, 'F', '0', '0', 'modules:cal:details:customer:details:btn', '#', '1', 'admin', '2024-06-05 17:56:01', '2024-07-04 17:57:19', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641985, '新增条件SOP', 1433699711154641826, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:add', '#', '1', NULL, '2024-07-04 21:16:46', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641987, '查看详情', 1433699711154641826, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:details', '#', '1', NULL, '2024-07-04 21:17:26', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641986, '编辑', 1433699711154641826, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:edit', '#', '1', 'admin', '2024-07-04 21:17:02', '2024-05-21 21:17:08', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642001, '停用/启用', 1433699711154641826, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:details:stopstart', '#', '1', NULL, '2024-05-29 11:22:14', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642002, '编辑', 1433699711154641826, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:details:edit', '#', '1', NULL, '2024-05-29 11:22:30', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642003, '删除', 1433699711154641826, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:condition:btn:details:delete', '#', '1', NULL, '2024-05-29 11:22:47', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641988, '新增旅程SOP', 1433699711154641859, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:add', '#', '1', 'admin', '2024-07-04 21:17:58', '2024-05-22 10:03:40', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641990, '查看详情', 1433699711154641859, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:details', '#', '1', 'admin', '2024-07-04 21:19:41', '2024-05-22 10:03:57', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641989, '编辑', 1433699711154641859, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:edit', '#', '1', 'admin', '2024-07-04 21:19:22', '2024-05-22 10:03:47', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642007, '查看客户', 1433699711154641859, 7, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:details:lookcustomer', '#', '1', 'admin', '2024-05-29 11:36:36', '2024-07-04 11:37:30', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642004, '停用/启用', 1433699711154641859, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:details:stopstart', '#', '1', NULL, '2024-07-04 11:23:08', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642005, '编辑', 1433699711154641859, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:details:edit', '#', '1', NULL, '2024-07-04 11:23:24', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642006, '删除', 1433699711154641859, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:journeysop:btn:details:delete', '#', '1', NULL, '2024-05-29 11:23:38', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641991, '新增1V1SOP', 1433699711154641862, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:add', '#', '1', NULL, '2024-07-04 10:02:37', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641993, '查看详情', 1433699711154641862, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:details', '#', '1', NULL, '2024-07-04 10:03:31', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641992, '编辑', 1433699711154641862, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:edit', '#', '1', NULL, '2024-07-04 10:02:53', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641998, '停用/启用', 1433699711154641862, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:details:stopstart', '#', '1', NULL, '2024-07-04 10:37:30', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641999, '编辑', 1433699711154641862, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:details:edit', '#', '1', NULL, '2024-07-04 10:39:34', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642000, '删除', 1433699711154641862, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:oneseparateone:btn:details:delete', '#', '1', NULL, '2024-07-04 10:40:30', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641996, '提醒发送', 1433699711154641709, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:massCustomer:remindUser', '#', '1', NULL, '2024-07-04 00:01:06', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641997, '修改', 1433699711154641709, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:massCustomer:editTask', '#', '1', NULL, '2024-07-04 00:01:47', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642009, '提醒发表', 1433699711154641721, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:sendMoments:btn:details:publish', '#', '1', NULL, '2024-07-04 12:01:36', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642010, '提醒全部成员', 1433699711154641721, 7, '', NULL, 1, 0, 'F', '0', '0', 'modules:sendMoments:details:publishAll', '#', '1', NULL, '2024-07-04 12:01:50', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642008, '导出列表', 1433699711154641819, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:groupToGroup:btn:export', '#', '1', 'admin', '2024-07-04 11:56:49', '2024-07-04 11:56:49', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642014, '新增分组', 1433699711154641824, 20, '', NULL, 1, 0, 'F', '0', '0', 'modules:material:add:group:btn', '#', '1', NULL, '2024-07-04 09:51:07', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642015, '编辑分组', 1433699711154641824, 21, '', NULL, 1, 0, 'F', '0', '0', 'modules:material:edit:group:btn', '#', '1', NULL, '2024-07-04 09:51:23', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642016, '删除分组', 1433699711154641824, 22, '', NULL, 1, 0, 'F', '0', '0', 'modules:material:delete:group:btn', '#', '1', NULL, '2024-07-04 09:51:44', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642017, '新增分组', 1433699711154641724, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:enterpriseScript:add:group:btn', '#', '1', NULL, '2024-07-04 10:29:04', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642018, '编辑分组', 1433699711154641724, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:enterpriseScript:edit:group:btn', '#', '1', NULL, '2024-07-04 10:29:20', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154642019, '删除分组', 1433699711154641724, 6, '', NULL, 1, 0, 'F', '0', '0', 'modules:enterpriseScript:delete:group:btn', '#', '1', NULL, '2024-07-04 10:29:36', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641971, '编辑', 1433699711154641844, 4, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:card:edit', '#', '1', NULL, '2024-07-04 17:38:06', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641975, '删除', 1433699711154641844, 8, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:card:delete', '#', '1', NULL, '2024-07-04 17:41:35', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641972, '统计', 1433699711154641844, 5, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:card:statistics', '#', '1', NULL, '2024-07-04 17:38:24', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641973, '停止', 1433699711154641844, 6, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:card:stop', '#', '1', NULL, '2024-07-04 17:38:40', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641974, '复制链接', 1433699711154641844, 7, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:card:copylink', '#', '1', NULL, '2024-07-04 17:38:53', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES (1433699711154641865, '查看详情', 1433699711154641844, 1, '', NULL, 1, 0, 'F', '0', '0', 'questionnaire:project:info', '#', '1', NULL, '2024-07-04 18:32:46', '2024-07-04 18:32:46', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642029, '批量打标签', 1433699711154641728, 4, '', NULL, 1, 0, 'F', '0', '0', 'tp:customer:label', '#', '1', NULL, '2024-07-04 11:35:56', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642021, '创建', 1433699711154641849, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:journey:create:btn', '#', '1', NULL, '2024-07-04 11:07:52', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642022, '编辑旅程', 1433699711154641849, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:journey:edit:btn', '#', '1', NULL, '2024-07-04 11:08:02', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642023, '删除旅程', 1433699711154641849, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:journey:delete:btn', '#', '1', NULL, '2024-07-04 11:08:15', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642024, '设置客户旅程', 1433699711154641849, 5, '', NULL, 1, 0, 'F', '0', '0', 'modules:journey:setting:action', '#', '1', NULL, '2024-07-04 11:08:39', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641977, '旅程自动化', 1433699711154641849, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:journey:action', '#', '1', 'admin', '2024-07-04 11:57:43', '2024-07-04 11:58:41', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642020, '画像展示', 1433699711154641903, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:businesstag:list:switch', '#', '1', NULL, '2024-07-04 11:03:07', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641976, '同步数据', 1433699711154641782, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:communityManagement:sync', '#', '1', NULL, '2024-07-04 09:53:04', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642025, '客户详情', 1433699711154641846, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:dimission:details:customer:btn', '#', '1', NULL, '2024-07-04 11:25:35', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642026, '社群详情', 1433699711154641846, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:dimission:details:group:btn', '#', '1', NULL, '2024-07-04 11:26:00', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642027, '客户详情', 1433699711154641794, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:IS:details:customer:btn', '#', '1', NULL, '2024-07-04 11:27:52', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642028, '社群详情', 1433699711154641794, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:IS:details:group:btn', '#', '1', NULL, '2024-07-04 11:28:22', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641915, '新增规则', 1433699711154641910, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:add', '#', '1', NULL, '2024-07-04 10:33:59', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641918, '导入规则', 1433699711154641910, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:export', '#', '1', NULL, '2024-07-04 10:38:23', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641916, '废弃', 1433699711154641910, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:stop', '#', '1', 'admin', '2024-07-04 10:35:32', '2024-07-04 16:48:15', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641917, '编辑', 1433699711154641910, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveRules:edit', '#', '1', NULL, '2024-07-04 10:36:04', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641994, '导出', 1433699711154641911, 1, 'modules:sensitiveAlertLog:export', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveAlertLog:export', '#', '1', 'admin', '2024-07-04 09:41:37', '2024-07-04 16:42:15', 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641995, '导出', 1433699711154641912, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:sensitiveBehaviorWarningLog:export', '#', '1', NULL, '2024-07-04 16:43:01', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641919, '保存', 1433699711154641914, 1, '', NULL, 1, 0, 'F', '0', '0', 'module:replyTimeoutSetting:edit', '#', '1', NULL, '2024-07-04 09:50:29', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641981, '新增热词', 1433699711154641907, 1, '', NULL, 1, 0, 'F', '0', '0', 'modules:hotWorkSetting:btn:add', '#', '1', NULL, '2024-07-04 09:01:34', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641982, '导入热词', 1433699711154641907, 2, '', NULL, 1, 0, 'F', '0', '0', 'modules:hotWorkSetting:btn:lead', '#', '1', NULL, '2024-07-04 19:01:52', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641983, '编辑', 1433699711154641907, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:hotWorkSetting:btn:edit', '#', '1', NULL, '2024-07-04 19:02:04', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641984, '删除', 1433699711154641907, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:hotWorkSetting:btn:delete', '#', '1', NULL, '2024-07-04 09:02:15', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641980, '查看详情', 1433699711154641732, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:department:btn:details', '#', '1', NULL, '2024-07-04 09:45:07', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641978, '查看详情', 1433699711154641733, 3, '', NULL, 1, 0, 'F', '0', '0', 'modules:permissionManagement:btn:details', '#', '1', NULL, '2024-07-04 09:29:09', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154641979, '权限', 1433699711154641733, 4, '', NULL, 1, 0, 'F', '0', '0', 'modules:permissionManagement:btn:limitsAuthority', '#', '1', NULL, '2024-07-04 09:29:27', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`, `case_link`, `help_title`, `help_icon`, `help_link`, `icon1`, `icon2`, `corner_mark`, `corner_mark_back_ground`, `corner_mark_color`) VALUES(1433699711154642031, '确认', 1433699711154641735, 1, '', NULL, 1, 0, 'F', '0', '0', 'tp:config:edit', '#', '1', NULL, '2024-07-04 10:28:19', NULL, 0, '', '', '', '', '', '#', '#', '', '', '');

ALTER TABLE `sys_user` ADD COLUMN `domain_account` varchar(64) NULL COMMENT '域账号' AFTER `corp_open_user_id`;

ALTER TABLE `tb_statistic_customer` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_customer_group` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_hot_word` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_radar` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_reply_timeout` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_sens_act` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_sens_word` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_staff` ADD INDEX `idx_statistic_date`(`statistic_date`) USING BTREE;
ALTER TABLE `tb_statistic_customer_detail` ADD INDEX `idx_customer_id`(`statistic_customer_id`) USING BTREE;
