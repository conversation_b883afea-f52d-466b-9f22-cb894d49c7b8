package com.cenker.scrm.common;

import com.google.common.base.Strings;

import java.math.BigDecimal;

/**
 * BigDecimal 工具类
 * <p/>
 * (nvl-可返回null nvl2-不返回null)
 * Created by liman on 16-9-27.
 */
public class BigDecimalUtils {
    /**
     * 如果 value 为空,则返回null,否则返回value
     */
    public static BigDecimal toBigDecimal(String value) {
        return Strings.isNullOrEmpty(value) ? null : new BigDecimal(value);
    }

    /**
     * 如果 value 为空,则返回默认值defaultValue,否则返回value
     */
    public static BigDecimal toDefault(String value, String defaultValue) {
        return new BigDecimal(Strings.isNullOrEmpty(value) ? defaultValue : value);
    }

    /**
     * 如果 string 为空,则返回null,否则返回string
     *
     * @param string 参数
     * @return
     */
    public static BigDecimal nvl(String string) {
        return Strings.isNullOrEmpty(string) ? null : new BigDecimal(string);
    }

    /**
     * 如果 string 为空,则返回null,否则返回默认值value
     *
     * @param string 参数
     * @param value  默认值
     * @return
     */
    public static BigDecimal nvl(String string, String value) {
        return Strings.isNullOrEmpty(string) ? null : new BigDecimal(value);
    }

    /**
     * 如果 string 为空,则返回默认值value,否则返回string
     *
     * @param string 参数
     * @param value  默认值
     * @return
     */
    public static BigDecimal nvl2(String string, String value) {
        return new BigDecimal(Strings.isNullOrEmpty(string) ? value : string);
    }

    /**
     * 转字符串
     *
     * @param value
     * @return
     */
    public static String toNullOrString(BigDecimal value) {
        return value == null ? null : value.toPlainString();
    }

}
