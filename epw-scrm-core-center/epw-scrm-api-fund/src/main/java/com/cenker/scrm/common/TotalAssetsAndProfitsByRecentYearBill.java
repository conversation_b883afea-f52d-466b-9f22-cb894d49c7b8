package com.cenker.scrm.common;

import lombok.Data;

import java.util.List;

@Data
public class TotalAssetsAndProfitsByRecentYearBill {
    /**
     * 近一年月度账单
     */
    private List<AssetsAndProfits> profitList;

    @Data
    public static class AssetsAndProfits{
        /**
         * 资产日期
         */
        private String date;
        /**
         * 累计收益
         */
        private String totalProfit;
        /**
         * 总资产
         */
        private String assets;
        /**
         * 币种代码
         */
        private String currency;
        /**
         * 币种描述
         */
        private String currencyDesc;
    }
}
