package com.cenker.scrm.common;

import lombok.Data;
import org.joda.time.LocalDate;

import java.util.List;

/**
 * Created by hell on 2021/1/16.
 */
@Data
public class ProfitsAndRateDetailsBill {
    /**
     * 账单查询开始日期  扩展字段
     */
    private LocalDate billQueryStartDate;
    /**
     * 账单查询结束日期  扩展字段
     */
    private LocalDate billQueryEndDate;
    /**
     * 客户收益详情列表
     */
    private List<ProfitAndRatDetail> profitAndRateDetails;
    /**
     * 客户区间总收益
     */
    private List<TotalProfitAndRate> totalProfitAndRates;
    /**
     * 客户是否持仓
     */
    private Boolean holdingStatus;
}
