package com.cenker.scrm.common;

/**
 * Created by lij<PERSON> on 2017/9/21.
 */
public enum PortfolioOrderTypeEnum {
    /**
     * 购买
     */
    PURCHASE("BUY"),
    /**
     * 卖出
     */
    WITHDRAWAL("SELL"),
    /**
     * 调仓
     */
    REBALANCE("REBALANCE");

    private String value;


    PortfolioOrderTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static PortfolioOrderTypeEnum parse(String value) {
        if ("BUY".equals(value)) {
            return PURCHASE;
        } else if ("SELL".equals(value)) {
            return WITHDRAWAL;
        } else if ("REBALANCE".equals(value)) {
            return REBALANCE;
        } else {
            throw new IllegalArgumentException("The unit is invalid.");
        }
    }

//    public static com.efunds.market.common.constants.PortfolioOrderTypeEnum toPortfolioOrderTypeEnum(PortfolioOrderTypeEnum type){
//        for (com.efunds.market.common.constants.PortfolioOrderTypeEnum portfolioOrderTypeEnum :
//                com.efunds.market.common.constants.PortfolioOrderTypeEnum.values()) {
//            if (portfolioOrderTypeEnum.name().equals(type.getValue())) {
//                return portfolioOrderTypeEnum;
//            }
//        }
//        return null;
//    }
}
