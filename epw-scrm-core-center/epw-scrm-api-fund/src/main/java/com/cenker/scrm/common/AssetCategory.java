package com.cenker.scrm.common;

import lombok.Data;

import java.util.List;

/**
 * Created by huangys on 2019/3/11.
 * 分类汇总资产信息
 */
@Data
public class AssetCategory {
    /**
     * 类型
     */
    private String category;
    /**
     * 基金代码列表
     */
    private List<String> fundCodes;
    /**
     * 类型描述
     */
    private String categoryDesc;
    /**
     * 总资产（元，小数点两位）
     */
    private String totalAssets;
    /**
     * 总昨日收益（元，小数点两位）
     */
    private String totalYesterdayIncome;
    /**
     * 总累计收益（元，小数点两位）
     */
    private String totalProfit;
    /**
     * 净值日期
     */
    private String navDate;
    /**
     * 收益日期
     */
    private String incomeDate;
}
