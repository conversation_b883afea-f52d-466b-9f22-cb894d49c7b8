package com.cenker.scrm.common;

import com.google.common.collect.ComparisonChain;

/**
 * 资产走势图数据
 * Created by z<PERSON>gc<PERSON> on 2015/5/20.
 */
public class SimpleAsset implements Comparable<SimpleAsset>{

    /**
     * 资产日期，自然日期
     */
    private String date;
    /**
     * 资产
     */
    private String asset;

    /**
     * 币种
     */
    private String currency = "156";

    private String currencyDesc;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = LocalDates.toYearMonthDayDash(date);;
    }

    public String getAsset() {
        return asset;
    }

    public void setAsset(String asset) {
        this.asset = asset;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
        this.setCurrencyDesc(Currency.getCurrencyDesc(currency));
    }

    public String getCurrencyDesc() {
        return currencyDesc;
    }

    public void setCurrencyDesc(String currencyDesc) {
        this.currencyDesc = currencyDesc;
    }

    @Override
    public int compareTo(SimpleAsset asset) {
        return ComparisonChain.start().compare(asset.getDate(), date).result();
    }
}
