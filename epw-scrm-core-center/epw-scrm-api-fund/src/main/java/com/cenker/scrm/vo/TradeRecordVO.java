package com.cenker.scrm.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeRecordVO {
    /**
     * 交易标的名称
     */
    private String targetName;
    /**
     * 创建时间
     */
    private String createdTime;
    /**
     * 业务名称
     */
    private String businName;
    /**
     * 交易渠道
     */
    private String channel;
    /**
     * 申请份额
     */
    private String shareAmount;
    /**
     * 申请金额
     */
    private String amount;
    /**
     * 处理状态
     */
    private String status;


}