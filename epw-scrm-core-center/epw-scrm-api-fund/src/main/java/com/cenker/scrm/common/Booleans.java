package com.cenker.scrm.common;

import com.google.common.base.Strings;

/**
 * <AUTHOR>
 * @date 2015/9/11
 */
public class Booleans {

    public static String toString(boolean eWalletFlag) {
        return eWalletFlag ? Constants.TRUE : Constants.FALSE;
    }

    public static Boolean fromString(String b) {
        return Constants.TRUE.equals(b);
    }

    public static Boolean fromStringOrEmpty(String b) {
        return Strings.isNullOrEmpty(b) ? null : Constants.TRUE.equals(b);
    }
}
