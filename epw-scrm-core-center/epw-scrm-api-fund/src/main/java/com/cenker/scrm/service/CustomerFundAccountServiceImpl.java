package com.cenker.scrm.service;

import com.cenker.scrm.pojo.dto.fund.EnumPeriod;
import com.cenker.scrm.pojo.dto.fund.GetAssetDailyVariationDTO;
import com.cenker.scrm.pojo.dto.fund.GetTotalAssetsDTO;
import com.cenker.scrm.pojo.dto.fund.GetTradeRecordDTO;
import com.efunds.market.usp.ClientService;
import com.efunds.market.usp.constants.ProductTypeEnum;
import com.efunds.market.usp.entity.TradeRecord;
import com.efunds.market.usp.qc.iface.ITradeRecordService;
import com.efunds.market.usp.qc.vo.request.TradeRecordRequest;
import com.efunds.market.usp.qc.vo.response.TradeRecordResponse;
import com.efunds.market.usp.uic.iface.ICustomerAssetService;
import com.efunds.market.usp.uic.iface.ICustomerService;
import com.efunds.market.usp.uic.vo.request.Customer4ElementsRequest;
import com.efunds.market.usp.uic.vo.request.CustomerLatestTotalDetailAssetRequest;
import com.efunds.market.usp.uic.vo.request.ListProfitOfTotalAssetsRequest;
import com.efunds.market.usp.uic.vo.request.ListTotalAssetsRequest;
import com.efunds.market.usp.uic.vo.response.Customer4ElementsResponse;
import com.efunds.market.usp.uic.vo.response.CustomerLatestTotalDetailAssetResponse;
import com.efunds.market.usp.uic.vo.response.ListProfitOfTotalAssetsResponse;
import com.efunds.market.usp.uic.vo.response.ListTotalAssetsResponse;
import com.efunds.market.usp.vo.UspData;
import com.efunds.market.web.exception.LogicException;
import com.cenker.scrm.common.*;
import com.cenker.scrm.dto.*;
import com.cenker.scrm.vo.AssetSummaryVO;
import com.cenker.scrm.vo.GetTotalAssetsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CustomerFundAccountServiceImpl  extends ClientService implements CustomerFundAccountService {

    @Resource
    private ICustomerService customerService;
    @Resource
    private ICustomerAssetService iCustomerAssetService;
    @Resource
    private ITradeRecordService uspTradeRecordService;

    /**
     *  收益趋势查询
     * @param dto
     * @return
     */
    @Override
    public List<SimpleProfit> getListProfitOfTotalAssets(GetAssetDailyVariationDTO dto) {
        try {
            Customer4ElementsResponse getCustomer4Elements= getCustomer4Elements(dto.getCustNo());
            log.info("【收益趋势查询】调用getByCustNo接口获取客户资料信息成功");
            ListProfitOfTotalAssetsRequest uspReq = new ListProfitOfTotalAssetsRequest();
            uspReq.setCertID(getCustomer4Elements.getCertID());
            uspReq.setCertType(getCustomer4Elements.getCertType());
            uspReq.setCustomerName(getCustomer4Elements.getCustomerName());
            uspReq.setCustomerType(getCustomer4Elements.getCustomerType());
            uspReq.setPeriod(EnumPeriod.toAssetProfitsPeriod(dto.getPeriod()));
            ProductType pt = ProductType.NOT_CONTAIN_TAX_DEFER;
            uspReq.setProductType(ProductType.toProductTypeEnum(pt));
//        if (currency != null && !currency.equals("")) {
//            uspReq.setCurrency(currency);
//        }

            String uspResStr = iCustomerAssetService.listProfitOfTotalAssets(toRequestJson(uspReq));
            log.info("【收益趋势查询】调用listProfitOfTotalAssets接口获取资产收益趋势成功");
            UspData uspData = parseResponseJson(uspResStr);
            ListProfitOfTotalAssetsResponse uspRes = parseResponseJson(uspResStr, ListProfitOfTotalAssetsResponse.class);

            //一个月汇总资产列表对象（总汇资产收益走势图）对象转换
            List<SimpleProfit> simpleAssetList = AssetConvert.convertToSimpleProfit(uspRes.getData(), uspRes.getCurrency());
            return simpleAssetList;
        } catch (Exception e) {
            log.error("获取资产收益趋势异常", e);
            throw e;
        }
    }
    /**
     * 资产趋势图
     */
    @Override
    public List<SimpleAsset> getAssetDailyVariation(GetAssetDailyVariationDTO dto) {
        try{
            Customer4ElementsResponse getCustomer4Elements= getCustomer4Elements(dto.getCustNo());
            log.info("【资产趋势查询】调用getByCustNo接口获取客户资料信息成功");
            ListTotalAssetsRequest uspReq = new ListTotalAssetsRequest();
            uspReq.setCertID(getCustomer4Elements.getCertID());
            uspReq.setCertType(getCustomer4Elements.getCertType());
            uspReq.setCustomerName(getCustomer4Elements.getCustomerName());
            uspReq.setCustomerType(getCustomer4Elements.getCustomerType());
            uspReq.setPeriod(EnumPeriod.toAssetProfitsPeriod(dto.getPeriod()));
            ProductType pt = ProductType.NOT_CONTAIN_TAX_DEFER;
            uspReq.setProductType(ProductType.toProductTypeEnum(pt));
    //        if (currency != null && !currency.equals("")) {
    //            uspReq.setCurrency(currency);
    //        }
            String uspResStr = iCustomerAssetService.listTotalAssets(toRequestJson(uspReq));
            log.info("【资产趋势查询】调用listTotalAssets接口获取资产趋势成功");
            UspData res=parseResponseJson(uspResStr);
            if(res.isSuccess()){
                ListTotalAssetsResponse uspRes =parseResponseJson(uspResStr,ListTotalAssetsResponse.class);
                //一个月汇总资产列表对象（总汇资产走势图）对象转换
                List<SimpleAsset> simpleAssetList = AssetConvert.convertToSimpleAsset(uspRes.getData(), uspRes.getCurrency());
                log.info("【资产趋势查询】返回sucess，转换资产趋势成功");
                return simpleAssetList;
            }

        }catch (LogicException ex){
            log.error("获取资产趋势异常, 异常信息：{}",ex);
        }
        return new ArrayList<>();
    }

    @Override
    public GetTotalAssetsVO getTotalAssets(GetTotalAssetsDTO dto) {
        AssetSummaryVO assetSummaryVO=getLatestTotalAsset(dto);
        return new GetTotalAssetsVO(assetSummaryVO.getTotalAssets());
    }

    /**
     * 获取总资产
     * @param dto
     * @return
     */
    @Override
    public AssetSummaryVO getLatestTotalAsset(GetTotalAssetsDTO dto) {
        try {

            Customer4ElementsResponse customer4Elements=getCustomer4Elements(dto.getCustNo());
            log.info("【获取总资产】调用getByCustNo接口获取客户资料信息成功");
            CustomerLatestTotalDetailAssetRequest uspReq = new CustomerLatestTotalDetailAssetRequest();
            uspReq.setCustomerName(customer4Elements.getCustomerName());
            uspReq.setCustomerType(customer4Elements.getCustomerType());
            uspReq.setCertID(customer4Elements.getCertID());
            uspReq.setCertType(customer4Elements.getCertType());
            uspReq.setProductCategory(null);
            uspReq.setProductType(ProductType.toProductTypeEnum(ProductType.NOT_CONTAIN_TAX_DEFER));
            String retJson = iCustomerAssetService.getLatestTotalAsset(toRequestJson(uspReq));
            log.info("【获取总资产】调用getLatestTotalAsset接口获取资产总览成功");
            CustomerLatestTotalDetailAssetResponse response=parseResponseJson(retJson,CustomerLatestTotalDetailAssetResponse.class);
            AssetSummary assetSummary= AssetConvert.convertToAssetSummary(response);
            return new AssetSummaryVO(assetSummary.getAssetDetails(),assetSummary.getMoneyFundAsset(),assetSummary.getTotalAssets());
        }catch (Exception e) {
            log.error("获取总资产异常，异常信息：{}", e);
            throw e;
        }
    }
    /**
     * 获取交易记录
     */
    @Override
    public PageBuilder<TransactionRecord> getTradeRecord(GetTradeRecordDTO dto) {

        try {
            Customer4ElementsResponse customer4Elements=getCustomer4Elements(dto.getCustNo());
            log.info("【获取交易记录】调用getByCustNo接口获取客户资料信息成功");
            TradeRecordRequest uspReq = new TradeRecordRequest();
            uspReq.setCustomerName(customer4Elements.getCustomerName());
            uspReq.setCustomerType(customer4Elements.getCustomerType());
            uspReq.setCertID(customer4Elements.getCertID());
            uspReq.setCertType(customer4Elements.getCertType());
            uspReq.setFundCode("");
            uspReq.setBusinType("");
            uspReq.setStartDate(dto.getStartDate().replace("-", ""));
            uspReq.setEndDate(dto.getEndDate().replace("-", ""));
            // uspReq.setAssetType(criteria.getAssetType());
            uspReq.setChannelCode("");
            uspReq.setStatus("");
            uspReq.setProductType(ProductTypeEnum.NOT_CONTAIN_TAX_DEFER);
            // 确定分页参数，如果前端未提供，则使用默认值
            int pageNum = (dto.getPageNum() != null) ? dto.getPageNum() : 1; // 默认为第一页
            int pageSize = (dto.getPageSize() != null) ? dto.getPageSize() : 20; // 默认每页10条记录
            uspReq.setStartNum(String.valueOf((pageNum - 1) * pageSize + 1));
            uspReq.setNumCount(String.valueOf(pageSize));
            //排序
            uspReq.setOrderBy("OrderSetupdate");
            uspReq.setOrderAscOrDesc("Desc");

            String uspResStr = uspTradeRecordService.queryList(toRequestJson(uspReq));
            log.info("【获取交易记录】调用uspTradeRecordService.queryList接口获取交易记录成功");
            //数据解析
            UspData uspData = parseResponseJson(uspResStr);
            TradeRecordResponse uspRes = parseResponseJson(uspResStr, TradeRecordResponse.class);
            List<TransactionRecord> items = new ArrayList<TransactionRecord>();
            for (TradeRecord order : uspRes.getList()) {
                if(!"FCTS".equals(order.getSysCode())) {
                    items.add(TradeRecordDetailConverter.convertTransactionRecord(order));
                }
            }
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
            return new PageBuilder<TransactionRecord>().content(items)
                    .pageable(pageable)
                    .total(uspRes.getTotalNum() + "");
        } catch (Exception e) {
            log.error("【获取交易记录】异常，异常信息：{}", e);
            throw e;
        }
    }

    @Override
    public Customer4ElementsResponse getCustomer4Elements(String custNo) {
        Customer4ElementsRequest uspReq=new Customer4ElementsRequest();
        uspReq.setCustNo(custNo);
        String reqJson=toRequestJson(uspReq);
        String resultJson= customerService.getByCustNo(reqJson);
        log.info("【获取客户资料】调用getByCustNo接口获取客户资料信息成功");
        Customer4ElementsResponse response=parseResponseJson(resultJson,Customer4ElementsResponse.class);
        return response;
    }
}