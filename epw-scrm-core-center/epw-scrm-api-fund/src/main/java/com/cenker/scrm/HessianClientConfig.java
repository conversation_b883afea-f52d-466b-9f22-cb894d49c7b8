package com.cenker.scrm;

import com.cenker.scrm.common.MeasuringHessianProxyFactoryBean;
import com.efunds.market.usp.qc.iface.ITradeRecordService;
import com.efunds.market.usp.uic.iface.ICustomerAssetService;
import com.efunds.market.usp.uic.iface.ICustomerService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class HessianClientConfig {

    @Value("${usp.service.customer}")
    private String customerUrl;

    @Value("${usp.service.fund}")
    private String queryServiceUrl;

    @Bean
    public MeasuringHessianProxyFactoryBean customerService() throws Exception {
        MeasuringHessianProxyFactoryBean factory = new MeasuringHessianProxyFactoryBean();
        factory.setServiceUrl(customerUrl + "/customerService");
        factory.setServiceInterface(ICustomerService.class);
        return factory;
    }

    @Bean
    public MeasuringHessianProxyFactoryBean customerAssetService() throws Exception {
        MeasuringHessianProxyFactoryBean factory = new MeasuringHessianProxyFactoryBean();
        factory.setServiceUrl(customerUrl + "/customerAssetService");
        factory.setServiceInterface(ICustomerAssetService.class);
        return factory;
    }

    @Bean
    public MeasuringHessianProxyFactoryBean iTradeRecordService() {
        MeasuringHessianProxyFactoryBean bean = new MeasuringHessianProxyFactoryBean();
        bean.setServiceUrl(queryServiceUrl + "/tradeRecordService");
        bean.setServiceInterface(ITradeRecordService.class);
        return bean;
    }

}
