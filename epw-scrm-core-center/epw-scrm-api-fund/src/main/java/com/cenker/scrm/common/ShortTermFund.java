package com.cenker.scrm.common;

import org.joda.time.LocalDate;

/**
 * 理财基金到期日类
 * Created by zhengcb on 2015/5/28.
 */
public class ShortTermFund {

    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金简称
     */
    private String fundName;
    /**
     * 份额类别
     */
    private String shareType;
    /**
     * 交易渠道(销售商转义+交易经办人转义)
     */
    private String salesChannel;
    /**
     * 份额
     */
    private String shareAmount;
    /**
     * 未付收益
     */
    private String accruedIncome;
    /**
     * 份额注册日期
     */
    private LocalDate registrationDate;
    /**
     * 付款账户
     */
    private String settlementAccount;
    /**
     * 最新到期日
     */
    private LocalDate maturityDate;


    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getSalesChannel() {
        return salesChannel;
    }

    public void setSalesChannel(String salesChannel) {
        this.salesChannel = salesChannel;
    }

    public String getShareAmount() {
        return shareAmount;
    }

    public void setShareAmount(String shareAmount) {
        this.shareAmount = shareAmount;
    }

    public String getAccruedIncome() {
        return accruedIncome;
    }

    public void setAccruedIncome(String accruedIncome) {
        this.accruedIncome = accruedIncome;
    }

    public LocalDate getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(LocalDate registrationDate) {
        this.registrationDate = registrationDate;
    }

    public String getSettlementAccount() {
        return settlementAccount;
    }

    public void setSettlementAccount(String settlementAccount) {
        this.settlementAccount = settlementAccount;
    }

    public LocalDate getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(LocalDate maturityDate) {
        this.maturityDate = maturityDate;
    }
}
