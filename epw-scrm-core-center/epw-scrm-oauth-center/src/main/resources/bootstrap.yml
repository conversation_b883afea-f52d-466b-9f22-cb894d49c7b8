# 环境配置
server:
  # 服务器的HTTP端口
  port: 9563
  undertow:
    no-request-timeout: -1
    buffer-size: 1024
    direct-buffers: true
    max-http-post-size: -1B

app:
  id: ${APOLLO_APP_ID:scrm_efunds_dev}
apollo:
  meta: ${APOLLO_META:http://cenker-apollo-config.cenker.cn}
  bootstrap:
    eagerLoad:
      enabled: true
    enabled: true
    namespaces: epw-scrm-application,epw-scrm-datasource

spring:
  application:
    name: epw-scrm-oauth-center
  profiles:
    active: ${PROFILE_ACTIVE:dev}
  main:
    # 允许存在多个Feign调用相同Service的接口
    allow-bean-definition-overriding: true
    # 允许循环依赖 解决分页插件问题
    allow-circular-references: true

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.cenker.scrm.pojo
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: none

