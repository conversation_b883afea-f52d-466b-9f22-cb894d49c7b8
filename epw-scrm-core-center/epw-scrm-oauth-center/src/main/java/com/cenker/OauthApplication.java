package com.cenker;

import com.cenker.scrm.constants.SpringBootConstant;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @Date 2021/8/13
 * @Description 认证授权服务
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableApolloConfig
@EnableEurekaClient
@MapperScan(SpringBootConstant.MAPPER_SCAN)
@EnableFeignClients
@ComponentScan(value = {SpringBootConstant.OSS_SCAN, SpringBootConstant.COMPONENT_SCAN})
public class OauthApplication {
    public static void main(String[] args) {
        SpringApplication.run(OauthApplication.class,args);
    }

}
