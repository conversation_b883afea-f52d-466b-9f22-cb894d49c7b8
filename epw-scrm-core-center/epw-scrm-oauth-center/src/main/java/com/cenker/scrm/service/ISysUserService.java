package com.cenker.scrm.service;


import com.cenker.scrm.pojo.entity.system.SysUser;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService {

    /**
     * 校验用户是否有效
     * @param userName
     * @return
     */
    int checkUserIsValidByName(String userName);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
     SysUser selectUserByUserName(String userName);

    /**
     * 通过域账号查询用户
     *
     * @param domainAccount 域账号
     * @return 用户对象信息
     */
    SysUser selectUserByDomainAccount(String domainAccount);



    SysUser selectUserByCorpInfo(String corpId, String corpUserId);

    /**
     * 校验用户是否有效
     *
     * @param corpId
     * @param corpUserId
     * @return
     */
     int checkUserIsValid(String corpId, String corpUserId);


    SysUser selectUserByCorpInfoV2(String corpId, String userId);

    /**
     * 根据userId查询是否通讯录企业管理员
     */
    Integer checkUserIsCorpAdmin(String corpUserId, String corpId);

    /**
     * 是否安装第三方应用
     * @param corpId
     */
    void checkCorpProviderInstall(String corpId);

    SysUser createSysUser(String corpId, String userId);

    /**
     * 查询企业配置id
     * @param corpUserId 明文员工id
     * @param corpId 企业id
     * @return 配置id
     */
    Long selectTbWxConfigId(String corpUserId, String corpId);
}
