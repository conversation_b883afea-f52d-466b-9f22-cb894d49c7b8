package com.cenker.scrm.pojo;

import com.cenker.scrm.pojo.dto.WechatUser;
import com.cenker.scrm.pojo.entity.system.SysDept;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategory;
import com.cenker.scrm.pojo.entity.wechat.TbWxDepartment;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    /**
     * 部门下的成员
     */
    private List<WechatUser> userList = new ArrayList<>();

    public TreeSelect() {

    }

    public TreeSelect(SysDept dept) {
        this.id = Long.valueOf(dept.getDeptId());
        this.label = dept.getDeptName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(TbWxDepartment dept) {
        this.id = Long.valueOf(dept.getId());
        this.label = dept.getName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        this.userList = dept.getUserList();
    }

    public TreeSelect(SysMenu menu) {
        this.id = Long.valueOf(menu.getMenuId());
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(TbWxCategory category) {
        this.id = Long.valueOf(category.getId());
        this.label = category.getName();
        this.children = category.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<TreeSelect> getChildren() {
        return children;
    }

    public void setChildren(List<TreeSelect> children) {
        this.children = children;
    }

    public List<WechatUser> getUserList() {
        return userList;
    }

    public TreeSelect setUserList(List<WechatUser> userList) {
        this.userList = userList;
        return this;
    }
}
