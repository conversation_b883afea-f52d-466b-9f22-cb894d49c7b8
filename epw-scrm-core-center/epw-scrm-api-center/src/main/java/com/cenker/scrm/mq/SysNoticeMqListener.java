package com.cenker.scrm.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.NumberConstants;
import com.cenker.scrm.constants.RabbitMqQueuesConstant;
import com.cenker.scrm.enums.SysNoticeEnum;
import com.cenker.scrm.pojo.dto.system.SysNoticeDto;
import com.cenker.scrm.pojo.dto.system.SysNoticeMsgDto;
import com.cenker.scrm.pojo.entity.system.SysNotice;
import com.cenker.scrm.pojo.entity.system.SysNoticeSendRecords;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.service.ISysNoticeSendRecordsService;
import com.cenker.scrm.service.ISysNoticeService;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.service.IWorkCorpCpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 应用消息-MQ消息监听器
 * <AUTHOR>
 * @title AgentMessageMqListener
 * @date 2024/12/4 16:20
 * @description TODO
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SysNoticeMqListener {

    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    private final ISysNoticeSendRecordsService sysNoticeSendRecordsService;
    private final ISysNoticeService sysNoticeService;
    private final ISysUserService sysUserService;

    /**
     * 异步处理应用消息发送
     */
    @RabbitListener(queues = RabbitMqQueuesConstant.SYS_MSG_NOTICE, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void handlerSendSysMessage(Message message) {
        log.info("【MQ消息】接收到系统消息-待发送应用消息数据:{}", message);
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【MQ消息】接收到系统消息-待发送应用消息数据,:{}", messageBody);
        SysNoticeDto sysNoticeMqDto = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), SysNoticeDto.class);

        SysNotice sysNotice = sysNoticeMqDto.getSysNotice();
        handlerAgentMsgSend(sysNotice, sysNoticeMqDto, sysNotice.getNoticeContent());
    }

    private void handlerAgentMsgSend(SysNotice sysNotice, SysNoticeDto sysNoticeMqDto, String agentMsgContent) {
        String corpId = sysNotice.getCorpId();
        WxCpServiceImpl wxCpService = workCorpCpService.getWxCpAgentServiceByCorpId(corpId);

        Map<String, SysNoticeSendRecords> qwWorkNoticeMap = sysNoticeMqDto.getSendQwWorkNoticeMap();
        if(sysNoticeMqDto.getCorpUserIds().size() == NumberConstants.INT_ONE){
            String toUser = sysNoticeMqDto.getCorpUserIds().get(0);
            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setAgentId(wxCpMessage.getAgentId());
            String content = "【" + sysNotice.getNoticeTitle() + "】\n" + agentMsgContent;
            wxCpMessage.setContent(content);
            wxCpMessage.setToUser(toUser);
            wxCpMessage.setMsgType(WxConsts.MassMsgType.TEXT);
            try {
                WxCpMessageSendResult wxCpMessageSendResult = wxCpService.getMessageService().send(wxCpMessage);
                String msgId = wxCpMessageSendResult.getMsgId();

                qwWorkNoticeMap.forEach((corpUserId, sysNoticeSendRecords) -> {
                    if (sysNoticeMqDto.getCorpUserIds().contains(corpUserId)) {
                        sysNoticeSendRecords.setMsgId(msgId);
                    }
                });
                log.info("系统消息-发送应用消息成功，消息通知Id：{}, 通知员工：{}, 消息ID：{}", sysNotice.getNoticeId(), toUser, msgId);
            } catch (WxErrorException e) {
                log.error("系统消息-发送应用消息失败，消息通知Id：{}, 通知员工：{}, 失败原因：{}", sysNotice.getNoticeId(), toUser, e.getMessage());
            }
        }else{
            List<List<String>> corpUserIdPages = ListUtil.partition(sysNoticeMqDto.getCorpUserIds(), 1000);
            corpUserIdPages.forEach(corpUserIds -> {
                String toUser = String.join("|", corpUserIds);
                WxCpMessage wxCpMessage = new WxCpMessage();
                wxCpMessage.setAgentId(wxCpMessage.getAgentId());
                String content = "【" + sysNotice.getNoticeTitle() + "】\n" + agentMsgContent;
                wxCpMessage.setContent(content);
                wxCpMessage.setToUser(toUser);
                wxCpMessage.setMsgType(WxConsts.MassMsgType.TEXT);
                try {
                    WxCpMessageSendResult wxCpMessageSendResult = wxCpService.getMessageService().send(wxCpMessage);
                    String msgId = wxCpMessageSendResult.getMsgId();

                    qwWorkNoticeMap.forEach((corpUserId, sysNoticeSendRecords) -> {
                        if(corpUserIds.contains(corpUserId)){
                            sysNoticeSendRecords.setMsgId(msgId);
                        }
                    });
                    log.info("系统消息-发送应用消息成功，消息通知Id：{}, 通知员工：{}, 消息ID：{}", sysNotice.getNoticeId(), toUser, msgId);
                } catch (WxErrorException e) {
                    log.error("系统消息-发送应用消息失败，消息通知Id：{}, 通知员工：{}, 失败原因：{}", sysNotice.getNoticeId(), toUser, e.getMessage());
                }
            });
        }
        if(CollectionUtil.isNotEmpty(qwWorkNoticeMap)){
            sysNoticeSendRecordsService.saveBatch(qwWorkNoticeMap.values());
        }
    }

    /**
     * 异步处理应用消息撤回
     */
    @RabbitListener(queues = RabbitMqQueuesConstant.SYS_MSG_AGENT_REVOCATION, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void handlerRevocationMessage(Message message) {
        log.info("【MQ消息】接收到待撤回应用消息数据:{}", message);
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【MQ消息】接收到待撤回应用消息数据,:{}", messageBody);
        SysNoticeMsgDto sysNoticeMsgDto = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), SysNoticeMsgDto.class);
        if(CollectionUtil.isNotEmpty(sysNoticeMsgDto.getMsgIds())){
            sysNoticeMsgDto.getMsgIds().forEach(msgId -> {
                log.info("待撤回应用消息发起撤回,msgId:{}", msgId);
                try {
                    workCorpCpService.getWxCpAgentServiceByCorpId(sysNoticeMsgDto.getCorpId()).getMessageService().recall(msgId);
                } catch (WxErrorException e) {
                    log.info("待撤回应用消息发起撤回失败,msgId:{}, 失败原因：{}", msgId, e.getMessage());
                }
            });
        }
    }

    @RabbitListener(queues = RabbitMqQueuesConstant.SYS_MSG_NOTICE_SEND, containerFactory = RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public void handlerSysMessage(Message message) {
        log.info("【MQ消息】接收到系统消息-待发送系统消息数据:{}", message);
        String messageBody = new String(message.getBody(), Charset.defaultCharset());
        log.info("【MQ消息】接收到系统消息-待发送系统消息数据,:{}", messageBody);
        SysNoticeMsgDto sysNoticeMqDto = JSONObject.toJavaObject(JSONObject.parseObject(messageBody), SysNoticeMsgDto.class);
        
        SysNotice sysNotice = new SysNotice();
        sysNotice.setNoticeContent(sysNoticeMqDto.getNoticeContent());
        sysNotice.setNoticeTitle(sysNoticeMqDto.getNoticeTitle());
        sysNotice.setCorpId(sysNoticeMqDto.getCorpId());
        sysNotice.setNoticeUser(sysNoticeMqDto.getNoticeUser());
        sysNotice.setIsNoticeAllUser(NumberConstants.INT_ZERO);
        sysNotice.setNoticeMode(sysNoticeMqDto.getNoticeMode());
        sysNotice.setTimedTask(NumberConstants.INT_ZERO);
        sysNotice.setNoticeType(SysNoticeEnum.NOTICE_TYPE_MSG.getCode());
        sysNotice.setStatus(SysNoticeEnum.NOTICE_STATUS_PUBLISH.getCode());
        sysNotice.setCreateBy(Constants.DEFAULT_USER);
        sysNotice.setCreateTime(new Date());
        sysNotice.setBusinessId(sysNoticeMqDto.getBusinessId());
        sysNotice.setBusinessType(sysNoticeMqDto.getBusinessType());
        sysNotice.setBusinessOper(sysNoticeMqDto.getBusinessOper());
        sysNotice.setBusinessJson(sysNoticeMqDto.getBusinessJson());
        sysNoticeService.save(sysNotice);

        // 获取全部系统账号
        List<SysUser> sysUsers = sysUserService.list(new LambdaQueryWrapper<>(SysUser.class).eq(SysUser::getDelFlag, NumberConstants.INT_ZERO));

        SysNoticeDto newSysNoticeMqDto = sysNoticeService.handlerNoticeSendRecords(sysNotice, sysUsers);
        if (CollectionUtil.isNotEmpty(newSysNoticeMqDto.getSendQwWorkNoticeMap())) {
            handlerAgentMsgSend(sysNotice, newSysNoticeMqDto, sysNoticeMqDto.getAgentMsgContent());
        }

        if(CollectionUtil.isNotEmpty(newSysNoticeMqDto.getSendAdminNoticeList())){
            sysNoticeSendRecordsService.saveBatch(newSysNoticeMqDto.getSendAdminNoticeList());
        }
    }
}
