package com.cenker.scrm.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cenker.scrm.biz.FileService;
import com.cenker.scrm.biz.manager.WxMqSendMessageManager;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.efunds.WechatEngineClient;
import com.cenker.scrm.efunds.config.WechatEngineProperties;
import com.cenker.scrm.efunds.model.WxMpTokenGetReq;
import com.cenker.scrm.efunds.model.WxMpTokenGetRsp;
import com.cenker.scrm.enums.AppTypeEnum;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.MaterialInfoAcquisitionMethod;
import com.cenker.scrm.enums.MaterialMatchMethod;
import com.cenker.scrm.pojo.dto.system.BuMaterialJobRunDto;
import com.cenker.scrm.pojo.entity.system.BuMaterialJob;
import com.cenker.scrm.pojo.entity.system.BuMaterialJobDetail;
import com.cenker.scrm.pojo.entity.system.SysAppConfig;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.service.IBuMaterialJobDetailService;
import com.cenker.scrm.service.IBuMaterialJobService;
import com.cenker.scrm.service.ISysAppConfigService;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.service.radar.ITbWxRadarContentService;
import com.cenker.scrm.service.radar.ITbWxRadarInteractService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.messagebuilder.NewsBuilder;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.mp.api.WxMpFreePublishService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishArticles;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishItem;
import me.chanjar.weixin.mp.bean.freepublish.WxMpFreePublishList;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能物料定时生成任务
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class MaterialGenerateTask {

    public static final int MAX_TITLE_LENGTH = 80;
    public static final int MAX_DIGEST_LENGTH = 120;

    private final IBuMaterialJobService materialJobService;
    private final IBuMaterialJobDetailService buMaterialJobDetailService;
    private final ISysAppConfigService appConfigService;
    private final WechatEngineProperties wechatEngineProperties;
    private final ITbWxRadarInteractService radarInteractService;
    private final ITbWxRadarContentService radarContentService;
    private final FileService fileService;
    private final RedisCache redisCache;
    private final WxMqSendMessageManager mqSendMessageManager;
    private final ISysUserService sysUserService;

    @XxlJob(XxlJobContant.AUTO_GENERATE_MATERIAL)
    public void generateMaterial() {
        BuMaterialJobRunDto params = JSONUtil.toBean(XxlJobHelper.getJobParam(), BuMaterialJobRunDto.class);
        XxlJobHelper.log("【定时制作】智能物料定时生成任务【{}】开始执行!", params.getJobName());
        log.info("【定时制作】智能物料定时生成任务【{}】开始执行!", params.getJobName());
        // 根据任务ID获取任务配置
        BuMaterialJob materialJob = materialJobService.getById(params.getId());
        if (materialJob == null) {
            XxlJobHelper.log("【定时制作】智能物料定时生成任务执行失败，未找到对应任务【{}】", params.getJobName());
            log.info("【定时制作】智能物料定时生成任务执行失败，未找到对应任务【{}】", params.getJobName());
            return;
        }

        // 获取来源应用配置明细
        SysAppConfig appConfig = appConfigService.getById(materialJob.getAppConfigId());
        if (appConfig == null) {
            XxlJobHelper.log("【定时制作】智能物料定时生成任务【{}】执行失败，未找到对应来源应用配置!", params.getJobName());
            log.info("【定时制作】智能物料定时生成任务【{}】执行失败，未找到对应来源应用配置!", params.getJobName());
            return;
        }

        // 从来源获取物料信息
        List<BuMaterialJobDetail> sourceMaterialDetails = getMaterialInfoBySource(appConfig, materialJob);

        // 过滤掉已生成的物料
        List<BuMaterialJobDetail> filteredMaterialDetails = filterMaterial(materialJob.getId(), sourceMaterialDetails);
        if (CollectionUtil.isEmpty(filteredMaterialDetails)) {
            XxlJobHelper.log("【定时制作】智能物料定时生成任务【{}】执行结束，未找到有效的来源物料!", params.getJobName());
            log.info("【定时制作】智能物料定时生成任务【{}】执行结束，未找到有效的来源物料!", params.getJobName());
            return;
        }

        // 生成智能物料
        this.makeMaterial(materialJob, filteredMaterialDetails);

        XxlJobHelper.log("【定时制作】智能物料定时生成任务【{}】执行成功!", params.getJobName());
        log.info("【定时制作】智能物料定时生成任务【{}】执行成功!", params.getJobName());
    }

    /**
     * 根据来源应用配置获取物料信息
     * @param appConfig
     * @param materialJob
     * @return
     */
    private List<BuMaterialJobDetail> getMaterialInfoBySource(SysAppConfig appConfig, BuMaterialJob materialJob) {
        // TODO 根据来源应用配置获取物料信息
        String appTypeUpper = (StrUtil.isNotBlank(materialJob.getAppType())) ? materialJob.getAppType().toUpperCase() : null;
        AppTypeEnum appType = AppTypeEnum.valueOf(appTypeUpper);
        switch (appType) {
            case WX_PUBLIC_ACCOUNT:
                return getArticleInfo(appConfig, materialJob);
            case MATERIAL_LIBRARY:
                // TODO 获取物料库物料信息
                return getMaterialInfo(appConfig, materialJob);
            default:
                break;
        }

        return null;
    }

    /**
     * 过滤掉已生成的物料
     * @param materialJobId
     * @param sourceMaterialDetails
     * @return
     */
    private List<BuMaterialJobDetail> filterMaterial(Long materialJobId, List<BuMaterialJobDetail> sourceMaterialDetails) {
        if (CollectionUtil.isEmpty(sourceMaterialDetails)) {
            return new ArrayList<>();
        }

        List<BuMaterialJobDetail> filteredMaterialDetails = new ArrayList<>();

        List<String> sourceIds = sourceMaterialDetails.stream().map(BuMaterialJobDetail::getSourceId).collect(Collectors.toList());
        List<BuMaterialJobDetail> existMaterialDetails = buMaterialJobDetailService.lambdaQuery()
               .eq(BuMaterialJobDetail::getJobId, materialJobId)
               .in(BuMaterialJobDetail::getSourceId, sourceIds)
               .list();
        log.info("【定时制作】任务JobId【{}】来源物料数量：{}，已存在物料数量：{}", materialJobId, sourceMaterialDetails.size(), existMaterialDetails.size());
        XxlJobHelper.log("【定时制作】任务JobId【{}】来源物料数量：{}，已存在物料数量：{}", materialJobId, sourceMaterialDetails.size(), existMaterialDetails.size());

        for (BuMaterialJobDetail sourceMaterialDetail : sourceMaterialDetails) {
            boolean exist = existMaterialDetails.stream().anyMatch(m -> m.getSourceId().equals(sourceMaterialDetail.getSourceId()));
            if (!exist) {
                filteredMaterialDetails.add(sourceMaterialDetail);
                log.info("【定时制作】任务JobId【{}】新增来源物料SourceId【{}】", materialJobId, sourceMaterialDetail.getSourceId());
                XxlJobHelper.log("【定时制作】任务JobId【{}】新增来源物料SourceId【{}】", materialJobId, sourceMaterialDetail.getSourceId());
            } else {
                log.info("【定时制作】任务JobId【{}】已存在来源物料SourceId【{}】，跳过", materialJobId, sourceMaterialDetail.getSourceId());
                XxlJobHelper.log("【定时制作】任务JobId【{}】已存在来源物料SourceId【{}】，跳过", materialJobId, sourceMaterialDetail.getSourceId());
            }
        }

        return filteredMaterialDetails;
    }


    /**
     * 智能物料生成逻辑
     * 自动读取公众号文章，生成智能物料
     * @param materialJob
     */
    private void makeMaterial(BuMaterialJob materialJob, List<BuMaterialJobDetail> materialDetails) {
        // 一篇文章需要生成的智能物料数量
        Integer materialNumber = materialJob.getMaterialNumber();
        // 计划生成智能物料数量
        int targetMaterialNumber = materialNumber * materialDetails.size();
        log.info("【定时制作】任务【{}】开始生成智能物料，来源物料数量：{}，生成物料数量：{}", materialJob.getJobName(), materialDetails.size(), targetMaterialNumber);
        XxlJobHelper.log("【定时制作】任务【{}】开始生成智能物料，来源物料数量：{}，生成物料数量：{}", materialJob.getJobName(), materialDetails.size(), targetMaterialNumber);

        List<TbWxRadarContent> materialContentList = new ArrayList<>();

        for (int i = 0; i < materialDetails.size(); i++) {
            BuMaterialJobDetail materialDetail = materialDetails.get(i);
             for (int j = 0; j < materialNumber; j++) {
                 boolean isSameName = StrUtil.isNotBlank(materialJob.getNamingRule());
                 String serialNumber = generateSerialNumber(isSameName,materialDetails.size(), materialNumber, i, j);

                 // 构建智能物料信息，并保存到数据库
                 TbWxRadarInteract material = buildMaterialInfo(materialJob, materialDetail.getTitle(), serialNumber);
                 radarInteractService.save(material);

                 // 构建智能物料卡片信息，并保存到数据库
                 TbWxRadarContent content = buildMaterialContent(material.getId(), materialJob, materialDetail);
                 radarContentService.save(content);
                 materialContentList.add(content);

                 // 保存来源物料信息到数据库
                 BuMaterialJobDetail jobDetail = BeanUtil.copyProperties(materialDetail, BuMaterialJobDetail.class);
                 jobDetail.setMaterialId(material.getId());
                 buMaterialJobDetailService.save(jobDetail);

                 log.info("【定时制作】任务【{}】生成智能物料：{}", material.getTitle());
                 XxlJobHelper.log("【定时制作】任务【{}】生成智能物料：{}", material.getTitle());
             }
        }

        sendMsg(materialJob, materialContentList);
        log.info("【定时制作】任务【{}】生成智能物料成功！", materialJob.getJobName());
        XxlJobHelper.log("【定时制作】任务【{}】生成智能物料成功！", materialJob.getJobName());
    }

    /**
     * 获取智能物料标题序号
     * 规则：
     * 1、标题重名自动在末尾加上序号，示例：“物料标题 1”、“物料标题2”；
     * 2、在统一命名的情况下，若匹配到两篇及以上的文章，并且生成次数达到 2 次及以上，物料标题的命名格式应为：“物料标题1.1”、“物料标题1.2”、……
     * @param isSameName 是否为统一命名
     * @param sourceSize 来源物料数量
     * @param materialNumber 生成次数
     * @param i 当前来源物料序号
     * @param j 当前生成物料序号
     * @return
     */
    private static String generateSerialNumber(boolean isSameName, int sourceSize, Integer materialNumber, int i, int j) {
        if (isSameName && sourceSize > 1 && materialNumber > 1) {
            // 统一命名且来源物料数量大于1且生成次数大于1
            return String.format("_%d.%d", i + 1, j + 1);
        } else if (j > 0) {
            // 当前生成物料序号大于0，需要加序号
            return String.format("_%d", j + 1);
        } else {
            // 默认情况下不需要加序号
            return "";
        }
    }

    /**
     * 构建智能物料信息
     * @param sourceMaterialTitle
     * @param materialJob
     * @return
     */
    private TbWxRadarInteract buildMaterialInfo(BuMaterialJob materialJob, String sourceMaterialTitle, String serialNumber) {
        String materialTitle = getMaterialTitle(sourceMaterialTitle, materialJob.getNamingRule(), serialNumber);

        TbWxRadarInteract material = new TbWxRadarInteract();
        material.setTitle(materialTitle);
        material.setCorpId(materialJob.getCorpId());
        material.setScope(materialJob.getMaterialScope());
        material.setType(materialJob.getMaterialType());
        material.setNamingRule(materialJob.getNamingRule());
        material.setCategoryId(materialJob.getMaterialCategoryId());
        material.setDeptId(materialJob.getDeptId());
        material.setCreateBy(materialJob.getCreateBy());
        material.setCreateTime(new Date());
        // 默认开启行为通知
        material.setBehaviorInform(StatusConstants.OPEN_FLAG);
        // 默认开启动态通知
        material.setDynamicInform(StatusConstants.OPEN_FLAG);
        // 对接公众号生成的物料，不需要判断是否需要审核，直接设置为 使用中
        material.setStatus(ApprovalStatusEnum.EXECUTING.getStatus());
        return material;
    }

    /**
     * 智能物料卡片封面图处理
     * 若封面图超过限制大小，需要进行自动压缩
     * 若封面图为空，则返回null
     *
     * @param cover
     * @return
     */
    private String getMaterialCoverUrl(String cover) {
        if (StrUtil.isBlank(cover)) {
            log.info("【定时制作】智能物料【{}】封面图为空，返回默认封面图", cover);
            XxlJobHelper.log("【定时制作】智能物料【{}】封面图为空，返回默认封面图");
            return redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER);
        }

        // 若封面图超过限制大小，需要进行自动压缩
        List<String> coverUrlList = fileService.compressImage(Arrays.asList(cover));
        if (CollectionUtil.isEmpty(coverUrlList)) {
            log.info("【定时制作】智能物料【{}】封面图压缩失败，返回默认封面图", cover);
            XxlJobHelper.log("【定时制作】智能物料【{}】封面图压缩失败，返回默认封面图",cover);
            return redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER);
        }

        return coverUrlList.get(0);
    }

    /**
     * 智能物料标题处理
     * 1、若有启用命名规则，则直接取任务配置中的命名规则去自动生成标题：
     * 1.1 命名规则变量：固定字符FIXED_CHAR、分隔符SEPARATOR、自定义字符CUSTOM_CHAR、时间戳TIME_STAMP、自定义选项CUSTOM_OPTION
     * 1.2 命名规则示例：[{"variable":"CUSTOM_CHAR","value":"CUSTOM","sort":0},{"variable":"FIXED_CHAR","value":"FIX","sort":1},{"variable":"TIME_STAMP","value":"20250321","sort":2},{"variable":"SEPARATOR","value":"_","sort":3},{"variable":"SEPARATOR","value":"—","sort":4},{"variable":"CUSTOM_OPTION","options":"选项1,选项2","value":"选项1","sort":5},{"variable":"TIME_STAMP","value":"20250408","sort":6}]
     * 1.3 按命名规则拼装标题：sort越小越靠前，若sort相同，则按照变量名排序; 时间戳TIME_STAMP变量，取值取当天的日期，如20250321；其他变量直接取value值
     * 1.4 标题重名自动在末尾加上序号，示例：“物料标题 1”、“物料标题2”；在统一命名的情况下，若匹配到两篇及以上的文章，并且生成次数达到 2 次及以上，物料标题的命名格式应为：“物料标题1.1”、“物料标题1.2”、……
     *
     * 2、若命名规则未开启，则无需填写物料标题，直接采用公众号文章标题；
     * 3、若物料标题重名自动在末尾加上序号（当 index >= 1，才需要加序号）
     *
     * @param sourceTitle 公众号文章标题
     * @param namingRule 命名规则
     * @param serialNumber 序号
     * @return
     */
    private static String getMaterialTitle(String sourceTitle, String namingRule, String serialNumber) {
        if (StrUtil.isBlank(namingRule)) {
            // 若命名规则未开启，则无需填写物料标题，直接采用公众号文章标题
            return sourceTitle + serialNumber;
        }

        try {
            List<Map<String, Object>> ruleList = JSON.parseObject(namingRule, new TypeReference<List<Map<String, Object>>>() {});
            ruleList.sort((a, b) -> {
                int sortA = (int) a.get("sort");
                int sortB = (int) b.get("sort");
                if (sortA != sortB) {
                    return sortA - sortB;
                }
                return ((String) a.get("variable")).compareTo((String) b.get("variable"));
            });

            String today = DateUtil.format(new Date(), "yyyyMMdd");
            StringBuilder titleBuilder = new StringBuilder();
            for (Map<String, Object> rule : ruleList) {
                String variable = (String) rule.get("variable");
                switch (variable) {
                    case "TIME_STAMP":
                        titleBuilder.append(today);
                        break;
                    case "SEPARATOR":
                    case "FIXED_CHAR":
                    case "CUSTOM_CHAR":
                    case "CUSTOM_OPTION":
                        titleBuilder.append(rule.get("value"));
                        break;
                }
            }

            // 若命名规则中不包含时间戳变量，则必须在标题末尾加上当天日期
            boolean containsTimeStamp = ruleList.stream().anyMatch(r -> "TIME_STAMP".equals(r.get("variable")));
            if (!containsTimeStamp) {
                titleBuilder.append(StrUtil.UNDERLINE).append(today);
            }

            // 若标题重名自动在末尾加上序号
            titleBuilder.append(serialNumber);
            return titleBuilder.toString();
        } catch (Exception e) {
            log.error("【定时制作】智能物料【{}】生成标题失败！", sourceTitle, e);
            return sourceTitle + serialNumber;
        }
    }


    /**
     * 拼装智能物料卡片信息
     * 若来源为自动获取，则直接取文章标题、摘要、封面图地址、文章链接等信息；
     * 若来源为手动输入，则取任务配置中的标题、摘要、封面图地址、文章链接等信息；
     *
     * @param radarId
     * @param materialJob
     * @param materialDetail
     * @return
     */
    private TbWxRadarContent buildMaterialContent(String radarId, BuMaterialJob materialJob, BuMaterialJobDetail materialDetail) {
        TbWxRadarContent content = new TbWxRadarContent();
        content.setRadarId(radarId);
        content.setAuthor(materialDetail.getAuthor());
        content.setContent(materialDetail.getContent());
        content.setTips(redisCache.getCacheObject(DefaultConstants.DEFAULT_RADAR_TIP));
        content.setCreateBy(materialJob.getCreateBy());
        content.setCreateTime(new Date());

        // 若来源为自动获取，则直接取文章信息
        if (MaterialInfoAcquisitionMethod.AUTO.name().equalsIgnoreCase(materialJob.getAcquisitionMethod())) {
            String coverUrl = getMaterialCoverUrl(materialDetail.getCover());
            // 若标题长度超过限制，则截取前30个字符，并加上省略号
            String title = StrUtil.maxLength(materialDetail.getTitle(), MAX_TITLE_LENGTH);
            // 若摘要长度超过限制，则截取前120个字符，并加上省略号
            String digest = StrUtil.maxLength(materialDetail.getDigest(), MAX_DIGEST_LENGTH);
            content.setTitle(title);
            content.setCover(coverUrl);
            content.setDigest(digest);
        } else {
            // 若来源为手动输入，则取任务配置中的标题
            JSONObject cardInfo = JSON.parseObject(materialJob.getCustomInfo());
            content.setTitle(cardInfo.getString("title"));
            content.setCover(cardInfo.getString("cover"));
            content.setDigest(getDigest(cardInfo.getString("digest")));
        }

        return content;
    }


    /**
     * 获取公众号当天发布的最新一篇文章的标题、摘要、封面图地址、文章链接等信息
     *
     * 接口地址：POST https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token=ACCESS_TOKEN
     * 入参示例：
     * {
     *     "offset":OFFSET,
     *     "count":COUNT,
     *     "no_content":NO_CONTENT
     * }
     * 返回值示例：
     * {
     *     "total_count":TOTAL_COUNT,
     *     "item_count":ITEM_COUNT,
     *     "item":[
     *         {
     *             "article_id":ARTICLE_ID,
     *             "content": {
     *                 "news_item" : [
     *                     {
     *                         "title":TITLE,
     *                         "author":AUTHOR,
     *                         "digest":DIGEST,
     *                         "content":CONTENT,
     *                         "content_source_url":CONTENT_SOURCE_URL,
     *                         "thumb_media_id":THUMB_MEDIA_ID,
     *                         "show_cover_pic":1,
     *                         "need_open_comment":0,
     *                         "only_fans_can_comment":0,
     *                         "url":URL,
     *                         "is_deleted":false
     *                     }
     *                     //多图文消息会在此处有多篇文章
     *                 ]
     *             },
     *             "update_time": UPDATE_TIME
     *         },
     *         //可能有多个图文消息item结构
     *     ]
     * }
     *
     * @param
     * @return
     */
    private List<BuMaterialJobDetail> getArticleInfo(SysAppConfig appConfig, BuMaterialJob materialJob) {
        XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息", materialJob.getJobName(), appConfig.getAppName());
        log.info("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息", materialJob.getJobName(), appConfig.getAppName());
        String matchMethod = materialJob.getMatchMethod();
        int offset = 0;
        int count = (MaterialMatchMethod.KEYWORD.name().equalsIgnoreCase(matchMethod))? 10 : 1;
        List<BuMaterialJobDetail> materialJobDetails = new ArrayList<>();

        try {
            WxMpService wxMpService = getWxMpService(appConfig);
            WxMpFreePublishService wxMpFreePublishService = wxMpService.getFreePublishService();
            WxMpFreePublishList wxMpFreePublishList =wxMpFreePublishService.getPublicationRecords(offset, count);

            if (Objects.nonNull(wxMpFreePublishList) && CollectionUtil.isNotEmpty(wxMpFreePublishList.getItems())) {
                log.info("【定时制作】任务【{}】获取公众号【{}】文章数量：{}", materialJob.getJobName(), appConfig.getAppName(), wxMpFreePublishList.getItems().size());
                XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】文章数量：{}", materialJob.getJobName(), appConfig.getAppName(), wxMpFreePublishList.getItems().size());
                for (WxMpFreePublishItem item : wxMpFreePublishList.getItems()) {
                    WxMpFreePublishArticles wxMpFreePublishArticles = item.getContent().getNewsItem().get(0);

                    if (MaterialMatchMethod.KEYWORD.name().equalsIgnoreCase(matchMethod)
                            && !StrUtil.contains(wxMpFreePublishArticles.getTitle(), materialJob.getKeyword())) {
                        log.info("【定时制作】任务【{}】获取公众号【{}】文章标题【{}】不包含关键词【{}】，跳过", materialJob.getJobName(), appConfig.getAppName(), wxMpFreePublishArticles.getTitle(), materialJob.getKeyword());
                        XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】文章标题【{}】不包含关键词【{}】，跳过", materialJob.getJobName(), appConfig.getAppName(), wxMpFreePublishArticles.getTitle(), materialJob.getKeyword());
                        continue;
                    }

                    BuMaterialJobDetail materialJobDetail = new BuMaterialJobDetail();
                    materialJobDetail.setJobId(materialJob.getId());
                    materialJobDetail.setSourceId(item.getArticleId());

                    materialJobDetail.setTitle(wxMpFreePublishArticles.getTitle());
                    materialJobDetail.setDigest(getDigest(wxMpFreePublishArticles.getDigest()));
                    materialJobDetail.setCover(wxMpFreePublishArticles.getThumbUrl());
                    materialJobDetail.setAuthor(wxMpFreePublishArticles.getAuthor());
                    materialJobDetail.setContent(wxMpFreePublishArticles.getUrl());
                    materialJobDetails.add(materialJobDetail);
                }
            } else {
                log.info("【定时制作】任务【{}】获取公众号【{}】文章数量：0", materialJob.getJobName(), appConfig.getAppName());
                XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】文章数量：0", materialJob.getJobName(), appConfig.getAppName());
            }

            log.info("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息成功！数量：{}", materialJob.getJobName(), appConfig.getAppName(), materialJobDetails.size());
            XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息成功！数量：{}", materialJob.getJobName(), appConfig.getAppName(), materialJobDetails.size());
        } catch (WxErrorException e) {
            log.error("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息失败！", materialJob.getJobName(), appConfig.getAppName(), e);
            XxlJobHelper.log("【定时制作】任务【{}】获取公众号【{}】最新发布的文章信息失败！", materialJob.getJobName(), appConfig.getAppName());
        }

        return materialJobDetails;
    }

    /**
     * 获取摘要
     * @param digest
     * @return
     */
    private static String getDigest(String digest) {
        return StrUtil.isBlank(StrUtil.trim(digest)) ? "点击查看" : digest;
    }

    /**
     * 获取物料库物料信息
     * TODO 待实现
     * @param appConfig
     * @param materialJob
     * @return
     */
    private List<BuMaterialJobDetail> getMaterialInfo(SysAppConfig appConfig, BuMaterialJob materialJob) {
        return null;
    }

    /**
     * 获取微信公众号服务
     * TODO 建议后续优化，统一管理微信公众号服务。因时间紧急，原来垃圾代码太多，先暂时这样。
     *
     * @param appConfig
     * @return
     */
    private WxMpService getWxMpService(SysAppConfig appConfig) {
        JSONObject appConfigJson =JSON.parseObject(appConfig.getAppConfig());
        String serviceAccount = appConfigJson.getString("serviceAccount");
        String appId = appConfigJson.getString("appId");
        String secret = appConfigJson.getString("appSecret");

        WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
        wxMpConfigStorage.setAppId(appId);
        wxMpConfigStorage.setSecret(secret);

        if (Objects.nonNull(wechatEngineProperties) && wechatEngineProperties.isEnableAccess()) {
            WxMpTokenGetRsp wxMpTokenGetRsp = getWxMpAccessToken(serviceAccount);
            wxMpConfigStorage.updateAccessToken(wxMpTokenGetRsp.getToken(), wxMpTokenGetRsp.getExpiresIn());
        }

        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(wxMpConfigStorage);

        return wxMpService;
    }

    /**
     * 获取微信公众号access_token
     *
     * @param serviceAccount
     * @return
     */
    private WxMpTokenGetRsp getWxMpAccessToken(String serviceAccount) {
        String baseUrl = wechatEngineProperties.getAccessUrl();
        String wechatKey = wechatEngineProperties.getWechatKey();
        WxMpTokenGetReq wxMpTokenGetReq = new WxMpTokenGetReq(baseUrl, serviceAccount, wechatKey);
        WxMpTokenGetRsp wxMpTokenGetRsp = WechatEngineClient.getWxMpToken(wxMpTokenGetReq);
        if (Objects.isNull(wxMpTokenGetRsp) || !wxMpTokenGetRsp.isSuccess()) {
            throw new RuntimeException("获取微信公众号access_token失败！");
        }
        return wxMpTokenGetRsp;
    }

    /**
     * 制作成功，发送应用消息通知相关人员
     * 应用消息以图片卡片方式展示，一次应用消息通知支持最多 8 个图文消息，如果生成物料超过 8 条，则拆封成多条应用消息通知。
     * 首个图文消息固定展示通知内容：智能物料【物料标题】已通过任务【任务名称】自动创建成功！
     * 其他图文消息展示每条生成的物料信息：物料标题、封面图等
     *
     * @param materialJob 物料任务信息
     * @param materialContentList 生成的智能物料内容列表
     */
    private void sendMsg(BuMaterialJob materialJob, List<TbWxRadarContent> materialContentList) {
        // 参数校验
        if (materialJob == null) {
            log.warn("【定时制作】发送通知失败：物料任务信息为空");
            XxlJobHelper.log("【定时制作】发送通知失败：物料任务信息为空");
            return;
        }

        if (CollectionUtil.isEmpty(materialContentList)) {
            log.info("【定时制作】任务【{}】未生成智能物料，跳过发送通知", materialJob.getJobName());
            XxlJobHelper.log("【定时制作】任务【{}】未生成智能物料，跳过发送通知", materialJob.getJobName());
            return;
        }

        if (materialJob.getMaterialScope() == 1 && StrUtil.isBlank(materialJob.getNoticeUser())) {
            log.info("【定时制作】任务【{}】未配置通知用户，跳过发送通知", materialJob.getJobName());
            XxlJobHelper.log("【定时制作】任务【{}】未配置通知用户，跳过发送通知", materialJob.getJobName());
            return;
        }

        // 个人物料，通知用户为创建人
        if (materialJob.getMaterialScope() == 2) {
            SysUser user = sysUserService.selectUserById(Long.valueOf(materialJob.getCreateBy()));
            if (user == null || StrUtil.isBlank(user.getCorpUserId())) {
                log.warn("【定时制作】任务【{}】通知用户不存在，跳过发送通知", materialJob.getJobName());
                XxlJobHelper.log("【定时制作】任务【{}】通知用户不存在，跳过发送通知", materialJob.getJobName());
                return;
            }
            materialJob.setNoticeUser(user.getCorpUserId());
        }

        try {
            log.info("【定时制作】开始发送任务【{}】制作智能物料成功通知，物料数量：{}",
                    materialJob.getJobName(), materialContentList.size());
            XxlJobHelper.log("【定时制作】开始发送任务【{}】制作智能物料成功通知，物料数量：{}",
                    materialJob.getJobName(), materialContentList.size());

            // 处理通知用户，将逗号分隔的用户ID转换为竖线分隔的格式
            String noticeUser = StrUtil.join("|", StrUtil.split(materialJob.getNoticeUser(), StrUtil.COMMA));

            // 创建通知文章（标题文章）
            NewArticle noticeArticle = createNoticeArticle(materialJob);

            // 企业微信限制每条图文消息最多包含8个图文，首条固定为通知内容，所以每批次最多可以包含7个物料
            // 需要将物料列表分批发送
            int batchSize = 7; // 每批次最多包含7个物料（加上1个通知文章，共8个）
            int totalBatches = NumberUtil.ceilDiv(materialContentList.size(), batchSize); // 向上取整计算批次数

            log.info("【定时制作】任务【{}】需要分{}批次发送通知", materialJob.getJobName(), totalBatches);
            XxlJobHelper.log("【定时制作】任务【{}】需要分{}批次发送通知", materialJob.getJobName(), totalBatches);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                // 计算当前批次的起始和结束索引
                int fromIndex = batchIndex * batchSize;
                int toIndex = Math.min(fromIndex + batchSize, materialContentList.size());

                // 获取当前批次的物料列表
                List<TbWxRadarContent> batchMaterials = materialContentList.subList(fromIndex, toIndex);

                // 创建消息对象
                WxCpMessage wxCpMessage = new WxCpMessage();
                wxCpMessage.setMsgType(WxCpConsts.AppChatMsgType.NEWS);
                wxCpMessage.setToUser(noticeUser);

                // 构建图文消息
                NewsBuilder newsBuilder = new NewsBuilder();

                // 添加首条通知图文（每批次都需要添加）
                newsBuilder.addArticle(noticeArticle);

                // 添加当前批次的物料文章
                for (TbWxRadarContent materialContent : batchMaterials) {
                    if (materialContent != null && StrUtil.isNotBlank(materialContent.getId())) {
                        NewArticle article = createMaterialArticle(materialContent);
                        newsBuilder.addArticle(article);
                    }
                }

                // 设置图文消息内容并发送
                wxCpMessage.setArticles(newsBuilder.build().getArticles());
                mqSendMessageManager.sendAgentMessage(wxCpMessage);

                log.info("【定时制作】任务【{}】批次{}/{}发送成功，包含{}个物料", materialJob.getJobName(), batchIndex + 1, totalBatches, batchMaterials.size());
                XxlJobHelper.log("【定时制作】任务【{}】批次{}/{}发送成功，包含{}个物料", materialJob.getJobName(), batchIndex + 1, totalBatches, batchMaterials.size());
            }

            log.info("【定时制作】任务【{}】制作智能物料成功通知发送完成，共发送{}批次，{}个物料", materialJob.getJobName(), totalBatches, materialContentList.size());
            XxlJobHelper.log("【定时制作】任务【{}】制作智能物料成功通知发送完成，共发送{}批次，{}个物料", materialJob.getJobName(), totalBatches, materialContentList.size());
        } catch (Exception e) {
            log.error("【定时制作】任务【{}】发送通知失败：{}", materialJob.getJobName(), e.getMessage(), e);
            XxlJobHelper.log("【定时制作】任务【{}】发送通知失败：{}", materialJob.getJobName(), e.getMessage());
        }
    }

    /**
     * 创建首条通知
     *
     * @param materialJob 物料任务信息
     * @return 通知图文对象
     */
    private NewArticle createNoticeArticle(BuMaterialJob materialJob) {
        NewArticle noticeArticle = new NewArticle();
        String msgContent = String.format("定时制作任务【%s】成功创建智能物料！", materialJob.getJobName());
        noticeArticle.setTitle(msgContent);
        noticeArticle.setDescription(msgContent);

        // 获取默认图片URL
        String defaultPicUrl = redisCache.getCacheObject(DefaultConstants.DEFAULT_MSG_PIC);
        noticeArticle.setPicUrl(defaultPicUrl);

        return noticeArticle;
    }

    /**
     * 创建物料文章通知
     *
     * @param materialContent 物料内容
     * @return 物料图文对象
     */
    private NewArticle createMaterialArticle(TbWxRadarContent materialContent) {
        NewArticle article = new NewArticle();

        // 设置文章基本信息
        article.setTitle(materialContent.getTitle());
        article.setDescription(materialContent.getDigest());

        // 构建物料详情页URL
        String contentId = materialContent.getId();
        String url = RadarConfig.getContentPage() + "?id=" + contentId;
        article.setUrl(url);

        // 设置封面图
        article.setPicUrl(materialContent.getCover());

        return article;
    }

}
