package com.cenker.scrm;


import com.cenker.ApiCenterApplication;
import com.cenker.scrm.biz.sop.ConditionSopBiz;
import com.cenker.scrm.biz.sop.JourneySopBiz;
import com.cenker.scrm.biz.sop.MassCustomerSopBiz;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.pojo.entity.wechat.sop.SopContentInfo;
import com.cenker.scrm.pojo.exception.DataNotExistException;
import com.cenker.scrm.pojo.exception.DataNotExistOrDeletedException;
import com.cenker.scrm.pojo.request.sop.ConditionSopRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopRequest;
import com.cenker.scrm.pojo.vo.sop.ConditionSopDetailVO;
import com.cenker.scrm.pojo.vo.sop.JourneySopDetailVO;
import com.cenker.scrm.service.journey.ITbWxExtJourneyCustomerStageService;
import com.cenker.scrm.service.sop.ISopCustomerInfoService;
import com.cenker.scrm.util.StringUtils;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

/**
 * <AUTHOR>
 * @title ChatArchiveCenterApplicationTest
 * @date 2024/5/9 10:03
 * @description TODO
 */
@Slf4j
@SpringBootTest(classes = ApiCenterApplication.class)
@RunWith(SpringRunner.class)
public class SopContentRunTaskTest {

    @Autowired
    private  ConditionSopBiz conditionSopBiz;
    @Autowired
    private  JourneySopBiz journeySopBiz;
    @Autowired
    private  MassCustomerSopBiz massCustomerSopBiz;
    @Autowired
    private  ISopCustomerInfoService sopCustomerInfoService;
    @Autowired
    private  ITbWxExtJourneyCustomerStageService tbWxExtJourneyCustomerStageService;

    @Test
    public void startConditionContent() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("【条件sop】内容序列开始执行，执行参数：{}", jobParam);
        if (StringUtils.isNotEmpty(jobParam)) {
            String[] jobParamSplit = jobParam.split(SeparatorEnum.UNDERLINE.getSeparator());
            Long sopId = Long.valueOf(jobParamSplit[0]);
            Long contentId = Long.valueOf(jobParamSplit[1]);
            String contentVersion = jobParamSplit[2];
            SopContentInfo contentInfo = conditionSopBiz.getSopContentInfoService().getById(contentId);
            Optional.ofNullable(contentInfo).orElseThrow(() -> new DataNotExistOrDeletedException(ErrCodeEnum.SOP_CONTENT_NOT_EXIST_ERROR));
            log.info("【条件sop】内容序列准备执行，执行sopId【{}】，contentId【{}】,执行版本号【{}】", sopId, contentId, contentVersion);
            conditionSopBiz.runConditionContent(contentInfo);
            return;
        }
        log.error("【条件sop】内容序列执行错误，错误原因：缺失定时任务参数");
    }

    @Test
    public void startConditionSopScan() {
        /**
         * 当客户量大的时候 大批量的in查询等势必会导致性能极具下降甚至报错
         * 包括群发也有数量的限制 但出于时间考虑 这里只考虑逻辑上的闭环 剩余的做后续优化 2023-07-18
         */
        String jobParam = XxlJobHelper.getJobParam();
        log.info("【条件sop】sop扫描人群包开始执行，执行参数：{}", jobParam);
        if (StringUtils.isNotEmpty(jobParam)) {
            Long sopId = Long.valueOf(jobParam);
            ConditionSopRequest conditionSopRequest = new ConditionSopRequest();
            conditionSopRequest.setSopId(sopId);
            ConditionSopDetailVO conditionSopDetailVO = conditionSopBiz.detailConditionSop(conditionSopRequest);
            Optional.ofNullable(conditionSopDetailVO).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
            sopCustomerInfoService.conditionSopCustomerScan(conditionSopDetailVO);
            return;
        }
        log.error("【条件sop】sop扫描人群包执行错误，错误原因：缺失定时任务参数");
    }

    @Test
    public void startJourneySopScan() {
        Long sopId = Long.valueOf("1852283723988439041");
        JourneySopRequest journeySopRequest = new JourneySopRequest();
        journeySopRequest.setSopId(sopId);
        JourneySopDetailVO journeySopDetailVO = journeySopBiz.detailJourneySop(journeySopRequest);
        Optional.ofNullable(journeySopDetailVO).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        tbWxExtJourneyCustomerStageService.journeySopCustomerScan(journeySopDetailVO);
    }

    @Test
    public void startJourneySopContent() {
        String jobParam = "1830508684226170881_1830508684620435457_RW202409020001-V0001";
        log.info("【旅程sop】内容序列开始执行，执行参数：{}", jobParam);
        if (StringUtils.isNotEmpty(jobParam)) {
            String[] jobParamSplit = jobParam.split(SeparatorEnum.UNDERLINE.getSeparator());
            Long contentId = Long.valueOf(jobParamSplit[1]);
            SopContentInfo contentInfo = journeySopBiz.getSopContentInfoService().getById(contentId);
            Optional.ofNullable(contentInfo).orElseThrow(() -> new DataNotExistOrDeletedException(ErrCodeEnum.SOP_CONTENT_NOT_EXIST_ERROR));
            log.info("【旅程sop】内容序列准备执行，执行sopId【{}】，contentId【{}】,执行版本号【{}】，执行阶段id:【{}】",
                    Long.valueOf(jobParamSplit[0]), contentId, jobParamSplit[2], contentInfo.getStageId());
            journeySopBiz.runConditionContent(contentInfo);
            return;
        }
        log.error("【旅程sop】内容序列执行错误，错误原因：缺失定时任务参数");
    }

    @Test
    public void startMassCustomerSopContent() {
        String jobParam = XxlJobHelper.getJobParam();
        jobParam="1834193208451592193_1834198969600258049_RW202409120001-V0002";
        log.info("【1V1sop】内容序列开始执行，执行参数：{}", jobParam);
        if (StringUtils.isNotEmpty(jobParam)) {
            String[] jobParamSplit = jobParam.split(SeparatorEnum.UNDERLINE.getSeparator());
            SopContentInfo contentInfo = massCustomerSopBiz.getSopContentInfoService().getById(Long.valueOf(jobParamSplit[1]));
            Optional.ofNullable(contentInfo).orElseThrow(() -> new DataNotExistOrDeletedException(ErrCodeEnum.SOP_CONTENT_NOT_EXIST_ERROR));
            log.info("【1V1sop】内容序列准备执行，执行sopId【{}】，contentId【{}】,执行版本号【{}】", Long.valueOf(jobParamSplit[0]), contentInfo.getId(), jobParamSplit[2]);
            massCustomerSopBiz.runConditionContent(contentInfo);
            return;
        }
        log.error("【1V1sop】内容序列执行错误，错误原因：缺失定时任务参数");
    }

}