<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.CkAiAppMapper">

    <resultMap type="CkAiApp" id="CkAiAppResult">
        <result property="id" column="id"/>
        <result property="appName" column="app_name" />
        <result property="appDescription" column="app_description" />
        <result property="appUrl" column="app_url" />
        <result property="logoUrl" column="logo_url" />
        <result property="temperature" column="temperature" />
        <result property="status" column="status" />
        <result property="createTime" column="create_time" />
        <result property="replySetting" column="reply_setting" />
        <result property="replyValue" column="reply_value" />
        <result property="apiCode" column="api_code" />
        <result property="apiSecret" column="api_secret" />
    </resultMap>

    <select id="selectCkAiAppList" parameterType="CkAiRequest" resultMap="CkAiAppResult">
        SELECT a.id, a.app_name, a.app_description, a.app_url, a.logo_url, a.temperature, a.status, a.create_time, IFNULL(COUNT(m.id), 0) AS ckAiRepositoryNum
        FROM ck_ai_app a
        LEFT JOIN ck_ai_app_repository_mapping m ON a.id = m.app_id AND m.is_deleted = 0
        <where>+
            a.is_deleted = 0
            <if test="name != null and name != ''">
                AND a.app_name like concat('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>
        </where>
        GROUP BY a.id, a.app_name, a.app_description, a.app_url, a.logo_url, a.temperature, a.status, a.create_time
    </select>

</mapper>