package com.cenker.scrm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.CkAiChatRecordMapper;
import com.cenker.scrm.pojo.dto.knowledgebaseai.CkAiRequest;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiChatRecord;
import com.cenker.scrm.service.ICkAiChatRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CkAiChatRecordServiceImpl extends ServiceImpl<CkAiChatRecordMapper, CkAiChatRecord> implements ICkAiChatRecordService {
    @Override
    public List<CkAiChatRecord> selectChatRecordList(CkAiRequest ckAiRequest) {
        return this.baseMapper.selectChatRecordList(ckAiRequest);
    }
}
