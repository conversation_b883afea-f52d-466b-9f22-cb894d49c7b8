package com.cenker.scrm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.KnowledgeBaseAiApiBusiness;
import com.cenker.scrm.mapper.CkAiUploadFileInfoMapper;
import com.cenker.scrm.pojo.dto.knowledgebaseai.CkAiRequest;
import com.cenker.scrm.pojo.dto.knowledgebaseai.KnowledgeBaseAiDTO;
import com.cenker.scrm.pojo.dto.knowledgebaseai.document.Document;
import com.cenker.scrm.pojo.dto.knowledgebaseai.segment.TextSegment;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiChatRecord;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiSegment;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiUploadFileInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.ICkAiChatRecordService;
import com.cenker.scrm.service.ICkAiSegmentService;
import com.cenker.scrm.service.ICkAiUploadFileInfoService;
import com.cenker.scrm.utlis.Document.UrlDocumentLoader;
import com.cenker.scrm.utlis.Document.splitter.CharacterSplitter;
import com.cenker.scrm.utlis.Document.transformer.HtmlTextExtractor;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.cky.common.storage.cloud.OssStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.cenker.scrm.utlis.Document.DocumentLoaderUtils.detectDocumentType;
import static com.cenker.scrm.utlis.Document.DocumentLoaderUtils.parserFor;
import static com.cenker.scrm.utlis.Document.DocumentType.HTML;

@Slf4j
@Service
@RequiredArgsConstructor
public class CkAiUploadFileInfoImpl extends ServiceImpl<CkAiUploadFileInfoMapper, CkAiUploadFileInfo> implements ICkAiUploadFileInfoService {
    private final OssStorageFactory ossStorageFactory;

    private final KnowledgeBaseAiApiBusiness knowledgeBaseAiApiBusiness;

    private final ICkAiChatRecordService ckAiChatRecordService;

    private final ICkAiSegmentService ckAiSegmentService;

    @Override
    public List<CkAiUploadFileInfo> selectUploadFileInfoList(CkAiRequest ckAiRequest) {
        return this.baseMapper.selectUploadFileInfoList(ckAiRequest);
    }

    @Override
    public List<CkAiUploadFileInfo> selectSourceTypeByRepositoryId(Long repositoryId) {
        return this.baseMapper.selectSourceTypeByRepositoryId(repositoryId);
    }

    /**
     * 上传文件
     *
     * @param file
     * @param repositoryId 知识库ID
     * @param corpId
     * @param userId
     * @return
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file, Long repositoryId, String corpId, String userId) {
        // 上传文件到对象存储
        String sourceUrl = knowledgeBaseAiApiBusiness.uploadFileForOssStorage(file, corpId);
        if (StringUtils.isEmpty(sourceUrl)) {
            return AjaxResult.error();
        }
        // 保存到上传记录表
        CkAiUploadFileInfo ckAiUploadFileInfo = buildFileInfo(repositoryId, sourceUrl, file.getOriginalFilename(), userId);
        saveOrUpdate(ckAiUploadFileInfo);
        return AjaxResult.success(ckAiUploadFileInfo);
    }

    private CkAiUploadFileInfo buildFileInfo(Long repositoryId, String sourceUrl, String originalFilename, String userId) {
        return CkAiUploadFileInfo.builder()
                .sourceUrl(sourceUrl)
                .name(originalFilename)
                .repositoryId(repositoryId)
                .uploadType(1)
                .status("1")
                .createBy(userId)
                .build();
    }

    /**
     * 上传文件训练
     *
     * @param id     上传信息表ID
     * @param corpId
     * @param userId
     * @return
     */
    @Override
    public AjaxResult addForFile(Long id, String corpId, String userId) {
        CkAiUploadFileInfo ckAiUploadFileInfoById = getById(id);
        String sourceUrl = ckAiUploadFileInfoById.getSourceUrl();
        Long uploadInfoId = ckAiUploadFileInfoById.getId();

        // 使用对象存储加载
        InputStream inputStream = ossStorageFactory.build().download(sourceUrl);
        // 格式转化
        Document parseDocument = parserFor(detectDocumentType(sourceUrl)).parse(inputStream);
        // 分割 todo 可以改成单例
        CharacterSplitter characterSplitter = knowledgeBaseAiApiBusiness.getCharacterSplitter();
        List<TextSegment> segmentList = characterSplitter.split(parseDocument);

        // 构建存储分段记录
        List<CkAiSegment> ckAiSegmentList = buildSegmentByTextSegment(segmentList, userId, uploadInfoId);
        // 批量分段组成文档上传到对象存储  todo 注意里面ckAiSegment.setSegmentSourceUrl(uploadUrl);
        String segmentSourceUrlList = knowledgeBaseAiApiBusiness.textConverTxTUploadBatch(corpId, ckAiSegmentList);
        ckAiSegmentService.saveOrUpdateBatch(ckAiSegmentList);

        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingAddApi(segmentSourceUrlList, String.valueOf(ckAiUploadFileInfoById.getRepositoryId()))
        );
        knowledgeBaseAiFuture.thenAccept(execFutureForAddBatch(ckAiUploadFileInfoById, ckAiSegmentList));
        return AjaxResult.success(ckAiUploadFileInfoById);
    }

    private Consumer<? super KnowledgeBaseAiDTO> execFutureForAddBatch(CkAiUploadFileInfo ckAiUploadFileInfo, List<CkAiSegment> ckAiSegmentList) {
        return knowledgeBaseAiDTO -> {
            try {
                // 维护分段key
                Map<String, String> keyMap = knowledgeBaseAiDTO.getKeys();
                ckAiSegmentList.forEach(ckAiSegment -> {
                    String segmentKey = keyMap.getOrDefault(ckAiSegment.getSegmentSourceUrl(), null);
                    ckAiSegment.setSegmentKey(segmentKey);
                });
                // 维护上传信息状态
                if (knowledgeBaseAiDTO.isError()) {
                    ckAiUploadFileInfo.setStatus("1");
                } else if (knowledgeBaseAiDTO.isSuccess()) {
                    ckAiUploadFileInfo.setStatus("0");
                }
                ckAiSegmentService.saveOrUpdateBatch(ckAiSegmentList);
                this.saveOrUpdate(ckAiUploadFileInfo);
            } catch (Exception e) {
                log.error("【知识库AI.提取学习】失败，参数：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        };
    }

    private List<CkAiSegment> buildSegmentByTextSegment(List<TextSegment> segmentList, String userId, Long uploadInfoId) {
        return segmentList.stream()
                .map(textSegment -> CkAiSegment.builder()
                        .uploadFileInfoId(uploadInfoId)
                        .segmentContent(textSegment.getText())
                        .remark(textSegment.getMetadata().toString())
                        .createBy(userId)
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * FAQ训练
     *
     * @param id     上传信息表ID
     * @param corpId
     * @param userId
     * @return
     */
    public AjaxResult addForFaq(Long id, String corpId, String userId) {
        CkAiUploadFileInfo ckAiUploadFileInfo = this.getById(id);
        String text = this.getText(ckAiUploadFileInfo.getName(), ckAiUploadFileInfo.getDescription());
        String sourceUrl = knowledgeBaseAiApiBusiness.textConverTxTUpload(corpId, text);
        if (StringUtils.isEmpty(sourceUrl)) {
            return AjaxResult.error();
        }
        // 维护上传信息记录表
        ckAiUploadFileInfo.setSourceUrl(sourceUrl);
        this.saveOrUpdate(ckAiUploadFileInfo);
        // 构建分段表记录
        CkAiSegment ckAiSegment = buildSegmentByTextAndsourceUrl(ckAiUploadFileInfo.getId(), text, sourceUrl);
        this.ckAiSegmentService.saveOrUpdate(ckAiSegment);
        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingAddApi(sourceUrl, String.valueOf(ckAiUploadFileInfo.getRepositoryId()))
        );
        knowledgeBaseAiFuture.thenAccept(execFutureForAdd(ckAiUploadFileInfo, ckAiSegment));
        return AjaxResult.success(ckAiUploadFileInfo);
    }

    private CkAiSegment buildSegmentByTextAndsourceUrl(Long uploadFileInfoId, String text, String sourceUrl) {
        return CkAiSegment.builder()
                .uploadFileInfoId(uploadFileInfoId)
                .segmentContent(text)
                .segmentSourceUrl(sourceUrl)
                .build();
    }

    /**
     * 反向训练  未命中训练接口
     *
     * @param ckAiRequest
     * @param corpId
     * @param userId
     * @return
     */
    @Override
    public AjaxResult addForText(CkAiRequest ckAiRequest, String corpId, String userId) {
        String name = ckAiRequest.getName();
        Long repositoryId = ckAiRequest.getRepositoryId();
        String content = ckAiRequest.getContent();
        String text = this.getText(name, content);
        String sourceUrl = knowledgeBaseAiApiBusiness.textConverTxTUpload(corpId, text);
        if (StringUtils.isEmpty(sourceUrl)) {
            return AjaxResult.error();
        }
        // 维护上传信息记录表
        CkAiUploadFileInfo ckAiUploadFileInfo = getTextInfo(userId, name, repositoryId, content, sourceUrl);
        this.saveOrUpdate(ckAiUploadFileInfo);
        // 更新聊天记录是否学习字段
        CkAiChatRecord ckAiChatRecordById = ckAiChatRecordService.getById(ckAiRequest.getChatRecordId());
        ckAiChatRecordById.setLearn("1");
        ckAiChatRecordService.saveOrUpdate(ckAiChatRecordById);
        // 构建分段表记录
        CkAiSegment ckAiSegment = buildSegmentByTextAndsourceUrl(ckAiUploadFileInfo.getId(), text, sourceUrl);
        ckAiSegmentService.saveOrUpdate(ckAiSegment);

        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingAddApi(sourceUrl, String.valueOf(repositoryId))
        );
        knowledgeBaseAiFuture.thenAccept(execFutureForAdd(ckAiUploadFileInfo, ckAiSegment));
        return AjaxResult.success(ckAiUploadFileInfo);
    }

    private Consumer<KnowledgeBaseAiDTO> execFutureForAdd(CkAiUploadFileInfo ckAiUploadFileInfo, CkAiSegment ckAiSegment) {
        return knowledgeBaseAiDTO -> {
            try {
                // 维护分段key
                Map<String, String> keyMap = knowledgeBaseAiDTO.getKeys();
                String key = keyMap.getOrDefault(ckAiSegment.getSegmentSourceUrl(), null);
                ckAiSegment.setSegmentKey(key);
                // 维护上传信息状态
                if (knowledgeBaseAiDTO.isError()) {
                    ckAiUploadFileInfo.setStatus("1");
                } else if (knowledgeBaseAiDTO.isSuccess()) {
                    ckAiUploadFileInfo.setStatus("0");
                }
                ckAiSegmentService.saveOrUpdate(ckAiSegment);
                saveOrUpdate(ckAiUploadFileInfo);
            } catch (Exception e) {
                log.error("【知识库AI.提取学习】失败，参数：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        };
    }

    @Override
    public AjaxResult addForUrl(CkAiRequest ckAiRequest, String corpId, String userId) {
        String url = ckAiRequest.getSourceUrl();
        Long repositoryId = ckAiRequest.getRepositoryId();

        // 格式加载转化
        Document document = UrlDocumentLoader.load(url, HTML);
        // html转化
        HtmlTextExtractor transformer = new HtmlTextExtractor();
        Document transformedDocument = transformer.transform(document);
        //获取title
        String title = getTitle(document);
        // 分割
        CharacterSplitter characterSplitter = knowledgeBaseAiApiBusiness.getCharacterSplitter();
        List<TextSegment> segmentList = characterSplitter.split(transformedDocument);
        // 维护上传信息记录表
        CkAiUploadFileInfo ckAiUploadFileInfo = getTextInfo(userId, title, repositoryId, ckAiRequest.getContent(), url);
        this.saveOrUpdate(ckAiUploadFileInfo);
        // 构建存储分段记录
        List<CkAiSegment> ckAiSegmentList = buildSegmentByTextSegment(segmentList, userId, ckAiUploadFileInfo.getId());
        // 批量分段组成文档上传到对象存储  todo 注意里面ckAiSegment.setSegmentSourceUrl(uploadUrl);
        String segmentSourceUrlList = knowledgeBaseAiApiBusiness.textConverTxTUploadBatch(corpId, ckAiSegmentList);
        ckAiSegmentService.saveOrUpdateBatch(ckAiSegmentList);

        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingAddApi(segmentSourceUrlList, String.valueOf(repositoryId))
        );
        knowledgeBaseAiFuture.thenAccept(execFutureForAddBatch(ckAiUploadFileInfo, ckAiSegmentList));
        return AjaxResult.success(ckAiUploadFileInfo);
    }

    /**
     * 删除学习
     *
     * @param id     上传信息表ID
     * @param userId
     * @return
     */
    @Override
    public AjaxResult stopById(Long id, String userId) {
        CkAiUploadFileInfo ckAiUploadFileInfoById = this.getById(id);
        ckAiUploadFileInfoById.setUpdateBy(userId);
        // 通过UploadFileInfoId 获取分段表的key
        List<CkAiSegment> listByUploadInfoId = ckAiSegmentService.getListByUploadInfoId(ckAiUploadFileInfoById.getId());
        String[] keysArray = listByUploadInfoId.stream()
                .map(CkAiSegment::getSegmentKey)
                .filter(Objects::nonNull)
                .toArray(String[]::new);
        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingDelApi(keysArray)
        );
        knowledgeBaseAiFuture.thenAccept(execFutureForDel(ckAiUploadFileInfoById));
        return AjaxResult.success();
    }

    private Consumer<KnowledgeBaseAiDTO> execFutureForDel(CkAiUploadFileInfo ckAiUploadFileInfo) {
        return knowledgeBaseAiDTO -> {
            try {
                // 维护上传信息表状态
                if (knowledgeBaseAiDTO.isError()) {
                    ckAiUploadFileInfo.setStatus("0");
                }
                if (knowledgeBaseAiDTO.isSuccess()) {
                    ckAiUploadFileInfo.setStatus("1");
                }
                this.saveOrUpdate(ckAiUploadFileInfo);
            } catch (Exception e) {
                log.error("【知识库AI.取消学习】失败，参数：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        };
    }

    @Override
    public AjaxResult stopByIds(Long[] ids, String userId) {
        // 通过ids获取Segment keys
        List<CkAiSegment> listByUploadInfoIds = this.ckAiSegmentService.getListByUploadInfoIds(ids);
        String[] keysArray = listByUploadInfoIds.stream()
                .map(CkAiSegment::getSegmentKey)
                .filter(key -> key != null && !key.isEmpty())
                .toArray(String[]::new);
        // 异步请求知识库AI
        CompletableFuture<KnowledgeBaseAiDTO> knowledgeBaseAiFuture = CompletableFuture.supplyAsync(() ->
                knowledgeBaseAiApiBusiness.embeddingDelApi(keysArray)
        );
        // 维护上传文件信息表
        List<CkAiUploadFileInfo> uploadFileInfoList = this.baseMapper.selectBatchIds(Arrays.asList(ids));
        knowledgeBaseAiFuture.thenAccept(execFutureForDelBatch(uploadFileInfoList));
        return AjaxResult.success();
    }

    private Consumer<? super KnowledgeBaseAiDTO> execFutureForDelBatch(List<CkAiUploadFileInfo> uploadFileInfoList) {
        return knowledgeBaseAiDTO -> {
            try {
                if (knowledgeBaseAiDTO.isSuccess()) {
                    uploadFileInfoList.forEach(ckAiUploadFileInfo -> ckAiUploadFileInfo.setStatus("1"));
                    this.saveOrUpdateBatch(uploadFileInfoList);
                }
            } catch (Exception e) {
                log.error("【知识库AI.取消学习】失败，参数：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        };
    }


    @Override
    public AjaxResult delById(Long id, String userId) {
        // 判断是否停用 ck_ai_upload_file_info status 1 为停用
        CkAiUploadFileInfo ckAiUploadFileInfo = this.getById(id);
        if (isCheckStop(ckAiUploadFileInfo)) {
            return AjaxResult.error("请先停用学习");
        }
        // 删除原文件连接 ck_ai_segment  segment_source_url
//        List<CkAiSegment> listByUploadInfoId = this.ckAiSegmentService.getListByUploadInfoId(id);
//        List<String> sourceUrlList = listByUploadInfoId.stream()
//                .map(CkAiSegment::getSegmentSourceUrl)
//                .filter(key -> key != null && !key.isEmpty())
//                .collect(Collectors.toList());
//        sourceUrlList.forEach(sourceUrl->{
//            this.ossStorageFactory.build().delete(sourceUrl);
//        });
        // 软删除记录
        this.removeById(id);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult delByIds(Long[] ids, String userId) {
        // 判断是否都已经禁用
        List<CkAiUploadFileInfo> uploadFileInfoList = this.baseMapper.selectBatchIds(Arrays.asList(ids));
        boolean isLearningStopped = uploadFileInfoList.stream()
                .anyMatch(this::isCheckStop);
        if (isLearningStopped) {
            return AjaxResult.error("请先停用学习");
        }
//        List<CkAiSegment> listByUploadInfoIds = this.ckAiSegmentService.getListByUploadInfoIds(ids);
//        List<String> sourceUrlList = listByUploadInfoIds.stream()
//                .map(CkAiSegment::getSegmentSourceUrl)
//                .filter(key -> key != null && !key.isEmpty())
//                .collect(Collectors.toList());
//        sourceUrlList.forEach(sourceUrl->{
//            this.ossStorageFactory.build().delete(sourceUrl);
//        });
        // 软删除记录
        this.removeByIds(Arrays.asList(ids));
        return AjaxResult.success();
    }

    private boolean isCheckStop(CkAiUploadFileInfo ckAiUploadFileInfo) {
        return StringUtils.isNotEmpty(ckAiUploadFileInfo.getStatus()) && !"1".equals(ckAiUploadFileInfo.getStatus());
    }

    private String getTitle(Document document) {
        org.jsoup.nodes.Document jsoupDocument = Jsoup.parse(document.text());
        return jsoupDocument.title();
    }


    private static CkAiUploadFileInfo getTextInfo(String userId, String name, Long repositoryId, String content, String sourceUrl) {
        return CkAiUploadFileInfo.builder()
                .sourceUrl(sourceUrl)
                .name(name)
                .description(content)
                .repositoryId(repositoryId)
                .uploadType(0)
                .createBy(userId)
                .build();
    }

    private String getText(String name, String content) {
        return new StringBuilder().append("提问问题：")
                .append(name).append("/n")
                .append("该问题回答内容：")
                .append(content)
                .toString();
    }
}
