package com.cenker.scrm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.knowledgebaseai.CkAiRequest;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiApp;

import java.util.List;

public interface ICkAiAppService extends IService<CkAiApp> {
    List<CkAiApp> selectCkAiAppList(CkAiRequest ckAiRequest);

    int insertCkAiApp(CkAiApp ckAiApp);

    int updateCkAiApp(CkAiApp ckAiApp);
}
