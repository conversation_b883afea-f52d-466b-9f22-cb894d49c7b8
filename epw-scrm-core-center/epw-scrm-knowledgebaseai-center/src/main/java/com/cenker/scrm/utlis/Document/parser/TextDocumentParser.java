package com.cenker.scrm.utlis.Document.parser;


import com.cenker.scrm.pojo.dto.knowledgebaseai.document.Document;
import com.cenker.scrm.pojo.dto.knowledgebaseai.document.Metadata;
import com.cenker.scrm.utlis.Document.DocumentType;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;

import static com.cenker.scrm.utlis.Document.ValidationUtils.ensureNotNull;
import static java.nio.charset.StandardCharsets.UTF_8;

public class TextDocumentParser implements DocumentParser {

    private final DocumentType documentType;
    private final Charset charset;

    public TextDocumentParser(DocumentType documentType) {
        this(documentType, UTF_8);
    }

    public TextDocumentParser(DocumentType documentType, Charset charset) {
        this.documentType = ensureNotNull(documentType, "documentType");
        this.charset = ensureNotNull(charset, "charset");
    }

    @Override
    public Document parse(InputStream inputStream) {
        try {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();

            String text = new String(buffer.toByteArray(), charset);

            return Document.from(text, new Metadata().add(DOCUMENT_TYPE, documentType.toString()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
