package com.cenker.scrm.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.ai.AzureOpenApiRequest;
import com.cenker.scrm.pojo.entity.ai.AzureChatRecord;
import com.cenker.scrm.pojo.vo.ai.AzureChatRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
public interface AzureChatRecordMapper extends BaseMapper<AzureChatRecord> {

    /**
     * 获取聊天记录
     * @param azureOpenApiRequest
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<AzureChatRecordVO> getChatRecord(@Param("azureOpenApiRequest") AzureOpenApiRequest azureOpenApiRequest, @Param("pageNum") Integer pageNum, @Param("pageSize")Integer pageSize);
}
