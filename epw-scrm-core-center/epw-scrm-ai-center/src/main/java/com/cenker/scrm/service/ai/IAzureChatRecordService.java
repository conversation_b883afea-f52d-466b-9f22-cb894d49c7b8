package com.cenker.scrm.service.ai;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.ai.AzureOpenApiRequest;
import com.cenker.scrm.pojo.entity.ai.AzureChatRecord;
import com.cenker.scrm.pojo.vo.ai.AzureChatRecordVO;

import java.util.List;

public interface IAzureChatRecordService extends IService<AzureChatRecord> {
    /**
     * 获取聊天记录
     * @param azureOpenApiRequest
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<AzureChatRecordVO> getChatRecord(AzureOpenApiRequest azureOpenApiRequest, Integer pageNum, Integer pageSize);
}
