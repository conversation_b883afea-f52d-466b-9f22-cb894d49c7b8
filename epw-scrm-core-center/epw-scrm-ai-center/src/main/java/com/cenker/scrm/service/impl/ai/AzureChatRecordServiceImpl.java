package com.cenker.scrm.service.impl.ai;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.ai.AzureChatRecordMapper;
import com.cenker.scrm.pojo.dto.ai.AzureOpenApiRequest;
import com.cenker.scrm.pojo.entity.ai.AzureChatRecord;
import com.cenker.scrm.pojo.vo.ai.AzureChatRecordVO;
import com.cenker.scrm.service.ai.IAzureChatRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/25
 * @Description ai聊天
 */
@Service
public class AzureChatRecordServiceImpl extends ServiceImpl<AzureChatRecordMapper, AzureChatRecord> implements IAzureChatRecordService {
    @Override
    public List<AzureChatRecordVO> getChatRecord(AzureOpenApiRequest azureOpenApiRequest, Integer pageNum, Integer pageSize) {
        // 前端以一段对话作为分组 所以这里需要已提问做分页 而不是包含结果
        pageNum = (pageNum - 1) * pageSize;
        return baseMapper.getChatRecord(azureOpenApiRequest, pageNum, pageSize);
    }
}
