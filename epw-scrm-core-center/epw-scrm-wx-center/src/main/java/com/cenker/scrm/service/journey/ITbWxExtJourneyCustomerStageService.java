package com.cenker.scrm.service.journey;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.journey.ExternalJourneyTransferDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtJourneyCustomerStage;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtJourneyInfo;
import com.cenker.scrm.pojo.vo.sop.JourneySopDetailVO;


public interface ITbWxExtJourneyCustomerStageService extends IService<TbWxExtJourneyCustomerStage> {
    /**
     * 设置客户阶段（转移）
     * @param journeyTransferDTO
     */
    void setStage(ExternalJourneyTransferDTO journeyTransferDTO, TbWxExtJourneyInfo journeyInfo);

    /**
     * 扫描旅程sop人群
     * @param journeySopDetailVO
     */
    void journeySopCustomerScan(JourneySopDetailVO journeySopDetailVO);
}
