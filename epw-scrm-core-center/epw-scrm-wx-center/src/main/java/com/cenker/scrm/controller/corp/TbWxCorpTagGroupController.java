package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.enums.GroupTagTypeEnum;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.tag.ITbDcUserTagsService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupService;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 企业客户标签组Controller
 */
@Slf4j
@RestController
@RequestMapping("/tp/groupTag")
public class TbWxCorpTagGroupController extends BaseController {
    @Autowired
    private ITbWxCorpTagGroupService tbWxCorpTagGroupService;
    @Autowired
    private ITbWxCorpTagService tbWxCorpTagService;
    @Autowired
    private TokenParseUtil tokenService;

    @Autowired
    private ITbDcUserTagsService iTbDcUserTagsService;

    /**
     * 查询企业客户标签组列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody TbWxCorpTagGroup tbWxCorpTagGroup) {
        startPage();
        List<TbWxCorpTagGroup> list = tbWxCorpTagGroupService.selectTbWxCorpTagGroupList(tbWxCorpTagGroup);
        return getDataTable(list);
    }

    /**
     * 查询企业客户标签是否存在
     */
    @RequestMapping("/queryTagName")
    public AjaxResult queryTagName(String groupId, String tagName, String corpId) {
        String flag = "Y";
        QueryWrapper<TbWxCorpTag> wrapper = new QueryWrapper<>();
        wrapper.eq("group_id", groupId);
        wrapper.eq("name", tagName);
        wrapper.eq("status", UserConstants.NORMAL);
        wrapper.eq("corp_id", corpId);
        if (tbWxCorpTagService.list(wrapper).size() < 1) {
            flag = "N";
        }
        return AjaxResult.success("", flag);
    }

    /**
     * 获取企业客户标签组详细信息
     */
    @RequestMapping("/getInfo")
    public AjaxResult getInfo(@RequestBody TbWxCorpTagGroup tbWxCorpTagGroup) {
        log.info("查询企业客户标签组详细信息，入参：{}", tbWxCorpTagGroup.getGroupId());
        tbWxCorpTagGroup.setStatus(StatusConstants.DEL_FLAG_FALSE);
        List<TbWxCorpTagGroup> data = tbWxCorpTagGroupService.selectTbWxCorpTagGroupList(tbWxCorpTagGroup);
        log.info("查询企业客户标签组详细信息，出参：{}", data);
        return AjaxResult.success(data);
    }

    /**
     * 新增企业客户标签
     */
    @RequestMapping("/add")
    public AjaxResult add(@RequestBody TbWxCorpTagGroup tbWxCorpTagGroup, HttpServletRequest request) {
        log.info("新增企业客户标签，入参：{}", tbWxCorpTagGroup);
        AjaxResult x = queryGroupName(tbWxCorpTagGroup, false);
        if (x != null) {
            return x;
        }
        boolean isMatch = Arrays.stream(GroupTagTypeEnum.values()).anyMatch(e -> e.getCode().equals(tbWxCorpTagGroup.getGroupTagType()));
        if (!isMatch) {
            throw new CustomException("标签类型不正确!");
        }
        TbWxCorpTagGroup group = tbWxCorpTagGroupService.insertTbWxCorpTagGroup(tbWxCorpTagGroup, true);
        if (group != null) {
            if (StringUtils.isNotEmpty(group.getRemark())) {
                return AjaxResult.error("创建标签失败:" + group.getRemark());
            }
            log.info("新增企业客户标签完成");
            return AjaxResult.success();
        } else {
            return AjaxResult.error("创建标签失败");
        }
    }

    /**
     *
     * @param tbWxCorpTagGroup 标签组对象
     * @param edit 是否编辑
     * @return 校验结果 通过返回null
     */
    private AjaxResult queryGroupName(TbWxCorpTagGroup tbWxCorpTagGroup, boolean edit) {
        if (StringUtils.isEmpty(tbWxCorpTagGroup.getGroupName())) {
            return AjaxResult.error("标签组名称不能为空");
        }
        if (queryExists(tbWxCorpTagGroup.getCorpId(), tbWxCorpTagGroup.getGroupName(),edit ? tbWxCorpTagGroup.getGroupId() : null)) {
            return AjaxResult.error("标签组名称已存在");
        }
        // 遍历tag 不能存在相同
        if (queryTagSame(tbWxCorpTagGroup.getTagList())) {
            return AjaxResult.error("标签名不能重复");
        }
        return null;
    }

    /**
     * 校验添加标签是否重复
     */
    private boolean queryTagSame(List<TbWxCorpTag> tagList) {
        if (CollectionUtils.isNotEmpty(tagList)) {
            Set<String> tagSet = Sets.newHashSet();
            List<String> tagArr = Lists.newArrayList();
            for (TbWxCorpTag tbWxCorpTag : tagList) {
                tagSet.add(tbWxCorpTag.getName());
                tagArr.add(tbWxCorpTag.getName());
            }
            return tagSet.size() != tagArr.size();
        }
        return false;
    }

    /**
     * 修改企业客户标签组
     * 在某一次企微更新后当删除标签组下所有标签再添加会接口报错，所以前端传过来的标签必须有一个带有tagId的老标签，除此之外，企微还出了
     * 让api接口无法删除由企微后台创建的标签组及标签
     */
    @RequestMapping("edit")
    public AjaxResult edit(@RequestBody TbWxCorpTagGroup tbWxCorpTagGroup) throws WxErrorException {
        log.info("修改企业客户标签组，入参：{}", tbWxCorpTagGroup);
        // 校验
        AjaxResult x = queryGroupName(tbWxCorpTagGroup, true);
        if (x != null) {
            return x;
        }
        boolean isMatch = Arrays.stream(GroupTagTypeEnum.values()).anyMatch(e -> e.getCode().equals(tbWxCorpTagGroup.getGroupTagType()));
        if (!isMatch) {
            throw new CustomException("标签类型不正确!");
        }
        List<String> collect = tbWxCorpTagGroup.getTagList().stream().map(TbWxCorpTag::getTagId).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            throw new CustomException("暂不支持删除全部标签后再创建新的标签，请至少保留一个原标签");
        }
        tbWxCorpTagGroupService.updateTbWxCorpTagAndCorpTagGroup(tbWxCorpTagGroup, true);
        log.info("修改企业客户标签组完成");
        return AjaxResult.success();
    }

    /**
     * 删除企业客户标签组
     */
    @RequestMapping("/remove/{groupIds}")
    public AjaxResult remove(@PathVariable String[] groupIds, HttpServletRequest request) throws WxErrorException {
        log.info("删除企业客户标签组，入参：{}", Arrays.toString(groupIds));
        LoginUser loginUser = tokenService.getLoginUser(request);
        String username = loginUser.getUsername();
        String corpId = loginUser.getUser().getCorpId();
        tbWxCorpTagGroupService.deleteUserTagByGroupId(groupIds, loginUser.getUser().getUserName(), corpId);
        tbWxCorpTagGroupService.deleteTagGroup(groupIds, username, corpId);
        log.info("删除企业客户标签组完成");
        return AjaxResult.success();
    }

    private boolean queryExists(String corpId, String groupName,String groupId) {
        // 判断tag是否存在
        LambdaQueryWrapper<TbWxCorpTagGroup> wrapper = Wrappers.lambdaQuery(TbWxCorpTagGroup.class)
                .eq(TbWxCorpTagGroup::getGroupName, groupName);
        wrapper.eq(TbWxCorpTagGroup::getStatus, UserConstants.NORMAL);
        wrapper.eq(TbWxCorpTagGroup::getCorpId, corpId);
        if (groupId != null) {
            wrapper.ne(TbWxCorpTagGroup::getGroupId, groupId);
        }
        List<TbWxCorpTagGroup> list = tbWxCorpTagGroupService.list(wrapper);
        return CollectionUtils.isNotEmpty(list);
    }

    /**
     * 查询企业客户标签组列表
     */
    @RequestMapping("/getTagList")
    public TableDataInfo getTagList(@RequestBody TbWxCorpTagGroup tbWxCorpTagGroup) {
        tbWxCorpTagGroup.setStatus(StatusConstants.DEL_FLAG_FALSE);
        List<TbWxCorpTagGroup> list = tbWxCorpTagGroupService.selectTbWxCorpTagGroupList(tbWxCorpTagGroup);
        return getDataTable(list);
    }

    /**
     * 同步企业客户标签组详细信息
     */
    @RequestMapping("/synTag")
    public AjaxResult synTagInfos(String corpId) {
        tbWxCorpTagGroupService.batchSynTags(corpId);
        return AjaxResult.success("同步中，请稍后再次查询");
    }

    /**
     * 查询所有标签组名
     */
    @RequestMapping("/tagNameGroupList")
    public AjaxResult list(String corpId) {
        return AjaxResult.success(tbWxCorpTagGroupService.selectTagGroupListName(corpId));
    }


    /**
     * 查询发送客户查询条件列表
     * @param corpId
     * @return
     */
    @RequestMapping("/getQueryList")
    public AjaxResult getQueryList(String corpId) {
        return AjaxResult.success(iTbDcUserTagsService.getQueryList());
    }

    @GetMapping("/countTagByCategoryId")
    public RemoteResult<Integer> countTagByCategoryId(@RequestParam("categoryId") String categoryId) {
        Integer count = tbWxCorpTagGroupService.lambdaQuery()
                .eq(TbWxCorpTagGroup::getCategoryId, categoryId)
                .eq(TbWxCorpTagGroup::getStatus, CommonConstants.STATUS_NORMAL)
                .count();
        return RemoteResult.data(count);
    }

}
