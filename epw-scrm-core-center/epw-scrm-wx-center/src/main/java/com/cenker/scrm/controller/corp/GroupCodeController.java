package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.biz.group.GroupCodeBiz;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.PageDomain;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.base.TableSupport;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.request.data.GroupCodeStatisticQuery;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.request.group.GroupCodeStatisticsRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.group.*;
import com.cenker.scrm.service.group.IGroupCodeConfigService;
import com.cenker.scrm.service.group.IGroupCodeInfoService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description 企微官方接口最多配置五个群
 * 为了突破限制 后端对该群配置做管理 实际展示到前端页面的并非实际的企微关联信息
 */
@RestController
@RequestMapping("/code/group")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class GroupCodeController extends BaseController {

    private final GroupCodeBiz groupCodeBiz;
    private final IGroupCodeInfoService groupCodeInfoService;
    private final IGroupCodeConfigService groupCodeConfigService;
    private final TokenParseUtil tokenService;

    @RequestMapping("/addGroupCode")
    public AjaxResult addGroupCode(@RequestBody GroupCodeRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        request.setDeptId(loginUser.getDeptId());
        // 保存社群活码信息
        GroupCodeInfo groupCodeInfo = groupCodeInfoService.saveGroupCodeInfo(request);
        // 保存社群列表信息
        groupCodeConfigService.saveGroupCodeConfig(request, false);
        // 生成二维码
        String groupCodeUrl = groupCodeBiz.generateQrCode(request);
        // 更新社群活码地址
        groupCodeInfo.setGroupCodeUrl(groupCodeUrl);
        groupCodeInfoService.updateById(groupCodeInfo);
        GroupCodeInfo groupCodeInfoResult = GroupCodeInfo.builder().id(groupCodeInfo.getId()).groupCodeUrl(groupCodeUrl).build();
        return AjaxResult.success(groupCodeInfoResult);
    }

    @RequestMapping("/listGroupCode")
    public TableDataInfo list(@RequestBody GroupCodeRequest request) {
        startPage();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, request);
        List<GroupCodeListVO> list = groupCodeInfoService.listGroupCode(request);
        return getDataTable(list);
    }

    @RequestMapping("/updateGroupCode")
    public AjaxResult update(@RequestBody GroupCodeRequest request) {
        // 保存社群活码信息
        GroupCodeInfo groupCodeInfo = groupCodeInfoService.saveGroupCodeInfo(request);
        // 社群列表或者入群设置发生变化 需要把原来的企微配置码给重置 以防出现分裂过的群无法再次分裂的问题
        groupCodeBiz.removeWxGroupCode(String.valueOf(groupCodeInfo.getId()));

        // 保存社群列表信息
        groupCodeConfigService.saveGroupCodeConfig(request, true);
        GroupCodeInfo groupCodeInfoResult = GroupCodeInfo.builder().id(groupCodeInfo.getId()).groupCodeUrl(groupCodeInfo.getGroupCodeUrl()).build();
        return AjaxResult.success(groupCodeInfoResult);
    }

    @RequestMapping("/detailGroupCode")
    public AjaxResult detail(@RequestBody GroupCodeRequest request) {
        GroupCodeDetailVO detailVO = groupCodeInfoService.detailGroupCode(request);
        groupCodeBiz.checkNewGroupHasCreate(detailVO);
        return AjaxResult.success(detailVO);
    }

    @RequestMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody GroupCodeRequest request) {
        GroupCodeDataStatisticsVO codeDataStatisticsVO = groupCodeInfoService.getDataStatistics(request);
        return AjaxResult.success(codeDataStatisticsVO);
    }

    @RequestMapping("/getDataStatisticsDetail")
    public TableDataInfo getDataStatisticsDetail(@RequestBody GroupCodeStatisticsRequest request) {
        if (request.getStatisticsType() == 1) {
            startPage();
            return getDataTable(groupCodeInfoService.getDataStatisticsDetailByGroup(request));
        } else {
            PageDomain pageDomain = TableSupport.buildPageRequest();
            Integer pageNum = pageDomain.getPageNum();
            Integer pageSize = pageDomain.getPageSize();
            // 日期倒序反转及分页
            List<GroupCodeDataStatisticsDetailDateVO> list = Lists.reverse(groupCodeInfoService.getDataStatisticsDetailByDate(request));
            TableDataInfo rspData = new TableDataInfo();
            rspData.setTotal(list.size());
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            List<List<GroupCodeDataStatisticsDetailDateVO>> partition = Lists.partition(list, pageSize);
            rspData.setRows(CollectionUtil.isNotEmpty(partition.get(pageNum - 1)) ? partition.get(pageNum - 1) : Lists.newArrayList());
            return rspData;
        }
    }

    @RequestMapping("/removeGroupCode")
    public AjaxResult remove(@RequestBody GroupCodeRequest request) {
        groupCodeBiz.removeGroupCode(request);
        return AjaxResult.success();
    }

    /**
     * 废弃社群活码
     *
     * @param params
     * @return
     */
    @DeleteMapping("/disable")
    public AjaxResult disable(@RequestBody Map<String, String> params) {
        String id = params.get("id");
        if (StrUtil.isBlank(id)) {
            return AjaxResult.success();
        }

        GroupCodeInfo groupCodeInfo = groupCodeInfoService.getById(id);
        if (Objects.nonNull(groupCodeInfo)) {
            String logDesc = String.format("废弃社群活码【%s】", groupCodeInfo.getCodeName());
            LogUtil.recordOperLog(ModuleEnum.COMMUNITY_CODE, BusinessType.DELETE, logDesc);
            groupCodeBiz.removeWxGroupCode(id);

            groupCodeInfoService.lambdaUpdate()
                    .set(GroupCodeInfo::getUseStatus, CommonConstants.NUM_0)
                    .set(GroupCodeInfo::getUpdateTime, DateUtil.date())
                    .eq(GroupCodeInfo::getId, id)
                    .update();
        }

        return AjaxResult.success();
    }

    @RequestMapping("/exportDataStatisticsDetailByGroup")
    public List<GroupCodeDataStatisticsDetailGroupVO> exportDataStatisticsDetailByGroup(@RequestBody GroupCodeStatisticsRequest request) {
        return groupCodeInfoService.getDataStatisticsDetailByGroup(request);
    }

    @RequestMapping("/exportDataStatisticsDetailByDate")
    public List<GroupCodeDataStatisticsDetailDateVO> exportDataStatisticsDetailByDate(@RequestBody GroupCodeStatisticsRequest request) {
        return groupCodeInfoService.getDataStatisticsDetailByDate(request);
    }

    @RequestMapping("/getDataStatisticsTendency")
    public AjaxResult getDataStatisticsTendency(@RequestBody GroupCodeStatisticQuery statisticQuery) {
        if (ObjectUtil.isNull(statisticQuery.getToday())) {
            throw new CustomException(ErrCodeEnum.PARAM_ERROR);
        }
        Date startTime = null;
        Date endTime = DateUtils.getNowDate();
        // 查询时间范围
        if (statisticQuery.getToday()) {
            startTime = DateUtils.getDayStartTime(endTime);
            endTime = DateUtils.getDayEndTime(endTime);
        } else if (ObjectUtil.isNotNull(statisticQuery.getSeven()) && statisticQuery.getSeven()) {
            startTime = DateUtils.addDays(endTime, -7);
        } else if (ObjectUtil.isNotNull(statisticQuery.getThirty()) && statisticQuery.getThirty()) {
            startTime = DateUtils.addDays(endTime, -30);
        } else {
            if (StringUtils.isNotBlank(statisticQuery.getBeginTime()) ^ StringUtils.isNotBlank(statisticQuery.getEndTime())) {
                throw new CustomException(ErrCodeEnum.PARAM_ERROR);
            }
            try {
                startTime = DateUtils.parseDate(statisticQuery.getBeginTime() + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
                endTime = DateUtils.parseDate(statisticQuery.getEndTime() + " 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS);
            } catch (ParseException e) {
                log.error("错误信息:{}", e);
            }
        }
        GroupCodeDataStatisticsVO groupCodeDataStatisticsVO = groupCodeInfoService.getDataStatisticsTendency(startTime, endTime, statisticQuery);
        return AjaxResult.success(groupCodeDataStatisticsVO);
    }

    @RequestMapping("/getGroupCodeByState")
    public RemoteResult<GroupCodeInfo> getGroupCodeByState(@RequestParam("state") String state) {
        GroupCodeInfo groupCodeInfo = groupCodeInfoService.lambdaQuery()
                .eq(GroupCodeInfo::getState, state)
                .eq(GroupCodeInfo::getDeleted, CommonConstants.NOT_DELETED)
                .one();
        return RemoteResult.data(groupCodeInfo);
    }
}
