package com.cenker.scrm.service.impl.kf;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.kf.WkWxKfMsgRespMapper;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgResp;
import com.cenker.scrm.service.kf.IWkWxKfMsgRespService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class WkWxKfMsgRespServiceImpl extends ServiceImpl<WkWxKfMsgRespMapper, WkWxKfMsgResp> implements IWkWxKfMsgRespService {

}
