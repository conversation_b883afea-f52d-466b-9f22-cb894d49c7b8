package com.cenker.scrm.handler.factory;

import com.cenker.scrm.handler.message.*;
import com.cenker.scrm.util.SpringUtils;

/**
 * <AUTHOR>
 * @Date 2022/6/16
 * @Description 群发消息责任链工厂
 */
public class MassMessageHandlerFactory {

    public static MassMessageHandler getMassMessageHandler(){
        // 群发消息前置处理
        MainMassMessageHandler mainMassMessageHandler = SpringUtils.getBean(MainMassMessageHandler.class);
        // 企微发送群发消息
        SendMassMessageHandler sendMassMessageHandler = SpringUtils.getBean(SendMassMessageHandler.class);
        // 数据库记录
        RecordMassMessageHandler recordMassMessageHandler = SpringUtils.getBean(RecordMassMessageHandler.class);
        // 触发停止群发
        CancleGroupSendMessageHandler cancleGroupSendMessageHandler = SpringUtils.getBean(CancleGroupSendMessageHandler.class);
        // 日志记录
        LogMassMessageHandler logMassMessageHandler = SpringUtils.getBean(LogMassMessageHandler.class);

        mainMassMessageHandler.addNextHandler(sendMassMessageHandler);
        sendMassMessageHandler.addNextHandler(recordMassMessageHandler);
        recordMassMessageHandler.addNextHandler(cancleGroupSendMessageHandler);
        cancleGroupSendMessageHandler.addNextHandler(logMassMessageHandler);

        return mainMassMessageHandler;
    }
}
