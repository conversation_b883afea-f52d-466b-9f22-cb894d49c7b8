package com.cenker.scrm.mapper.statistic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomerGroup;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticCustomerGroupSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 数据统计-客户群数据 Mapper
*
* <AUTHOR>
* @since 2024-04-16 17:02
*/
@Mapper
public interface TbStatisticCustomerGroupMapper extends BaseMapper<TbStatisticCustomerGroup> {

    /**
     * 保存统计数据
     * @param statDate
     */
    void saveStatisticDateByDay(@Param("statDate") String statDate);

    /**
     * 汇总数据
     * @param query
     * @return
     */
    StatisticCustomerGroupSummaryVo summary(StatisticSummaryQuery query);

    /**
     * 图表
     * @param query
     * @return
     */
    List<StatisticCustomerGroupSummaryVo> graph(StatisticSummaryQuery query);
}
