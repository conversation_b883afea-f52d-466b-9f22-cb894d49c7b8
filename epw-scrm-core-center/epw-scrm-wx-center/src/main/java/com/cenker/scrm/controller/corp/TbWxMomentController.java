package com.cenker.scrm.controller.corp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.event.SyncMomentDataEvent;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.PageDomain;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.base.TableSupport;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.moment.MomentSyncDto;
import com.cenker.scrm.pojo.dto.moment.QueryMomentLikeAndCommentDTO;
import com.cenker.scrm.pojo.dto.moment.QueryPersonMomentListDTO;
import com.cenker.scrm.pojo.dto.moment.TbWxMomentStatisticsDTO;
import com.cenker.scrm.pojo.entity.moment.TbWxMomentStatistics;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentTaskInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.moment.*;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.moment.ITbWxMomentSendUserService;
import com.cenker.scrm.service.moment.ITbWxMomentService;
import com.cenker.scrm.service.moment.ITbWxMomentStatisticsService;
import com.cenker.scrm.service.moment.ITbWxMomentTaskInfoService;
import com.cenker.scrm.util.BeanUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/1/10
 * @Description 朋友圈
 */
@Slf4j
@RestController
@RequestMapping("/tp/moment")
@RequiredArgsConstructor
public class TbWxMomentController extends BaseController {
    
    private final ITbWxMomentTaskInfoService momentTaskInfoService;
    private final ITbWxMomentStatisticsService momentStatisticsService;
    private final ITbWxMomentSendUserService sendUserService;
    private final ITbWxUserService userService;
    private final TokenParseUtil tokenService;
    private final ITbWxMomentService momentService;
    private final RedisCache redisCache;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final IApprovalService approvalService;

    /**
     * 发朋友圈
     */
    @RequestMapping("/add")
//    @Transactional(rollbackFor = Exception.class)
    public Result add(@RequestBody SendMomentVo sendMomentVo) {
        Integer cnt = momentTaskInfoService.count(new LambdaQueryWrapper<TbWxMomentTaskInfo>()
                .eq(TbWxMomentTaskInfo::getTaskName, sendMomentVo.getTaskName())
                .eq(TbWxMomentTaskInfo::getDelFlag, CommonConstants.NOT_DELETED));
        if (cnt > 0) {
            return Result.error(500, "已存在同名任务，请重新命名");
        }

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, sendMomentVo);
        // 校验是否启用了审批流程
        // 获取用户所在部门是否启用审批
        TbWxCorpConfig config = approvalService.getTbWxCorpConfig(loginUser.getUser().getCorpId());
        boolean enableApproval = getEnableApproval(loginUser.getDeptId()+"", config);
        sendMomentVo.setEnableApproval(enableApproval);
        log.info("创建发朋友圈：设置是否需要审批：{}", enableApproval);
        momentTaskInfoService.add(sendMomentVo);
        return Result.success();
    }
    /**
     * 发朋友圈
     */
    @RequestMapping("/update")
    public Result update(@RequestBody SendMomentVo sendMomentVo) {
        Integer cnt = momentTaskInfoService.count(new LambdaQueryWrapper<TbWxMomentTaskInfo>()
                .eq(TbWxMomentTaskInfo::getTaskName, sendMomentVo.getTaskName())
                .eq(TbWxMomentTaskInfo::getDelFlag, CommonConstants.NOT_DELETED)
                // 排除自己 id 不等于当前id
                .ne(TbWxMomentTaskInfo::getId, sendMomentVo.getId())
        );
        if (cnt > 0) {
            return Result.error(500, "已存在同名任务，请重新命名");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, sendMomentVo);
        // 校验是否启用了审批流程
        // 获取用户所在部门是否启用审批
        TbWxMomentTaskInfo byId = this.momentTaskInfoService.getById(sendMomentVo.getId());
        sendMomentVo.setEnableApproval(byId.isEnableApproval());
        log.info("更新发朋友圈：设置是否需要审批：{}", byId.isEnableApproval());


        // 异常状态的任务不允许修改
        if (ApprovalStatusEnum.REJECTED.getStatus().equals(byId.getCheckStatus())
                || ApprovalStatusEnum.REVOKED.getStatus().equals(byId.getCheckStatus())
                || ApprovalStatusEnum.PENDING_EXEC.getStatus().equals(byId.getCheckStatus())
                || ApprovalStatusEnum.EXEC_EXCEPTION.getStatus().equals(byId.getCheckStatus())
        ) {
            momentTaskInfoService.update(sendMomentVo);
        } else {
            return Result.error(500, "非法状态，不允许修改");
        }

        return Result.success();
    }

    @RequestMapping("/list")
    public TableDataInfo<SendMomentListVO> list(@RequestBody SendMomentVo sendMomentVo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, sendMomentVo);

        // 附件信息使用json存储 不方便使用模糊查询 代码处理会导致分页失效
        if (StringUtils.isEmpty(sendMomentVo.getContent())) {
            startPage();
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        sendMomentVo.setUserId(loginUser.getUser().getUserId());
        log.info("【查询企业发朋友圈列表】入参：{}", sendMomentVo);
        List<SendMomentListVO> list = momentTaskInfoService.getMomentList(sendMomentVo, pageNum, pageSize);
        return getDataTable(list);
    }

    @RequestMapping("/list/v2")
    public TableDataInfo listV2(@RequestBody SendMomentVo sendMomentVo) {
        // 附件信息使用json存储 不方便使用模糊查询 代码处理会导致分页失效
        if (StringUtils.isEmpty(sendMomentVo.getContent())) {
            startPage();
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCreateByPermission(loginUser.getUser(), sendMomentVo);
        sendMomentVo.setUserId(loginUser.getUser().getUserId());
        List<SendMomentListVO> list = momentTaskInfoService.getMomentList(sendMomentVo, pageNum, pageSize);
        return getDataTable(list);
    }



    @RequestMapping("/detail/{id}")
    public Result<SendMomentListVO> detail(@PathVariable("id") String id) {
        log.info("【查看发朋友圈详情】入参：{}", id);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SendMomentListVO sendMomentListVo = momentTaskInfoService.getMoment(id, loginUser.getUser().getUserId());
        log.info("【查看发朋友圈详情】出参：{}", sendMomentListVo);
        return Result.success("操作成功", sendMomentListVo);
    }

    /**
     * 获取发表成员列表
     */
    @RequestMapping("/getMomentSendUserList")
    public TableDataInfo getMomentSendUserList(@RequestBody SendMomentVo sendMomentVo) {
        startPage();
        List<SendMomentUserListVo> list = sendUserService.getMomentSendUserList(sendMomentVo);
        return getDataTable(list);
    }

    /**
     * 提醒发送 提醒全部携带任务id 单个则数据id 优先单个
     */
    @RequestMapping("/remindToSend")
    public AjaxResult remindToSend(@RequestBody SendMomentVo sendMomentVo) {
        String msg;
        if (StringUtils.isNotEmpty(sendMomentVo.getId())) {
            msg = sendUserService.remindToSendById(sendMomentVo.getId());
        } else {
            // 此时提醒全部
            TbWxMomentTaskInfo taskInfo = momentTaskInfoService.getById(sendMomentVo.getMomentTaskId());
            if (taskInfo != null) {
                sendUserService.remindToSendAll(taskInfo);
                return AjaxResult.success();
            }
            msg = "数据不存在，请检查重试";
        }
        if (StringUtils.isNotEmpty(msg)) {
            return AjaxResult.error(msg);
        }
        return AjaxResult.success();
    }

    /**
     * 同步朋友圈数据
     */
    @RequestMapping("/synchronizeMomentList")
    public AjaxResult synchronizeMomentList(@RequestBody SendMomentVo sendMomentVo) {
        try {
            // 入参时间范围为日期格式yyyy-MM-dd，需转为时间格式yyyy-MM-dd HH:mm:ss
            if (StrUtil.isNotBlank(sendMomentVo.getBeginTime())) {
                String startTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(sendMomentVo.getBeginTime())));
                sendMomentVo.setBeginTime(startTime);
            }

            if (StrUtil.isNotBlank(sendMomentVo.getEndTime())) {
                String endTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(sendMomentVo.getEndTime())));
                sendMomentVo.setEndTime(endTime);
            }
            MomentSyncDto momentSyncDto = MomentSyncDto.builder().filterType(1).syncContentType("2,3")
                    .startTime(sendMomentVo.getBeginTime()).endTime(sendMomentVo.getEndTime()).build();
            applicationEventPublisher.publishEvent(new SyncMomentDataEvent(momentSyncDto));
        } catch (Exception e) {
            log.error("同步朋友圈数据失败", e);
            return AjaxResult.error("同步朋友圈数据失败！");
        }

        return AjaxResult.success("同步成功");
    }

    /**
     * 同步数据-获取任务创建结果
     */
    @Deprecated
    @RequestMapping("/getTaskResult")
    public AjaxResult getTaskResult(@RequestBody SendMomentVo sendMomentVo) {
        List<SendMomentListVO> list = Lists.newArrayList();
        List<String> ids = sendMomentVo.getIds();
        for (String id : ids) {
            MomentSyncDto momentSyncDto = MomentSyncDto.builder()
                    .filterType(1)
                    .syncContentType("2,3")
                    .momentTaskId(id)
                    .build();
            applicationEventPublisher.publishEvent(new SyncMomentDataEvent(momentSyncDto));
        }
        return AjaxResult.success(list);
    }

    /**
     * 同步数据-获取执行情况
     */
    @RequestMapping("/getTaskUserResult")
    public AjaxResult getTaskUserResult(@RequestBody SendMomentVo sendMomentVo) {
        MomentSyncDto momentSyncDto = MomentSyncDto.builder()
                .filterType(1)
                .syncContentType("2,3")
                .momentTaskId(sendMomentVo.getId())
                .build();

        applicationEventPublisher.publishEvent(new SyncMomentDataEvent(momentSyncDto));
        return AjaxResult.success();
    }

    /**
     * 同步朋友圈数据情况
     */
    @PostMapping("/syncMomentData")
    public AjaxResult syncMomentData(@RequestBody MomentSyncDto params) {
        try {
            // 入参时间范围为日期格式yyyy-MM-dd，需转为时间格式yyyy-MM-dd HH:mm:ss
            if (StrUtil.isNotBlank(params.getStartTime())) {
                String startTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(params.getStartTime())));
                params.setStartTime(startTime);
            }

            if (StrUtil.isNotBlank(params.getEndTime())) {
                String endTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(params.getEndTime())));
                params.setEndTime(endTime);
            }

            applicationEventPublisher.publishEvent(new SyncMomentDataEvent(params));
        } catch (Exception e) {
            log.error("同步朋友圈数据失败", e);
            return AjaxResult.error("参数错误！");
        }

        return AjaxResult.success();
    }

    /**
     * 数据统计
     */
    @RequestMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody SendMomentVo sendMomentVo) {
        log.info("【朋友圈详情】开始查询朋友圈【任务ID：{}】的指标数据!", sendMomentVo.getId());
        // 2025-01-09 从原来的调用 momentTaskInfoService.getDataStatistics 改成直接查询 tb_wx_moment_statistics 表中对应的指标数据
        TbWxMomentStatistics statistics = momentStatisticsService.getOne(new LambdaQueryWrapper<TbWxMomentStatistics>()
                .eq(TbWxMomentStatistics::getTaskId, sendMomentVo.getId()));
        TbWxMomentStatisticsDTO statisticsDTO = TbWxMomentStatisticsDTO.builder().taskId(Long.valueOf(sendMomentVo.getId())).build();

        if (statistics != null) {
            BeanUtils.copyProperties(statistics, statisticsDTO);
        }
        log.info("【朋友圈详情】完成查询朋友圈【任务ID：{}】的指标数据!", sendMomentVo.getId());
        return AjaxResult.success(statisticsDTO);
    }

    /**
     * 获取消息预见客户数
     */
    @RequestMapping("/getMomentPredictedNum")
    public AjaxResult getMomentPredictedNum(@RequestBody SendMomentVo sendMomentVo) {
        Integer num = 0;
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sendMomentVo.getDepartmentList())) {
            // 如果选择了部门
            List<TbWxUser> list = userService.list(new LambdaQueryWrapper<TbWxUser>()
                    .eq(TbWxUser::getCorpId, sendMomentVo.getCorpId())
                    .in(TbWxUser::getMainDepartment, sendMomentVo.getDepartmentList())
                    .eq(TbWxUser::getDelFlag, 1));
            if (CollectionUtils.isEmpty(list)) {
                result.put("predictedNum", num);
                return AjaxResult.success(result);
            }
            List<String> userIdList = list.stream().map(TbWxUser::getUserid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sendMomentVo.getUserList())) {
                List<String> allUserIds = Lists.newArrayList();
                allUserIds.addAll(userIdList);
                allUserIds.addAll(sendMomentVo.getUserList());
                List<String> collect = allUserIds.stream().distinct().collect(Collectors.toList());
                sendMomentVo.setUserList(collect);
            } else {
                sendMomentVo.setUserList(userIdList);
            }
        }
        // 根据选择成员及标签获取预见客户
        num = momentTaskInfoService.getMomentPredictedNum(sendMomentVo);
        result.put("predictedNum", num);
        return AjaxResult.success(result);
    }

    /**
     * 取消发送朋友圈
     */
    @RequestMapping("/remove")
    public Result remove(@RequestBody SendMomentVo sendMomentVo) {
        return momentTaskInfoService.cancelSendMomentTask(sendMomentVo);
    }

    /**
     * 获取点赞评论列表
     */
    @RequestMapping("/getMomentSendCusList")
    public TableDataInfo getMomentSendCusList(@RequestBody SendMomentVo sendMomentVo) {
        List<SendMomentUserListVo> list = Lists.newArrayList();
        // 根据发送者表id查询对应的点赞评论
        TbWxMomentSendUser sendUser = sendUserService.getById(sendMomentVo.getId());
        if (sendUser != null && sendUser.getPublishStatus() == StatusConstants.PUBLISH_MOMENT_TRUE) {
            sendMomentVo.setMomentTaskId(sendUser.getMomentTaskId());
            sendMomentVo.setUserId(sendUser.getUserId());
            startPage();
            list = sendUserService.getMomentSendCusList(sendMomentVo);
            return getDataTable(list);
        }
        return getDataTable(list);
    }


    /**
     * 查询个人发表朋友圈列表
     */
    @RequestMapping("/getPersonalMomentList")
    public TableDataInfo getPersonalMomentList(QueryPersonMomentListDTO dto) {
        startPage();
        filterWkUserIdPermission(tokenService.getLoginUser(ServletUtils.getRequest()).getUser(), dto);
        List<PersonMomentListVO> list = momentService.getPersonalMomentList(getCurrentUser(),dto);
        return getDataTable(list);
    }

    /**
     * 查询个人朋友圈点赞和评论用户列表
     */
    @RequestMapping("/getMomentSendCusListByMomentId")
    public AjaxResult getMomentSendCusListByMomentId(QueryMomentLikeAndCommentDTO dto) {
        startPage();
        List<MomentUserListVO> list = momentService.getMomentSendCusListByMomentId(getCurrentUser(),dto);
        return AjaxResult.success(list);
    }

    /**
     * 查询朋友圈可见用户列表
     */
    @RequestMapping("/getMomentVisibleUserList")
    public TableDataInfo getMomentVisibleUserList(QueryMomentLikeAndCommentDTO dto) {
        startPage();
       return getDataTable(momentService.getMomentVisibleUserList(getCurrentUser(),dto));
    }

    /**
     * 同步朋友圈数据
     */
    @RequestMapping("/synchronizeAllMoment")
    public AjaxResult synchronizeAllMoment() {
        CurrentUserDTO currentUser = getCurrentUser();
        redisCache.expire(CacheKeyConstants.MOMENT_LAST_SYNC_TIME_KEY + currentUser.getCorpId(), 0);

        MomentSyncDto momentSyncDto = MomentSyncDto.builder()
                .filterType(2)
                .syncContentType("2,3")
                .creator(currentUser.getUserId())
                .build();
        applicationEventPublisher.publishEvent(new SyncMomentDataEvent(momentSyncDto));

        return AjaxResult.success("同步成功");
    }

    /**
     *  同步朋友圈点赞和评论数
     */
    @RequestMapping("/synchronizeLikeAndCommentCount")
    public AjaxResult synchronizeLikeAndCommentCount() {
        try {
            momentService.synchronizeLikeAndCommentCount(getCurrentUser());
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success("同步成功");
    }

    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return momentTaskInfoService.approve(approvalVO, loginUser);
    }
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return momentTaskInfoService.revoked(approvalVO, loginUser);
    }
    /**
     * 获取当前用户
     */
    protected CurrentUserDTO getCurrentUser() {
       // return new CurrentUserDTO("wwfebce5c54c7af782","1");
         SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
         return new CurrentUserDTO(user.getCorpId(),user.getUserId());
    }

}
