package com.cenker.scrm.service.open;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.open.AppGoodDTO;
import com.cenker.scrm.pojo.entity.open.store.OpenApiAppGood;
import com.cenker.scrm.pojo.vo.open.store.AppGoodVO;


import java.util.List;

public interface IOpenApiAppGoodService extends IService<OpenApiAppGood> {

    /**
     * 获取商品列表
     * @param appGoodDTO
     * @return
     */
    List<AppGoodVO> listGood(AppGoodDTO appGoodDTO);
}
