package com.cenker.scrm.service.impl.subscr;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.mapper.subscr.BuSectionRadarMapper;
import com.cenker.scrm.pojo.entity.subscr.BuSectionRadar;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarVO;
import com.cenker.scrm.service.subscr.IBuSectionRadarService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 雷达服务实现类
 * 提供对雷达的CRUD操作及自定义业务方法
 */
@Slf4j
@AllArgsConstructor
@Service
public class BuSectionRadarServiceImpl extends ServiceImpl<BuSectionRadarMapper, BuSectionRadar> implements IBuSectionRadarService {
    @Override
    public List<BuSectionRadarVO> getBuSectionRadarVOS(BuSectionRadarQuery query) {
        List<BuSectionRadarVO> voList = baseMapper.getBuSectionRadarVOS(query);
        if (CollectionUtil.isNotEmpty(voList)) {
            voList.forEach(radar -> {
                TbWxRadarContent content = radar.getTbWxRadarContent();
                if (content!= null) {
                    StringBuilder urlBuilder = new StringBuilder();
                    urlBuilder.append(RadarConfig.getContentPage()).append("?id=").append(content.getId());
                    urlBuilder.append("&clickSource=8");
                    urlBuilder.append("&staffId=").append("");
                    content.setUrl(urlBuilder.toString());
                }
            });
        }
        return voList;
    }
}