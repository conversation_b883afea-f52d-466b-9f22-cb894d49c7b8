package com.cenker.scrm.controller.session;


import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.task.SensWordAlarmTask;
import com.cenker.scrm.model.base.BaseController;

import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.*;
import com.cenker.scrm.pojo.entity.session.CkSessionHotCheckMapping;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordInfo;
import com.cenker.scrm.pojo.entity.session.CkSessionSensCheckMapping;
import com.cenker.scrm.pojo.entity.session.CkSessionSensRuleInfo;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.session.CkSessionHotWordInfoVO;
import com.cenker.scrm.pojo.vo.session.CkSessionSensRuleInfoVO;
import com.cenker.scrm.service.session.ICkSessionSensCheckMappingService;
import com.cenker.scrm.service.session.ICkSessionSensRuleInfoService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 敏感规则信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@Slf4j
@RequestMapping("/session/sensitive")
public class CkSessionSensRuleInfoController extends BaseController
{
    @Autowired
    private ICkSessionSensRuleInfoService ckSessionSensRuleInfoService;
    @Autowired
    private ICkSessionSensCheckMappingService ckSessionSensCheckMappingService;


    @Autowired
    private SensWordAlarmTask sensWordAlarmTask;


    /**
     * 查询敏感规则信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QrySensRuleDto qrySensRuleDto)
    {
        startPage();
        PageHelper.orderBy(" g.create_time desc ");
        List<CkSessionSensRuleInfoVO> listVo = new ArrayList<>();
        List<CkSessionSensRuleInfo> list = ckSessionSensRuleInfoService.selectCkSessionSensRuleInfoList(qrySensRuleDto);
        for (CkSessionSensRuleInfo wordInfo:list) {
            //BeanUtils.copyBeanProp(wordInfo,wordInfoVO);
            CkSessionSensRuleInfoVO sensRuleInfoVO = new CkSessionSensRuleInfoVO();
            sensRuleInfoVO.setRuleId(wordInfo.getRuleId());
            sensRuleInfoVO.setRuleName(wordInfo.getRuleName());
            sensRuleInfoVO.setSensitiveWords(wordInfo.getSensitiveWords());
            sensRuleInfoVO.setInterceptType(wordInfo.getInterceptType());
            sensRuleInfoVO.setActTypes(wordInfo.getActTypes());
            sensRuleInfoVO.setStatus(wordInfo.getStatus());
            sensRuleInfoVO.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,wordInfo.getCreateTime()));
            Date updDate = wordInfo.getUpdateTime();
            if(updDate !=null) {
                sensRuleInfoVO.setUpdTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,updDate ));
            }
            sensRuleInfoVO.setCreateUserName(wordInfo.getNickName());
            sensRuleInfoVO.setSensitiveWordNum(wordInfo.getSensitiveWordNum());
            CkSessionSensCheckMapping checkMapping = new CkSessionSensCheckMapping();
            checkMapping.setRuleId(wordInfo.getRuleId());
            List<CkSessionSensCheckMapping> listCheck = ckSessionSensCheckMappingService.selectCkSessionSensCheckMappingList(checkMapping);
            List<UserDto> checkList = new ArrayList<>();
            for (CkSessionSensCheckMapping mapping:listCheck) {
                UserDto userDto = new UserDto();
                userDto.setUserId(mapping.getCheckUserId());
                userDto.setUserName(mapping.getName());
                userDto.setAvatar(mapping.getAvatar());
                checkList.add(userDto);
            }
            sensRuleInfoVO.setUserConditionList(checkList);
            listVo.add(sensRuleInfoVO);
        }
        TableDataInfo backTab = getDataTable(listVo);
        backTab.setTotal(new PageInfo(list).getTotal());
        return backTab;

    }

    /**
     * 获取敏感规则信息详细信息
     */
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo(Long ruleId)
    {
        CkSessionSensRuleInfo wordInfo = ckSessionSensRuleInfoService.selectCkSessionSensRuleInfoByRuleId(ruleId);
        CkSessionSensRuleInfoVO sensRuleInfoVO = new CkSessionSensRuleInfoVO();
        sensRuleInfoVO.setRuleId(wordInfo.getRuleId());
        sensRuleInfoVO.setRuleName(wordInfo.getRuleName());
        sensRuleInfoVO.setSensitiveWords(wordInfo.getSensitiveWords());
        sensRuleInfoVO.setInterceptType(wordInfo.getInterceptType());
        sensRuleInfoVO.setActTypes(wordInfo.getActTypes());
        sensRuleInfoVO.setStatus(wordInfo.getStatus());
        sensRuleInfoVO.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,wordInfo.getCreateTime()));
        Date updTime = wordInfo.getUpdateTime();
        if(updTime !=null) {
            sensRuleInfoVO.setUpdTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,updTime));
        }
        sensRuleInfoVO.setCreateUserName(wordInfo.getNickName());
        CkSessionSensCheckMapping checkMapping = new CkSessionSensCheckMapping();
        checkMapping.setRuleId(wordInfo.getRuleId());
        List<CkSessionSensCheckMapping> listCheck = ckSessionSensCheckMappingService.selectCkSessionSensCheckMappingList(checkMapping);
        List<UserDto> checkList = new ArrayList<>();
        for (CkSessionSensCheckMapping mapping:listCheck) {
            UserDto userDto = new UserDto();
            userDto.setUserId(mapping.getCheckUserId());
            userDto.setUserName(mapping.getName());
            userDto.setAvatar(mapping.getAvatar());
            checkList.add(userDto);
        }
        sensRuleInfoVO.setUserConditionList(checkList);
        sensRuleInfoVO.setSensitiveWordNum(wordInfo.getSensitiveWordNum());
        return AjaxResult.success(sensRuleInfoVO);
    }

    /**
     * 新增敏感规则信息
     */
    // @Log(title = "新增敏感规则信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AddSensRuleDto addSensRuleDto)
    {
        try {
            ckSessionSensRuleInfoService.insertCkSessionSensRuleInfo(addSensRuleDto);
            return AjaxResult.success("新增成功");
        }catch (Exception e){
            log.error("新增敏感规则信息失败",e);
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 修改敏感规则信息
     */
    // @Log(title = "保存敏感规则信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AddSensRuleDto addSensRuleDto)
    {
        try {
            ckSessionSensRuleInfoService.updateCkSessionSensRuleInfo(addSensRuleDto);
            return AjaxResult.success("保存成功");
        }catch (Exception e){
            log.error("保存敏感规则信息",e);
            return AjaxResult.error("保存失败");
        }
    }

    /**
     * 废弃规则
     */
    // @Log(title = "更新敏感规则信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updStatus")
    public AjaxResult updStatus(@RequestBody UpdSensRuleStatusDto updSensRuleStatusDto)
    {
        ckSessionSensRuleInfoService.updStatus(updSensRuleStatusDto);
        return AjaxResult.success("废弃成功");
    }

    @PostMapping("/import")
    // @Log(title = "导入敏感规则信息", businessType = BusinessType.IMPORT)
    public AjaxResult importExcel(@RequestBody List<AddSensRuleDto> wordList)
    {
        try {
            ckSessionSensRuleInfoService.insertCkSessionHotWordInfoList(wordList);
            return AjaxResult.success("导入成功");
        }catch (Exception e){
            log.error("###########导入热词失败",e);
        }
        return AjaxResult.error("导入失败");
    }

    //// @Log(title = "统计敏感数", businessType = BusinessType.UPDATE)
    @PostMapping("/cntRuleNameRep")
    public Long cntRuleNameRep(@RequestBody CkSessionSensRuleInfo ckSessionSensRuleInfo)
    {
        //sensWordAlarmTask.statSenWordAlarm();
        return ckSessionSensRuleInfoService.cntRuleNameIsRep(ckSessionSensRuleInfo);
    }
}
