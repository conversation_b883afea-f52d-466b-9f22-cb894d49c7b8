package com.cenker.scrm.service.tag;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.tag.TagRuleListDto;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxAutoTag;

import java.util.List;

/**
 * 企业客户标签组Service接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface ITbWxAutoTagService extends IService<TbWxAutoTag> {

    void saveRule(TbWxAutoTag tbWxAutoTag, String corpId, String userId);

    void updateRule(TbWxAutoTag tbWxAutoTag, String corpId, String userId);

    TbWxAutoTag findById(Long autoTagId);

    List<TbWxAutoTag> selectDataList(TagRuleListDto tagRuleListDto);

    List<String> queryAutoTagUserList(String condition);

    void deleteRelatedTagByRule(TbWx<PERSON>utoTag autoTag, SysUser user);
}
