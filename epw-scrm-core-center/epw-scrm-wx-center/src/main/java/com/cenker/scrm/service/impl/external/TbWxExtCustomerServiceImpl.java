package com.cenker.scrm.service.impl.external;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.biz.customer.CustomerConditionBizHandler;
import com.cenker.scrm.biz.manager.WxMqSendMessageManager;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.constants.*;
import com.cenker.scrm.enums.*;
import com.cenker.scrm.esb.EsbClient;
import com.cenker.scrm.esb.SqlServiceClient;
import com.cenker.scrm.event.SynCustomerEvent;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.mapper.external.TbWxAccessAuthRecordMapper;
import com.cenker.scrm.mapper.external.TbWxCustomerAuthRecordMapper;
import com.cenker.scrm.mapper.external.TbWxExtCustomerMapper;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.dto.external.CustomerAuthDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.export.ChurnCustomerExport;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO;
import com.cenker.scrm.pojo.vo.contact.CorpAddWayVO;
import com.cenker.scrm.pojo.vo.contact.CorpUserInfoVO;
import com.cenker.scrm.pojo.vo.external.*;
import com.cenker.scrm.pojo.vo.group.CustomerGroupVO;
import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.service.contact.ITbWxContactService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.external.*;
import com.cenker.scrm.service.tag.*;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.external.contact.FollowedUser;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.UUID;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 企业微信外部客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class TbWxExtCustomerServiceImpl extends ServiceImpl<TbWxExtCustomerMapper, TbWxExtCustomer> implements TbWxExtCustomerService {

    private final TbWxExtFollowUserService tbWxExtFollowUserService;
    private final ITbWxUserService tbWxUserService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ITbWxCorpTagService tbWxCorpTagService;
    private final TbWxExtFollowUserTagService tbWxExtFollowUserTagService;
    private final ITbWxCustomerTrajectoryService trajectoryService;
    private final ITbWxUserService userService;
    private final ITbWxContactService contactService;
    private final ITbWxCorpTagGroupService tagGroupService;
    private final ISysUserService sysUserService;
    private final WxCpFeign wxCpFeign;
    private final WxMqSendMessageManager mqSendMessageManager;
    private final TbWxCustomerAuthRecordMapper tbWxCustomerAuthRecordMapper;
    private final TbWxAccessAuthRecordMapper tbWxAccessAuthRecordMapper;
    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    private final SqlServiceClient sqlServiceClient;
    private final IVFScrmLabelService vfScrmLabelService;
    private final ITStaffService sensitiveCustInfoService;
    private final CustomerConditionBizHandler customerConditionBizHandler;
    private final ITbWxExtCustomerWorkService tbWxExtCustomerWorkService;
    private final MqSendMessageManager mqSendMessageManager2;
    private final ITbDcUserTagsService tbDcUserTagsService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExtCustomer(WxCpExternalContactInfo externalContactInfo, String corpId, String userId) {
        TbWxExtCustomer tbWxExtCustomer = new TbWxExtCustomer();
        BeanUtils.copyProperties(externalContactInfo.getExternalContact(), tbWxExtCustomer);
        tbWxExtCustomer.setStatus(UserStatus.OK.getCode());
        tbWxExtCustomer.setCorpId(corpId);
        tbWxExtCustomer.setCreateBy(Constants.DEFAULT_USER);
        tbWxExtCustomer.setSource(Constants.SOURCE_CP);
        List<TbWxExtFollowUser> followUsers = new ArrayList<>();
        // StringBuilder tag = new StringBuilder();
        String addFirstUserId = "";
        List<TbWxExtFollowUserTag> extFollowUserTags = new ArrayList<>();
        // 客户详情2.0 标签同名加标签名
        List<ExternalUserTagVO> tagVos = new ArrayList<>();
        // 客户详情2.0 共同维护手机号
        String mobiles = "";
        for (FollowedUser user : externalContactInfo.getFollowedUsers()) {
            TbWxExtFollowUser extFollowUser = new TbWxExtFollowUser();
            BeanUtils.copyProperties(user, extFollowUser);

            extFollowUser.setCorpId(corpId);
            extFollowUser.setCreateBy(Constants.DEFAULT_USER);
            extFollowUser.setStatus(UserStatus.OK.getCode());
            extFollowUser.setCreateTime(new Date(user.getCreateTime() * 1000));
            extFollowUser.setExternalUserId(externalContactInfo.getExternalContact().getExternalUserId());

            /**
             * 由于存在客户删除员工，重新添加获取的addWay会为0，因此在存在数据的情况下，修改当前获取的addWay为上次流失的 2022-04-20
             */
            if ("0".equals(extFollowUser.getAddWay()) && userId.equals(extFollowUser.getUserId())) {
                TbWxExtFollowUser tbWxExtFollowUser = tbWxExtFollowUserService.getOne(new LambdaQueryWrapper<TbWxExtFollowUser>()
                        .eq(TbWxExtFollowUser::getCorpId, corpId)
                        .eq(TbWxExtFollowUser::getUserId, userId)
                        .eq(TbWxExtFollowUser::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                        .select(TbWxExtFollowUser::getAddWay)
                        .orderByDesc(TbWxExtFollowUser::getCreateTime)
                        .last("limit 1")
                );
                if (tbWxExtFollowUser != null) {
                    extFollowUser.setAddWay(tbWxExtFollowUser.getAddWay());
                }

            }

            followUsers.add(extFollowUser);
            addFirstUserId = extFollowUser.getUserId();
//            StringBuilder followUserTag = new StringBuilder();
//            StringBuilder followUserSelfTag = new StringBuilder();
            // 记录客户标签
            if (user.getTags() != null && user.getTags().length > 0) {
                for (FollowedUser.Tag tag1 : user.getTags()) {
                    ExternalUserTagVO externalUserTagVo = new ExternalUserTagVO();
                    BeanUtils.copyProperties(tag1, externalUserTagVo);
                    // tag.append(tag1.getTagName() + ",");
                    //type 为1 表示企业标签、为2 表示个人标签
                    if (tag1.getType() == StatusConstants.CORP_TAG) {
//                        followUserTag.append(tag1.getTagName()).append(",");
                        // 只添加企业标签 来做统计
                        tagVos.add(externalUserTagVo);
                    } else {
//                        followUserSelfTag.append(tag1.getTagName()).append(",");
                    }
                    buildCorpUserTag(corpId, tbWxExtCustomer, extFollowUserTags, user, tag1);
                }
            }

            // 记录用户手机号码
            if (user.getRemarkMobiles() != null && user.getRemarkMobiles().length > 0) {
                StringBuilder mobilesBud = new StringBuilder();
                Sets.newHashSet(user.getRemarkMobiles()).forEach(new Consumer<String>() {
                    @Override
                    public void accept(String value) {
                        mobilesBud.append(value).append(",");
                    }
                });
                String remarkmobiles = mobilesBud.toString();
                remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(","));
                //remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(",")-1);
                extFollowUser.setRemarkMobiles(remarkmobiles);
                if (StringUtils.isEmpty(mobiles)) {
                    mobiles = remarkmobiles;
                } else {
                    mobiles = mobiles + "," + remarkmobiles;
                }
            }

//            extFollowUser.setTag(followUserTag.toString());
//            extFollowUser.setSelfTag(followUserSelfTag.toString());
        }
        log.info("【外部联系人】添加外部联系人，企微接口返回followUser,{}", followUsers);
        // 客户详情2.0 所有员工修改手机号会同步到一张表内 去重 2022-03-03
        if (StringUtils.isNotEmpty(mobiles)) {
            mobiles = Arrays.stream(mobiles.split(",")).distinct().collect(Collectors.joining(","));
        }
        tbWxExtCustomer.setMobiles(mobiles);
        // 客户详情2.0 标签同名加标签名
        List<String> tag = setTagVos(tagVos);
        if (CollectionUtils.isNotEmpty(tag)) {
            tbWxExtCustomer.setTag(JSON.toJSONString(tag));
        }

        // 判断followUser是否存在表中
        // QueryWrapper wrapper = new QueryWrapper();
        List<String> extUserIds = Lists.newArrayList(tbWxExtCustomer.getExternalUserId());
        List<CustomerFollowVO> existUserFollows = tbWxExtFollowUserService.queryUserIdByCorpIdAndExtUserId(extUserIds, corpId);
        List<TbWxExtFollowUser> addFollowUserList;
        Map<String, CustomerFollowVO> existMap = new HashMap<>(6);
        if (existUserFollows.size() > 0) {
            existMap = existUserFollows.stream().collect(
                    Collectors.toMap(x -> x.getUserId() + x.getExtUserId(), Function.identity(), (x, y) -> y));
            /**
             * 客户删除员工 followUser还会返回该员工信息 导致同步等操作时系统会误以为这是正常的数据将该已流失的数据重新返回为正常
             * 处理方式是在有数据的情况下不新增followUser
             * 然后新增客户回调是重新拉取数据  会和这个逻辑冲突 所以这里排除当前添加人组合
             */
            existMap.put(userId + tbWxExtCustomer.getExternalUserId(), null);
        }
        Map<String, CustomerFollowVO> finalExistMap = existMap;
        log.info("【外部联系人】添加外部联系人，数据库followUser,{}", finalExistMap);
        addFollowUserList = followUsers.stream().filter(tbWxExtFollowUser ->
                finalExistMap.get(tbWxExtFollowUser.getUserId() + tbWxExtFollowUser.getExternalUserId()) == null).collect(Collectors.toList());
        // 查询表中数据存在该客户信息
        if (baseMapper.selectOne(new LambdaQueryWrapper<TbWxExtCustomer>().eq(TbWxExtCustomer::getExternalUserId, tbWxExtCustomer.getExternalUserId())) == null) {
            tbWxExtCustomer.setFirstAddUserId(addFirstUserId);
            save(tbWxExtCustomer);
        }
        /**
         * 客户画像0.3 客户存在系统时为协同逻辑 新的员工添加客户不应改动原先的协同数据 2022-03-24
         * 完全流失状态需要更新 只更新状态 2022-04-15
         */
        // else {
        //     wrapper.clear();
        //     wrapper.eq("external_user_id", tbWxExtCustomer.getExternalUserId());
        //     update(tbWxExtCustomer, wrapper);
        // }
        else {
            update(new LambdaUpdateWrapper<TbWxExtCustomer>()
                    .set(TbWxExtCustomer::getStatus, UserStatus.OK.getCode())
                    .eq(TbWxExtCustomer::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                    .eq(TbWxExtCustomer::getCorpId, tbWxExtCustomer.getCorpId())
            );
        }
        if (addFollowUserList.size() > 0) {
            // tbWxExtFollowUserService.saveBatch(followUsers);
            // 直接添加企微返回的follerUser会导致多条记录 只添加变更的 2022-02-21
            tbWxExtFollowUserService.saveBatch(addFollowUserList);
        }
        if (extFollowUserTags.size() > 0) {
            LambdaQueryWrapper<TbWxExtFollowUserTag> warpper = new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                    .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
                    .eq(TbWxExtFollowUserTag::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                    .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                    .in(TbWxExtFollowUserTag::getUserId, extFollowUserTags.stream().map(TbWxExtFollowUserTag::getUserId).collect(Collectors.toList()));
            tbWxExtFollowUserTagService.removeByWrapperWithLog(warpper);
            tbWxExtFollowUserTagService.saveBatchWithLog(extFollowUserTags);
        }
    }

    private List<String> setTagVos(List<ExternalUserTagVO> tagVos) {
        List<String> tags = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tagVos)) {
            // 标签id 去重
            List<ExternalUserTagVO> tagList = tagVos.stream()
                    .collect(Collectors.collectingAndThen
                            (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExternalUserTagVO::getTagId))), ArrayList::new
                            ));
            List<ExternalUserTagVO> sameTagNameList = tagList.stream().filter(distinctByKey(ExternalUserTagVO::getTagName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameTagNameList)) {
                List<String> sameTagNameStrList = sameTagNameList.stream().map(ExternalUserTagVO::getTagName).collect(Collectors.toList());
                for (ExternalUserTagVO externalUserTagVo : tagList) {
                    if (sameTagNameStrList.contains(externalUserTagVo.getTagName())) {
                        externalUserTagVo.setTagName(externalUserTagVo.getGroupName() + "：" + externalUserTagVo.getTagName());
                    }
                }
            }
            return tagList.stream().map(ExternalUserTagVO::getTagName).collect(Collectors.toList());
        }
        return tags;
    }

    private void buildCorpUserTag(String corpId, TbWxExtCustomer tbWxExtCustomer, List<TbWxExtFollowUserTag> extFollowUserTags, FollowedUser user, FollowedUser.Tag tag1) {
        TbWxExtFollowUserTag userTag = new TbWxExtFollowUserTag();
        userTag.setCorpId(corpId);
        userTag.setTag(tag1.getTagName());
        userTag.setTagId(tag1.getTagId());
        userTag.setType("" + tag1.getType());
        userTag.setUserId(user.getUserId());
        userTag.setExternalUserId(tbWxExtCustomer.getExternalUserId());
        userTag.setGroupName(tag1.getGroupName());
        userTag.setCreateBy(Constants.DEFAULT_USER);
        userTag.setCreateTime(new Date());
        extFollowUserTags.add(userTag);
    }

    @Override
    public List<CustomerChurnVO> queryChurnCustomer(CustomerChurnDTO dto) {
        return baseMapper.queryChurnCustomer(dto);
    }

    @Override
    public List<CustomerInfoVo> queryCustomerInfo(CustomerChurnDTO dto) {
        return baseMapper.queryCustomerInfo(dto);
    }

    @Override
    public CustomerInfoVo queryCustomerInfoDetail(String extUserId, String corpId) {
        CustomerChurnDTO dto = new CustomerChurnDTO();
        CustomerInfoVo vo = null;
        dto.setExtUserId(extUserId);
        dto.setCorpId(corpId);
        List<CustomerInfoVo> list = baseMapper.queryCustomerInfo(dto);
        if (CollectionUtils.isNotEmpty(list)) {
            vo = list.get(0);
            if (StringUtils.isNotEmpty(vo.getTag())) {
                String[] strs = vo.getTag().substring(0, vo.getTag().length() - 1).split(",");
                vo.setTags(Arrays.asList(strs));
            }

            //查询客户对应的follow
            List<CorpUserInfoVO> followUsers = baseMapper.queryFollowUserByExtUserId(extUserId, corpId);
            for (CorpUserInfoVO corpUserInfoVo : followUsers) {
                if (StringUtils.isNotEmpty(corpUserInfoVo.getTag())) {
                    String[] strs = corpUserInfoVo.getTag().substring(0, corpUserInfoVo.getTag().length() - 1).split(",");
                    corpUserInfoVo.setTags(Arrays.asList(strs));
                }
                if (StringUtils.isNotEmpty(corpUserInfoVo.getState())) {
                    String remark = contactService.selectTbWxContactRemarkByState(corpUserInfoVo.getState());
                    if (StringUtils.isNotEmpty(remark)) {
                        corpUserInfoVo.setAddWay(remark);
                    }
                }
                if (StringUtils.isNotEmpty(corpUserInfoVo.getSelfTag())) {
                    String[] strs = corpUserInfoVo.getSelfTag().substring(0, corpUserInfoVo.getSelfTag().length() - 1).split(",");
                    corpUserInfoVo.setSelfTags(Arrays.asList(strs));
                }
            }
            vo.setFollowUser(followUsers);
        }
        return vo;
    }

    @Override
    public void batchSynCustomerInfos(String corpId) {
        //批量查询企业成员
        List<String> userIds = tbWxUserService.listAllUserIdByCorpId(corpId);
        if (userIds.size() > 0) {
            publishEvent(corpId, userIds);
        }
    }

    private void publishEvent(String corpId, List<String> userIds) {
        SynCustomerEvent event = new SynCustomerEvent(userIds, corpId);
        applicationEventPublisher.publishEvent(event);
    }

    @Override
    public void remindTags(String corpId) {
        try {
            log.info("开始提醒{}企业成员打标签", corpId);
            if (StringUtils.isNotBlank(corpId)) {
                log.info("根据{}获取前一天新增的客户信息", corpId);
                List<Map<String, Object>> remindList = baseMapper.queryRemindTags(corpId);
                log.info("根据{}获取前一天新增的客户信息为：{}", corpId, JSONObject.toJSONString(remindList));
                if (CollectionUtils.isNotEmpty(remindList)) {
                    // String accessToken = ((WxCpTpServiceImpl) wxCpTpService).getCorpIdAccessToken(false, corpId);
                    // 发送服务商应用消息
                    // WxTpCpServiceImpl wxCpService = wxMultiTpCorpService.getWxCpServiceByCorpId(corpId, 1);
                    // String accessToken = wxCpService.getAccessToken();

                    // 通过api发送
                    // wxCpService.getMessageService().send()
                    // String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send";
                    // String uriWithAccessToken = url + (url.contains("?") ? "&" : "?") + "access_token=" + accessToken;
                    for (Map<String, Object> map : remindList) {
                        // Integer agentId = Integer.parseInt(String.valueOf(map.get("agentId")));
                        // String extUserName = String.valueOf(map.get("extUserName"));
                        // WxMessage message = new WxMessage();
                        // message.setTouser(userId);
                        // message.setMsgtype(WxConsts.KefuMsgType.TEXT);
                        // message.setAgentid(agentId);
                        // Text text = new Text();
                        // text.setContent("请给您的客户" + extUserName + "打标签");
                        // message.setText(text);
                        // log.info("根据{}获取前一天新增的客户信息为：{}", corpId, JSONObject.toJSONString(remindList));
                        // ((WxCpTpServiceImpl) wxCpTpService).postWithToken(uriWithAccessToken, WxCpGsonBuilder.create().toJson(message));
                        String extUserName = String.valueOf(map.get("extUserName"));
                        WxCpMessage wxCpMessage = new WxCpMessage();
                        wxCpMessage.setMsgType("text");
                        wxCpMessage.setToUser(String.valueOf(map.get("userId")));
                        wxCpMessage.setContent("请给您的客户" + extUserName + "打标签");
                        mqSendMessageManager.sendAgentMessage(wxCpMessage);
                    }
                }
            }
            log.info("结束提醒{}企业成员打标签", corpId);
        } catch (Exception ex) {
            log.error("调用TbWxExtCustomerServiceImpl.remindTags服务异常，接收参数为{}；异常信息为{}", corpId, ex);
        }
    }

    @Override
    public List<CustomerExportVo> exprotChurnCustomer(CustomerChurnDTO dto) {
        return baseMapper.exprotChurnCustomer(dto);
    }

    @Override
    public List<ChurnCustomerExport> exprotChurnCustomerLost(CustomerChurnDTO dto) {
        return baseMapper.exprotChurnCustomerLost(dto);
    }

    @Override
    public List<TbWxExtCustomer> selectWeCustomerAllList(TbWxExtCustomer tbWxExtCustomer) {
        // todo 指定员工时应该到中间表查询对应外部联系人
        if (StringUtils.isEmpty(tbWxExtCustomer.getExternalUserId())) {
            // 代表全部客户
            return baseMapper.selectList(new QueryWrapper<TbWxExtCustomer>()
                    .eq("corp_id", tbWxExtCustomer.getCorpId()));
        }
        return baseMapper.selectList(new QueryWrapper<TbWxExtCustomer>()
                .eq("corp_id", tbWxExtCustomer.getCorpId())
                .eq("external_user_id", tbWxExtCustomer.getExternalUserId()));
    }

    /**
     * 遍历外部联系人信息，组装客户信息，提取跟进员工数据、跟进员工标签数据
     * @param corpId
     * @param list
     * @param updateFlag
     * @param tagMap
     * @param allFollowUsers
     * @param extFollowUserTags
     */
    private List<TbWxExtCustomer> handlerExtCustomerFollowAndTag(String corpId, List<WxCpExternalContactInfo> list, boolean updateFlag,
                                                Map<String, String> tagMap, List<TbWxExtFollowUser> allFollowUsers,
                                                List<TbWxExtFollowUserTag> extFollowUserTags){
        List<TbWxExtCustomer> customers = new ArrayList<>();
        for (WxCpExternalContactInfo externalContact : list) {
            TbWxExtCustomer tbWxExtCustomer = new TbWxExtCustomer();
            BeanUtils.copyProperties(externalContact.getExternalContact(), tbWxExtCustomer);
            tbWxExtCustomer.setCorpId(corpId);
            if (!updateFlag) {
                tbWxExtCustomer.setStatus(UserStatus.OK.getCode());
                tbWxExtCustomer.setCreateBy(Constants.DEFAULT_USER);
                tbWxExtCustomer.setSource(Constants.SOURCE_CP);
            }
            // StringBuilder tag = new StringBuilder();
            // 客户详情2.0 标签同名加标签名
            List<ExternalUserTagVO> tagVos = new ArrayList<>();
            // 针对客户的跟进员工进行排序，最早跟进排第一位
            List<FollowedUser> sortFollowerUsers = externalContact.getFollowedUsers();
            sortFollowerUsers.sort((x, y) -> x.getCreateTime() > y.getCreateTime() ? 0 : 1);
            // 客户详情2.0 共同维护手机号
            StringBuffer mobileBuffer = new StringBuffer();
            // 获取跟进成员列表
            // 处理客户的员工跟进记录
            List<TbWxExtFollowUser> followUsers = handlerFollowUser(corpId, sortFollowerUsers, tagVos, tagMap, tbWxExtCustomer, extFollowUserTags, updateFlag, mobileBuffer);
            allFollowUsers.addAll(followUsers);

            String mobiles = mobileBuffer.toString();
            if (!updateFlag) {
                // 客户详情2.0 所有员工修改手机号会同步到一张表内 去重 2022-03-03 初次添加才同步手机号 2022-03-17
                if (StringUtils.isNotEmpty(mobiles)) {
                    mobiles = Arrays.stream(mobiles.split(",")).distinct().collect(Collectors.joining(","));
                }
                tbWxExtCustomer.setMobiles(mobiles);
            }
            // 更新首次添加客户的员工
            tbWxExtCustomer.setFirstAddUserId(sortFollowerUsers.get(0).getUserId());
            // 更新首次添加客户时间
            Date firstDate = new Date(sortFollowerUsers.get(0).getCreateTime() * 1000);
            tbWxExtCustomer.setFirstAddDate(firstDate);

            // 客户详情2.0 标签同名加标签组名
            List<String> tag = setTagVos(tagVos);
            if (CollectionUtils.isNotEmpty(tag)) {
                tbWxExtCustomer.setTag(JSON.toJSONString(tag));
            }
            customers.add(tbWxExtCustomer);
        }
        return customers;
    }

    /**
     *
     * 保存或更新客户信息
     * @param corpId
     * @param updateFlag
     * @param customers
     * @param allFollowUsers
     * @param extUserIds
     */
    private void handlerExtCustomerAddOrUpdate(String corpId, boolean updateFlag, List<TbWxExtCustomer> customers,
                                               List<TbWxExtFollowUser> allFollowUsers, List<String> extUserIds){
        if (!updateFlag) {
            saveBatch(customers, 300);
            tbWxExtFollowUserService.saveBatch(allFollowUsers, 300);
        } else {
            /**
             * 客户画像0.3 客户存在系统时为协同逻辑 新的员工添加客户不应改动原先的协同数据 2022-03-24 更新昵称和头像 2022-03-25 更新标签 2022-04-01
             * 更新unionId 2022-08-22 updateBatchById存在无法更新的问题（主键不是外部客户id）单个更新处理
             */
            // List<TbWxExtCustomer> customerList = Lists.newArrayList();
            for (TbWxExtCustomer customer : customers) {
                TbWxExtCustomer tbWxExtCustomer = new TbWxExtCustomer();
                tbWxExtCustomer.setExternalUserId(customer.getExternalUserId());
                tbWxExtCustomer.setName(customer.getName());
                tbWxExtCustomer.setAvatar(customer.getAvatar());
                tbWxExtCustomer.setTag(customer.getTag());
                tbWxExtCustomer.setUnionId(customer.getUnionId());
                tbWxExtCustomer.setFirstAddDate(customer.getFirstAddDate());
                tbWxExtCustomer.setFirstAddUserId(customer.getFirstAddUserId());
                // 单个更新处理
                update(tbWxExtCustomer, new LambdaUpdateWrapper<TbWxExtCustomer>()
                        .eq(TbWxExtCustomer::getExternalUserId, customer.getExternalUserId())
                        .eq(TbWxExtCustomer::getCorpId, corpId)
                );
                // customerList.add(tbWxExtCustomer);

                // 3.1.4 微信昵称/头像统一，同时更新昵称/头像
                tbWxExtCustomerWorkService.updateMpWxUser(tbWxExtCustomer.getUnionId(), tbWxExtCustomer.getName(), tbWxExtCustomer.getAvatar());
            }
            // updateBatchById(customerList, 300);
            //查询客户对应的follow是否存在
            List<CustomerFollowVO> existUserFollows = tbWxExtFollowUserService.queryUserIdByCorpIdAndExtUserId(extUserIds, corpId);
            if (existUserFollows.size() > 0) {
                Map<String, CustomerFollowVO> map = existUserFollows.stream().collect(
                        Collectors.toMap(x -> x.getUserId() + x.getExtUserId(), Function.identity(), (x, y) -> y));
                List<TbWxExtFollowUser> addFollowUsers = allFollowUsers.stream().filter(x -> map.get(x.getUserId() + x.getExternalUserId()) == null).collect(Collectors.toList());
                tbWxExtFollowUserService.saveBatch(addFollowUsers, 300);
                //更新外部客户followUser
                allFollowUsers.removeAll(addFollowUsers);
                for (TbWxExtFollowUser followUser : allFollowUsers) {
                    followUser.setCreateTime(null);
                    followUser.setCreateBy(null);
                    CustomerFollowVO followUser1 = map.get(followUser.getUserId() + followUser.getExternalUserId());
                    followUser.setId(followUser1.getId());
                    followUser.setUpdateTime(new Date());
                    followUser.setUpdateBy(Constants.DEFAULT_USER);
                    /**
                     * 客户删除员工 followUser还会返回该员工信息 导致同步等操作时系统会误以为这是正常的数据将该已流失的数据重新返回为正常
                     */
                    followUser.setStatus(null);
                }
                tbWxExtFollowUserService.updateBatchById(allFollowUsers, 200);
            } else {
                tbWxExtFollowUserService.saveBatch(allFollowUsers, 300);
            }
        }
    }


    /**
     * @param corpId
     * @param sortFollowerUsers
     * @param tagVos
     * @param tagMap
     * @param tbWxExtCustomer
     * @param extFollowUserTags
     * @param updateFlag 是否更新客户信息
     * @param mobileBuffer 手机号码拼接
     * @return
     */
    private List<TbWxExtFollowUser> handlerFollowUser(String corpId, List<FollowedUser> sortFollowerUsers, List<ExternalUserTagVO> tagVos,
                                                      Map<String, String> tagMap, TbWxExtCustomer tbWxExtCustomer,
                                                      List<TbWxExtFollowUserTag> extFollowUserTags, boolean updateFlag, StringBuffer mobileBuffer){
        List<TbWxExtFollowUser> followUsers = new ArrayList<>();
        for (FollowedUser user : sortFollowerUsers) {
            TbWxExtFollowUser extFollowUser = new TbWxExtFollowUser();
            BeanUtils.copyProperties(user, extFollowUser);
            extFollowUser.setCorpId(corpId);
//            StringBuilder strTag = new StringBuilder();
//            StringBuilder selfTag = new StringBuilder();

            Date currentTime = new Date(user.getCreateTime() * 1000);
            // 记录外部联系人自定义标签
            if (user.getTags() != null && user.getTags().length > 0) {
                for (FollowedUser.Tag tag1 : user.getTags()) {
                    if (tag1.getType() == Integer.valueOf(TypeConstants.CORP_TAG_TYPE)) {
//                        strTag.append(tag1.getTagName()).append(",");
                        ExternalUserTagVO externalUserTagVo = new ExternalUserTagVO();
                        BeanUtils.copyProperties(tag1, externalUserTagVo);
                        log.info("【外部联系回调】添加标签{}", externalUserTagVo);
                        tagVos.add(externalUserTagVo);
                        buildCorpUserTag(corpId, tbWxExtCustomer, extFollowUserTags, user, tag1);
                    } else if (tag1.getType() == Integer.valueOf(TypeConstants.PERSON_TAG_TYPE)) {
//                        selfTag.append(tag1.getTagName()).append(",");
                    }
                    // tag.append(tag1.getTagName() + ",");
                }
//                extFollowUser.setTag(strTag.toString());
//                extFollowUser.setSelfTag(selfTag.toString());

                // 客户信息动态-标签对比 如果发生改变 记录轨迹信息 -- 只在回调时处理 同步时不做处理 因为回调才走这个if
                if (updateFlag) {
                    recordCustomerTagTrajectory(tbWxExtCustomer, user);
                }
            } else if (user.getTagIds() != null && user.getTagIds().length > 0) {
                /**
                 * 由于回调是客户详情接口 同步是批量获取详情接口 所以这里多一层判断
                 * 标签信息只会返回企业标签和规则组标签的tag_id，个人标签将不再返回
                 * https://developer.work.weixin.qq.com/document/path/93010
                 */
                // 记录企业标签 followUser表
                for (String tagId : user.getTagIds()) {
                    TbWxExtFollowUserTag userTag = new TbWxExtFollowUserTag();
                    if (tagMap.get(tagId) != null) {
//                        strTag.append(tagMap.get(tagId)).append(",");
                        userTag.setType(TypeConstants.CORP_TAG_TYPE);
                        userTag.setTag(tagMap.get(tagId));
                    }
                    userTag.setCorpId(corpId);
                    userTag.setTagId(tagId);
                    userTag.setUserId(user.getUserId());
                    userTag.setExternalUserId(tbWxExtCustomer.getExternalUserId());
                    userTag.setGroupName(tagGroupService.selectTbWxCorpTagGroupNameByTagId(tagId));
                    userTag.setCreateBy(Constants.DEFAULT_USER);
                    userTag.setCreateTime(currentTime);
                    extFollowUserTags.add(userTag);

                    // 此处涉及方法多 不好修改 暂时先数据库查询处理 后面统一优化
                    if (tagMap.get(tagId) != null) {
                        String groupName = tagGroupService.selectTbWxCorpTagGroupNameByTagId(tagId);
                        ExternalUserTagVO externalUserTagVo = new ExternalUserTagVO();
                        externalUserTagVo.setTagName(tagMap.get(tagId));
                        externalUserTagVo.setTagId(tagId);
                        externalUserTagVo.setGroupName(groupName);
                        tagVos.add(externalUserTagVo);
                    }

                }
            }


            // 记录用户手机号码
            if (user.getRemarkMobiles() != null && user.getRemarkMobiles().length > 0) {
                StringBuilder mobilesBud = new StringBuilder();
                Sets.newHashSet(user.getRemarkMobiles()).forEach(new Consumer<String>() {
                    @Override
                    public void accept(String value) {
                        mobilesBud.append(value).append(",");
                    }
                });
                String remarkmobiles = mobilesBud.toString();
                //remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(",") - 1);
                remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(","));
                extFollowUser.setRemarkMobiles(remarkmobiles);
                if (StringUtils.isEmpty(mobileBuffer.toString())) {
                    mobileBuffer.append(remarkmobiles);
                } else {
                    mobileBuffer.append(",").append(remarkmobiles);
                }
            }
            extFollowUser.setCreateBy(Constants.DEFAULT_USER);
            extFollowUser.setStatus(UserStatus.OK.getCode());
            extFollowUser.setCreateTime(currentTime);
            extFollowUser.setExternalUserId(tbWxExtCustomer.getExternalUserId());
            followUsers.add(extFollowUser);
        }

        return followUsers;
    }

    /**
     * 对比数据库 发生改变记录轨迹信息
     *
     * @param tbWxExtCustomer 客户
     * @param user            员工
     */
    private void recordCustomerTagTrajectory(TbWxExtCustomer tbWxExtCustomer, FollowedUser user) {
        String corpId = tbWxExtCustomer.getCorpId();
        String userId = user.getUserId();
        String externalUserId = tbWxExtCustomer.getExternalUserId();
        log.info("【外部联系人回调】开始比对标签信息，corpId:【{}】，userId:【{}】，externalUserId:【{}】", corpId, userId, externalUserId);
        FollowedUser.Tag[] tags = user.getTags();
        List<TbWxExtFollowUserTag> dataTagList = tbWxExtFollowUserTagService.list(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .eq(TbWxExtFollowUserTag::getUserId, userId)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                // 企业标签
                .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
        );
        List<String> tagIdList = null;
        List<String> notTagIdList = null;
        if (tags != null && tags.length > 0) {
            tagIdList = new ArrayList<>(Arrays.asList(tags)).stream().map(FollowedUser.Tag::getTagId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(dataTagList)) {
            notTagIdList = dataTagList.stream().map(TbWxExtFollowUserTag::getTagId).collect(Collectors.toList());
        }
        // 由于同步数据接口返回的只是tagId 所以这里用tagId来判断 而不是tagName -- 如果员工离职 不做记录
        TbWxUser tbWxUser = userService.selectTbWxUserById(corpId, userId);
        if (tbWxUser != null && UserStatus.DISABLE.getCode().equals(tbWxUser.getDelFlag())) {
            log.info("【外部联系回调】记录员工编辑标签事件");
            String content = getTagContentString(corpId, notTagIdList, tagIdList, tbWxUser);
            if (content != null) {
                trajectoryService.informationNews2(userId, externalUserId, corpId, TypeConstants.MESSAGE_DYNAMIC, TypeConstants.CUSTOMER_TRAJECTORY_INFORMATION_TAG, content);
            }
        }

    }

    @Override
    public CustomerPortraitVo findWeCustomerInfo(TbWxCustomerTrajectory tbWxCustomerTrajectory) {
        String userId = tbWxCustomerTrajectory.getUserId();
        String corpId = tbWxCustomerTrajectory.getCorpId();
        String externalUserId = tbWxCustomerTrajectory.getExternalUserId();
        CustomerPortraitVo customerPortraitVo = baseMapper.findWeCustomerInfo(externalUserId, userId, corpId);
        if (customerPortraitVo != null) {
            // 计算生日
            if (customerPortraitVo.getBirthday() != null) {
                try {
                    customerPortraitVo.setAge(DateUtils.getAge(customerPortraitVo.getBirthday()));
                } catch (Exception e) {
                    log.error("【客户画像】计算客户年龄失败");
                    log.error("错误信息:{}",e);
                }
            }
        }
        return customerPortraitVo;
    }

    @Override
    public List<CustomerAddUserVO> findAddEmployers(String externalUserId, String corpId) {
        return baseMapper.findAddEmployers(externalUserId, corpId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLockAspect(key = CacheKeyConstants.CUSTOMER_PORTRAIT_TAG, value = "#weMakeCustomerTag.corpId +  #weMakeCustomerTag.externalUserId", waitTime = 6)
    public void updateWeCustomerPorTraitTag(WeMakeCustomerTag weMakeCustomerTag) {
        this.saveFollowUserTag(weMakeCustomerTag);
    }

    private String getTagContentString(String corpId, List<String> notTagIdList, List<String> tagIdList, TbWxUser tbWxUser) {
        CustomerTrajectoryContentVO vo = new CustomerTrajectoryContentVO();
        /**
         * 通过对比添加删除二者区别 来判断是添加还是删除
         */
        if (CollectionUtils.isNotEmpty(tagIdList) && CollectionUtils.isEmpty(notTagIdList)) {
            log.info("【客户轨迹】添加企微客户标签");
            // 仅添加
            String[] strings = new ArrayList<>(tagIdList).toArray(new String[]{});
            vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
        } else if (CollectionUtils.isNotEmpty(notTagIdList) && CollectionUtils.isEmpty(tagIdList)) {
            log.info("【客户轨迹】删除企微客户标签");
            // 仅删除
            String[] strings = new ArrayList<>(notTagIdList).toArray(new String[]{});
            vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
        } else if (CollectionUtils.isNotEmpty(tagIdList) && CollectionUtils.isNotEmpty(notTagIdList)) {
            Set<String> list = Sets.newHashSet();
            list.addAll(tagIdList);
            list.addAll(notTagIdList);
            log.info("【客户轨迹】既添加又删除企微客户标签，{}", list);

            // 抽取二者不同的标签
            List<String> addTagIdList = list.stream().filter(tagId -> !notTagIdList.contains(tagId)).collect(Collectors.toList());
            List<String> removeTagIdList = list.stream().filter(tagId -> !tagIdList.contains(tagId)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(addTagIdList) && CollectionUtils.isNotEmpty(removeTagIdList)) {
                log.info("【客户轨迹】同时新增了客户标签，{}", addTagIdList);
                // 新增了客户标签
                String[] strings = new ArrayList<>(addTagIdList).toArray(new String[]{});
                vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
                // 删除了客户标签
                strings = new ArrayList<>(removeTagIdList).toArray(new String[]{});
                vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
            } else if (CollectionUtils.isNotEmpty(addTagIdList) && CollectionUtils.isEmpty(removeTagIdList)) {
                // 新增了客户标签
                String[] strings = new ArrayList<>(addTagIdList).toArray(new String[]{});
                vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
            } else {
                // 删除了客户标签
                if (CollectionUtils.isNotEmpty(removeTagIdList)) {
                    String[] strings = new ArrayList<>(removeTagIdList).toArray(new String[]{});
                    vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
                } else {
                    // 没有新增删除操作不做处理
                    return null;
                }
            }
        }
        if (CollectionUtils.isEmpty(vo.getRemoveTagList()) && CollectionUtils.isEmpty(vo.getAddTagList())) {
            return null;
        }
        vo.setStaffAction("编辑了企业标签");
        vo.setActionType(TypeConstants.CUSTOMER_TRAJECTORY_STAFF_ACTION);
        vo.setStaffHeadImg(tbWxUser.getAvatar());
        vo.setStaffName(tbWxUser.getName());
        return JSON.toJSONString(vo);
    }

    @Override
    @RedisLockAspect(key = CacheKeyConstants.CUSTOMER_PORTRAIT_INFO, value = "#weCustomerPortrait.corpId +  #weCustomerPortrait.externalUserId", waitTime = 6)
    @Async
    public void updateWeCustomerPortrait(CustomerPortraitVo weCustomerPortrait) {
        // 查询到数据库数据进行比对
        TbWxExtCustomer extCustomer = getOne(new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getCorpId, weCustomerPortrait.getCorpId())
                .eq(TbWxExtCustomer::getExternalUserId, weCustomerPortrait.getExternalUserId())
        );
        if (extCustomer == null) {
            throw new CustomException("数据不存在");
        }
        boolean update = false;
        CustomerTrajectoryContentVO vo = new CustomerTrajectoryContentVO();
        LambdaUpdateWrapper<TbWxExtCustomer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbWxExtCustomer::getCorpId, weCustomerPortrait.getCorpId()).eq(TbWxExtCustomer::getExternalUserId, weCustomerPortrait.getExternalUserId());
        String fieldName = weCustomerPortrait.getFieldName();
        // 根据修改字段修改对应值
        if ("gender".equals(fieldName)) {
            if (!weCustomerPortrait.getGender().equals(extCustomer.getGender())) {
                update = true;
                // 并且修改为用户修改值
                updateWrapper.set(TbWxExtCustomer::getGender, weCustomerPortrait.getGender());
            }
        } else if ("birthday".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getBirthday, weCustomerPortrait.getBirthday());
            update =!Objects.equals(DateUtils.getDate(weCustomerPortrait.getBirthday()), DateUtils.getDate(extCustomer.getBirthday()));
        } else if ("remarkMobiles".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getMobiles, weCustomerPortrait.getRemarkMobiles());
            update = !Objects.equals(weCustomerPortrait.getRemarkMobiles(), extCustomer.getMobiles());
        } else if ("address".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getAddress, weCustomerPortrait.getAddress());
            update = !Objects.equals(weCustomerPortrait.getAddress(), extCustomer.getAddress());
        } else if ("qq".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getQq, weCustomerPortrait.getQq());
            update = !Objects.equals(weCustomerPortrait.getQq(), extCustomer.getQq());
        } else if ("email".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getEmail, weCustomerPortrait.getEmail());
            update = !Objects.equals(weCustomerPortrait.getEmail(), extCustomer.getEmail());
        } else if ("position".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getPosition, weCustomerPortrait.getPosition());
            update = !Objects.equals(weCustomerPortrait.getPosition(), extCustomer.getPosition());
        } else if ("corpName".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getCorpName, weCustomerPortrait.getCorpName());
            update = !Objects.equals(weCustomerPortrait.getCorpName(), extCustomer.getCorpName());
        } else if ("corpFullName".equals(fieldName)) {
            updateWrapper.set(TbWxExtCustomer::getCorpFullName, weCustomerPortrait.getCorpFullName());
            update = !Objects.equals(weCustomerPortrait.getCorpFullName(), extCustomer.getCorpFullName());
        }
        if (update) {
            update(updateWrapper);
            // 记录轨迹信息
            DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_INFO_EDIT.getSubEventType());
            OperTrackParams operTrackParams = OperTrackParams.builder()
                    .externalUserId(weCustomerPortrait.getExternalUserId())
                    .userId(weCustomerPortrait.getUserId())
                    .corpId(weCustomerPortrait.getCorpId())
                    .customer(extCustomer)
                    .weCustomerPortrait(weCustomerPortrait)
                    .build();
            BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
            mqSendMessageManager2.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
            log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
        }
    }

    @Override
    public int countTotalCustomer(MobileUser mobileUser) {
        return baseMapper.countTotalCustomer(mobileUser);
    }

    @Override
    public int countAddOrDecreaseByDay(String day, String status, MobileUser mobileUser) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("corpId", mobileUser.getCorpId());
        params.put("userId", mobileUser.getUserId());
        params.put("status", status);
        if (StatusConstants.CUSTOMER_NORMAL_STATUS.equals(status)) {
            // 查新增
            params.put("createTime", day);
        } else if (StatusConstants.CUSTOMER_DEL_STATUS.equals(status)) {
            // 查流失
            params.put("delTime", day);
        }
        return baseMapper.countAddOrDecreaseByDay(params);
    }

    @Override
    public int countCustomerByCustomerIdsAndCorpId(Set<String> customerIds, String corpId) {
        return baseMapper.countCustomerByCustomerIdsAndCorpId(customerIds, corpId);
    }

    @Override
    public String selectExtCustomerByCorpIdAndNickName(String corpId, String nickName) {
        return baseMapper.selectExtCustomerByCorpIdAndNickName(corpId, nickName);
    }

    @Override
    public CustomerInfoVo queryCustomerInfoDetail(CustomerChurnDTO dto) {
        CustomerInfoVo customerInfoVo = baseMapper.queryCustomerInfoDetail2(dto);
        if (Objects.isNull(customerInfoVo)) {
            return null;
        }

        String internalEmployee = sensitiveCustInfoService.getInternalEmployeeTag(customerInfoVo.getCustomerNo());
        customerInfoVo.setInternalEmployee(internalEmployee);
        // 处理标签
        setTags(customerInfoVo);
        // 处理渠道
        List<CorpUserInfoVO> followUserList = customerInfoVo.getFollowUser();
        setAddWay(customerInfoVo, followUserList);

        // 处理年龄
        if (customerInfoVo.getBirthday() != null) {
            try {
                int age = DateUtils.getAge(customerInfoVo.getBirthday());
                customerInfoVo.setAge(age);
            } catch (Exception e) {
                log.info("【客户管理】计算客户年龄失败");
            }
        }
        return customerInfoVo;
    }

    /**
     * 处理渠道
     */
    private void setAddWay(CustomerInfoVo customerInfoVo, List<CorpUserInfoVO> followUserList) {
        // 渠道汇总及 渠道去重 同一渠道添加如 卢、张等2名员工
        List<CorpAddWayVO> corpAddWays = Lists.newArrayList();
        // 去除空渠道
        followUserList = followUserList.stream().filter(f -> StringUtils.isNotEmpty(f.getAddWay())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(followUserList)) {
            // 去重渠道
            List<CorpUserInfoVO> differentFollowUserList = followUserList.stream().collect(Collectors.collectingAndThen
                    (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CorpUserInfoVO::getAddWay))), ArrayList::new
                    ));
            // 同名渠道
            List<String> sameAddWayNameList = followUserList.stream().filter(distinctByKey(CorpUserInfoVO::getAddWay)).map(CorpUserInfoVO::getAddWay).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameAddWayNameList)) {
                // 按渠道产生时间排序
                differentFollowUserList = differentFollowUserList.stream().sorted(Comparator.comparing(CorpUserInfoVO::getAddDate).reversed()).collect(Collectors.toList());
                for (CorpUserInfoVO corpUserInfoVo : differentFollowUserList) {
                    CorpAddWayVO corpAddWayVo = new CorpAddWayVO();
                    corpAddWayVo.setAddWay(corpUserInfoVo.getAddWay());
                    if (sameAddWayNameList.contains(corpUserInfoVo.getAddWay())) {
                        List<CorpUserInfoVO> sameAddWayFollowUserList = followUserList.stream().filter(f -> corpUserInfoVo.getAddWay().equals(f.getAddWay())).collect(Collectors.toList());
                        // 添加3人以上 ...等n名
                        int size = sameAddWayFollowUserList.size();
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("添加成员");
                        if (size > 2) {
                            for (int i = 0; i < 2; i++) {
                                stringBuilder.append("【");
                                stringBuilder.append(sameAddWayFollowUserList.get(i).getUserName());
                                stringBuilder.append("】");
                            }
                            stringBuilder.append("等");
                            stringBuilder.append(sameAddWayFollowUserList.size());
                            stringBuilder.append("名员工");
                        } else {
                            for (CorpUserInfoVO corpUserInfoVo1 : sameAddWayFollowUserList) {
                                stringBuilder.append("【");
                                stringBuilder.append(corpUserInfoVo1.getUserName());
                                stringBuilder.append("】");
                            }
                        }
                        corpAddWayVo.setRemark(stringBuilder.toString());
                    } else {
                        // 该渠道仅添加一人
                        corpAddWayVo.setRemark("添加成员【" + corpUserInfoVo.getUserName() + "】");
                    }
                    corpAddWays.add(corpAddWayVo);
                }
            } else {
                // 此时不存在同名的标签 直接添加即可
                differentFollowUserList.forEach(corpUserInfoVo -> corpAddWays.add(new CorpAddWayVO(corpUserInfoVo.getAddWay(), "添加成员【" + corpUserInfoVo.getUserName() + "】")));
            }
        }
        customerInfoVo.setAddWays(corpAddWays);
    }

    /**
     * 处理标签
     * // 处理标签 去重标签、重名标签加上标签组名：标签名, 离职员工的打上标签不计入
     */
    private void setTags(CustomerInfoVo customerInfoVo) {
        List<ExternalUserTagVO> tagList = customerInfoVo.getFollowUser().stream()
                .filter(f -> CollectionUtils.isNotEmpty(f.getUserTagVoList()))
                .flatMap(f -> f.getUserTagVoList().stream())
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExternalUserTagVO::getTagId))), ArrayList::new));

        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }

        // 同名标签拼接上标签组名
        tagList.stream()
                .filter(distinctByKey(ExternalUserTagVO::getTagName))
                .forEach(t -> t.setTagName(t.getGroupName() + "：" + t.getTagName()));

        customerInfoVo.setUserTagVoList(tagList);
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.FALSE) != null;
    }

    @Override
    public List<CorpUserInfoVO> queryFollowUserByExtUserId(CustomerChurnDTO dto) {
        return baseMapper.queryFollowUserByExtUserId(dto.getExtUserId(), dto.getCorpId());
    }

    @Override
    public List<CustomerGroupVO> queryCustomerGroupList(CustomerChurnDTO dto) {
        return baseMapper.queryCustomerGroupList(dto);
    }

    @Override
    public List<CustomerInfoVo> getExtCustomerList(CustomerChurnDTO dto) {
        dto.setStageConditionList();
        return baseMapper.getExtCustomerList(dto);
    }
    @Override
    public List<CorpAddWayVO> getAddWayList(CustomerChurnDTO dto) {
        return baseMapper.getAddWayList(dto);
    }

    @Override
    public CustomerPortraitVo getCustomerInfoByIdAndUserId(TbWxCustomerTrajectory tbWxCustomerTrajectory) {
        CustomerPortraitVo customerPortraitVo = baseMapper.getCustomerInfoByIdAndUserId(tbWxCustomerTrajectory);
        if (customerPortraitVo == null) {
            return new CustomerPortraitVo();
        }

        if (StrUtil.isNotBlank(customerPortraitVo.getCustomerNo())) {
            String internalEmployee = sensitiveCustInfoService.getInternalEmployeeTag(customerPortraitVo.getCustomerNo());
            customerPortraitVo.setInternalEmployee(internalEmployee);

            // 判断是否有授权,在授权有效期内，才展示客户编号。暂时废弃。update by Grant 需求：SCRM-侧边栏显示客户编号
            /*QueryWrapper<TbWxCustomerAccessAuthRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("custno", customerPortraitVo.getCustomerNo());
            wrapper.ge("expiration_time",new Date()).last("LIMIT 1");
            TbWxCustomerAccessAuthRecord tbWxCustomerAccessAuthRecord = tbWxAccessAuthRecordMapper.selectOne(wrapper);

            if (Objects.isNull(tbWxCustomerAccessAuthRecord)) {
                customerPortraitVo.setCustomerNo(null);
            }*/
        }

        List<ExternalUserTagVO> tagList = customerPortraitVo.getTagVoList();
        if (CollectionUtils.isNotEmpty(tagList)) {
            // 处理标签
            // 根据标签id去重
            tagList = tagList.stream()
                    .collect(Collectors.collectingAndThen
                            (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExternalUserTagVO::getTagId))), ArrayList::new
                            ));
            // 剩下的同名标签拼接上标签组名
            List<ExternalUserTagVO> sameTagNameList = tagList.stream().filter(distinctByKey(ExternalUserTagVO::getTagName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameTagNameList)) {
                List<String> sameTagNameStrList = sameTagNameList.stream().map(ExternalUserTagVO::getTagName).collect(Collectors.toList());
                for (ExternalUserTagVO externalUserTagVo : tagList) {
                    if (sameTagNameStrList.contains(externalUserTagVo.getTagName())) {
                        externalUserTagVo.setTagName(externalUserTagVo.getGroupName() + "：" + externalUserTagVo.getTagName());
                    }
                }
            }

        }
        return customerPortraitVo;
    }

    @Override
    public CorpRealTimeDataVO getBehaviorData(Date startTime, Date endTime, StatisticQuery statisticQuery) {

        CorpRealTimeDataVO vo = new CorpRealTimeDataVO();
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        // 数据统计
        List<ContactStatisticsDailyVO> dailyDataList = Lists.newArrayList();
        // 根据type判断查询哪种数据
        if (statisticQuery.getType() == 1) {
            dailyDataList = baseMapper.statCumulativeCustomerData(statisticQuery.getCorpId(), startTime, endTime);
            dailyDataList = DataUtil.fillAccumulatedData(dailyDataList, startTime, endTime);
        } else if (statisticQuery.getType() == 2) {
            dailyDataList = tbWxExtFollowUserService.statNewCustomerData(statisticQuery.getCorpId(), startTime, endTime);
            dailyDataList = DataUtil.fillEmptyDateData(dailyDataList, startTime, endTime);
        } else  if (statisticQuery.getType() == 3) {
            dailyDataList = tbWxExtFollowUserService.statLostCustomerData(statisticQuery.getCorpId(), startTime, endTime);
            dailyDataList = DataUtil.fillEmptyDateData(dailyDataList, startTime, endTime);
        } else if (statisticQuery.getType() == 4) {
            dailyDataList = baseMapper.statActiveCustomerData(statisticQuery.getCorpId(), startTime, endTime, 1);
            dailyDataList = DataUtil.fillEmptyDateData(dailyDataList, startTime, endTime);
        }

        vo.setData(dailyDataList);
        return vo;
    }


    @Override
    public List<ExternalUserTagVO> getCustomerTag(TbWxCustomerTrajectory tbWxCustomerTrajectory) {
        List<ExternalUserTagVO> list = baseMapper.getCustomerTag(tbWxCustomerTrajectory);
        return list;
    }

    /**
     * 批量打标签
     *
     * @param dto
     */
    @Override
    public void batchSetTag(CustomerChurnDTO dto) {
        // 如果是全选，按条件筛选数据
        if (BatchType.ALL.name().equals(dto.getBatchType())) {
            // 客户属性查询
            if (BatchTagType.ATTR.name().equals(dto.getSearchType())) {
                dto.setStageConditionList();
                List<CustomerInfoVo> lstCustomer = baseMapper.getExtCustomerList(dto);
                log.info("批量打标签，客户数量：{}", lstCustomer.size());
                // 数据为空直接返回
                if (CollectionUtils.isEmpty(lstCustomer)) {
                    return;
                }
                String ids = lstCustomer.stream().map(CustomerInfoVo::getId).map(String::valueOf).collect(Collectors.joining(","));
                dto.setId(ids);
            } else {
                CustomerChurnDTO cusDto = new CustomerChurnDTO();
                if (StringUtils.isNotEmpty(dto.getCondition())) {
                    // 自定义查询
                    List<String> extUserIdList = customerConditionBizHandler.qrySendMsgCustUserList(dto.getCorpId(), dto.getCondition());
                    // 数据为空直接返回
                    if (CollectionUtils.isEmpty(extUserIdList)) {
                        return;
                    }
                    cusDto.setExternalUserIdList(extUserIdList);
                }
                cusDto.setCorpId(dto.getCorpId());
                cusDto.setUserId(dto.getUserId());
                cusDto.setWxUserId(dto.getWxUserId());
                cusDto.setDeptId(dto.getDeptId());
                cusDto.setDataScope(dto.getDataScope());
                cusDto.setPermissionDeptIds(dto.getPermissionDeptIds());
                List<CustomerInfoVo> lstCustomer = baseMapper.getExtCustomerList(cusDto);
                log.info("批量打标签，客户数量：{}", lstCustomer.size());
                String ids = lstCustomer.stream().map(CustomerInfoVo::getId).map(String::valueOf).collect(Collectors.joining(","));
                dto.setId(ids);
            }
        }

        String tagLog = dto.getTagDetailLists().stream().map(TagVO::getTagName).collect(Collectors.joining(","));
        String logInfo = String.format("批量添加标签：用户数量【%s】，标签【%s】", dto.getId().split(",").length, tagLog);
        LogUtil.recordOperLog(logInfo);

        String corpUserId = dto.getCorpUserId();
        List<TagVO> tagDetailLists = dto.getTagDetailLists();

        // 获取批量打标签管理员 nickname
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(corpUserId));
        String nickName = sysUser.getNickName();

        List<String> customerIds = StrUtil.split(dto.getId(), StrUtil.COMMA);
        List<TbWxExtCustomer> customerList = selectCustomerListByPage(customerIds);
        List<String> externalUserIds = customerList.stream().map(x -> x.getExternalUserId()).collect(Collectors.toList());
        List<TagVO> tagVOList = tagDetailLists.stream().map(tagDetailList -> new TagVO(tagDetailList.getTagId(), tagDetailList.getTagName(), tagDetailList.getGroupId(), tagDetailList.getGroupName())).collect(Collectors.toList());
        String operId = UUID.randomUUID().toString();

        // 记录运营动态
        TrackEventTypeEnum bulkEditTagAdd = TrackEventTypeEnum.BULK_EDIT_TAG_ADD;
        sendTrackEventMsg(dto, bulkEditTagAdd, tagDetailLists, customerList, sysUser, operId);

        CustomerAddTagMsgDto msgDto = CustomerAddTagMsgDto.builder()
                .eventTypeEnum(bulkEditTagAdd)
                .corpId(dto.getCorpId())
                .externalUserIds(externalUserIds)
                .userId(sysUser.getUserId())
                .tagList(tagVOList)
                .tagSource(TagSource.CUSTOMER_LIST.name())
                .fromCallback(false)
                .isRetry(false)
                .operId(operId)
                .nickName(nickName)
                .isMsgNotify(true)
                .build();
        mqSendMessageManager2.sendCustomerAddTagMessage(msgDto);
        log.info("批量添加标签: 发送客户添加标签MQ消息成功！");
    }

    private List<TbWxExtCustomer> selectCustomerListByPage(List<String> customerIds){
        List<TbWxExtCustomer> customerList = Lists.newArrayList();
        ListUtil.page(customerIds, NumberConstants.INT_1000, customerIdsPage -> {
            List<TbWxExtCustomer> lstCustomer = baseMapper.selectList(new LambdaQueryWrapper<>(TbWxExtCustomer.class)
                    .in(TbWxExtCustomer::getId, customerIdsPage));
            customerList.addAll(lstCustomer);
        });
        return customerList;
    }

    @Override
    public int countActiveCustomerByDay(String day,String corpId) {
      return baseMapper.countActiveCustomerByDay(day,corpId);
    }

    @Override
    public CorpRealTimeDataVO getDailyTotalAuthCustomerCountGraph(QueryRadarStatisticsRankDTO dto) {
        CorpRealTimeDataVO vo = new CorpRealTimeDataVO();
        try {
            Date startDate = DateUtils.parseDate(dto.getBeginTime(), DateUtils.YYYY_MM_DD);
            Date endDate = DateUtils.parseDate(dto.getEndTime(), DateUtils.YYYY_MM_DD);
            vo.setStartTime(startDate);
            vo.setEndTime(endDate);
            // 指定开始日期之前的所有用户的累计总数
            int totalCountBeforeStartDate= tbWxCustomerAuthRecordMapper.selectCount(new QueryWrapper<TbWxCustomerAuthRecord>().le("create_time", startDate));
            List<ContactStatisticsDailyVO> originalData = baseMapper.getDailyTotalAuthCustomerCountGraph(dto);
            // 如果原始数据为空，并且你需要在这种情况下展示累计总数（即totalCountBeforeStartDate）
            if (originalData.isEmpty()) {
                ContactStatisticsDailyVO fillItem = new ContactStatisticsDailyVO();
                fillItem.setDay(dto.getBeginTime());
                // 填充在开始日期之前的累计总数
                fillItem.setCount(totalCountBeforeStartDate);
                originalData.add(fillItem);
            }

            Map<Date, ContactStatisticsDailyVO> dataMap = new HashMap<>();
            for (ContactStatisticsDailyVO item : originalData) {
                dataMap.put(DateUtils.parseDate(item.getDay(), DateUtils.YYYY_MM_DD), item);
            }

            List<ContactStatisticsDailyVO> filledData = new ArrayList<>();
            int lastCount = 0; // 初始化为0或totalCountBeforeStartDate
            for (Date date = startDate; !date.after(endDate); date = DateUtils.addDays(date, 1)) {
                ContactStatisticsDailyVO item = dataMap.get(date);
                if (item != null) {
                    lastCount = item.getCount();
                    filledData.add(item);
                } else {
                    ContactStatisticsDailyVO fillItem = new ContactStatisticsDailyVO();
                    fillItem.setDay(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date));
                    fillItem.setCount(lastCount);
                    filledData.add(fillItem);
                }
            }

            vo.setData(filledData);
        } catch (ParseException e) {
            log.error("已认证总数统计图失败", e);
            return vo;
        }
        return vo;
    }

    @Override
    public CorpRealTimeDataVO getActiveCustomerCount1v1DailyGraph(QueryRadarStatisticsRankDTO dto) {
        CorpRealTimeDataVO vo = new CorpRealTimeDataVO();
        try {
            vo.setStartTime(DateUtils.parseDate(dto.getBeginTime(), DateUtils.YYYY_MM_DD));
            vo.setEndTime(DateUtils.parseDate(dto.getEndTime(), DateUtils.YYYY_MM_DD));
        } catch (ParseException e) {
            log.error("【客户管理】获取客户活跃度统计图失败", e);
            return vo;
        }
        vo.setData(baseMapper.getActiveCustomerCount1v1DailyGraph(dto));
        return vo;
    }

    @Override
    public WxCustomerBindingVO checkAuthStatusByUnionId(String unionId) {
        log.info("【认证状态检查】检查客户是否已认证 unionId={}", unionId);
        LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getUnionId, unionId).last("LIMIT 1");
        TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);
        if (null == tbWxExtCustomer) {
            log.error("【认证状态检查】未查询到客户信息 unionId={}", unionId);
            throw new CustomException("未添加顾问企微好友，请添加后再进行认证");
        }

        boolean isAuth = (null != tbWxExtCustomer.getIsAuth())? tbWxExtCustomer.getIsAuth() : false;
        log.info("【认证状态检查】客户【{}】的认证状态为：{}", tbWxExtCustomer.getName(), isAuth);
        return new WxCustomerBindingVO(isAuth, unionId, tbWxExtCustomer.getCustno(), tbWxExtCustomer.getRealName());
    }

    @Override
    public void accessAuthCallBack(CustomerAuthDTO dto) {
        log.info("授权回调更新, 入参：{}", dto.toString());
        tbWxAccessAuthRecordMapper.insert(new TbWxCustomerAccessAuthRecord(dto.getCustNo()));
        notifyStaff(dto.getCustNo(), null, false);
    }

    @Override
    public WxCustomerBindingVO checkAccessAuthStatus(CustomerAuthDTO dto) {
        log.info("检查客户是否已授权 入参：{}", dto.toString());
        QueryWrapper<TbWxCustomerAccessAuthRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("custno", dto.getCustNo());
        //判断授权到期时间expirationTime 未到期
        wrapper.ge("expiration_time",new Date()).last("LIMIT 1");
        TbWxCustomerAccessAuthRecord tbWxCustomerAccessAuthRecord = tbWxAccessAuthRecordMapper.selectOne(wrapper);

        if(tbWxCustomerAccessAuthRecord==null){
            return new WxCustomerBindingVO(false);
        }

        LambdaQueryWrapper<TbWxExtCustomer> queryCustomer = new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getCustno, dto.getCustNo())
                .eq(StringUtils.isNotBlank(dto.getUnionId()), TbWxExtCustomer::getUnionId, dto.getUnionId())
                .last("LIMIT 1");

        TbWxExtCustomer tbWxExtCustomer = this.getOne(queryCustomer, false);
        log.info("检查客户是否已授权 unionId={} custNo={}", tbWxExtCustomer.getUnionId(), tbWxExtCustomer.getCustno());
        return new WxCustomerBindingVO(true,tbWxExtCustomer.getUnionId(),tbWxExtCustomer.getCustno(),tbWxExtCustomer.getRealName());
    }

    /**
     * 异步通知员工
     * @param custno
     * @param unionId
     * @param isAuth 是否认证，true 认证，false 授权
     * @return
     */
    private void notifyStaff(String custno, String unionId, boolean isAuth) {
        log.info("【推送应用消息】开始通知客户【unionId={}】认证/授权完成!", unionId);

        try {
            LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                    .eq(TbWxExtCustomer::getCustno, custno)
                    .eq(StringUtils.isNotBlank(unionId), TbWxExtCustomer::getUnionId, unionId)
                    .last("LIMIT 1");
            TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);

            if(tbWxExtCustomer == null){
                log.error("【推送应用消息】通知员工失败！未查询到客户信息： custno={}, unionId={}", custno, unionId);
                return;
            }

            String message = isAuth? tbWxExtCustomer.getName() + " 已认证" : "您的客户【"+tbWxExtCustomer.getRealName()+"】已授权成功";

            //查询添加了该客户的所有企业成员
            QueryWrapper<TbWxExtFollowUser> tbWxExtFollowUserQueryWrapper = new QueryWrapper<>();
            tbWxExtFollowUserQueryWrapper.eq("external_user_id", tbWxExtCustomer.getExternalUserId())
                    .eq("del_flag","0")
                    .eq("status","0").select("user_id");
            List<TbWxExtFollowUser> tbWxExtFollowUsers = tbWxExtFollowUserService.list(tbWxExtFollowUserQueryWrapper);
            if(CollectionUtil.isNotEmpty(tbWxExtFollowUsers)){
                //获取所有的user_id，去重，并且用|分隔
                String userIds = tbWxExtFollowUsers.stream().map(TbWxExtFollowUser::getUserId).distinct().collect(Collectors.joining("|"));
                //通知客户授权完成
                WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(tbWxExtCustomer.getCorpId());
                WxCpMessage msgTemp=new WxCpMessage();
                msgTemp.setToUser(userIds);
                msgTemp.setMsgType("text");
                msgTemp.setAgentId(wxCpService.getWxCpConfigStorage().getAgentId());
                msgTemp.setContent(message);
                wxCpService.getMessageService().send(msgTemp);
                if (isAuth) {
                    // 认证的时候，记录动态
                    DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_AUTH.getSubEventType());
                    OperTrackParams operTrackParams = OperTrackParams.builder()
                            .externalUserId(tbWxExtCustomer.getExternalUserId())
                            .userId(tbWxExtFollowUsers.get(0).getUserId())
                            .corpId(tbWxExtCustomer.getCorpId())
                            .build();
                    BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
                    mqSendMessageManager2.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
                    log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
                }
            }

            log.info("【推送应用消息】结束通知客户【unionId={}】认证/授权完成!", unionId);
        } catch (Exception e) {
            log.error("【推送应用消息】通知客户【custno={}, unionId={}】认证/授权完成时异常！", custno, unionId, e);
        }
    }


    @Override
    public WxCustomerBindingVO checkBindingByUnionId(String unionId) {
        log.info("【易服务绑定状态检查】检查客户【unionId={}】是否已绑定易服务", unionId);
        if (StrUtil.isBlank(unionId)) {
            throw new CustomException("系统繁忙，请稍后再试！");
        }

        LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getUnionId, unionId).last("LIMIT 1");
        TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);

        if(tbWxExtCustomer == null){
            log.error("【易服务绑定状态检查】未查询到客户信息, unionId={}", unionId);
            throw new CustomException("未添加顾问企微好友，请添加后再进行认证");
        }

        // 已认证,直接返回
        if (tbWxExtCustomer.getIsAuth()) {
            log.info("【易服务绑定状态检查】客户【unionId={}】已认证!", unionId);
            return new WxCustomerBindingVO(true, unionId, tbWxExtCustomer.getCustno(), tbWxExtCustomer.getRealName());
        }

        return getWxCustomerBindingVO(unionId, false);
    }

    /**
     * 同步客户绑定状态
     *
     * @param unionId
     * @param updateToNoAuth
     * @return
     */
    private WxCustomerBindingVO getWxCustomerBindingVO(String unionId, boolean updateToNoAuth) {
        CustomerAuthDTO dto = getCustomerNoFromESB(unionId);
        if (dto != null && StringUtils.isNotEmpty(dto.getCustNo())) {
            boolean needUpdateCustomer = true;
            boolean needUpdateAuthRecord = true;
            if (updateToNoAuth) {
                // 前端直接调用接口时，需要将 SCRM 和 ESB 中的客户认证状态 进行比较，有变动再进行更新
                LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                        .select(TbWxExtCustomer::getIsAuth, TbWxExtCustomer::getUnionId, TbWxExtCustomer::getExternalUserId,
                                TbWxExtCustomer::getCustno, TbWxExtCustomer::getRealName)
                        .eq(TbWxExtCustomer::getUnionId, unionId).last("LIMIT 1");
                TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);
                Boolean isAuth = tbWxExtCustomer.getIsAuth();
                TbWxCustomerAuthRecord tbWxCustomerAuthRecord = tbWxCustomerAuthRecordMapper.selectOne(new QueryWrapper<TbWxCustomerAuthRecord>()
                        .eq("union_id", unionId).eq("custno", dto.getCustNo()).eq("del_flag", 0)
                        .last("LIMIT 1"));
                // 如果数据库中是未认证状态 或者 不存在已认证记录
                needUpdateAuthRecord =  isAuth == null || !isAuth || tbWxCustomerAuthRecord == null;
                // is_auth、custNo 和 realName 发生变化
                needUpdateCustomer = needUpdateAuthRecord || !Objects.equals(tbWxExtCustomer.getCustno(), dto.getCustNo()) || !Objects.equals(tbWxExtCustomer.getRealName(), dto.getCustName());
            }
            if (needUpdateCustomer) {
                log.info("【认证校验】更新客户信息, unionId={}, custNo={}, realName={}", unionId, dto.getCustNo(), dto.getCustName());
                // 更新客户表
                baseMapper.update(null, new UpdateWrapper<TbWxExtCustomer>()
                        .eq("union_id", unionId)
                        .set("is_auth", 1)
                        .set("custno", dto.getCustNo())
                        .set("real_name", dto.getCustName())
                        .set("update_time", new Date()));
            }
            if (needUpdateAuthRecord) {
                log.info("【认证校验】更新客户认证记录, unionId={}, custNo={}", unionId, dto.getCustNo());
                // 保存认证记录
                tbWxCustomerAuthRecordMapper.insert(new TbWxCustomerAuthRecord(unionId, dto.getCustNo()));

                log.info("【认证校验】开始通知客户【unionId={}】认证完成!", unionId);
                // 异步通知员工
                notifyStaff(dto.getCustNo(), unionId, true);

                log.info("【认证校验】【易服务绑定状态检查】客户【unionId={}】已绑定易服务！", unionId);
            }
            return new WxCustomerBindingVO(true, unionId, dto.getCustNo(), dto.getCustName());
        } else {
             log.info("【认证校验】【易服务绑定状态检查】客户【unionId={}】未绑定易服务！", unionId);
            if (updateToNoAuth) {
                 // 如果是解绑状态，查询SCRM表客户是否已认证，若是，需要更新SCRM表客户认证状态为未认证
                TbWxExtCustomer customer = this.lambdaQuery()
                        .select(TbWxExtCustomer::getIsAuth, TbWxExtCustomer::getUnionId, TbWxExtCustomer::getExternalUserId,
                                TbWxExtCustomer::getCustno, TbWxExtCustomer::getRealName).
                        eq(TbWxExtCustomer::getUnionId, unionId).last("LIMIT 1").one();
                if (customer != null && customer.getIsAuth()) {
                    log.info("【认证校验】客户【unionId={}】已认证，需要更新为未认证状态！", unionId);
                    List list = new ArrayList<>();
                    list.add(customer);
                    updateUnAuthCustomers(list, new Date());
                }
             }
            return new WxCustomerBindingVO(false);
        }
    }

    /**
     * 根据 unionId 去易服务检查客户是否绑定，并更新 SCRM 中客户认证状态
     * @param unionId
     * @return
     */
    @Override
    public WxCustomerBindingVO checkEsbBindingByUnionId(String unionId) {
        LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                .select(TbWxExtCustomer::getUnionId)
                .eq(TbWxExtCustomer::getUnionId, unionId).last("LIMIT 1");
        TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);
        if (tbWxExtCustomer == null) {
            log.warn("【认证校验】未查询到客户信息, unionId={}", unionId);
            throw new CustomException("身份信息获取失败，请退出小程序后，通过顾问链接重新进入。");
        }
        WxCustomerBindingVO wxCustomerBindingVO = getWxCustomerBindingVO(unionId, true);
        WxCustomerBindingVO result = new WxCustomerBindingVO(wxCustomerBindingVO.getIsAuth());
        // 需要将wxCustomerBindingVO.getCustName()加密后设置到result中，如隐藏姓氏，保留名称
        if (StringUtils.isNotEmpty(wxCustomerBindingVO.getCustName())) {
            String custName = wxCustomerBindingVO.getCustName();
            result.setCustName(custName.length() > 1 ? "*"+custName.substring(1) : "*");
        }
        return result;
    }

    @Override
    public void bindingByUnionId(CustomerAuthDTO dto) {
        log.info("客户认证通过，更新客户编号！入参：{}", JSONUtil.toJsonStr(dto));

        LambdaQueryWrapper<TbWxExtCustomer> wrapper = new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getUnionId, dto.getUnionId()).last("LIMIT 1");
        TbWxExtCustomer tbWxExtCustomer = this.getOne(wrapper, false);

        if(tbWxExtCustomer!=null){
            baseMapper.update(null,new UpdateWrapper<TbWxExtCustomer>().eq("union_id", dto.getUnionId())
                    .set("is_auth",1)
                    .set("custno",dto.getCustNo())
                    .set("real_name",dto.getCustName())
                    .set("update_time",new Date()));
            //将之前的删除标志设置为true
            UpdateWrapper<TbWxCustomerAuthRecord> queryWrapper = new UpdateWrapper<>();
            queryWrapper.eq("union_id", dto.getUnionId());
            queryWrapper.eq("del_flag",0);
            queryWrapper.set("del_flag",1);
            tbWxCustomerAuthRecordMapper.update(null,queryWrapper);
            tbWxCustomerAuthRecordMapper.insert(new TbWxCustomerAuthRecord(dto.getUnionId(), dto.getCustNo()));

            notifyStaff(dto.getCustNo(), dto.getUnionId(), true);
        }
    }

    @Override
    public void unbindingByUnionId(String unionId) {
        //调用ESB 解除绑定
    }
    @Override
    public List<CustomerInfoVo> qryExtCustomerList(CustomerChurnDTO dto) {
        return baseMapper.qryExtCustomerList(dto);
    }

    /**
     * 保存客户标签数据
     * @param custNo
     */
    private void saveCustomerLabel(String custNo) {
        // 查询是否存在标签数据
        Integer count = vfScrmLabelService.lambdaQuery().eq(VFScrmLabel::getCustno, custNo).count();
        if(count > 0){
            return;
        }

        // 不存在，则调用数据中心接口查询标签数据
        List<VFScrmLabel> customerLabels = sqlServiceClient.queryScrmLabel(custNo);
        if (CollectionUtil.isEmpty(customerLabels)) {
            return;
        }

        // 保存标签数据
        vfScrmLabelService.save(customerLabels.get(0));
    }

    /**
     * 调用 ESB 接口获取客户最新绑定状态
     * @param unionId
     * @return
     */
    private CustomerAuthDTO getCustomerNoFromESB(String unionId) {
        try {
            JSONObject requestBody = new JSONObject();
            requestBody.put("unionId", unionId);
            JSONObject responseObject = JSON.parseObject(EsbClient.getMDCWXSERVICEPort().wsGetCustnoByUnionId(requestBody.toJSONString()));
            log.info("【认证校验】请求参数：{}，ESB 返回结果：{}", requestBody.toJSONString(), responseObject.toJSONString());
            // 检查 retCode 是否为 "0000"
            if (!"0000".equals(responseObject.getString("retCode"))) {
                log.info("【认证校验】ESB 返回结果 retCode 不为 0000，unionId={}", unionId);
                return null;
            }
            // 获取 N_CUSTNO
            JSONArray bizData = responseObject.getJSONArray("bizData");
            if (bizData != null && !bizData.isEmpty()) {
                JSONArray firstArray = bizData.getJSONArray(0);
                if (firstArray != null && !firstArray.isEmpty()) {
                    JSONObject dataObject = firstArray.getJSONObject(0);
                    if (dataObject != null) {
                        log.info("【认证校验】ESB 返回结果 bizData 不为空，unionId={}, custNo={}, custName={}",
                                unionId, dataObject.getString("N_CUSTNO"), dataObject.getString("CUSTNAME"));
                        String nCustNo = dataObject.getString("N_CUSTNO");
                        return new CustomerAuthDTO(unionId,nCustNo,null,dataObject.getString("CUSTNAME"));
                    }
                }
            }
            log.info("【认证校验】ESB 返回结果 bizData 为空，unionId={}", unionId);
        } catch (Exception e) {
            log.error("【认证校验】调用ESB接口异常",e);
            log.error("调用ESB接口异常",e);
        }
        return null;
    }


    /**
     * tagList format to string --> ["","",""]
     */
    private String tagListFormat(List<String> tagList) {
        StringBuilder tags = new StringBuilder();
        for (String tag : tagList) {
            tags.append(tag).append("\",\"");
        }

        return "[\"" + (tags.length() > 3 ? tags.substring(0, tags.length() - 3) : tags) + "\"]";
    }

    /**
     * 作废，批量操作标签时，如果员工离职，取最新一条数据时，会导致企微那边拿不到userid
     * @param tbWxExtCustomer
     * @return
     */
    @Deprecated
    private List<TbWxExtFollowUser> getFollowUser(TbWxExtCustomer tbWxExtCustomer) {
        String corpId = tbWxExtCustomer.getCorpId();
        String externalUserId = tbWxExtCustomer.getExternalUserId();

        QueryWrapper<TbWxExtFollowUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("external_user_id", externalUserId)
                .eq("corp_id", corpId)
                .orderByDesc("update_time")
                .orderByDesc("create_time");

        return tbWxExtFollowUserService.list(queryWrapper);
    }

    /**
     * 获取客户和在职员工关系，获取员工userid
     * @param extUserId
     * @param corpId
     * @return
     */
    private String queryFollowUserByExtUserId(String extUserId, String corpId) {
        List<CorpUserInfoVO> lstUser = baseMapper.queryFollowUserByExtUserId(extUserId, corpId);
        if (CollectionUtil.isEmpty(lstUser)) {
            return null;
        } else {
            lstUser = lstUser.stream().filter(t -> CommonConstants.STATUS_NORMAL.equals(t.getStatus())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(lstUser)) {
                return lstUser.get(0).getUserId();
            }
        }
        return null;
    }

    /**
     * 批量移除标签
     * @param dto
     */
    @Override
    public void batchUnSetTag(CustomerChurnDTO dto) {
        // 如果是全选，按条件筛选数据
        if (BatchType.ALL.name().equals(dto.getBatchType())) {
            // 客户属性查询
            if (BatchTagType.ATTR.name().equals(dto.getSearchType())) {
                List<CustomerInfoVo> lstCustomer = baseMapper.getExtCustomerList(dto);
                // 数据为空直接返回
                if (CollectionUtils.isEmpty(lstCustomer)) {
                    return;
                }
                String ids = lstCustomer.stream().map(CustomerInfoVo::getId).map(String::valueOf).collect(Collectors.joining(","));
                dto.setId(ids);
            } else {
                CustomerChurnDTO cusDto = new CustomerChurnDTO();
                if (StringUtils.isNotEmpty(dto.getCondition())) {
                    // 自定义查询
                    List<String> extUserIdList = customerConditionBizHandler.qrySendMsgCustUserList(dto.getCorpId(), dto.getCondition());
                    // 数据为空直接返回
                    if (CollectionUtils.isEmpty(extUserIdList)) {
                        return;
                    }
                    cusDto.setExternalUserIdList(extUserIdList);
                }
                cusDto.setCorpId(dto.getCorpId());
                cusDto.setUserId(dto.getUserId());
                cusDto.setWxUserId(dto.getWxUserId());
                cusDto.setDeptId(dto.getDeptId());
                cusDto.setDataScope(dto.getDataScope());
                cusDto.setPermissionDeptIds(dto.getPermissionDeptIds());
                List<CustomerInfoVo> lstCustomer = baseMapper.getExtCustomerList(cusDto);
                String ids = lstCustomer.stream().map(CustomerInfoVo::getId).map(String::valueOf).collect(Collectors.joining(","));
                dto.setId(ids);
            }
        }

        String tagLog = dto.getTagDetailLists().stream().map(TagVO::getTagName).collect(Collectors.joining(","));
        String logInfo = String.format("批量移除标签：用户数量【%s】，标签【%s】",dto.getId().split(",").length , tagLog);
        LogUtil.recordOperLog(logInfo);

        String corpUserId = dto.getCorpUserId();
        List<TagVO> tagDetailLists = dto.getTagDetailLists();

        // 获取批量移除标签管理员 nickname
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(corpUserId));
        String nickName = sysUser.getNickName();

        List<String> customerIds = StrUtil.split(dto.getId(), StrUtil.COMMA);
        List<TbWxExtCustomer> customerList = selectCustomerListByPage(customerIds);
        List<String> externalUserIds = customerList.stream().map(x -> x.getExternalUserId()).collect(Collectors.toList());
        List<TagVO> tagVOList = tagDetailLists.stream().map(tagDetailList -> new TagVO(tagDetailList.getTagId(), tagDetailList.getTagName(), tagDetailList.getGroupId(), tagDetailList.getGroupName())).collect(Collectors.toList());
        String operId = UUID.randomUUID().toString();

        // 记录运营动态
        TrackEventTypeEnum bulkEditTagRemove = TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE;
        sendTrackEventMsg(dto, bulkEditTagRemove, tagDetailLists, customerList, sysUser, operId);

        CustomerRemoveTagMsgDto msgDto = CustomerRemoveTagMsgDto.builder()
                .corpId(dto.getCorpId())
                .externalUserIds(externalUserIds)
                .tagList(tagVOList)
                .tagSource(TagSource.CUSTOMER_LIST.name())
                .isRetry(false)
                .eventTypeEnum(bulkEditTagRemove)
                .operId(operId)
                .userId(sysUser.getUserId())
                .nickName(nickName)
                .isMsgNotify(true)
                .build();
        mqSendMessageManager2.sendCustomerRemoveTagMessage(msgDto);
        log.info("批量移除标签: 发送客户移除标签MQ消息成功！");
    }

    private void sendTrackEventMsg(CustomerChurnDTO dto, TrackEventTypeEnum bulkEditTagAdd, List<TagVO> tagDetailLists, List<TbWxExtCustomer> customerList, SysUser sysUser, String operId) {
        DefaultBuOperTrackHandler  handler = DefaultBuOperTrackHandler.getEventHandler(bulkEditTagAdd.getSubEventType());
        RelatedResource relatedResource = new RelatedResource();
        if (TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE.equals(bulkEditTagAdd)) {
            relatedResource.setRemoveTagList(tagDetailLists.stream().map(x->x.getTagName()).collect(Collectors.toList()));
        } else {
            relatedResource.setAddTagList(tagDetailLists.stream().map(x->x.getTagName()).collect(Collectors.toList()));
        }
        relatedResource.setCustomerList(customerList.stream().map(x->x.getName()).collect(Collectors.toList()));
        if (BatchType.ALL.name().equals(dto.getBatchType())) {
            List<TbDcUserTags> list = null;
            if (!BatchTagType.ATTR.name().equals(dto.getSearchType())) {
                list = tbDcUserTagsService.lambdaQuery().list();
            }
            String condition = BuOperTrackUtils.buildCondition(dto, list);
            if (StrUtil.isNotBlank(condition)) {
                relatedResource.setCondition(condition);
            } else {
                relatedResource.setAllSelected(true);
            }
        }
        OperTrackParams operTrackParams = OperTrackParams.builder()
                .relatedResource(relatedResource)
                .externalUserId("")
                .userId(sysUser.getUserName())
                .corpId(dto.getCorpId())
                .operId(operId)
                .build();
        BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
        mqSendMessageManager2.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
        log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
    }

    @Override
    public List<String> getNullRelationshipIds() {
        return baseMapper.getNullRelationshipIds();
    }

    /**
     * 保存客户的企业标签数据
     *
     */
    public void saveFollowUserTag(WeMakeCustomerTag weMakeCustomerTag) {
        String corpId = weMakeCustomerTag.getCorpId();
        String userId = weMakeCustomerTag.getUserId();
        String externalUserId = weMakeCustomerTag.getExternalUserId();

        List<String> currentTagIds = Optional.ofNullable(weMakeCustomerTag.getAddTag()).orElse(Collections.emptyList())
                .stream().map(TbWxCorpTag::getTagId).collect(Collectors.toList());

        log.info("【客户标签更新】修改客户标签，客户id：{}, 员工id：{}, 标签id：{}", externalUserId, userId, StrUtil.join(StrUtil.COMMA, currentTagIds));
        try {
            // 查询员工名称、头像
            TbWxUser user = this.getUserByUserId(corpId, userId);
            // 查询已经给客户打上且有权限查看的标签
            List<TbWxExtFollowUserTag> existFollowUserTags = getExistFollowUserTags(weMakeCustomerTag);
            // 批量移除客户标签
            removeCustomerTag(corpId, externalUserId, currentTagIds, existFollowUserTags, user);
            // 批量添加客户标签
            addCustomerTag(corpId, externalUserId, user, currentTagIds);
        } catch (Exception e) {
            log.error("【客户标签更新】修改客户标签失败，客户id：{}, 员工id：{}, 标签id：{}", externalUserId, userId, StrUtil.join(StrUtil.COMMA, currentTagIds), e);
            throw new CustomException("系统繁忙，请稍后重试");
        }

        log.info("【客户标签更新】修改客户标签成功，客户id：{}, 员工id：{}, 标签id：{}", externalUserId, userId, StrUtil.join(StrUtil.COMMA, currentTagIds));
    }

    /**
     * 批量添加客户标签
     * @param corpId
     * @param externalUserId
     * @param user
     * @param currentTagIds
     * @return
     */
    private void addCustomerTag(String corpId, String externalUserId, TbWxUser user,
                                        List<String> currentTagIds) {
        // currentTagIds 为空直接返回
        if (CollectionUtil.isEmpty(currentTagIds)) {
            return ;
        }
        String userId = user.getUserid();
        List<TagVO> newTags = tbWxCorpTagService.selectValidTagByIds(currentTagIds);
        if (CollectionUtil.isEmpty(newTags)) {
            return ;
        }

        CustomerAddTagMsgDto customerAddTagMsgDto = CustomerAddTagMsgDto.builder()
                .eventTypeEnum(TrackEventTypeEnum.CORPORATE_TAG_EDIT_ADD)
                .corpId(corpId)
                .externalUserIds(Lists.newArrayList(externalUserId))
                .userId(userId)
                .tagList(newTags)
                .tagSource(TagSource.CUSTOMER_PROFILE.name())
                .fromCallback(false)
                .isRetry(false)
                .operId(user.getUserid())
                .nickName(user.getName())
                .build();
        mqSendMessageManager2.sendCustomerAddTagMessage(customerAddTagMsgDto);
        log.info("【更新客户画像标签】发送客户添加标签MQ消息成功");
    }


    /**
     * 批量移除客户标签
     * 移除所有有权限移除的指定标签，比如：员工拥有本部门权限，则可以移除本部门所有员工给该客户打上的标签，员工拥有全部权限，则可以移除该客户的全部标签
     *
     * @param corpId
     * @param externalUserId
     * @param currentTagIds
     * @param existFollowUserTags
     * @param user
     */
    private void removeCustomerTag(String corpId, String externalUserId,
                                           List<String> currentTagIds, List<TbWxExtFollowUserTag> existFollowUserTags, TbWxUser user) {
        // 筛选出需要移除的标签
        List<TagVO> removeFollowUserTags = existFollowUserTags.stream()
                .filter(tag -> !currentTagIds.contains(tag.getTagId())).map(tag -> {
                    return TagVO.builder().tagId(tag.getTagId()).tagName(tag.getTag()).groupName(tag.getGroupName()).build();
                }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(removeFollowUserTags)) {
            return ;
        }
        CustomerRemoveTagMsgDto customerRemoveTagMsgDto = CustomerRemoveTagMsgDto.builder()
                .corpId(corpId)
                .externalUserIds(Lists.newArrayList(externalUserId))
                .tagList(removeFollowUserTags)
                .tagSource(TagSource.CUSTOMER_PROFILE.name())
                .isRetry(false)
                .eventTypeEnum(TrackEventTypeEnum.CORPORATE_TAG_EDIT_REMOVE)
                .userId(user.getUserid())
                .fromCallback(false)
                .operId(user.getUserid())
                .nickName(user.getName())
                .build();
        mqSendMessageManager2.sendCustomerRemoveTagMessage(customerRemoveTagMsgDto);
        log.info("【更新客户画像标签】发送客户移除标签MQ消息成功");
    }

    /**
     * 查询已经给客户打上且有权限查看的标签
     * @return
     */
    private List<TbWxExtFollowUserTag> getExistFollowUserTags(WeMakeCustomerTag weMakeCustomerTag) {
        String corpId = weMakeCustomerTag.getCorpId();
        String externalUserId = weMakeCustomerTag.getExternalUserId();
        String userId = weMakeCustomerTag.getUserId();
        List<Integer> permissionDeptIds = weMakeCustomerTag.getPermissionDeptIds();
        String dataScope = weMakeCustomerTag.getDataScope();

        List<String> permissionUserIds = new ArrayList<>();
        permissionUserIds.add(userId);

        // 查询部门下的员工
        if (CollectionUtil.isNotEmpty(permissionDeptIds)) {
            List<TbWxUser> userList = tbWxUserService.lambdaQuery()
                    .select(TbWxUser::getUserid)
                    .eq(TbWxUser::getCorpId, corpId)
                    .eq(TbWxUser::getStatus, CommonConstants.NUM_1)
                    .in(TbWxUser::getMainDepartment, permissionDeptIds).list();
            if (CollectionUtil.isNotEmpty(userList)) {
                permissionUserIds.addAll(userList.stream().map(TbWxUser::getUserid).collect(Collectors.toList()));
            }
        }

        List<TbWxExtFollowUserTag> existFollowUserTags = tbWxExtFollowUserTagService.lambdaQuery()
                .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                .in(Objects.isNull(dataScope) || !DataScopeEnum.ALL.getValue().equals(dataScope), TbWxExtFollowUserTag::getUserId, permissionUserIds).list();
        return existFollowUserTags;
    }

    private TbWxUser getUserByUserId(String corpId, String userId) {
        TbWxUser user = userService.lambdaQuery()
                .select(TbWxUser::getName, TbWxUser::getAvatar)
                .eq(TbWxUser::getUserid, userId)
                .last("LIMIT 1").one();

        if (user == null) {
            user = new TbWxUser();
        }

        user.setCorpId(corpId);
        user.setUserid(userId);
        return user;
    }



    /**
     * 更新已认证客户
     * 更新客户认证状态和认证记录
     *
     * @param authList 需要认证的客户列表
     * @param now      当前时间
     */
    @Override
    public void updateAuthCustomers(List<TbWxExtCustomer> authList, Date now) {
        if (CollectionUtils.isEmpty(authList)) {
            return;
        }
        for (TbWxExtCustomer customer : authList) {
            // 更新客户认证状态
            this.lambdaUpdate()
                    .set(TbWxExtCustomer::getIsAuth, true)
                    .set(TbWxExtCustomer::getCustno, customer.getCustno())
                    .set(TbWxExtCustomer::getUpdateTime, now)
                    .set(TbWxExtCustomer::getRealName, customer.getRealName())
                    .eq(TbWxExtCustomer::getUnionId, customer.getUnionId())
                    .eq(TbWxExtCustomer::getExternalUserId, customer.getExternalUserId())
                    .update();

            // 更新认证记录
            updateAuthRecord(customer, now);
        }
    }

    /**
     * 更新认证记录
     * 将旧记录标记为删除并创建新记录
     *
     * @param customer 客户信息
     * @param now      当前时间
     */
    private void updateAuthRecord(TbWxExtCustomer customer, Date now) {
        // 将之前的记录标记为删除
        UpdateWrapper<TbWxCustomerAuthRecord> queryWrapper = new UpdateWrapper<>();
        queryWrapper.eq("union_id", customer.getUnionId())
                .eq("del_flag", 0)
                .set("update_time", now)
                .set("del_flag", 1);
        tbWxCustomerAuthRecordMapper.update(null, queryWrapper);

        // 插入新的认证记录
        TbWxCustomerAuthRecord entity = new TbWxCustomerAuthRecord(customer.getUnionId(), customer.getCustno());
        tbWxCustomerAuthRecordMapper.insert(entity);
    }

    /**
     * 更新未认证客户
     * 更新客户认证状态和相关记录
     *
     * @param unAuthList 需要解除认证的客户列表
     * @param now        当前时间
     */
    @Override
    public void updateUnAuthCustomers(List<TbWxExtCustomer> unAuthList, Date now) {
        if (CollectionUtils.isEmpty(unAuthList)) {
            return;
        }
        for (TbWxExtCustomer customer : unAuthList) {
            // 更新客户认证状态
            this.lambdaUpdate()
                    .set(TbWxExtCustomer::getIsAuth, false)
                    .set(TbWxExtCustomer::getCustno, customer.getCustno())
                    .set(TbWxExtCustomer::getRealName, customer.getRealName())
                    .set(TbWxExtCustomer::getUpdateTime, now)
                    .eq(TbWxExtCustomer::getUnionId, customer.getUnionId())
                    .eq(TbWxExtCustomer::getExternalUserId, customer.getExternalUserId())
                    .update();

            // 更新认证记录和授权记录
            updateUnAuthRecords(customer, now);
        }
    }

    /**
     * 更新未认证记录
     * 更新认证记录和授权记录状态
     *
     * @param customer 客户信息
     * @param now      当前时间
     */
    private void updateUnAuthRecords(TbWxExtCustomer customer, Date now) {
        // 更新认证记录为删除状态
        UpdateWrapper<TbWxCustomerAuthRecord> authWrapper = new UpdateWrapper<>();
        authWrapper.eq("union_id", customer.getUnionId())
                .eq("del_flag", 0)
                .set("update_time", now)
                .set("del_flag", 1);
        tbWxCustomerAuthRecordMapper.update(null, authWrapper);

        // 更新未过期的授权记录为已过期
        UpdateWrapper<TbWxCustomerAccessAuthRecord> accessWrapper = new UpdateWrapper<>();
        accessWrapper
                .set("expiration_time", now)
                .eq("custno", customer.getCustno())
                .ge("expiration_time", now);
        tbWxAccessAuthRecordMapper.update(null, accessWrapper);
    }

    /**
     * 更新客户信息
     * 更新客户 custNo 和 rēalName
     * @param updateList
     * @param now
     */
    @Override
    public void updateCustomers(List<TbWxExtCustomer> updateList, Date now) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        for (TbWxExtCustomer customer : updateList) {
            // 更新客户 custNo 和 rēalName
            this.lambdaUpdate()
                    .set(TbWxExtCustomer::getCustno, customer.getCustno())
                    .set(TbWxExtCustomer::getRealName, customer.getRealName())
                    .set(TbWxExtCustomer::getUpdateTime, now)
                    .eq(TbWxExtCustomer::getUnionId, customer.getUnionId())
                    .eq(TbWxExtCustomer::getExternalUserId, customer.getExternalUserId())
                    .update();
        }
    }
}
