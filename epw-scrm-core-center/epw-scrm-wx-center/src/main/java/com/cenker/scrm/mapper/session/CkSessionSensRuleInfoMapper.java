package com.cenker.scrm.mapper.session;

import java.util.List;

import com.cenker.scrm.pojo.dto.session.QrySensRuleDto;
import com.cenker.scrm.pojo.entity.session.CkSessionSensRuleInfo;

/**
 * 敏感规则信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface CkSessionSensRuleInfoMapper 
{
    /**
     * 查询敏感规则信息
     * 
     * @param ruleId 敏感规则信息主键
     * @return 敏感规则信息
     */
    public CkSessionSensRuleInfo selectCkSessionSensRuleInfoByRuleId(Long ruleId);

    /**
     * 查询敏感规则信息列表
     * 
     * @param ckSessionSensRuleInfo 敏感规则信息
     * @return 敏感规则信息集合
     */
    public List<CkSessionSensRuleInfo> selectCkSessionSensRuleInfoList(QrySensRuleDto qrySensRuleDto);

    /**
     * 新增敏感规则信息
     * 
     * @param ckSessionSensRuleInfo 敏感规则信息
     * @return 结果
     */
    public int insertCkSessionSensRuleInfo(CkSessionSensRuleInfo ckSessionSensRuleInfo);

    /**
     * 修改敏感规则信息
     * 
     * @param ckSessionSensRuleInfo 敏感规则信息
     * @return 结果
     */
    public int updateCkSessionSensRuleInfo(CkSessionSensRuleInfo ckSessionSensRuleInfo);

    /**
     * 删除敏感规则信息
     * 
     * @param ruleId 敏感规则信息主键
     * @return 结果
     */
    public int deleteCkSessionSensRuleInfoByRuleId(Long ruleId);

    /**
     * 批量删除敏感规则信息
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCkSessionSensRuleInfoByRuleIds(Long[] ruleIds);
    public Long cntRuleNameIsRep(CkSessionSensRuleInfo ckSessionSensRuleInfo);

}
