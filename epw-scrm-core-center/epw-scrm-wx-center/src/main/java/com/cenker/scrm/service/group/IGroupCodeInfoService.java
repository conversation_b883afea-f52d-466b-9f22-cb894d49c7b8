package com.cenker.scrm.service.group;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import com.cenker.scrm.pojo.request.data.GroupCodeStatisticQuery;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.request.group.GroupCodeStatisticsRequest;
import com.cenker.scrm.pojo.vo.group.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description
 */
public interface IGroupCodeInfoService extends IService<GroupCodeInfo> {


    /**
     * 保存社群活码
     * @param request
     * @return 社群活码
     */
    GroupCodeInfo saveGroupCodeInfo(GroupCodeRequest request);

    /**
     * 社群活码列表
     * @param request
     * @return
     */
    List<GroupCodeListVO> listGroupCode(GroupCodeRequest request);

    /**
     * 查询社群活码详情
     * @param request
     * @return 社群活码详情
     */
    GroupCodeDetailVO detailGroupCode(GroupCodeRequest request);

    /**
     * 社群活码数据概览
     * @param request
     * @return
     */
    GroupCodeDataStatisticsVO getDataStatistics(GroupCodeRequest request);

    /**
     * 社群活码统计明细-按群聊查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailGroupVO> getDataStatisticsDetailByGroup(GroupCodeStatisticsRequest request);

    /**
     * 社群活码统计明细-按日期查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetailByDate(GroupCodeStatisticsRequest request);

    /**
     * 社群活码数据统计趋势
     * @param startTime
     * @param endTime
     * @param statisticQuery
     * @return
     */
    GroupCodeDataStatisticsVO getDataStatisticsTendency(Date startTime, Date endTime, GroupCodeStatisticQuery statisticQuery);
}
