package com.cenker.scrm.service.impl.open;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.open.OpenApiAppGoodMapper;
import com.cenker.scrm.pojo.dto.open.AppGoodDTO;
import com.cenker.scrm.pojo.entity.open.store.OpenApiAppGood;
import com.cenker.scrm.pojo.vo.open.store.AppGoodVO;
import com.cenker.scrm.service.open.IOpenApiAppGoodService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/19
 * @Description
 */
@Service
public class OpenApiAppGoodServiceImpl extends ServiceImpl<OpenApiAppGoodMapper, OpenApiAppGood> implements IOpenApiAppGoodService {

    @Override
    public List<AppGoodVO> listGood(AppGoodDTO appGoodDTO) {
        return baseMapper.listGood(appGoodDTO);
    }
}
