package com.cenker.scrm.biz.sop;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.biz.ApprovalMsgNotify;
import com.cenker.scrm.biz.customer.CustomerConditionBizHandler;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.constants.NumberConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.handler.factory.MassMessageHandlerFactory;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.condition.MessageConditionDTO;
import com.cenker.scrm.pojo.dto.message.MassMessageChainDTO;
import com.cenker.scrm.pojo.dto.message.MassMessageChainDetailDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopConditionInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopContentInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.exception.DataNotExistException;
import com.cenker.scrm.pojo.request.sop.BaseSopRequest;
import com.cenker.scrm.pojo.request.sop.ConditionSopRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.sop.SopInfoVO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.journey.ITbWxExtJourneyCustomerStageService;
import com.cenker.scrm.service.journey.ITbWxExtJourneyInfoService;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.service.sop.ISopConditionInfoService;
import com.cenker.scrm.service.sop.ISopContentInfoService;
import com.cenker.scrm.service.sop.ISopCustomerInfoService;
import com.cenker.scrm.service.sop.ISopInfoService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.SnowflakeIdUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.util.XxlJobUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/7/10
 * @Description sop基础服务层
 */
@Data
@RequiredArgsConstructor
@Slf4j
public abstract class BaseSopBiz {

    private final ISopInfoService sopInfoService;
    private final ISopContentInfoService sopContentInfoService;
    private final XxlJobUtil xxlJobUtil;
    private final ISopCustomerInfoService sopCustomerInfoService;
    private final ITbWxMassMessageInfoService tbWxMassMessageInfoService;
    private final ITbWxExtJourneyInfoService tbWxExtJourneyInfoService;
    private final ISopConditionInfoService sopConditionInfoService;
    private final ITbWxExtJourneyCustomerStageService tbWxExtJourneyCustomerStageService;
    private final CustomerConditionBizHandler customerConditionBizHandler;
    private final IApprovalService approvalService;
    private final ApprovalMsgNotify approvalMsgNotify;
    private final RedissonClient redissonClient;
    public void saveSopInfo(BaseSopRequest request) {
        log.info("【SOP】开始保存sop信息，请求原文体：{}", request);
        SopInfo sopInfo;
        if (ObjectUtil.isEmpty(request.getSopId())) {
            SopInfo.SopInfoBuilder sopInfoBuilder = SopInfo.builder().sopType(request.getSopType())
                    .sopName(request.getSopName())
                    .remark(request.getRemark())
                    .deleted(false)
                    .createBy(request.getCreateBy())
                    .deptId(request.getDeptId())
                    .enableApproval(request.isEnableApproval())
                    .corpId(request.getCorpId());
            if (request.isEnableApproval()) {
                sopInfoBuilder.alive(ApprovalStatusEnum.PENDING_APPROVAL.getStatus());
            } else {
                sopInfoBuilder.alive(ApprovalStatusEnum.EXEC_INTERRUPTED.getStatus());
            }
            if (request instanceof ConditionSopRequest) {
                // 由于条件存储结构发送变化 另起表存 不影响原有逻辑 暂时兼容条件sop存储sop主表
                ConditionSopRequest conditionSopRequest = (ConditionSopRequest) request;
                sopInfoBuilder.sendCondition(conditionSopRequest.getSendCondition())
                        .viewSendCondition(conditionSopRequest.getSendCondition());
            }
            if (request instanceof JourneySopRequest) {
                JourneySopRequest journeySopRequest = (JourneySopRequest) request;
                sopInfoBuilder.journeyId(journeySopRequest.getJourneyId());
            }
            sopInfo = sopInfoBuilder.build();
            request.setAlive(sopInfo.getAlive());
            sopInfoService.save(sopInfo);
            request.setSopId(sopInfo.getId());
            request.setCreateBy(sopInfo.getCreateBy());
            return;
        }
        sopInfo = sopInfoService.getById(request.getSopId());
        Optional.ofNullable(sopInfo).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        if (ApprovalStatusEnum.isRunning(sopInfo.getAlive())) {
            throw new CustomException(ErrCodeEnum.SOP_IS_RUNNING_MUST_DELETE);
        }

        request.setEnableApproval(sopInfo.isEnableApproval());
        request.setCreateBy(sopInfo.getCreateBy());
        if (sopInfo.isEnableApproval()) {
            sopInfo.setAlive(ApprovalStatusEnum.PENDING_APPROVAL.getStatus());
        } else {
            sopInfo.setAlive(ApprovalStatusEnum.EXEC_INTERRUPTED.getStatus());
        }
        request.setAlive(sopInfo.getAlive());
        // 条件sop不允许修改条件 后续sop看需不需要做适配 目前条件不做更新
        sopInfoService.update(Wrappers.lambdaUpdate(SopInfo.class)
                .set(SopInfo::getSopName, request.getSopName())
                .set(SopInfo::getRemark, request.getRemark())
                .set(SopInfo::getUpdateBy, request.getUpdateBy())
                .set(SopInfo::getAlive, sopInfo.getAlive())
                .eq(SopInfo::getId, sopInfo.getId())
        );
    }

    /**
     * 创建内容序列
     *
     * @param baseSopRequest sop内容序列请求体
     * @param update         是否新增
     */
    public abstract void saveSopContentInfo(BaseSopRequest baseSopRequest, boolean update);

    /**
     * 校验运行状态 运行中抛出异常
     *
     * @param request
     * @return SopInfo
     */
    public SopInfo validSopAliveStatus(BaseSopRequest request) {
        SopInfo sopInfo = sopInfoService.getById(request.getSopId());
        Optional.ofNullable(sopInfo).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        if (ApprovalStatusEnum.isRunning(sopInfo.getAlive())) {
            throw new CustomException(ErrCodeEnum.SOP_IS_RUNNING_MUST_DELETE);
        }
        if(Objects.equals(ApprovalStatusEnum.PENDING_APPROVAL.getStatus(),sopInfo.getAlive())) {
            throw new CustomException(ErrCodeEnum.SOP_IS_APPROVAL_MUST_DELETE);
        }
        return sopInfo;
    }

    /**
     * 删除sop内容序列
     *
     * @param request
     */
    public void removeSopContent(BaseSopRequest request) {
        sopContentInfoService.update(Wrappers.lambdaUpdate(SopContentInfo.class)
                .set(SopContentInfo::getDeleted, true)
                .set(SopContentInfo::getAliveVersion, false)
                .eq(SopContentInfo::getSopId, request.getSopId()));
    }

    /**
     * 删除sop主信息
     *
     * @param request
     */
    public void removeSopInfo(BaseSopRequest request) {
        SopInfo sopInfo = sopInfoService.getById(request.getSopId());
        LogUtil.logOperDesc(sopInfo.getSopName());
        sopInfoService.removeById(request.getSopId());
    }

    /**
     * 修改sopInfo状态
     *
     * @param request
     */
    public boolean changeSopStatus(BaseSopRequest request) {
        return sopInfoService.update(
                Wrappers.lambdaUpdate(SopInfo.class).eq(SopInfo::getId, request.getSopId())
                        .set(SopInfo::getAlive, request.getAlive())
                        .set(ApprovalStatusEnum.isRunning(request.getAlive()), SopInfo::getStartTime, DateUtils.getNowDate())
                        .set(!ApprovalStatusEnum.isRunning(request.getAlive()), SopInfo::getEndTime, DateUtils.getNowDate())
                        .set(ObjectUtil.isNotNull(request.getJobId()), SopInfo::getJobId, request.getJobId())
        );
    }

    public void toMessageMass(SopContentInfo contentInfo, List<String> externalUserIdList, boolean userDistinct) {
        // 构造群发数据
        MassMessageChainDTO massMessageChainDTO = new MassMessageChainDTO();
        massMessageChainDTO.setId(SnowflakeIdUtil.getSnowId());
        // 群发任务名 内容id + 版本号
        massMessageChainDTO.setMassName(contentInfo.getContentVersion());
        massMessageChainDTO.setMassScene(TypeConstants.MASS_MESSAGE_SCENE_SOP_CORP);
        massMessageChainDTO.setCheckStatus(ApprovalStatusEnum.CREATED.getStatus());
        massMessageChainDTO.setUserDistinct(userDistinct);
        massMessageChainDTO.setSettingTime(DateUtils.getNowDate());
        massMessageChainDTO.setChatType(Constants.CHAT_TYPE_SINGLE);
        massMessageChainDTO.setCorpConfigId(DefaultConstants.PRIVATE_CORP_CONFIG_ID);
        massMessageChainDTO.setMassContent(contentInfo.getContentText());
        massMessageChainDTO.setAttachments(contentInfo.getContentAttachment());
        massMessageChainDTO.setSceneId(contentInfo.getId());
        massMessageChainDTO.setStopTaskHour(contentInfo.getStopTaskHour());
        // 群发对象
        MessageConditionDTO messageCondition = new MessageConditionDTO();
        messageCondition.setExternalUserIdList(externalUserIdList);
        massMessageChainDTO.setMessageCondition(messageCondition);

        massMessageChainDTO.setCorpId(CorpInfoProperties.getCorpId());
        massMessageChainDTO.setMassJson(JSON.toJSONString(massMessageChainDTO));

        log.info("【sop】执行sopId:【{}】创建群发消息", contentInfo.getSopId());

        List<MassMessageChainDetailDTO> details = Lists.newArrayList();
        List<TbWxExtFollowUser> followUserList = sopCustomerInfoService.selectNormalFollowUser(externalUserIdList);

        Map<String, List<String>> userExtCustomerMap = followUserList.stream()
                .collect(Collectors.groupingBy(TbWxExtFollowUser::getUserId, Collectors.mapping(TbWxExtFollowUser::getExternalUserId, Collectors.toList())));
        // 客户多员工发送处理
        for (Map.Entry<String, List<String>> entry : userExtCustomerMap.entrySet()) {
            List<String> externalUserIds = entry.getValue();
            String userId = entry.getKey();
            // 针对客户数超过1万的情况下，进行分批次处理
            if (externalUserIds.size() > NumberConstants.INT_10000) {
                List<List<String>> partition = Lists.partition(externalUserIds, NumberConstants.INT_10000);
                for (List<String> ids : partition) {
                    MessageConditionDTO messageConditionDTO = new MessageConditionDTO();
                    BeanUtils.copyProperties(messageCondition, messageConditionDTO);
                    messageConditionDTO.setExternalUserIdList(ids);

                    MassMessageChainDetailDTO massMessageChainDetailDTO = new MassMessageChainDetailDTO();
                    if (!userDistinct) {
                        massMessageChainDetailDTO.setSender(userId);
                    }
                    massMessageChainDetailDTO.setMessageCondition(messageConditionDTO);
                    details.add(massMessageChainDetailDTO);
                }
            } else {
                MessageConditionDTO messageConditionDTO = new MessageConditionDTO();
                BeanUtils.copyProperties(messageCondition, messageConditionDTO);
                messageConditionDTO.setExternalUserIdList(externalUserIds);

                MassMessageChainDetailDTO massMessageChainDetailDTO = new MassMessageChainDetailDTO();
                if (!userDistinct) {
                    massMessageChainDetailDTO.setSender(userId);
                }
                massMessageChainDetailDTO.setMessageCondition(messageConditionDTO);
                details.add(massMessageChainDetailDTO);
            }
        }
        massMessageChainDTO.setDetails(details);

        // 群发消息入库
        saveTbWxMassMessageInfo(massMessageChainDTO);
        // 存在附件校验及获取素材id
        String attachmentStr = JSON.toJSONString(massMessageChainDTO.getAttachments());
        massMessageChainDTO.setAttachments(JSON.parseArray(attachmentStr, WelcomeAttachmentVo.class));
        getTbWxMassMessageInfoService().getMediaId(massMessageChainDTO, massMessageChainDTO.getAttachments());
        // 加入群发序列
        MassMessageHandlerFactory.getMassMessageHandler().handler(massMessageChainDTO);
    }

    private void saveTbWxMassMessageInfo(MassMessageChainDTO massMessageChainDTO) {
        TbWxMassMessageInfo tbWxMassMessageInfo = new TbWxMassMessageInfo();
        tbWxMassMessageInfo.setId(massMessageChainDTO.getId());
        tbWxMassMessageInfo.setMassName(massMessageChainDTO.getMassName());
        tbWxMassMessageInfo.setMassScene(massMessageChainDTO.getMassScene());
        tbWxMassMessageInfo.setCheckStatus(massMessageChainDTO.getCheckStatus());
        tbWxMassMessageInfo.setUserDistinct(massMessageChainDTO.getUserDistinct());
        tbWxMassMessageInfo.setSettingTime(massMessageChainDTO.getSettingTime());
        tbWxMassMessageInfo.setChatType(massMessageChainDTO.getChatType());
        tbWxMassMessageInfo.setCorpConfigId(massMessageChainDTO.getCorpConfigId());
        tbWxMassMessageInfo.setStopTaskHour(massMessageChainDTO.getStopTaskHour());
        if (ObjectUtil.isNotNull(massMessageChainDTO.getAttachments())) {
            tbWxMassMessageInfo.setMassAttachment(JSON.toJSONString(massMessageChainDTO.getAttachments()));
        }
        tbWxMassMessageInfo.setSceneId(massMessageChainDTO.getSceneId());
        tbWxMassMessageInfo.setMassContent(massMessageChainDTO.getMassContent());
        tbWxMassMessageInfo.setMassJson(massMessageChainDTO.getMassJson());
        tbWxMassMessageInfoService.save(tbWxMassMessageInfo);
    }

    /**
     * 启动定时任务
     *
     * @param sopInfoVO
     */
    public abstract void createOrStartContentTask(SopInfoVO sopInfoVO);

    /**
     * 停用定时任务
     *
     * @param sopInfoVO
     */
    public abstract void stopContentTask(SopInfoVO sopInfoVO);

    /**
     * 启动sop级的定时任务定时任务
     *
     * @param sopInfoVO
     */
    public abstract void createOrStartSopScanTask(SopInfoVO sopInfoVO);

    /**
     * 停用sop级的定时任务定时任务
     *
     * @param sopInfoVO
     */
    public abstract void stopSopScanTask(SopInfoVO sopInfoVO);

    /**
     * 保存sop条件信息
     *
     * @param request
     */
    public abstract void saveSopConditionInfo(BaseSopRequest request);

    /**
     * 创建/启动sop
     *
     * @param sopInfoVO
     */
    public abstract void createOrStartSop(SopInfoVO sopInfoVO, BaseSopRequest baseSopRequest);

    /**
     * 执行内容序列
     *
     * @param contentInfo
     */
    public abstract void runConditionContent(SopContentInfo contentInfo);

    /**
     * 删除sop条件
     *
     * @param request
     */
    public void removeSopCondition(JourneySopRequest request) {
        sopConditionInfoService.remove(Wrappers.lambdaQuery(SopConditionInfo.class).eq(SopConditionInfo::getSopId, request.getSopId()));
    }

    @Async
    public void synchronizeData(BaseSopRequest request) {
        // 查询出当前sop所有未过期的群发消息
        List<TbWxMassMessageInfo> massMessageList = sopContentInfoService.selectValidMassMessageList(request);
        if (CollectionUtil.isNotEmpty(massMessageList)) {
            for (TbWxMassMessageInfo massMessageListVO : massMessageList) {
                tbWxMassMessageInfoService.synchronizeMassMessage(massMessageListVO.getId(), CorpInfoProperties.getCorpId());
            }
        }
    }

    /**
     * 处理数据 canApproval 字段
     *
     * @param list
     * @param loginUser
     * @param typeEnum
     */
    public void dealData(List<? extends SopInfoVO> list, LoginUser loginUser, ApprovalTypeEnum typeEnum) {
        // 处理 canApproval 字段
        // 是审批人，且不是创建人
        Result<Boolean> approvalUser = getApprovalService().isApprovalUser(typeEnum.getType());
        if (approvalUser.isSuccess() && approvalUser.getData()) {
            boolean isApproval = approvalUser.getData();
            for (SopInfoVO vo : list) {
                boolean canApproval = vo.isEnableApproval() && isApproval
                        && !vo.getCreateBy().equals(loginUser.getUser().getUserId())
                        && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(vo.getAlive());
                vo.setCanApproval(canApproval);
            }
        }
    }
    /**
     * 处理数据 canApproval 字段
     *
     * @param loginUser
     * @param typeEnum
     */
    public void dealData(SopInfoVO conditionSopDetailVO, LoginUser loginUser, ApprovalTypeEnum typeEnum) {
        Result<Boolean> approvalUser = getApprovalService().isApprovalUser(typeEnum.getType());
        if (approvalUser.isSuccess() && approvalUser.getData()) {
            boolean isApproval = approvalUser.getData();
            boolean canApproval = conditionSopDetailVO.isEnableApproval() && isApproval
                    && !conditionSopDetailVO.getCreateBy().equals(loginUser.getUser().getUserId())
                    && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(conditionSopDetailVO.getAlive());
            conditionSopDetailVO.setCanApproval(canApproval);
        }
    }

    protected Result approveCommon(ApprovalVO approvalVO, LoginUser loginUser, String type) {
        String key = "approval:" + approvalVO.getId() + ":" + type;
        RLock lock = redissonClient.getLock(key);
        try {
            boolean cacheRes = lock.tryLock(5L, 30L, TimeUnit.SECONDS);
            if (!cacheRes) {
                return Result.error(500, "获取锁失败，其他用户正在执行审核操作！");
            }
            log.info("【审核sop】加锁成功：{}", key);
            SopInfo info = getSopInfoService().getById(approvalVO.getId());
            if (info == null) {
                return Result.error(500, "未找到sop信息！");
            }
            log.info("【审核sop】审核对象：{}", JSON.toJSONString(info));
            Result<Object> validateApproval = approvalService.validateApproval(loginUser, type, info.getSopName(), info.getCreateBy()+"", info.getAlive());
            log.info("【审核sop】审核验证结果：{}", JSON.toJSONString(validateApproval));
            if (validateApproval != null) return validateApproval;
            if (Objects.equals(approvalVO.getAgree(), true)) {
                getSopInfoService().lambdaUpdate()
                        .set(SopInfo::getAlive, ApprovalStatusEnum.EXECUTING.getStatus())
                        .set(SopInfo::getApprovalUser, loginUser.getUser().getUserId())
                        .set(SopInfo::getApprovalRemark, approvalVO.getRemark())
                        .eq(SopInfo::getId, approvalVO.getId()).update();

                // 执行内容序列
                createOrStartSopAfterApproval(approvalVO.getId());

                // 发送通知
                approvalService.sendApprovalMsg(info, type, true,
                        SopInfo::getSopName,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString());
                log.info("【审核sop】审核通过，发送通知");
            } else {
                // 如果为 null 或者 "" 则替换为 "内容未满足审核标准，请与审核员沟通后修改并重新提交"
                if (StrUtil.isEmptyIfStr(approvalVO.getRemark())) {
                    approvalVO.setRemark("内容未满足审核标准，请与审核员沟通后修改并重新提交");
                }
                getSopInfoService().lambdaUpdate()
                        .set(SopInfo::getAlive, ApprovalStatusEnum.REJECTED.getStatus())
                        .set(SopInfo::getApprovalUser, loginUser.getUser().getUserId())
                        .set(SopInfo::getApprovalRemark, approvalVO.getRemark())
                        .eq(SopInfo::getId, approvalVO.getId()).update();
                // 发送通知
                approvalService.sendApprovalMsg(info, type, false,
                        SopInfo::getSopName,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString());
                log.info("【审核sop】审核驳回，发送通知");
            }
        } catch (InterruptedException e) {
            log.error("【审核sop】获取锁失败！", e);
            return Result.error(500, "获取锁失败！");
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("【审核sop】释放锁成功！");
            }
        }

        return Result.success();
    }

    protected abstract void createOrStartSopAfterApproval(@NotBlank(message = "id不能为空") String id);

    public Result revoked(ApprovalVO approvalVO, LoginUser loginUser) {
        SopInfo info = getSopInfoService().getById(approvalVO.getId());
        if (info == null) {
            return Result.error(500, "未找到sop信息！");
        }
        log.info("【撤回sop】撤回对象：{}", JSON.toJSONString(info));
        if (!info.isEnableApproval()) {
            return Result.error(500, "该任务不支持撤回操作！");
        }
        if (!Objects.equals(info.getAlive(), ApprovalStatusEnum.PENDING_APPROVAL.getStatus())) {
            return Result.error(500, "不是待审核状态不支持撤回操作！");
        }
        if (!Objects.equals(loginUser.getUser().getUserId(), info.getCreateBy()+"")) {
            return Result.error(500, "用户不能对其他人创建的内容进行撤回！");
        }
        log.info("【撤回sop】验证通过");

        getSopInfoService().lambdaUpdate()
                .set(SopInfo::getAlive, ApprovalStatusEnum.REVOKED.getStatus())
                .eq(SopInfo::getId, approvalVO.getId())
                .eq(SopInfo::getAlive, ApprovalStatusEnum.PENDING_APPROVAL.getStatus()).update();
        log.info("【撤回sop】更新为已撤回状态");
        return Result.success();
    }

    public void checkSopAndAlarm(SopInfo info, SopContentInfo contentInfo, ApprovalTypeEnum approvalTypeEnum) {
        /**
         * 若当前时间已超出定时任务的发布时间，
         * 但仍未对其进行审核，那么此任务的状态会自动更改为“已退回”，
         * 且退回理由将显示为：“超过审核时间未通过审核”
         */
        // TODO
        /**
         *  如果“待审核”任务超过创建时间 1 小时（发送时间为立即发送），
         *  系统将会再次向审核员发送消息通知，同时也会通知创建者。
         */
        // TODO
        /**
         *  如果定时任务在距离外发时间 30 分钟时仍处于“待审核”的状态，
         *  那么将会同时向审核员和创建者发送消息通知以作提醒。
         */
        // TODO
    }
}
