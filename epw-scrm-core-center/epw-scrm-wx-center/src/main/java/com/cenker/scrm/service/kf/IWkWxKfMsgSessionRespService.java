package com.cenker.scrm.service.kf;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgSessionResp;
import com.cenker.scrm.pojo.request.kf.WkWxKfChatRecordQuery;
import com.cenker.scrm.pojo.dto.kf.WkWxKfMsgChatDTO;

import java.util.List;

public interface IWkWxKfMsgSessionRespService extends IService<WkWxKfMsgSessionResp> {

    List<WkWxKfMsgSessionResp> getWkWxKfMsgSessionResp(WkWxKfMsgSessionResp wkWxKfMsgSessionResp);

    List<WkWxKfMsgChatDTO> getChatRecord(WkWxKfChatRecordQuery chatRecordQueryDTO);

}
