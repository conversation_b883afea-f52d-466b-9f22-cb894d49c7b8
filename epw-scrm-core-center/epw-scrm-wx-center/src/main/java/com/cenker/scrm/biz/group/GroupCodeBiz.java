package com.cenker.scrm.biz.group;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.config.GroupConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.*;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.OssStoragePathEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.mapper.ClickRecordMapper;
import com.cenker.scrm.mapper.group.WkWxGroupChatConfigMapper;
import com.cenker.scrm.pojo.dto.group.GroupCodeConfigDTO;
import com.cenker.scrm.pojo.entity.wechat.ClickRecord;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeConfig;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import com.cenker.scrm.pojo.entity.wechat.group.WkWxGroupChatConfig;
import com.cenker.scrm.pojo.entity.wechat.group.WkWxGroupChatInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.vo.group.GroupCodeDetailVO;
import com.cenker.scrm.service.group.IGroupCodeConfigService;
import com.cenker.scrm.service.group.IGroupCodeInfoService;
import com.cenker.scrm.service.group.IWkWxGroupChatInfoService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ImageUtils;
import com.cenker.scrm.util.http.RequestUtil;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpGroupJoinWayInfo;
import me.chanjar.weixin.cp.bean.external.WxCpGroupJoinWayResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.cenker.scrm.constants.CommonConstants.NUM_1;

/**
 * <AUTHOR>
 * @Date 2023/8/23
 * @Description 社群活码
 */
@Data
@RequiredArgsConstructor
@Slf4j
@Component
public class GroupCodeBiz {

    private final OssStorageFactory ossStorageFactory;
    private final IWkWxGroupChatInfoService wkWxGroupChatInfoService;
    private final WkWxGroupChatConfigMapper wkWxGroupChatConfigMapper;
    private final WxCpFeign wxCpFeign;
    private final ClickRecordMapper clickRecordMapper;
    private final IGroupCodeInfoService groupCodeInfoService;
    private final IGroupCodeConfigService groupCodeConfigService;
    private final RedisCache redisCache;

    public String generateQrCode(GroupCodeRequest request) {
        QrConfig qrConfig = new QrConfig(300, 300);
        qrConfig.setMargin(1);
        Color bgColor = new Color(255, 255, 255);
        Color foreColor = new Color(0, 0, 0);
        qrConfig.setForeColor(foreColor);
        qrConfig.setBackColor(bgColor);
        try {
            BufferedImage bufferedImageByUrl = ImageUtils.getBufferedImageByUrl(redisCache.getCacheObject(DefaultConstants.QR_LOGO));
            qrConfig.setImg(bufferedImageByUrl);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            QrCodeUtil.generate(GroupConfig.getCodeScanHandlerUrl() + request.getCodeId(), qrConfig, FileTypeConstant.IMAGE_JPG, os);
            // 上传对象存储
            return ossStorageFactory.build().upload(new ByteArrayInputStream(os.toByteArray()), FileTypeConstant.IMAGE_PNG_DOT,
                    OssStoragePathEnum.GROUP_CODE.getPath() + request.getCodeId() + SeparatorEnum.SLASH.getSeparator());
        } catch (IOException e) {
            log.error("【社群活码】生成二维码失败，错误信息：{}", e);
            throw new CustomException(ErrCodeEnum.GROUP_CODE_GENERATE_ERROR);
        }
    }


    /**
     * 获取企微群活码信息
     *
     * @param returnIndex 需要返回的活码索引下标
     * @param detailVO    社群活码详情
     * @return
     */
    @RedisLockAspect(key = CacheKeyConstants.GROUP_CODE_SCAN_LOCK, value = "#detailVO.codeId", waitTime = 60)
    public String getWkWxGroupCodeInfoByCodeId(int returnIndex, GroupCodeDetailVO detailVO) {
        Long codeId = detailVO.getCodeId();
        List<GroupCodeConfigDTO> groupList = detailVO.getGroupList();
        GroupCodeConfigDTO groupCodeConfigDTO = groupList.get(returnIndex);
        WkWxGroupChatInfo wkWxGroupChatInfo = wkWxGroupChatInfoService.getOne(Wrappers.lambdaQuery(WkWxGroupChatInfo.class)
                .select(WkWxGroupChatInfo::getId, WkWxGroupChatInfo::getQrCode, WkWxGroupChatInfo::getConfigId, WkWxGroupChatInfo::getState)
                .eq(WkWxGroupChatInfo::getGroupCodeId, codeId).last("limit 1"));
        if (ObjectUtil.isNull(wkWxGroupChatInfo)) {
            boolean firstGenerate = StringUtils.isEmpty(detailVO.getCodeState());
//            String state = firstGenerate ? WeConstants.SIGN_STATE_PREFIX + UUID.tokenBuild(20) : detailVO.getCodeState();
            log.info("【社群活码】开始配置客户群进群方式");
            WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo = new WxCpGroupJoinWayInfo();
            String remark = firstGenerate ? groupCodeConfigDTO.getChatId() + "初次生成" : groupCodeConfigDTO.getChatId() + "重新生成";
            WxCpGroupJoinWayInfo.JoinWay joinWay = getJoinWay(returnIndex, detailVO, remark, detailVO.getCodeState());
            wxCpGroupJoinWayInfo.setJoinWay(joinWay);
            // 需要生成企微群活码
            WxCpGroupJoinWayResult wxCpGroupJoinWayResult = wxCpFeign.addJoinWay(wxCpGroupJoinWayInfo, CorpInfoProperties.getCorpId());
            // 查询企微群活码信息
            wxCpGroupJoinWayInfo = wxCpFeign.getJoinWay(wxCpGroupJoinWayResult.getConfigId(), CorpInfoProperties.getCorpId());
            wkWxGroupChatInfo = WkWxGroupChatInfo.builder().groupCodeId(codeId).build();
            wkWxGroupChatInfo.setRemark(joinWay.getRemark());
            wkWxGroupChatInfoService.saveGroupChatInfo(wkWxGroupChatInfo, wxCpGroupJoinWayInfo);
            /*if (firstGenerate) {
                groupCodeInfoService.update(Wrappers.lambdaUpdate(GroupCodeInfo.class).set(GroupCodeInfo::getState, state).eq(GroupCodeInfo::getId, codeId));
            }*/
            // 存在最后一个群是满人群且自动建群的情况下 这时候会发生自动建群但未加入到群列表 所以需要重新查询一次群活码信息
     /*       if (groupCodeConfigDTO.getChatMemberCnt() > GroupConfig.getLimitMemberCnt() &&
                    StatusConstants.DEL_FLAG_FALSE_INT == wxCpGroupJoinWayInfo.getJoinWay().getAutoCreateRoom()) {
                wxCpGroupJoinWayInfo = wxCpFeign.getJoinWay(wxCpGroupJoinWayResult.getConfigId(), CorpInfoProperties.getCorpId());
                if (wxCpGroupJoinWayInfo.getJoinWay().getChatIdList().size() > 1) {

                }
            }*/
            return wkWxGroupChatInfo.getQrCode();
        }
        WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo = wxCpFeign.getJoinWay(wkWxGroupChatInfo.getConfigId(), CorpInfoProperties.getCorpId());
        List<String> chatIdList = wxCpGroupJoinWayInfo.getJoinWay().getChatIdList();
        if (chatIdList.size() > 1) {
            log.info("【社群活码】获取到的企微加入群聊社群数超过1个,configId:{}，chatIdList:{}", wkWxGroupChatInfo.getConfigId(), chatIdList);
            List<String> existChatIdList = groupList.stream().map(GroupCodeConfigDTO::getChatId).collect(Collectors.toList());
            // 说明已经开始自动建群了
            addNewGroup2CodeConfig(detailVO, wkWxGroupChatInfo, wxCpGroupJoinWayInfo, chatIdList, existChatIdList);
            return wkWxGroupChatInfo.getQrCode();
        }
        if (chatIdList.get(CommonConstants.NUM_0).equals(groupCodeConfigDTO.getChatId())) {
            // 如果编辑时把后面的群删掉了 并且刚好最后所在的群就是之前选定的群 那有可能出现不会自动建群的情况 这时候需要自动更新活码
            // 后面修改了编辑的逻辑直接创建新的活码，所以并不会出现上面的问题了
            return wkWxGroupChatInfo.getQrCode();
        }
        log.info("【社群活码】开始更新客户群进群方式");
        // 需要返回群非当前配置群,更新企微群活码
        WxCpGroupJoinWayInfo.JoinWay joinWay = getJoinWay(returnIndex, detailVO, groupCodeConfigDTO.getChatId() + "覆盖更新", wkWxGroupChatInfo.getState());
        joinWay.setConfigId(wkWxGroupChatInfo.getConfigId());
        wxCpGroupJoinWayInfo.setJoinWay(joinWay);
        wxCpFeign.updateJoinWay(wxCpGroupJoinWayInfo, CorpInfoProperties.getCorpId());
        wkWxGroupChatInfoService.saveGroupChatInfo(wkWxGroupChatInfo, wxCpGroupJoinWayInfo);
        return wkWxGroupChatInfo.getQrCode();
    }

    private void addNewGroup2CodeConfig(GroupCodeDetailVO detailVO, WkWxGroupChatInfo wkWxGroupChatInfo, WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo, List<String> chatIdList, List<String> existChatIdList) {
        for (String chatId : chatIdList) {
            if (!existChatIdList.contains(chatId)) {
                // 代表是新群
                createGroupCode4Auto(detailVO, wkWxGroupChatInfo, wxCpGroupJoinWayInfo, chatIdList, chatId);
                // 标记为已分裂
                groupCodeConfigService.update(Wrappers.lambdaUpdate(GroupCodeConfig.class)
                        .set(GroupCodeConfig::getRemark, "已分裂新群")
                        .eq(GroupCodeConfig::getGroupCodeId, detailVO.getCodeId())
                        .eq(GroupCodeConfig::getChatId, chatId));
            }
        }
    }

    /**
     * 更新企微活码
     *
     * @param detailVO
     * @param wkWxGroupChatInfo
     * @param wxCpGroupJoinWayInfo
     * @param chatIdList
     * @param chatId
     */
    private void createGroupCode4Auto(GroupCodeDetailVO detailVO, WkWxGroupChatInfo wkWxGroupChatInfo, WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo, List<String> chatIdList, String chatId) {
        Long codeId = detailVO.getCodeId();
        List<GroupCodeConfigDTO> groupList = detailVO.getGroupList();
        WxCpGroupJoinWayInfo.JoinWay joinWay = new WxCpGroupJoinWayInfo.JoinWay();
        joinWay.setScene(WeConstants.GROUP_QR_CODE_SCENE);
        joinWay.setRemark("自动建群更新");
        joinWay.setAutoCreateRoom(StatusConstants.DEL_FLAG_TRUE_INT);
        joinWay.setRoomBaseName(detailVO.getRoomBaseName());
        joinWay.setRoomBaseId(detailVO.getRoomBaseId());
        joinWay.setChatIdList(Lists.newArrayList(chatId));
        joinWay.setState(wkWxGroupChatInfo.getState());
        joinWay.setConfigId(wkWxGroupChatInfo.getConfigId());
        wxCpGroupJoinWayInfo.setJoinWay(joinWay);
        wxCpFeign.updateJoinWay(wxCpGroupJoinWayInfo, CorpInfoProperties.getCorpId());
        wkWxGroupChatInfoService.saveGroupChatInfo(wkWxGroupChatInfo, wxCpGroupJoinWayInfo);
        // 加入到社群活码社群列表
        GroupCodeConfig groupCodeConfig = new GroupCodeConfig();
        groupCodeConfig.setGroupCodeId(codeId);
        groupCodeConfig.setChatId(chatId);
        groupCodeConfig.setListSort(groupList.size());
        groupCodeConfig.setRemark("获取群列表" + chatIdList.toString());
        groupCodeConfig.setCorpId(CorpInfoProperties.getCorpId());
        groupCodeConfigService.save(groupCodeConfig);
    }

    /**
     * 获取客户群进群方式配置参数
     *
     * @param returnIndex 需要返回的活码索引下标
     * @param detailVO    活码详情
     * @return
     */
    private WxCpGroupJoinWayInfo.JoinWay getJoinWay(int returnIndex, GroupCodeDetailVO detailVO, String remark, String state) {
        List<GroupCodeConfigDTO> groupList = detailVO.getGroupList();
        GroupCodeConfigDTO groupCodeConfigDTO = groupList.get(returnIndex);
        WxCpGroupJoinWayInfo.JoinWay joinWay = new WxCpGroupJoinWayInfo.JoinWay();
        joinWay.setScene(WeConstants.GROUP_QR_CODE_SCENE);
        joinWay.setRemark(remark);
        // 如果不是社群列表最后一个群，不论是否选择新建群，暂时放弃自动建群
        if (groupList.size() - NUM_1 != returnIndex) {
            joinWay.setAutoCreateRoom(StatusConstants.DEL_FLAG_FALSE_INT);
        } else {
            joinWay.setAutoCreateRoom(detailVO.getAutoCreateRoom() ? StatusConstants.DEL_FLAG_TRUE_INT : StatusConstants.DEL_FLAG_FALSE_INT);
        }
        joinWay.setRoomBaseName(detailVO.getRoomBaseName());
        joinWay.setRoomBaseId(detailVO.getRoomBaseId());
        joinWay.setChatIdList(Lists.newArrayList(groupCodeConfigDTO.getChatId()));
        joinWay.setState(state);
        return joinWay;
    }

    @Async
    public void recordScanCnt(GroupCodeDetailVO groupCodeDetailVO, HttpServletRequest request) {
        String ipAddress = RequestUtil.getIpAddress(request);
        ClickRecord clickRecord = ClickRecord.builder().scene(1).sceneId(groupCodeDetailVO.getCodeId())
                .ip(ipAddress).clickTimestamp(System.currentTimeMillis()).clickDate(DateUtils.getNowDate())
                .corpId(CorpInfoProperties.getCorpId()).build();
        clickRecordMapper.insert(clickRecord);
    }

    public void removeGroupCode(GroupCodeRequest request) {
        List<String> codeNameList = groupCodeInfoService.lambdaQuery().in(GroupCodeInfo::getId, request.getCodeIdList()).list()
                        .stream().map(GroupCodeInfo::getCodeName).collect(Collectors.toList());
        LogUtil.logOperDesc(StrUtil.join(StrUtil.COMMA, codeNameList));

        groupCodeInfoService.removeByIds(request.getCodeIdList());
        // 删除企微配置
        groupCodeConfigService.remove(Wrappers.lambdaUpdate(GroupCodeConfig.class).in(GroupCodeConfig::getGroupCodeId, request.getCodeIdList()));
        // 删除掉企微码
        List<WkWxGroupChatInfo> list = wkWxGroupChatInfoService.list(Wrappers.lambdaQuery(WkWxGroupChatInfo.class).in(WkWxGroupChatInfo::getGroupCodeId, request.getCodeIdList()));
        if (CollectionUtil.isNotEmpty(list)) {
            for (WkWxGroupChatInfo wkWxGroupChatInfo : list) {
                wkWxGroupChatInfoService.remove(Wrappers.lambdaUpdate(WkWxGroupChatInfo.class).eq(WkWxGroupChatInfo::getId, wkWxGroupChatInfo.getId()));
                wxCpFeign.deleteJoinWay(wkWxGroupChatInfo.getConfigId(), CorpInfoProperties.getCorpId());
                wkWxGroupChatConfigMapper.delete(Wrappers.lambdaQuery(WkWxGroupChatConfig.class).eq(WkWxGroupChatConfig::getGroupChatId, wkWxGroupChatInfo.getId()));
            }
        }

    }

    public boolean removeWxGroupCode(String groupCodeId) {
        log.info("【社群活码】废弃社群活码：{}", groupCodeId);
        LambdaQueryWrapper<WkWxGroupChatInfo> queryWrapper = Wrappers.lambdaQuery(WkWxGroupChatInfo.class)
                .eq(WkWxGroupChatInfo::getGroupCodeId, groupCodeId)
                .last("limit 1");
        WkWxGroupChatInfo wkWxGroupChatInfo = wkWxGroupChatInfoService.getOne(queryWrapper, false);
        if (ObjectUtil.isNull(wkWxGroupChatInfo)) {
            return false;
        }

        wkWxGroupChatInfoService.removeById(wkWxGroupChatInfo.getId());
        wxCpFeign.deleteJoinWay(wkWxGroupChatInfo.getConfigId(), wkWxGroupChatInfo.getCorpId());
        wkWxGroupChatConfigMapper.delete(Wrappers.lambdaQuery(WkWxGroupChatConfig.class).eq(WkWxGroupChatConfig::getGroupChatId, wkWxGroupChatInfo.getId()));

        log.info("【社群活码】成功废弃社群活码：{}", groupCodeId);
        return true;
    }

    public void checkNewGroupHasCreate(GroupCodeDetailVO detailVO) {
        Long codeId = detailVO.getCodeId();
        WkWxGroupChatInfo wkWxGroupChatInfo = wkWxGroupChatInfoService.getOne(Wrappers.lambdaQuery(WkWxGroupChatInfo.class)
                .select(WkWxGroupChatInfo::getId, WkWxGroupChatInfo::getQrCode, WkWxGroupChatInfo::getConfigId, WkWxGroupChatInfo::getState)
                .eq(WkWxGroupChatInfo::getGroupCodeId, codeId).last("limit 1"));
        if (ObjectUtil.isNotNull(wkWxGroupChatInfo)) {
            List<GroupCodeConfigDTO> groupList = detailVO.getGroupList();
            WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo = wxCpFeign.getJoinWay(wkWxGroupChatInfo.getConfigId(), CorpInfoProperties.getCorpId());
            List<String> chatIdList = wxCpGroupJoinWayInfo.getJoinWay().getChatIdList();
            if (chatIdList.size() > 1) {
                log.info("【社群活码】检测到开始自动建群，codeId{}", codeId);
                List<String> existChatIdList = groupList.stream().map(GroupCodeConfigDTO::getChatId).collect(Collectors.toList());
                addNewGroup2CodeConfig(detailVO, wkWxGroupChatInfo, wxCpGroupJoinWayInfo, chatIdList, existChatIdList);
            }
        }
    }
}
