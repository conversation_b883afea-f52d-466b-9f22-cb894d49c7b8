package com.cenker.scrm.event;


import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalGroupChatList;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * <AUTHOR>
 * 同步客户群事件
 */
@Slf4j
@Data
public class SynCustomerGroupEvent extends ApplicationEvent {
    private WxCpUserExternalGroupChatList groupChatList;

    private String corpId;


    public SynCustomerGroupEvent(WxCpUserExternalGroupChatList groupChatList, String corpId) {
        super(groupChatList);
        this.groupChatList = groupChatList;
        this.setCorpId(corpId);
        log.info("接收到同步客户群事件:startDate:{},接收参数:{}", new Date(), JSON.toJSONString(groupChatList));
    }
}
