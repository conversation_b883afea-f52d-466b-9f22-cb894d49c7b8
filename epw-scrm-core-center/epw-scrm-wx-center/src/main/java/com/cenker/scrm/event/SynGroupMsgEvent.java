package com.cenker.scrm.event;


import com.alibaba.fastjson.JSON;
import com.cenker.scrm.pojo.vo.message.WxCpGroupMsgList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class SynGroupMsgEvent extends ApplicationEvent {

    List<WxCpGroupMsgList.GroupMember> groupMsgLists;
    private String corpId;
    private String chatType;

    public SynGroupMsgEvent(List<WxCpGroupMsgList.GroupMember> source, String copId, String chatType) {
        super(source);
        this.groupMsgLists = source;
        this.chatType = chatType;
        this.corpId = copId;
        log.info("接收到同步企业群发事件:startDate:{},接收参数:{}", new Date(), JSON.toJSONString(source));
    }
}
