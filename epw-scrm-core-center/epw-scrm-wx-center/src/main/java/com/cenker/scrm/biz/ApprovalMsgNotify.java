package com.cenker.scrm.biz;

import com.cenker.scrm.enums.SysNoticeEnum;
import com.cenker.scrm.pojo.dto.system.SysNoticeMsgDto;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 审核消息通知
 */
@Component
@AllArgsConstructor
public class ApprovalMsgNotify {
    private final MqSendMessageManager mqSendMessageManager;
    /**
     * 处理消息通知
     *
     * @param corpId
     * @param businessType
     * @param businessId
     * @param businessOper
     * @param businessJson
     */
    public void handlerMsgNotify(String title, String content, String msgContent,
                                 String userId, String corpId, String businessType,
                                 String businessId, String businessOper, String businessJson) {
        SysNoticeMsgDto sysNoticeMsgDto = new SysNoticeMsgDto();
        sysNoticeMsgDto.setNoticeTitle(title);
        sysNoticeMsgDto.setCorpId(corpId);
        sysNoticeMsgDto.setNoticeContent(content);
        sysNoticeMsgDto.setAgentMsgContent(msgContent);
        sysNoticeMsgDto.setNoticeUser(userId);
        sysNoticeMsgDto.setNoticeMode(SysNoticeEnum.NOTICE_MODE_ADMIN.getCode()+"");
        sysNoticeMsgDto.setBusinessId(businessId);
        sysNoticeMsgDto.setBusinessType(businessType);
        sysNoticeMsgDto.setBusinessOper(businessOper);
        sysNoticeMsgDto.setBusinessJson(businessJson);
        mqSendMessageManager.sendSysMsg(sysNoticeMsgDto);
    }
}
