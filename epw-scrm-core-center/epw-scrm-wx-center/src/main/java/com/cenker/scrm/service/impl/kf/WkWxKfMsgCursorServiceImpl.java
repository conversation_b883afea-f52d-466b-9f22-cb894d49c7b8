package com.cenker.scrm.service.impl.kf;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.kf.WkWxKfMsgCursorRespMapper;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgCursorResp;
import com.cenker.scrm.service.kf.IWkWxKfMsgCursorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class WkWxKfMsgCursorServiceImpl extends ServiceImpl<WkWxKfMsgCursorRespMapper, WkWxKfMsgCursorResp> implements IWkWxKfMsgCursorService {

    @Override
    public WkWxKfMsgCursorResp selectUnavailableWxKfCursor(String openKfId) {
        return baseMapper.selectUnavailableWxKfCursor(openKfId);
    }

    @Override
    public WkWxKfMsgCursorResp selectAvailableWxKfCursor(String openKfId) {
        return baseMapper.selectAvailableWxKfCursor(openKfId);
    }
}
