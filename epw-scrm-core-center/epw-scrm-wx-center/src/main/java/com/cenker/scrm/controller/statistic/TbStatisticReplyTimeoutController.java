package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.enums.StatisticTypeEnum;
import com.cenker.scrm.event.StatisticUpdateEvent;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticReplyTimeout;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticReplyTimeoutListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticReplyTimeoutSummaryVo;
import com.cenker.scrm.service.statistic.ITbStatisticReplyTimeoutService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据统计-回复超时 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/statistic/replyTimeout")
public class TbStatisticReplyTimeoutController extends BaseController {

    private final ITbStatisticReplyTimeoutService tbStatisticReplyTimeoutService;
    private final TokenParseUtil tokenService;
    private final ApplicationEventPublisher applicationEventPublisher;


    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        StatisticReplyTimeoutSummaryVo vo = tbStatisticReplyTimeoutService.summary(query);
        return AjaxResult.success(vo);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(@RequestBody StatisticGraphQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<StatisticGraphVo> lstGraph = tbStatisticReplyTimeoutService.graph(query);
        return AjaxResult.success(lstGraph);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody StatisticReplyTimeoutListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        this.startPage();
        List<TbStatisticReplyTimeout> lstReplyTimeout = tbStatisticReplyTimeoutService.list(query);
        return getDataTable(lstReplyTimeout);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public List<TbStatisticReplyTimeout> export(@RequestBody StatisticReplyTimeoutListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<TbStatisticReplyTimeout> lstReplyTimeout = tbStatisticReplyTimeoutService.list(query);
        return lstReplyTimeout;
    }

    /**
     * 更新数据
     */
    @RequestMapping("/synData")
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        StatisticUpdateEvent event = new StatisticUpdateEvent(StatisticTypeEnum.REPLY_TIMEOUT, query.getBeginTime(), query.getEndTime(), ITbStatisticReplyTimeoutService.class, loginUser.getUserId());
        applicationEventPublisher.publishEvent(event);
        return Result.success("更新操作成功，数据同步中");
    }
}
