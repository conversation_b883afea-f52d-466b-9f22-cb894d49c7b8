package com.cenker.scrm.service.impl.session;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.session.CkSessionHotCheckMappingMapper;
import com.cenker.scrm.pojo.entity.session.CkSessionHotCheckMapping;
import com.cenker.scrm.service.session.ICkSessionHotCheckMappingService;
import com.cenker.scrm.util.DateUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 热词审计人关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@AllArgsConstructor
@Service
public class CkSessionHotCheckMappingServiceImpl extends ServiceImpl<CkSessionHotCheckMappingMapper, CkSessionHotCheckMapping> implements ICkSessionHotCheckMappingService
{
    private CkSessionHotCheckMappingMapper ckSessionHotCheckMappingMapper;

    /**
     * 查询热词审计人关联
     * 
     * @param id 热词审计人关联主键
     * @return 热词审计人关联
     */
    @Override
    public CkSessionHotCheckMapping selectCkSessionHotCheckMappingById(Long id)
    {
        return ckSessionHotCheckMappingMapper.selectCkSessionHotCheckMappingById(id);
    }

    /**
     * 查询热词审计人关联列表
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 热词审计人关联
     */
    @Override
    public List<CkSessionHotCheckMapping> selectCkSessionHotCheckMappingList(CkSessionHotCheckMapping ckSessionHotCheckMapping)
    {
        return ckSessionHotCheckMappingMapper.selectCkSessionHotCheckMappingList(ckSessionHotCheckMapping);
    }

    /**
     * 新增热词审计人关联
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 结果
     */
    @Override
    public int insertCkSessionHotCheckMapping(CkSessionHotCheckMapping ckSessionHotCheckMapping)
    {
        ckSessionHotCheckMapping.setCreateTime(DateUtils.getNowDate());
        return ckSessionHotCheckMappingMapper.insertCkSessionHotCheckMapping(ckSessionHotCheckMapping);
    }

    /**
     * 修改热词审计人关联
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 结果
     */
    @Override
    public int updateCkSessionHotCheckMapping(CkSessionHotCheckMapping ckSessionHotCheckMapping)
    {
        ckSessionHotCheckMapping.setUpdateTime(DateUtils.getNowDate());
        return ckSessionHotCheckMappingMapper.updateCkSessionHotCheckMapping(ckSessionHotCheckMapping);
    }

    /**
     * 批量删除热词审计人关联
     * 
     * @param ids 需要删除的热词审计人关联主键
     * @return 结果
     */
    @Override
    public int deleteCkSessionHotCheckMappingByIds(Long[] ids)
    {
        return ckSessionHotCheckMappingMapper.deleteCkSessionHotCheckMappingByIds(ids);
    }

    /**
     * 删除热词审计人关联信息
     * 
     * @param id 热词审计人关联主键
     * @return 结果
     */
    @Override
    public int deleteCkSessionHotCheckMappingById(Long id)
    {
        return ckSessionHotCheckMappingMapper.deleteCkSessionHotCheckMappingById(id);
    }

}
