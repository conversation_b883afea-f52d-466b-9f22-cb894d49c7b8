package com.cenker.scrm.service.impl.statistic;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatisticConstants;
import com.cenker.scrm.enums.DataScopeEnum;
import com.cenker.scrm.mapper.statistic.TbStatisticReplyTimeoutMapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticReplyTimeout;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticReplyTimeoutListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticReplyTimeoutSummaryVo;
import com.cenker.scrm.service.statistic.ITbStatisticBaseService;
import com.cenker.scrm.service.statistic.ITbStatisticReplyTimeoutService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据统计-回复超时 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Service
@Slf4j
@AllArgsConstructor
public class TbStatisticReplyTimeoutServiceImpl extends ServiceImpl<TbStatisticReplyTimeoutMapper, TbStatisticReplyTimeout> implements ITbStatisticReplyTimeoutService, ITbStatisticBaseService {

    private TbStatisticReplyTimeoutMapper tbStatisticReplyTimeoutMapper;

    @Override
    public void saveStatisticData(String statDate) {
        log.info("开始统计回复超时数据, statDate={}", statDate);

        this.lambdaUpdate().eq(TbStatisticReplyTimeout::getStatisticDate, statDate).remove();

        baseMapper.saveStatisticDateByDay(statDate);

        log.info("【数据统计更新数据】统计回复超时数据完成, statDate={}", statDate);
    }

    @Override
    public StatisticReplyTimeoutSummaryVo summary(StatisticSummaryQuery query) {
        StatisticReplyTimeoutSummaryVo vo = tbStatisticReplyTimeoutMapper.summary(query);
        return vo;
    }

    @Override
    public List<StatisticGraphVo> graph(StatisticGraphQuery query) {
        List<StatisticReplyTimeoutSummaryVo> lstVo = tbStatisticReplyTimeoutMapper.graph(query);
        List<StatisticGraphVo> lstGraphVo = new ArrayList<>();

        switch (query.getType()){
            case StatisticConstants.REPLYTIMEOUT_TIMEOUTTIMES:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getTimeoutTimes().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.REPLYTIMEOUT_TIMEOUTNUM:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getTimeoutNum().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            default:
                break;
        }

        return lstGraphVo;
    }

    @Override
    public List<TbStatisticReplyTimeout> list(StatisticReplyTimeoutListQuery query) {
        LambdaQueryWrapper<TbStatisticReplyTimeout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TbStatisticReplyTimeout::getStatisticDate, query.getBeginTime());
        queryWrapper.le(TbStatisticReplyTimeout::getStatisticDate, query.getEndTime());

        if (StringUtils.isNotBlank(query.getUserName())) {
            queryWrapper.like(TbStatisticReplyTimeout::getUserName, query.getUserName());
        }
        if (StringUtils.isNotBlank(query.getDeptName())) {
            queryWrapper.like(TbStatisticReplyTimeout::getDeptName, query.getDeptName());
        }
        if (StringUtils.isEmpty(query.getOrderByColumn()) ) {
            queryWrapper.orderByDesc(TbStatisticReplyTimeout::getStatisticDate);
        }

        if (query.getDataScope() == null || !DataScopeEnum.ALL.getValue().equals(query.getDataScope())) {
            // 用户有权限查看的部门
            if (CollectionUtil.isNotEmpty(query.getPermissionDeptIds())) {
                queryWrapper.in(TbStatisticReplyTimeout::getDeptId, query.getPermissionDeptIds());
            }

            // 用户为仅本人权限，只能查看员工为当前登录账号且主部门为归属部门的数据
            if (CollectionUtil.isEmpty(query.getPermissionDeptIds())) {
                queryWrapper.eq(TbStatisticReplyTimeout::getUserid, query.getWxUserId())
                        .eq(TbStatisticReplyTimeout::getDeptId, query.getDeptId());
            }
        }

        List<TbStatisticReplyTimeout> lstSensWord = tbStatisticReplyTimeoutMapper.selectList(queryWrapper);
        return lstSensWord;
    }

    @Override
    public void synData(Date statDate) {
        this.saveStatisticData(DateUtil.formatDate(statDate));
    }
}
