package com.cenker.scrm.esb;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cenker.scrm.config.EsbServiceConfig;
import com.cenker.scrm.pojo.entity.wechat.VFScrmLabel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SqlServiceClient {

    /**
     * 成功返回码
     */
    private static final String SUCCESS_CODE = "0000";

    /**
     * 默认超时时间，单位：毫秒
     */
    private static final Integer TIMEOUT = 3000;

    @Resource
    private EsbServiceConfig config;

    public List<VFScrmLabel> queryScrmLabel(String custNo) {
        String url = config.getSqlserviceUrl() + "/scrmLabel";
        Map<String, Object> params = new HashMap<>();
        params.put("reqSys", config.getReqSys());
        params.put("custno", custNo);

        log.info("调用sqlservice接口查询客户标签, url={}, params={}", url, JSONUtil.toJsonStr(params));

        try {
            HttpResponse response = HttpUtil.createPost(url)
                    .header("Esb-Token", config.getEsbToken())
                    .body(JSONUtil.toJsonStr(params))
                    .timeout(TIMEOUT).execute();

            log.info("调用sqlservice接口查询客户标签, 返回结果={}", response.body());

            if (StrUtil.isBlank(response.body())) {
                log.error("调用sqlservice接口查询客户标签, 失败！");
                return null;
            }

            JSONObject jsonObject = JSONUtil.parseObj(response.body());

            if (Objects.isNull(jsonObject) || !SUCCESS_CODE.equals(jsonObject.getStr("retCode"))) {
                log.error("调用sqlservice接口查询客户标签, 失败！");
                return null;
            }

            List<VFScrmLabel> vfScrmLabels = JSONUtil.toList(jsonObject.getJSONArray("data"), VFScrmLabel.class);
            return vfScrmLabels;
        } catch (Exception e) {
            log.error("调用sqlservice接口查询客户标签, 异常", e);
        }

        return null;
    }
}
