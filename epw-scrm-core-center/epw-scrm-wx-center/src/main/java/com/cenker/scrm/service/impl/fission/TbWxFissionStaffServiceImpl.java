package com.cenker.scrm.service.impl.fission;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.fission.TbWxFissionStaffMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxFissionStaff;
import com.cenker.scrm.service.fission.ITbWxFissionStaffService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/13
 * @Description 裂变任务员工列表
 */
@Service
public class TbWxFissionStaffServiceImpl extends ServiceImpl<TbWxFissionStaffMapper, TbWxFissionStaff> implements ITbWxFissionStaffService {

    @Override
    public void insertTbWxFissionStaffList(List<TbWxFissionStaff> tbWxFissionStaffs) {
        this.baseMapper.insertTbWxFissionStaffList(tbWxFissionStaffs);
    }
}
