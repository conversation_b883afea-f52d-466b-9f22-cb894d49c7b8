package com.cenker.scrm.handler.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.ConfigConstant;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.entity.MpWxCustomerAuthRecord;
import com.cenker.scrm.pojo.entity.system.SysConfig;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.service.IMpWxCustomerAuthRecordService;
import com.cenker.scrm.service.ISysConfigService;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.util.DateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户认证状态同步任务
 * 负责同步易服务客户的认证状态，包括认证和解除认证的处理
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CustomerAuthTask {

    private final IMpWxCustomerAuthRecordService mpWxCustomerAuthRecordService;
    private final ISysConfigService sysConfigService;
    private final TbWxExtCustomerService tbWxExtCustomerService;
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 同步客户认证状态的定时任务处理器
     * 执行流程：
     * 1. 获取需要同步的数据
     * 2. 处理认证状态变更
     * 3. 更新认证状态
     * 4. 更新同步时间
     *
     * @throws Exception 同步过程中的异常
     */
    @XxlJob(XxlJobContant.SYNCHRONIZE_CUSTOMER_AUTH)
    public void synCustomerAuthHandler() throws Exception {
        if (redisTemplate.hasKey(CacheKeyConstants.CUSTOMER_AUTH_SYNC)) {
            log.info("【同步易服务客户认证状态数据】上一次任务未完成，本次任务不执行");
            XxlJobHelper.log("【同步易服务客户认证状态数据】上一次任务未完成，本次任务不执行");
            return;
        }
        try {
            redisTemplate.opsForValue().set(CacheKeyConstants.CUSTOMER_AUTH_SYNC, "1", 30, TimeUnit.MINUTES);

            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            log.info("【同步易服务客户认证状态数据】开始同步");
            XxlJobHelper.log("【同步易服务客户认证状态数据】开始同步");

            // 获取需要同步的数据
            SyncDataResult syncData = getSyncData();
            if (syncData.isEmpty()) {
                log.info("【同步易服务客户认证状态数据】无需同步的数据");
                XxlJobHelper.log("【同步易服务客户认证状态数据】无需同步的数据");
                return;
            }

            // 处理认证状态变更
            ProcessResult processResult = processAuthStatus(syncData);

            // 批量更新客户数据
            updateAuthStatus(processResult, syncData.getNow());

            // 更新同步时间
            updateLastSyncTime(syncData.getNow());

            stopWatch.stop();
            log.info("【同步易服务客户认证状态数据】同步完成，耗时：{}ms", stopWatch.getTotalTimeMillis());
            XxlJobHelper.log("【同步易服务客户认证状态数据】同步完成，耗时：{}ms", stopWatch.getTotalTimeMillis());

        } finally {
            redisTemplate.delete(CacheKeyConstants.CUSTOMER_AUTH_SYNC);
        }
    }

    /**
     * 同步数据结果的内部类
     * 用于封装同步过程中的数据
     */
    @Data
    @AllArgsConstructor
    private static class SyncDataResult {
        /**
         * 是否全量同步
         */
        private boolean isAll;
        /**
         * 当前同步时间
         */
        private Date now;
        /**
         * 客户认证记录映射，key为unionId
         */
        private Map<String, List<MpWxCustomerAuthRecord>> customerMap;
        /**
         * 客户列表
         */
        private List<TbWxExtCustomer> customerList;

        /**
         * 检查是否存在需要同步的数据
         *
         * @return true 如果没有需要同步的数据
         */
        public boolean isEmpty() {
            return CollectionUtils.isEmpty(customerList);
        }
    }

    /**
     * 获取需要同步的数据
     * 包括认证记录和客户信息
     *
     * @return 同步数据结果
     */
    private SyncDataResult getSyncData() {
        Date lastDate = getLastSyncTime();
        boolean isAll = lastDate == null;
        Date now = new Date();

        // 获取认证记录
        List<MpWxCustomerAuthRecord> authRecords = getAuthRecords(lastDate, now);
        log.info("【同步易服务客户认证状态数据】获取易服务认证记录数量：{}, 开始时间：{}, 结束时间：{}", authRecords.size(), lastDate, now);
        XxlJobHelper.log("【同步易服务客户认证状态数据】获取易服务认证记录数量：{}, 开始时间：{}, 结束时间：{}", authRecords.size(), lastDate, now);
        if (CollectionUtils.isEmpty(authRecords)) {
            updateLastSyncTime(now);
            return new SyncDataResult(isAll, now, null, null);
        }

        // 按unionId分组
        Map<String, List<MpWxCustomerAuthRecord>> customerMap = authRecords.stream()
                .collect(Collectors.groupingBy(MpWxCustomerAuthRecord::getUnionId));

        // 获取客户列表
        List<TbWxExtCustomer> customerList = getCustomerList(isAll, customerMap);
        log.info("【同步易服务客户认证状态数据】查询客户列表数量：{}", customerList.size());
        XxlJobHelper.log("【同步易服务客户认证状态数据】查询客户列表数量：{}", customerList.size());
        return new SyncDataResult(isAll, now, customerMap, customerList);
    }

    /**
     * 获取认证记录
     *
     * @param lastDate 上次同步时间
     * @param now      当前时间
     * @return 认证记录列表
     */
    private List<MpWxCustomerAuthRecord> getAuthRecords(Date lastDate, Date now) {
        return mpWxCustomerAuthRecordService.selectAuthRecoreds(lastDate, now);
        /*LambdaQueryChainWrapper<MpWxCustomerAuthRecord> wrapper = mpWxCustomerAuthRecordService.lambdaQuery();
        if (lastDate != null) {
            log.info("【同步易服务客户认证状态数据】增量同步，上次同步时间：{}", lastDate);
            wrapper.ge(MpWxCustomerAuthRecord::getSyncTime, lastDate);
        }
        wrapper.le(MpWxCustomerAuthRecord::getSyncTime, now);
        return wrapper.list();*/
    }

    /**
     * 获取客户列表
     *
     * @param isAll       是否全量获取
     * @param customerMap 客户认证记录映射
     * @return 客户列表
     */
    private List<TbWxExtCustomer> getCustomerList(boolean isAll, Map<String, List<MpWxCustomerAuthRecord>> customerMap) {
        LambdaQueryChainWrapper<TbWxExtCustomer> customerWrapper = tbWxExtCustomerService.lambdaQuery()
                .select(TbWxExtCustomer::getExternalUserId,
                        TbWxExtCustomer::getUnionId,
                        TbWxExtCustomer::getIsAuth,
                        TbWxExtCustomer::getRealName,
                        TbWxExtCustomer::getCustno);
//        if (!isAll) {
        customerWrapper.in(TbWxExtCustomer::getUnionId, customerMap.keySet());
//        }
        return customerWrapper.list();
    }

    /**
     * 处理结果的内部类
     * 用于封装认证状态处理的结果
     */
    @Data
    @AllArgsConstructor
    private static class ProcessResult {
        /**
         * 需要认证的客户列表
         */
        private List<TbWxExtCustomer> authList;
        /**
         * 需要解除认证的客户列表
         */
        private List<TbWxExtCustomer> unAuthList;
        /**
         * 需要更新属性的客户列表
         */
        private List<TbWxExtCustomer> updateList;
    }

    /**
     * 处理认证状态
     * 将客户分类为需要认证和需要解除认证两组
     *
     * @param syncData 同步数据
     * @return 处理结果
     */
    private ProcessResult processAuthStatus(SyncDataResult syncData) {
        List<TbWxExtCustomer> authList = Lists.newArrayList();
        List<TbWxExtCustomer> unAuthList = Lists.newArrayList();
        List<TbWxExtCustomer> updateList = Lists.newArrayList();

        for (TbWxExtCustomer customer : syncData.getCustomerList()) {
            processCustomerAuthStatus(customer,
                    syncData.getCustomerMap().get(customer.getUnionId()),
                    authList,
                    unAuthList,
                    updateList);
        }
        log.info("【同步易服务客户认证状态数据】将客户分类为需要认证、需要解除认证和需要更新属性三组完成，需要认证数量：{}, 需要解除认证数量：{}, 需要更新属性数量：{}", authList.size(), unAuthList.size(), updateList.size());
        XxlJobHelper.log("【同步易服务客户认证状态数据】将客户分类为需要认证、需要解除认证和需要更新属性三组完成，需要认证数量：{}, 需要解除认证数量：{}, 需要更新属性数量：{}", authList.size(), unAuthList.size(), updateList.size());
        return new ProcessResult(authList, unAuthList, updateList);
    }

    /**
     * 处理单个客户的认证状态
     *
     * @param customer    客户信息
     * @param authRecords 认证记录
     * @param authList    需要认证的客户列表
     * @param unAuthList  需要解除认证的客户列表
     * @param updateList
     */
    private void processCustomerAuthStatus(TbWxExtCustomer customer,
                                           List<MpWxCustomerAuthRecord> authRecords,
                                           List<TbWxExtCustomer> authList,
                                           List<TbWxExtCustomer> unAuthList, List<TbWxExtCustomer> updateList) {
        if (customer.getIsAuth()) {
            processAuthorizedCustomer(customer, authRecords, unAuthList, updateList);
        } else {
            processUnauthorizedCustomer(customer, authRecords, authList, updateList);
        }
    }

    /**
     * 处理已认证客户
     * 检查是否需要解除认证
     *
     * @param customer    客户信息
     * @param authRecords 认证记录
     * @param unAuthList  需要解除认证的客户列表
     * @param updateList
     */
    private void processAuthorizedCustomer(TbWxExtCustomer customer,
                                           List<MpWxCustomerAuthRecord> authRecords,
                                           List<TbWxExtCustomer> unAuthList, List<TbWxExtCustomer> updateList) {
        if (authRecords != null) {
            MpWxCustomerAuthRecord authStatus = getLatestAuthStatus(authRecords, customer);
            boolean latestAuthStatus = Objects.equals(authStatus.getIsAuth(), "1");
            if (!latestAuthStatus) {
                unAuthList.add(customer);
                log.info("【同步易服务客户认证状态数据】客户{}需要解除认证，最新认证状态：{}", customer.getExternalUserId(), latestAuthStatus);
                XxlJobHelper.log("【同步易服务客户认证状态数据】客户{}需要解除认证，最新认证状态：{}", customer.getExternalUserId(), latestAuthStatus);
            } else if (authStatus.isHasChanged()) {
                // 如果属性发生变化，则需要更新
                updateList.add(customer);
                log.info("【同步易服务客户认证状态数据】客户{}属性发生变化，最新属性：{}", customer.getExternalUserId(), latestAuthStatus);
                XxlJobHelper.log("【同步易服务客户认证状态数据】客户{}属性发生变化，最新属性：{}", customer.getExternalUserId(), latestAuthStatus);
            }
//        } else {
//            unAuthList.add(customer);
        }
    }

    /**
     * 处理未认证客户
     * 检查是否需要认证
     *
     * @param customer    客户信息
     * @param authRecords 认证记录
     * @param authList    需要认证的客户列表
     * @param updateList
     */
    private void processUnauthorizedCustomer(TbWxExtCustomer customer,
                                             List<MpWxCustomerAuthRecord> authRecords,
                                             List<TbWxExtCustomer> authList, List<TbWxExtCustomer> updateList) {
        if (authRecords != null) {
            MpWxCustomerAuthRecord authStatus = getLatestAuthStatus(authRecords, customer);
            boolean latestAuthStatus = Objects.equals(authStatus.getIsAuth(), "1");
            if (latestAuthStatus) {
                authList.add(customer);
                log.info("【同步易服务客户认证状态数据】客户{}需要认证，最新认证状态：{}", customer.getExternalUserId(), latestAuthStatus);
                XxlJobHelper.log("【同步易服务客户认证状态数据】客户{}需要认证，最新认证状态：{}", customer.getExternalUserId(), latestAuthStatus);
            } else if (authStatus.isHasChanged()) {
                // 如果属性发生变化，则需要更新
                updateList.add(customer);
                log.info("【同步易服务客户认证状态数据】客户{}属性发生变化，最新属性：{}", customer.getExternalUserId(), latestAuthStatus);
                XxlJobHelper.log("【同步易服务客户认证状态数据】客户{}属性发生变化，最新属性：{}", customer.getExternalUserId(), latestAuthStatus);
            }
        }
    }

    /**
     * 获取最新的客户认证数据
     *
     * @param authRecords 认证记录列表
     * @param customer    客户信息
     */
    private MpWxCustomerAuthRecord getLatestAuthStatus(List<MpWxCustomerAuthRecord> authRecords, TbWxExtCustomer customer) {
        return authRecords.stream()
                .max(Comparator.comparing(MpWxCustomerAuthRecord::getUpdateTime))
                .map(t -> {
                    if (!Objects.equals(t.getCustNo(), customer.getCustno()) || !Objects.equals(t.getRealName(), customer.getRealName())) {
                        t.setHasChanged(true);
                    }
                    customer.setCustno(t.getCustNo());
                    customer.setRealName(t.getRealName());
                    return t;
                }).get();
    }

    /**
     * 更新认证状态
     * 包括更新认证和解除认证的客户状态
     *
     * @param processResult 处理结果
     * @param now           当前时间
     */
    private void updateAuthStatus(ProcessResult processResult, Date now) {
        // 处理需要认证的客户
        if (!CollectionUtils.isEmpty(processResult.getAuthList())) {
            tbWxExtCustomerService.updateAuthCustomers(processResult.getAuthList(), now);
            log.info("【同步易服务客户认证状态数据】处理需要认证的客户完成，数量：{}", processResult.getAuthList().size());
            XxlJobHelper.log("【同步易服务客户认证状态数据】处理需要认证的客户完成，数量：{}", processResult.getAuthList().size());
        }

        // 处理需要解除认证的客户
        if (!CollectionUtils.isEmpty(processResult.getUnAuthList())) {
            tbWxExtCustomerService.updateUnAuthCustomers(processResult.getUnAuthList(), now);
            log.info("【同步易服务客户认证状态数据】处理需要解除认证的客户完成，数量：{}", processResult.getUnAuthList().size());
            XxlJobHelper.log("【同步易服务客户认证状态数据】处理需要解除认证的客户完成，数量：{}", processResult.getUnAuthList().size());
        }

        // 处理需要更新属性的客户
        if (!CollectionUtils.isEmpty(processResult.getUpdateList())) {
            tbWxExtCustomerService.updateCustomers(processResult.getUpdateList(), now);
            log.info("【同步易服务客户认证状态数据】处理需要更新属性的客户完成，数量：{}", processResult.getUpdateList().size());
            XxlJobHelper.log("【同步易服务客户认证状态数据】处理需要更新属性的客户完成，数量：{}", processResult.getUpdateList().size());
        }
    }

    /**
     * 更新最后同步时间
     *
     * @param now 当前时间
     */
    private void updateLastSyncTime(Date now) {
        String lastUpdateTime = DateUtil.formatDateTime(now);
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigKey(ConfigConstant.SYNC_CUSTOME_AUTH_TIME);
        List<SysConfig> sysConfigs = sysConfigService.selectConfigList(sysConfig);
        if (!CollectionUtils.isEmpty(sysConfigs)) {
            sysConfig.setConfigId(sysConfigs.get(0).getConfigId());
            sysConfig.setConfigValue(lastUpdateTime);
            sysConfigService.updateConfig(sysConfig);
            log.info("【同步易服务客户认证状态数据】更新最后同步时间：{}", now);
            XxlJobHelper.log("【同步易服务客户认证状态数据】更新最后同步时间：{}", now);
        } else {
            log.error("【同步易服务客户认证状态数据】更新最后同步时间失败，未找到配置项");
            XxlJobHelper.log("【同步易服务客户认证状态数据】更新最后同步时间失败，未找到配置项");
        }
    }

    /**
     * 获取上次同步时间
     *
     * @return 上次同步时间，如果解析失败返回null
     */
    private Date getLastSyncTime() {
        String lastUpdateTime = sysConfigService.selectConfigByKey(ConfigConstant.SYNC_CUSTOME_AUTH_TIME);
        try {
            log.info("【同步易服务客户认证状态数据】上次同步时间：{}", lastUpdateTime);
            XxlJobHelper.log("【同步易服务客户认证状态数据】上次同步时间：{}", lastUpdateTime);
            DateTime dateTime = DateUtil.parseDateTime(lastUpdateTime);
            // 减2分钟，防止误差
            return DateUtils.addMinute(dateTime, -2);
        } catch (Exception e) {
            return null;
        }
    }
}