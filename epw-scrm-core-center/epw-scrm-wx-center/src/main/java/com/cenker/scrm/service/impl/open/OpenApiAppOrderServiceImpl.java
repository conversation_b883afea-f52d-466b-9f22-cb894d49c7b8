package com.cenker.scrm.service.impl.open;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.open.OpenApiAppOrderMapper;
import com.cenker.scrm.pojo.dto.open.AppCategoryDTO;
import com.cenker.scrm.pojo.entity.open.store.AppOrderGoods;
import com.cenker.scrm.pojo.entity.open.store.OpenApiAppOrder;
import com.cenker.scrm.pojo.vo.open.store.AppOrderVo;
import com.cenker.scrm.pojo.vo.open.store.AppStoreRFMVO;
import com.cenker.scrm.service.open.IOpenApiAppOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/18
 * @Description
 */
@Service
public class OpenApiAppOrderServiceImpl extends ServiceImpl<OpenApiAppOrderMapper, OpenApiAppOrder> implements IOpenApiAppOrderService {

    @Override
    public AppStoreRFMVO getConsumeRFM(AppCategoryDTO appCategoryDTO) {
        return baseMapper.getConsumeRFM(appCategoryDTO);
    }

    @Override
    public List<AppOrderVo> getOrderInfoList(AppCategoryDTO appCategoryDTO) {
        List<AppOrderVo> list = baseMapper.getOrderInfoList(appCategoryDTO);
        if (CollectionUtils.isNotEmpty(list)) {
            for (AppOrderVo appOrderVo : list) {
                List<AppOrderGoods> goods = appOrderVo.getGoods();
                goods = JSON.parseArray(JSON.toJSONString(goods), AppOrderGoods.class);
                appOrderVo.setTotalGoodNum(goods.stream().mapToInt(AppOrderGoods::getGoodNum).sum());
            }
        }
        return list;
    }
}
