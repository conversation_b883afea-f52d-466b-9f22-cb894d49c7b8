package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.biz.customer.CustomerConditionBizHandler;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.DictTypeConstants;
import com.cenker.scrm.enums.BatchTagType;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.external.CustomerSearchDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomerSearch;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.contact.CorpAddWayVO;
import com.cenker.scrm.pojo.vo.external.CustomerExportVo;
import com.cenker.scrm.pojo.vo.external.CustomerInfoVo;
import com.cenker.scrm.service.ISysDictDataService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryService;
import com.cenker.scrm.service.external.TbWxExtCustomerSearchService;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.journey.ITbWxExtJourneyInfoService;
import com.cenker.scrm.util.*;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 企业微信外部客户信息Controller
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Slf4j
@RestController
@RequestMapping("/api/wx/tp/customer")
@AllArgsConstructor
public class TbWxExtCustomerController extends BaseController {

    private final TbWxExtCustomerService tbWxExtCustomerService;
    private final ITbWxCustomerTrajectoryService trajectoryService;
    private final TokenParseUtil tokenService;
    private final ISysDictDataService dictDataService;
    private final ITbWxUserService tbWxUserService;
    private final ITbWxExtJourneyInfoService tbWxExtJourneyInfoService;
    private final TbWxExtCustomerSearchService tbWxExtCustomerSearchService;
    private final CustomerConditionBizHandler customerConditionBizHandler;


    /**
     * 导出企业微信外部客户信息列表
     */
    @RequestMapping("/export")
    public List<CustomerExportVo> export(@RequestBody CustomerChurnDTO dto) {
        if (StringUtils.isNotEmpty(dto.getCorpUserId())) {
            List<String> userId = Arrays.stream(dto.getCorpUserId().split(",")).collect(Collectors.toList());
            dto.setUserIds(userId);
        }
        if (dto.getParams() != null) {
            dto.setBeginTime(dto.getParams().get("beginTime"));
            dto.setEndTime(dto.getParams().get("endTime"));
        }
        return tbWxExtCustomerService.exprotChurnCustomer(dto);
    }

    /**
     * 查询企业微信外部客户信息列表2.0 2022-02-21
     */
    @RequestMapping("/getExtCustomerList")
    public TableDataInfo list(@RequestBody CustomerChurnDTO dto) {
        recordOperLog(dto);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        startPage();
        List<CustomerInfoVo> list = tbWxExtCustomerService.getExtCustomerList(dto);
        return getDataTable(list);
    }

    /**
     * 记录操作日志
     * @param dto
     */
    private void recordOperLog(CustomerChurnDTO dto) {
        StringBuilder operDesc = new StringBuilder();
        Optional.ofNullable(StrUtil.emptyToNull(dto.getCustomerName()))
                .ifPresent(s -> operDesc.append(StrUtil.C_LF).append("客户名称：").append(s));
        Optional.ofNullable(StrUtil.emptyToNull(dto.getUnionId()))
                .ifPresent(s -> operDesc.append(StrUtil.C_LF).append("客户unionid：").append(s));
        Optional.ofNullable(dto.getIsAuth())
                .ifPresent(s -> {
                    String isAuth = BooleanUtil.toString(s, "1", "0");
                    String authStatus = dictDataService.selectDictLabel(DictTypeConstants.USER_TAG_AUTH_STATUS, isAuth);
                    operDesc.append(StrUtil.C_LF).append("认证状态：").append(authStatus);
                });

        Optional.ofNullable(dto.getCorpUserIds()).ifPresent(userIds -> {
            if (CollectionUtil.isEmpty(userIds)) {
                return;
            }

            List<String> userNameList = tbWxUserService.lambdaQuery()
                    .in(TbWxUser::getUserid, userIds).list()
                    .stream().map(TbWxUser::getName).collect(Collectors.toList());
            operDesc.append(StrUtil.C_LF).append("添加人：").append(StrUtil.join(StrUtil.COMMA, userNameList));
        });

        Optional.ofNullable(StrUtil.emptyToNull(dto.getTag()))
                .ifPresent(s -> operDesc.append(StrUtil.C_LF).append("企业标签：").append(s));

        Optional.ofNullable(StrUtil.emptyToNull(dto.getAddWay()))
                .ifPresent(s -> operDesc.append(StrUtil.C_LF).append("渠道来源：").append(s));

        Optional.ofNullable(dto.getType())
                .ifPresent(s -> {
                    String customerType = dictDataService.selectDictLabel(DictTypeConstants.CUSTOMER_TYPE, StrUtil.toString(dto.getType()));
                    operDesc.append(StrUtil.C_LF).append("客户类型：").append(customerType);
                });

        Optional.ofNullable(StrUtil.emptyToNull(dto.getBeginTime()))
                .ifPresent(s -> operDesc.append(StrUtil.C_LF).append(String.format("首次添加时间：%s - %s", dto.getBeginTime(), dto.getEndTime())));

        Optional.ofNullable(StrUtil.emptyToNull(dto.getStageCondition()))
                .ifPresent(s -> {
                            List<String> stageNameList = tbWxExtJourneyInfoService.selectStageNameByStageId(dto.getStageCondition());
                            operDesc.append(StrUtil.C_LF).append("客户旅程：").append(StrUtil.join(StrUtil.COMMA, stageNameList));
                        });

        LogUtil.logOperDesc(operDesc.toString());
    }


    /**
     * 获取企业微信外部客户信息详细信息2.0 2022-02-16
     */
    @RequestMapping("/getInfo")
    public AjaxResult getInfo(@RequestBody CustomerChurnDTO dto) {
        CustomerInfoVo customerInfoVo = tbWxExtCustomerService.queryCustomerInfoDetail(dto);
        if (customerInfoVo == null) {
            return AjaxResult.error("客户数据不存在");
        }
        return AjaxResult.success(customerInfoVo);
    }

    /**
     * 获取企业微信外部客户社交关系 添加员工/所在群聊
     */
    @RequestMapping("/getAssociationInfo")
    public TableDataInfo getAssociationInfo(@RequestBody CustomerChurnDTO dto) {
        startPage();
        List<?> list = Lists.newArrayList();
        if (dto.getType() == 1) {
            list = tbWxExtCustomerService.queryFollowUserByExtUserId(dto);
        } else if (dto.getType() == 2) {
            list = tbWxExtCustomerService.queryCustomerGroupList(dto);
        }
        return getDataTable(list);
    }

    /**
     * 同步外部客户信息
     *
     * @param dto
     * @return
     */
    @GetMapping("/synCustomer")
    public AjaxResult synCustomer(@RequestBody CustomerChurnDTO dto) {
        tbWxExtCustomerService.batchSynCustomerInfos(dto.getCorpId());
        return AjaxResult.success("同步中，请稍后再次查询");
    }

    /**
     * 提醒成员给前一天新增客户打标签
     */
    @GetMapping(value = "/remindTags")
    public void remindTags(@RequestBody CustomerChurnDTO dto) {
        log.info("提醒成员给前一天新增客户打标签开始");
        tbWxExtCustomerService.remindTags(dto.getCorpId());
        log.info("提醒成员给前一天新增客户打标签结束");
    }

    /**
     * 查询渠道
     */
    @RequestMapping("/getAddWayList")
    public AjaxResult getAddWayList(@RequestBody CustomerChurnDTO dto) {
        List<CorpAddWayVO> addWayList = tbWxExtCustomerService.getAddWayList(dto);
        return AjaxResult.success(addWayList);
    }

    /**
     * 查询员工行为日志
     */
    @RequestMapping("/getStaffLog")
    public TableDataInfo getStaffLogTrajectory(@RequestBody CustomerChurnDTO dto) {
        startPage();
        return getDataTable(trajectoryService.getStaffLogTrajectory(dto));
    }

    /**
     * 批量打标签
     */
    @PutMapping(value = "/setLabel")
    public AjaxResult setLabel(@RequestBody CustomerChurnDTO dto) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        dto.setSearchType(BatchTagType.ATTR.name());
        tbWxExtCustomerService.batchSetTag(dto);
        return AjaxResult.success();
    }


    /**
     * 编辑会员客户的手机号码
     *
     * @param dto 到
     * @return {@link AjaxResult}
     */
    @PostMapping(value = "/editMobile")
    public AjaxResult editMobile(@RequestBody CustomerChurnDTO dto){
        TbWxExtCustomer customer = tbWxExtCustomerService.getOne(Wrappers.<TbWxExtCustomer>lambdaQuery()
                .eq(TbWxExtCustomer::getExternalUserId, dto.getExtUserId())
                .last("limit 1")
        );
        customer.setMobiles(dto.getMobile());
        return AjaxResult.success(tbWxExtCustomerService.updateById(customer));
    }


    /**
     * 解绑ESB账号
     */
    @PostMapping("/unbindingEsb")
    public AjaxResult unbindingByUnionId(@RequestParam String unionId) {
        tbWxExtCustomerService.unbindingByUnionId(unionId);
        return AjaxResult.success();
    }

    /**
     * 客户列表 - 自定义搜索
     */
    @PostMapping("/getExtCustomerListByCustom")
    public TableDataInfo getExtCustomerListByCustom(@RequestBody CustomerChurnDTO dto) {
        if (StringUtils.isNotEmpty(dto.getCondition())) {
            List<String> externalUserIdList = customerConditionBizHandler.qrySendMsgCustUserList(dto.getCorpId(), dto.getCondition());
            if (null != externalUserIdList && externalUserIdList.size() > 0) {
                dto.setExternalUserIdList(externalUserIdList);
            } else {
                // 如果自定义条件返回为空，那结果也为空
                return getDataTable(new ArrayList<CustomerInfoVo>());
            }
        }

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<CustomerInfoVo> list = tbWxExtCustomerService.getExtCustomerList(dto);
        return getDataTable(list);
    }

    /**
     * 批量移除标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/delLabel")
    public AjaxResult delLabel(@RequestBody CustomerChurnDTO dto) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        dto.setSearchType(BatchTagType.ATTR.name());
        tbWxExtCustomerService.batchUnSetTag(dto);
        return AjaxResult.success();
    }

    /**
     * 自定义查询-添加标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/custom/setLabel")
    AjaxResult setLabelByCustom(@RequestBody CustomerChurnDTO dto){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        dto.setSearchType(BatchTagType.CUST.name());
        tbWxExtCustomerService.batchSetTag(dto);
        return AjaxResult.success();
    }
    /**
     * 自定义查询-移除标签
     * @param dto
     * @return
     */
    @PutMapping(value = "/custom/delLabel")
    AjaxResult delLabelByCustom(@RequestBody CustomerChurnDTO dto){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        dto.setSearchType(BatchTagType.CUST.name());
        tbWxExtCustomerService.batchUnSetTag(dto);
        return AjaxResult.success();
    }

    /**
     * 客户列表-自定义筛选条件-列表
     * @return
     */
    @GetMapping(value = "/custom/search")
    public AjaxResult searchByCustom(@RequestParam("name") String name) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        LambdaQueryWrapper<TbWxExtCustomerSearch> query = new LambdaQueryWrapper<>();
        query.eq(TbWxExtCustomerSearch::getDelFlag, CommonConstants.NOT_DELETED);
        query.eq(TbWxExtCustomerSearch::getCreateBy, user.getUserId());
        if (StringUtils.isNotEmpty(name)) {
            query.eq(TbWxExtCustomerSearch::getName, name);
        }
        List<TbWxExtCustomerSearch> lstCustomerSearch = tbWxExtCustomerSearchService.list(query);
        return AjaxResult.success(lstCustomerSearch);
    }

    /**
     * 客户列表-自定义筛选条件-新增
     * @return
     */
    @PostMapping(value = "/custom/addSearch")
    public AjaxResult addSearchByCustom(@RequestBody CustomerSearchDTO dto) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        TbWxExtCustomerSearch save = new TbWxExtCustomerSearch();
        save.setName(dto.getName());
        save.setSearchCondition(dto.getCondition());
        save.setCreateBy(user.getUserId());
        save.setCreateTime(DateUtils.getNowDate());
        save.setUpdateTime(DateUtils.getNowDate());
        save.setUpdateBy(user.getUserId());
        save.setDelFlag(CommonConstants.NOT_DELETED);
        tbWxExtCustomerSearchService.save(save);
        return AjaxResult.success();
    }

    /**
     * 客户列表-自定义筛选条件-编辑
     * @return
     */
    @PutMapping(value = "/custom/editSearch")
    public AjaxResult editSearchByCustom(@RequestBody CustomerSearchDTO dto) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        TbWxExtCustomerSearch update = tbWxExtCustomerSearchService.getById(dto.getId());
        update.setName(dto.getName());
        update.setSearchCondition(dto.getCondition());
        update.setUpdateTime(DateUtils.getNowDate());
        update.setUpdateBy(user.getUserId());
        tbWxExtCustomerSearchService.updateById(update);
        return AjaxResult.success();
    }

    /**
     * 客户列表-自定义筛选条件-删除
     * @return
     */
    @DeleteMapping("/custom/delSearch/{id}")
    public AjaxResult delSearchByCustom(@PathVariable("id") Long id) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        TbWxExtCustomerSearch del = tbWxExtCustomerSearchService.getById(id);
        del.setDelFlag(CommonConstants.DELETED);
        del.setUpdateTime(DateUtils.getNowDate());
        del.setUpdateBy(user.getUserId());
        tbWxExtCustomerSearchService.updateById(del);
        return AjaxResult.success();
    }

    /**
     * 根据unionId获取企业微信用户信息
     * @param unionId
     * @return
     */
    @GetMapping("/getWxUserByUnionId")
    public RemoteResult<TbWxExtCustomer> getCustomerByUnionId(@RequestParam(value = "unionId") String unionId) {
        TbWxExtCustomer extCustomer = tbWxExtCustomerService
                .getOne(Wrappers.<TbWxExtCustomer>lambdaQuery().eq(TbWxExtCustomer::getUnionId, unionId), false);
        return RemoteResult.data(extCustomer);
    }

}
