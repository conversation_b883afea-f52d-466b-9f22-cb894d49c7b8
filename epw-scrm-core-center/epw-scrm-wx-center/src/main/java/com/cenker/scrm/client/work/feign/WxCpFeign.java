package com.cenker.scrm.client.work.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.exception.WeComException;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.bean.external.*;
import me.chanjar.weixin.cp.bean.external.acquisition.WxCpCustomerAcquisitionCreateResult;
import me.chanjar.weixin.cp.bean.external.acquisition.WxCpCustomerAcquisitionCustomerList;
import me.chanjar.weixin.cp.bean.external.acquisition.WxCpCustomerAcquisitionRequest;
import me.chanjar.weixin.cp.bean.external.interceptrule.WxCpInterceptRule;
import me.chanjar.weixin.cp.bean.external.interceptrule.WxCpInterceptRuleAddRequest;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 暂时罗列wx-cenker所调用的企微接口 消除work注入依赖
 *
 * <AUTHOR> 直接获取api对象存在限制  还是通过调用实际功能如添加渠道码来实现调用企微功能
 */
@FeignClient(value = ServiceNameConstants.WORK_API_SERVICE, path = "/service/cp")
public interface WxCpFeign {


    /**
     * 生成渠道码
     *
     * @param wxCpContactWayInfo 接口参数
     * @param corpId             企业
     * @return 创建结果
     */
    @PostMapping("/addContactWay")
    WxCpContactWayResult addContactWay(@RequestBody WxCpContactWayInfo wxCpContactWayInfo, @RequestParam("corpId") String corpId) throws WeComException;

    /**
     * 删除渠道活码
     *
     * @param configId 企微配置id
     * @param corpId   企业id
     * @return 结果
     */
    @PostMapping("/deleteContactWay")
    WxCpBaseResp deleteContactWay(@RequestParam("configId") String configId,
                                  @RequestParam("corpId") String corpId);

    /**
     * 修改渠道码
     *
     * @param wxCpContactWayInfo 接口参数
     * @param corpId             企业
     * @return 结果
     */
    @PostMapping("/updateContactWay")
    WxCpBaseResp updateContactWay(@RequestBody WxCpContactWayInfo wxCpContactWayInfo,
                                  @RequestParam("corpId") String corpId) throws WeComException;

    /**
     * 添加企业标签
     *
     * @param groupInfo 接口参数
     * @param corpId    企业
     * @return 结果
     */
    @PostMapping("/addCorpTag")
    WxCpUserExternalTagGroupInfo addCorpTag(@RequestBody WxCpUserExternalTagGroupInfo groupInfo,
                                            @RequestParam("corpId") String corpId) throws WeComException;

    /**
     * 创建群发
     *
     * @param cpMsgTemplate
     * @return
     */
    @PostMapping("/addMsgTemplate")
    WxCpMsgTemplateAddResult addMsgTemplate(@RequestBody WxCpMsgTemplate cpMsgTemplate,
                                            @RequestParam("corpId") String corpId);

    /**
     * 上传企微永久图片
     *
     * @param localUrl
     * @param corpId
     * @return
     */
    @PostMapping("/uploadImg")
    String uploadImg(@RequestParam("localUrl") String localUrl,
                     @RequestParam("corpId") String corpId);

    /**
     * 删除企业客户标签
     *
     * @param tagId
     * @param groupId
     * @param corpId
     * @return
     */
    @PostMapping("/delCorpTag")
    WxCpBaseResp delCorpTag(@RequestParam("tagId") String[] tagId,
                            @RequestParam("groupId") String[] groupId,
                            @RequestParam("corpId") String corpId);

    /**
     * 获取企业标签库
     *
     * @param tagId
     * @param groupId
     * @param corpId
     * @return
     */
    @PostMapping("/getCorpTagList")
    WxCpUserExternalTagGroupList getCorpTagList(@RequestParam("tagId") String[] tagId,
                                                @RequestParam("groupId") String[] groupId,
                                                @RequestParam("corpId") String corpId);

    /**
     * 编辑企业客户标签
     *
     * @param groupId
     * @param groupName
     * @param order
     * @param corpId
     * @return
     */
    @PostMapping("/editCorpTag")
    WxCpBaseResp editCorpTag(@RequestParam("groupId") String groupId,
                             @RequestParam("groupName") String groupName,
                             @RequestParam("order") Integer order,
                             @RequestParam("corpId") String corpId);

    /**
     * 获取客户群列表
     *
     * @param limit
     * @param cursor
     * @param statusFilter
     * @param ownerFilter
     * @param corpId
     * @return
     */
    @PostMapping("/listGroupChat")
    WxCpUserExternalGroupChatList listGroupChat(@RequestParam("limit") int limit, @RequestParam("cursor") String cursor,
                                                @RequestParam("statusFilter") int statusFilter, @RequestParam("ownerFilter") String[] ownerFilter,
                                                @RequestParam("corpId") String corpId);

    /**
     * 企业可通过此接口为指定成员的客户添加上由企业统一配置的标签
     *
     * @param userId
     * @param externalUserId
     * @param addTag
     * @param removeTag
     * @return
     */
    @PostMapping("/markTag")
    WxCpBaseResp markTag(@RequestParam("userId") String userId, @RequestParam("externalUserId") String externalUserId,
                         @RequestParam("addTag") String[] addTag, @RequestParam("removeTag") String[] removeTag, @RequestParam("corpId") String corpId);

    /**
     * 获取客户群详情
     *
     * @param chatId
     * @param needName
     * @param corpId
     * @return
     */
    @PostMapping("/getGroupChat")
    WxCpUserExternalGroupChatInfo getGroupChat(@RequestParam("chatId") String chatId, @RequestParam("needName") Integer needName, @RequestParam("corpId") String corpId);

    /**
     * 发送应用消息
     *
     * @param wxCpMessage
     * @param corpId
     * @return
     */
    @PostMapping("/agentSend")
    WxCpBaseResp agentSend(@RequestBody WxCpMessage wxCpMessage, @RequestParam("corpId") String corpId);

    /**
     * 停止企业群发
     *
     * @param msgId
     * @param corpId
     * @return
     */
    @PostMapping("/cancelGroupMsgSend")
    WxCpBaseResp stop(@RequestParam("msgId") String msgId, @RequestParam("corpId") String corpId);

    /**
     * 配置客户群进群方式
     *
     * @param wxCpGroupJoinWayInfo 企微接口参数
     * @param corpId               企业id
     * @return 创建结果
     */
    @PostMapping("/addJoinWay")
    WxCpGroupJoinWayResult addJoinWay(@RequestBody WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo, @RequestParam("corpId") String corpId);

    /**
     * 获取客户群进群方式配置
     *
     * @param configId 联系方式的配置id
     * @param corpId   企业id
     * @return 配置详情
     */
    @PostMapping("/getJoinWay")
    WxCpGroupJoinWayInfo getJoinWay(@RequestParam("configId") String configId, @RequestParam("corpId") String corpId);

    /**
     * 更新客户群进群方式
     * 注意：使用覆盖的方式更新。
     * @param wxCpGroupJoinWayInfo 企微接口参数
     * @param corpId               企业id
     * @return 创建结果
     */
    @PostMapping("/updateJoinWay")
    WxCpBaseResp updateJoinWay(@RequestBody WxCpGroupJoinWayInfo wxCpGroupJoinWayInfo, @RequestParam("corpId") String corpId);

    /**
     * 删除客户群进群方式配置
     *
     * @param configId 联系方式的配置id
     * @param corpId   企业id
     * @return 配置详情
     */
    @PostMapping("/deleteJoinWay")
    WxCpBaseResp deleteJoinWay(@RequestParam("configId") String configId, @RequestParam("corpId") String corpId);



    /**
     * 新增敏感词规则
     * 注意：使用覆盖的方式更新。
     * @param wxCpInterceptRuleResp 企微接口参数
     * @param corpId               企业id
     * @return 创建结果
     */
    @PostMapping("/addInterceptRule")
    WxCpInterceptRuleResultResp addInterceptRule(@RequestBody WxCpInterceptRuleAddRequest wxCpInterceptRuleResp, @RequestParam("corpId") String corpId);

    /**
     * 修改敏感词规则
     * 注意：使用覆盖的方式更新。
     * @param wxCpInterceptRuleResp 企微接口参数
     * @param corpId               企业id
     * @return 创建结果
     */
    @PostMapping("/updInterceptRule")
    WxCpInterceptRuleResultResp updInterceptRule(@RequestBody WxCpInterceptRule wxCpInterceptRuleResp, @RequestParam("corpId") String corpId);


    @PostMapping("/delInterceptRule")
    WxCpBaseResp delInterceptRule(@RequestBody WxCpInterceptRuleResp wxCpInterceptRuleResp, @RequestParam("corpId") String corpId);

    @PostMapping("/qryInterceptRule")
    WxCpInterceptRuleResp qryInterceptRule(@RequestBody WxCpInterceptRuleResp wxCpInterceptRuleResp, @RequestParam("corpId") String corpId);


    @PostMapping("/customerAcquisitionLinkCreate")
    public WxCpCustomerAcquisitionCreateResult customerAcquisitionLinkCreate(@RequestBody WxCpCustomerAcquisitionRequest acquisitionRequest, @RequestParam("corpId") String corpId);

    @PostMapping("/customerAcquisitionUpdate")
    public WxCpBaseResp customerAcquisitionUpdate(@RequestBody WxCpCustomerAcquisitionRequest acquisitionRequest, @RequestParam("corpId") String corpId) throws WxErrorException ;


    @PostMapping("/customerAcquisitionLinkDelete")
    public WxCpBaseResp customerAcquisitionLinkDelete(@RequestBody String linkId, @RequestParam("corpId") String corpId) throws WxErrorException ;

    @PostMapping("/customerAcquisitionCustomer")
    public WxCpCustomerAcquisitionCustomerList customerAcquisitionCustomer(@RequestParam("linkId") String linkId,
                                                                           @RequestParam("limit") Integer limit,
                                                                           @RequestParam("cursor") String cursor,
                                                                           @RequestParam("corpId") String corpId) throws WxErrorException ;
}
