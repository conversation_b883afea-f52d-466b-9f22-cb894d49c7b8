package com.cenker.scrm.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.system.SysUser;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表 数据层 todo 可以迁移到系统服务 2023-05-06重构冲突 暂时先改名
 *
 * <AUTHOR>
 */
public interface SysUserWxMapper extends BaseMapper<SysUser> {

    /**
     * 通过corpId和corpUseId 获取用户信息
     * @param corpId
     * @param corpUserId
     * @return
     */
    SysUser selectUserByUserCorpInfo(@Param("corpId") String corpId, @Param("corpUserId") String corpUserId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 根据企业微信信息修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserByCorp(SysUser user);


    SysUser selectUserByUserCorpInfoV2(@Param("corpId") String corpId, @Param("corpUserId") String corpUserId);
}
