package com.cenker.scrm.service.impl.message;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.message.TbWxCorpMassMessageLogMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpMassMessageLog;
import com.cenker.scrm.pojo.vo.message.MassWxSenderVO;
import com.cenker.scrm.service.message.TbWxCorpMassMessageLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TbWxCorpMassMessageLogServiceImpl extends ServiceImpl<TbWxCorpMassMessageLogMapper, TbWxCorpMassMessageLog>
        implements TbWxCorpMassMessageLogService {
    @Override
    public List<MassWxSenderVO> queryExistMessageId(String corpId, List<String> msgIds) {
        return baseMapper.queryExistMessageId(corpId, msgIds);
    }

    @Override
    public List<MassWxSenderVO> queryExistUserId(String corpId, List<String> msgIds) {
        return baseMapper.queryExistUserId(corpId, msgIds);
    }

    @Override
    public void updateActualSendNum(long messageInfoId) {
        baseMapper.updateActualSendNum(messageInfoId);
    }
}
