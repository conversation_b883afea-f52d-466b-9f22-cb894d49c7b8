package com.cenker.scrm.service.impl.kf;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.kf.WkWxKfMsgMediaMapper;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgItemCommonResp;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgMedia;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgResp;
import com.cenker.scrm.service.kf.IWkWxKfMsgMediaService;
import com.cenker.scrm.util.RedisIdWorker;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.cenker.scrm.constants.WkWxKfConstants.WK_KF_MSG_MEDIA_PREFIX;

@Service
@Transactional(rollbackFor = Exception.class)
public class WkWxKfMsgMediaServiceImpl extends ServiceImpl<WkWxKfMsgMediaMapper, WkWxKfMsgMedia> implements IWkWxKfMsgMediaService {

    @Autowired
    private RedisIdWorker redisIdWorker;

    @Override
    public boolean stock(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        WkWxKfMsgMedia entity = new WkWxKfMsgMedia();
        // 获取 id
        Long id = redisIdWorker.nextId(WK_KF_MSG_MEDIA_PREFIX);
        entity.setId(id);
        return save(entity.init(msgItem));
    }

    @Override
    public WkWxKfMsgItemCommonResp getOne(WkWxKfMsgResp wkWxKfMsgResp) {
        QueryWrapper<WkWxKfMsgMedia> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("send_time", wkWxKfMsgResp.getSendTime()).eq("msg_id", wkWxKfMsgResp.getMsgId());
        return getOne(queryWrapper);
    }

}
