package com.cenker.scrm.controller.subscr;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.subscr.BuSectionRadar;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionSection;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionRadarVO;
import com.cenker.scrm.service.radar.ITbWxRadarInteractService;
import com.cenker.scrm.service.subscr.IBuSectionRadarService;
import com.cenker.scrm.service.subscr.IBuSubscriptionSectionService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/bu/section/radar")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BuSectionRadarController  extends BaseController {

    private final IBuSectionRadarService buSectionRadarService;

    private final IBuSubscriptionSectionService buSubscriptionSectionService;

    private final ITbWxRadarInteractService tbWxRadarInteractService;

    private final TokenParseUtil tokenService;
    /**
     * 获取栏目的物料列表
     *
     * @param query 查询条件
     * @return 栏目物料列表
     */
    @GetMapping("/list")
    public TableDataInfo<BuSectionRadarVO> list(BuSectionRadarQuery query) {
        startPage();
        if (query.getEndTime() != null) {
            Date dateTime = DateUtils.parseDate(query.getEndTime());
            String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getDayEndTime(dateTime));
            query.setEndTime(dateStr);
        }
        log.info("【查询栏目物料列表】开始查询，参数：{}", query);
        List<BuSectionRadarVO> voList = buSectionRadarService.getBuSectionRadarVOS(query);
        log.info("【查询栏目物料列表】查询完成，结果数量：{}", voList.size());

        return getDataTable(voList);
    }

    /**
     * 新增栏目物料
     *
     * @param detail 栏目物料详情
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated(InsertGroup.class) BuSectionRadarVO detail) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        log.info("【新增栏目物料】开始新增，参数：{}", detail);
        // 验证栏目是否存在
        BuSubscriptionSection section = buSubscriptionSectionService.getById(detail.getSectionId());
        if (section == null) {
            log.warn("【新增栏目物料】订阅栏目不存在，sectionId：{}", detail.getSectionId());
            return Result.error(500, "订阅栏目不存在");
        }

        // 验证智能物料是否存在
        if (detail.getRadarId() != null) {
            TbWxRadarInteract interact = tbWxRadarInteractService.getById(detail.getRadarId());
            if (interact == null) {
                log.warn("【新增栏目物料】智能物料不存在，radarId：{}", detail.getRadarId());
                return Result.error(500, "智能物料不存在");
            }
        }

        BuSectionRadar existsOne = buSectionRadarService.getOne(new LambdaQueryWrapper<BuSectionRadar>()
                .eq(BuSectionRadar::getSectionId, detail.getSectionId())
                .eq(BuSectionRadar::getRadarId, detail.getRadarId())
                .eq(BuSectionRadar::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT));
        if (existsOne != null) {
            log.warn("【新增栏目物料】栏目物料已存在，sectionId：{}，radarId：{}", detail.getSectionId(), detail.getRadarId());
            return Result.error(500, "栏目物料已存在");
        }

        BuSectionRadar radar = new BuSectionRadar();
        BeanUtils.copyProperties(detail, radar);
        radar.setCreateBy(loginUser.getUser().getUserId());
        radar.setDelFlag(StatusConstants.DEL_FLAG_FALSE_INT); // 设置为未删除
        radar.setUploadTime(new Date()); // 设置上传时间

        boolean success = buSectionRadarService.save(radar);
        if (success) {
            log.info("【新增栏目物料】新增成功，id：{}", radar.getId());
            return Result.success("新增成功");
        } else {
            log.warn("【新增栏目物料】新增失败");
            return Result.error(500, "新增失败");
        }
    }

    /**
     * 删除栏目物料
     *
     * @param id 栏目物料ID
     * @return 操作结果
     */
    @DeleteMapping("/remove")
    public Result<Void> remove(@RequestParam("id") Long id) {
        log.info("【删除栏目物料】开始删除，id：{}", id);
        // 逻辑删除，将delFlag设置为1
        BuSectionRadar radar = buSectionRadarService.getById(id);
        if (radar == null) {
            log.warn("【删除栏目物料】栏目物料不存在，id：{}", id);
            return Result.error(500, "栏目物料不存在");
        }

        radar.setDelFlag(StatusConstants.DEL_FLAG_TRUE_INT); // 设置为已删除
        radar.setEndTime(new Date());
        boolean success = buSectionRadarService.updateById(radar);

        if (success) {
            log.info("【删除栏目物料】删除成功，id：{}", id);
            return Result.success("删除成功");
        } else {
            log.warn("【删除栏目物料】删除失败，id：{}", id);
            return Result.error(500, "删除失败");
        }
    }

    /**
     * 根据id获取栏目物料详情
     *
     * @param id 栏目物料ID
     * @return 栏目物料详情
     */
//    @GetMapping("/detail")
//    public Result<BuSubscriptionSectionDetail> detail(@RequestParam("id") Long id) {
//        BuSectionRadar radar = buSectionRadarService.getById(id);
//        if (radar == null) {
//            return Result.error(500, "栏目物料不存在");
//        }
//
//        // 获取栏目详情
//        BuSubscriptionSection section = buSubscriptionSectionService.getById(radar.getSectionId());
//        if (section == null) {
//            return Result.error(500, "订阅栏目不存在");
//        }
//
//        BuSubscriptionSectionDetail detail = new BuSubscriptionSectionDetail();
//        BeanUtils.copyProperties(section, detail);
//
//        return Result.data(detail);
//    }

    /**
     * 修改栏目物料
     *
     * @param detail 栏目物料详情
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@RequestBody @Validated(UpdateGroup.class) BuSectionRadarVO detail) {
        log.info("【修改栏目物料】开始修改，参数：{}", detail);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        BuSectionRadar radar = buSectionRadarService.getById(detail.getId());
        if (radar == null) {
            log.warn("【修改栏目物料】栏目物料不存在，id：{}", detail.getId());
            return Result.error(500, "栏目物料不存在");
        }

        // 验证栏目是否存在
        if (detail.getSectionId() != null) {
            BuSubscriptionSection section = buSubscriptionSectionService.getById(detail.getSectionId());
            if (section == null) {
                log.warn("【修改栏目物料】订阅栏目不存在，sectionId：{}", detail.getSectionId());
                return Result.error(500, "订阅栏目不存在");
            }
        }

        // 验证智能物料是否存在
        if (detail.getRadarId() != null) {
            TbWxRadarInteract interact = tbWxRadarInteractService.getById(detail.getRadarId());
            if (interact == null) {
                log.warn("【修改栏目物料】智能物料不存在，radarId：{}", detail.getRadarId());
                return Result.error(500, "智能物料不存在");
            }
        }

        BeanUtils.copyProperties(detail, radar);
        radar.setUpdateBy(loginUser.getUser().getUserId());
        boolean success = buSectionRadarService.updateById(radar);

        if (success) {
            log.info("【修改栏目物料】修改成功，id：{}", detail.getId());
            return Result.success("修改成功");
        } else {
            log.warn("【修改栏目物料】修改失败，id：{}", detail.getId());
            return Result.error(500, "修改失败");
        }
    }

    /**
     * 上传物料
     *
     * @param detail 物料详情
     * @return 操作结果
     */
    @PostMapping("/uploadRadar")
    public Result<Void> uploadRadar(@RequestBody BuSectionRadarVO detail) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        log.info("【上传物料】开始上传，参数：{}", detail);
        // 验证栏目是否存在
        BuSubscriptionSection section = buSubscriptionSectionService.getById(detail.getSectionId());
        if (section == null) {
            log.warn("【上传物料】订阅栏目不存在，sectionId：{}", detail.getSectionId());
            return Result.error(500, "订阅栏目不存在");
        }

        // 先保存智能物料内容
        TbWxRadarInteract interact = tbWxRadarInteractService.getById(detail.getRadarId());
        if (interact == null) {
            log.warn("【上传物料】智能物料不存在，radarId：{}", detail.getRadarId());
            return Result.error(500, "智能物料不存在");
        }

        BuSectionRadar existsOne = buSectionRadarService.getOne(new LambdaQueryWrapper<BuSectionRadar>()
                .eq(BuSectionRadar::getSectionId, detail.getSectionId())
                .eq(BuSectionRadar::getRadarId, detail.getRadarId())
                .eq(BuSectionRadar::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT));
        if (existsOne != null) {
            log.warn("【上传物料】栏目物料已存在，Id:{}, sectionId：{}，radarId：{}", existsOne.getId(), detail.getSectionId(), detail.getRadarId());
            // 已存在则进行更新
            existsOne.setSummary(detail.getSummary());
            existsOne.setUpdateBy(loginUser.getUser().getUserId());
            buSectionRadarService.updateById(existsOne);
            return Result. success("栏目物料已存在，更新成功");
        }
        // 保存栏目物料关联
        BuSectionRadar radar = new BuSectionRadar();
        BeanUtils.copyProperties(detail, radar);
        radar.setCreateBy(loginUser.getUser().getUserId());
        radar.setDelFlag(StatusConstants.DEL_FLAG_FALSE_INT); // 设置为未删除
        radar.setUploadTime(new Date()); // 设置上传时间

        boolean success = buSectionRadarService.save(radar);
        if (success) {
            log.info("【上传物料】上传成功，id：{}", radar.getId());
            return Result.success("上传成功");
        } else {
            log.warn("【上传物料】上传失败");
            return Result.error(500, "上传失败");
        }
    }
}