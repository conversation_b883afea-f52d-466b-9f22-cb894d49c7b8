
package com.cenker.scrm.wxmp;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cenker.scrm.config.wxmp.WxOpenConfiguration;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp;
import com.cenker.scrm.service.wxmp.WxAppService;
import com.cenker.scrm.util.wxmp.CorpContextHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 开发平台回调控制
 * <AUTHOR>
 */
@Slf4j
@Deprecated
@AllArgsConstructor
@RequestMapping("/api/open/notify")
public class WxOpenNotifyController {

	private final WxAppService wxAppService;

    @RequestMapping("/receive_ticket")
    public Object receiveTicket(@RequestBody(required = false) String requestBody, @RequestParam("timestamp") String timestamp,
								@RequestParam("nonce") String nonce, @RequestParam("signature") String signature,
								@RequestParam(name = "encrypt_type", required = false) String encType,
								@RequestParam(name = "msg_signature", required = false) String msgSignature) {
        log.info("\n接收微信请求：[signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                signature, encType, msgSignature, timestamp, nonce, requestBody);

        if (!StringUtils.equalsIgnoreCase("aes", encType) || !WxOpenConfiguration.getOpenService().getWxOpenComponentService().checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        // aes加密的消息
        WxOpenXmlMessage inMessage = WxOpenXmlMessage.fromEncryptedXml(requestBody, WxOpenConfiguration.getOpenService().getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
        log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
        try {
            WxOpenConfiguration.getOpenService().getWxOpenComponentService().route(inMessage);
			if("unauthorized".equals(inMessage.getInfoType())){
                // 取消授权通知
                log.info("【第三方平台】接收到取消授权通知【{}】",inMessage.getAuthorizerAppid());
				WxApp wxApp = wxAppService.findByAppId(inMessage.getAuthorizerAppid());
				if(wxApp != null){
					CorpContextHolder.setCorpId(wxApp.getCorpConfigId());
                    wxAppService.update(new LambdaUpdateWrapper<WxApp>()
                            .set(WxApp::getDelFlag, StatusConstants.DEL_FLAG_TRUE)
                            .set(WxApp::getDefaultUse, StatusConstants.SHOW_FLAG_FALSE)
                            .eq(WxApp::getAppId,inMessage.getAuthorizerAppid())
                            .eq(WxApp::getCorpConfigId,wxApp.getCorpConfigId())
                    );
				}
			}
			return "success";
        } catch (WxErrorException e) {
            log.error("receive_ticket", e);
			return "";
        }
    }

    @RequestMapping("{appId}/callback")
    public Object callback(@RequestBody(required = false) String requestBody,
                           @PathVariable("appId") String appId,
                           @RequestParam("signature") String signature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam("openid") String openid,
                           @RequestParam("encrypt_type") String encType,
                           @RequestParam("msg_signature") String msgSignature) {
        log.info(
                "\n接收微信请求：[appId=[{}], openid=[{}], signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                appId, openid, signature, encType, msgSignature, timestamp, nonce, requestBody);
        if (!StringUtils.equalsIgnoreCase("aes", encType)
                || !WxOpenConfiguration.getOpenService().getWxOpenComponentService().checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        String out = "";
        // aes加密的消息
        WxMpXmlMessage inMessage = WxOpenXmlMessage.fromEncryptedMpXml(requestBody,
				WxOpenConfiguration.getOpenService().getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
        log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
        // 全网发布测试用例
        if (StringUtils.equalsAnyIgnoreCase(appId, "wxd101a85aa106f53e", "wx570bc396a51b8ff8")) {
            try {
                if (StringUtils.equals(inMessage.getMsgType(), "text")) {
                    if (StringUtils.equals(inMessage.getContent(), "TESTCOMPONENT_MSG_TYPE_TEXT")) {
                        out = WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(
                                WxMpXmlOutMessage.TEXT().content("TESTCOMPONENT_MSG_TYPE_TEXT_callback")
                                        .fromUser(inMessage.getToUser())
                                        .toUser(inMessage.getFromUser())
                                        .build(),
								WxOpenConfiguration.getOpenService().getWxOpenConfigStorage()
                        );
                    } else if (StringUtils.startsWith(inMessage.getContent(), "QUERY_AUTH_CODE:")) {
                        String msg = inMessage.getContent().replace("QUERY_AUTH_CODE:", "") + "_from_api";
                        WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(msg).toUser(inMessage.getFromUser()).build();
						WxOpenConfiguration.getOpenService().getWxOpenComponentService().getWxMpServiceByAppid(appId).getKefuService().sendKefuMessage(kefuMessage);
                    }
                } else if (StringUtils.equals(inMessage.getMsgType(), "event")) {
                    WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(inMessage.getEvent() + "from_callback").toUser(inMessage.getFromUser()).build();
					WxOpenConfiguration.getOpenService().getWxOpenComponentService().getWxMpServiceByAppid(appId).getKefuService().sendKefuMessage(kefuMessage);
                }
            } catch (WxErrorException e) {
                log.error("callback", e);
            }
        }else{
            WxMpXmlOutMessage outMessage = WxOpenConfiguration.getWxOpenMessageRouter().route(inMessage, appId);
            if(outMessage != null){
                out = WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(outMessage, WxOpenConfiguration.getOpenService().getWxOpenConfigStorage());
            }
        }
        return out;
    }
}
