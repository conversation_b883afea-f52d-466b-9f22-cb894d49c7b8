package com.cenker.scrm.service.impl.cachecontent;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.cacheContent.TbWxCacheContentMapper;
import com.cenker.scrm.pojo.dto.cachecontent.CacheContentDTO;
import com.cenker.scrm.pojo.entity.cachecontent.TbWxCacheContent;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.cenker.scrm.service.cachecontent.ITbWxCacheContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @date 2023/5/23 10:27
 */
@Slf4j
@Service
public class TbWxCacheContentServiceImpl extends ServiceImpl<TbWxCacheContentMapper, TbWxCacheContent> implements ITbWxCacheContentService {
    @Override
    public Boolean updateLink(String id, String value) {
        TbWxCacheContent wxCacheContent = this.getById(id);
        String beforeUrl = (String) JSON.parseObject(wxCacheContent.getValue()).get("url");
        JSONObject jsonObjectAfter = JSON.parseObject(value);
        String AfterUrl =(String) jsonObjectAfter.get("url");
        if(!beforeUrl.equals(AfterUrl)){
            TbWxCacheContent tbWxCacheContent = new TbWxCacheContent();
            tbWxCacheContent.setType(wxCacheContent.getType());
            tbWxCacheContent.setValue(value);
            tbWxCacheContent.setName((String) jsonObjectAfter.get("title"));
            tbWxCacheContent.setCorpId(wxCacheContent.getCorpId());
            this.save(tbWxCacheContent);
            return true;
        }
        return false;
    }

    @Override
    public Boolean updateMiniprogram(String id, String value) {
        TbWxCacheContent wxCacheContent = this.getById(id);
        String BeforeName=(String)JSON.parseObject(wxCacheContent.getValue()).get("name");
        String AfterName=(String) JSON.parseObject(value).get("name");
        if(!BeforeName.equals(AfterName)){
            TbWxCacheContent tbWxCacheContent = new TbWxCacheContent();
            tbWxCacheContent.setValue(value);
            tbWxCacheContent.setName(AfterName);
            tbWxCacheContent.setCorpId(wxCacheContent.getCorpId());
            this.save(tbWxCacheContent);
            return true;
        }
        return false;
    }

    @Override
    @Async
    public Boolean save(String userId, String corpId, List<WelcomeAttachmentVo> attachments) {
        if(CollectionUtil.isNotEmpty(attachments)){
            for (WelcomeAttachmentVo attachment : attachments) {
                if(CommonConstants.MINIPROGRAM.equals(attachment.getMsgType())||CommonConstants.LINK.equals(attachment.getMsgType())){
                    TbWxCacheContent tbWxCacheContent = new TbWxCacheContent();
                    tbWxCacheContent.setUserId(userId);
                    tbWxCacheContent.setCorpId(corpId);
                    tbWxCacheContent.setType(attachment.getMsgType());
                    if(CommonConstants.MINIPROGRAM.equals(attachment.getMsgType())){
                        tbWxCacheContent.setName(attachment.getMiniProgramVo().getTitle());
                        tbWxCacheContent.setValue(JSON.toJSONString(attachment.getMiniProgramVo()));
                    }else if (CommonConstants.LINK.equals(attachment.getMsgType())){
                        tbWxCacheContent.setName(attachment.getLinkVo().getTitle());
                        tbWxCacheContent.setValue(JSON.toJSONString(attachment.getLinkVo()));
                    }else{
                        log.error("内容类型有误或该不支持缓存!");
                    }
                    this.save(tbWxCacheContent);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public Boolean addCacheContent(SysUser sysUser, CacheContentDTO cacheContentDTO) {
        TbWxCacheContent tbWxCacheContent = new TbWxCacheContent();
        if (CommonConstants.LINK.equals(cacheContentDTO.getType())) {
            tbWxCacheContent.setName((String) JSON.parseObject(cacheContentDTO.getValue()).get("title"));
        } else if (CommonConstants.MINIPROGRAM.equals(cacheContentDTO.getType())) {
            tbWxCacheContent.setName((String) JSON.parseObject(cacheContentDTO.getValue()).get("name"));
        } else {
            log.info(ErrCodeEnum.CONTENT_TYPE_ERROR_OR_NOT_SUPPORT_TYPE.getMessage());
            return false;
        }
        tbWxCacheContent.setCorpId(sysUser.getCorpId());
        tbWxCacheContent.setUserId(sysUser.getUserId());
        tbWxCacheContent.setValue(cacheContentDTO.getValue());
        tbWxCacheContent.setType(cacheContentDTO.getType());
        this.save(tbWxCacheContent);
        return true;
    }

}
