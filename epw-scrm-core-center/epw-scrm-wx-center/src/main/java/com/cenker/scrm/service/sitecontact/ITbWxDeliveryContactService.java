package com.cenker.scrm.service.sitecontact;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactSite;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.TbWxDeliveryContact;
import com.cenker.scrm.pojo.request.DeliveryUserContactRequest;
import com.cenker.scrm.pojo.vo.sitecontact.DeliveryContactVO;

public interface ITbWxDeliveryContactService extends IService<TbWxDeliveryContact> {
    /**
     * 通过站点活码参数生成配送员活码
     */
    String generateDeliveryContact(ContactSite contact);

    /**
     * 保存配送员活码数据库
     * @param contact
     * @param deliveryUserId
     */
    void saveTbWxDeliveryUserContact(ContactSite contact, Long deliveryUserId);

    /**
     * 处理配送员活码
     * @param contact
     * @param deliveryUserId
     */
    void handleDeliveryUserContact(ContactSite contact, Long deliveryUserId);

    /**
     * 获取配送员活码过期状态
     * @param deliveryUserContactRequest
     * @return
     */
    DeliveryContactVO getExpireStatus(DeliveryUserContactRequest deliveryUserContactRequest);

    /**
     * 根据唯一标识删除配送员活码
     * @param signId 标识
     * @param contactProvince 1 城市活码 2 门店 3 配送员
     */
    void removeBySignId(String signId, int contactProvince);
}
