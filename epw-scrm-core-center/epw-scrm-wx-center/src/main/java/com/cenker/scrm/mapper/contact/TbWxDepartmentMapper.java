package com.cenker.scrm.mapper.contact;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxDepartment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信组织架构Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface TbWxDepartmentMapper extends BaseMapper<TbWxDepartment> {
    /**
     * 查询企业微信组织架构列表
     *
     * @param tbWxDepartment 企业微信组织架构
     * @return 企业微信组织架构集合
     */
    List<TbWxDepartment> selectTbWxDepartmentList(TbWxDepartment tbWxDepartment);

    /**
     * 批量添加企业微信组织架构
     *
     * @param list 组织架构列表
     * @return 结果
     */
    int batchAddTbWxDepartments(List<TbWxDepartment> list);

    /**
     * 根据企业ID获取根节点
     *
     * @param corpId 企业Id
     * @return 结果
     */
    Long queryRootDeptByCorpId(String corpId);

    /**
     * 根据企业ID、部门ID查询部门信息
     *
     * @param corpId 企业ID
     * @param ids    部门Id列表
     * @return 结果
     */
    List<TbWxDepartment> getTbWxDepartmentList(@Param("corpId") String corpId, @Param("ids") List<Long> ids);
}
