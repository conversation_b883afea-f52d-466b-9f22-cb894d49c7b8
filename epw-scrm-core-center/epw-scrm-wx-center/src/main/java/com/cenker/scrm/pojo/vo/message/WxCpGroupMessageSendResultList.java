package com.cenker.scrm.pojo.vo.message;


import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WxCpGroupMessageSendResultList extends WxCpBaseResp implements Serializable {
    @SerializedName("next_cursor")
    private String nextCursor;

    @SerializedName("send_list")
    private List<Send> sendList;

    @Setter
    @Getter
    public static class Send {
        @SerializedName("userid")
        private String userId;
        private String status;
        @SerializedName("send_time")
        private Long sendTime;
        @SerializedName("external_userid")
        private String extUserId;
        @SerializedName("chat_id")
        private String chatId;
    }

    public static WxCpGroupMessageSendResultList fromJson(String json) {
        return WxCpGsonBuilder.create().fromJson(json, WxCpGroupMessageSendResultList.class);
    }
}
