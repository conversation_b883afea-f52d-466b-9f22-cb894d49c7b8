package com.cenker.scrm.controller.bu;

import com.cenker.scrm.pojo.dto.bu.ChatAgentDto;
import com.cenker.scrm.pojo.entity.bu.BuChatAgentLog;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.bu.IBuChatAgentLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * ChatAgent会话记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/chat/agent")
public class BuChatAgentLogController {

    private final IBuChatAgentLogService buChatAgentLogService;
    /**
     * 新增ChatAgent会话记录
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BuChatAgentLog buChatAgentLog) {
        return AjaxResult.success(buChatAgentLogService.save(buChatAgentLog));
    }

    /**
     * 发送话术记录
     */
    @PutMapping("/sendAnswer")
    AjaxResult sendAnswer(@RequestBody ChatAgentDto chatAgentDto) {
        BuChatAgentLog chatAgentLog = buChatAgentLogService.getById(chatAgentDto.getId());
        if (null == chatAgentLog) {
            log.error("更新发送话术记录，chatAgentLog is null, id={}", chatAgentDto.getId());
            return AjaxResult.success("找不到对应的数据！");
        }
        log.info("【发送话术记录】，id={}", chatAgentDto.getId());
        chatAgentLog.setSendAnswer(chatAgentDto.getSendAnswer());
        buChatAgentLogService.updateById(chatAgentLog);
        return AjaxResult.success();
    }

    /**
     * 修改
     */
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody BuChatAgentLog buChatAgentLog) {
        if (null == buChatAgentLog || null == buChatAgentLog.getId()) {
            log.error("更新发送话术记录，chatAgentLog is null or id is null");
            return AjaxResult.success("找不到对应的数据！");
        }
        log.info("【修改话术记录】，id={}", buChatAgentLog.getId());
        return AjaxResult.success(buChatAgentLogService.updateById(buChatAgentLog));
    }

    /**
     * 修改ChatAgent会话记录
     */
    @PutMapping("/stop")
    public AjaxResult stop(@RequestBody ChatAgentDto chatAgentDto) {
        if (null == chatAgentDto || null == chatAgentDto.getId()) {
            log.error("【停止生成话术】，id is null");
            return AjaxResult.success("【停止生成话术】找不到对应的数据！");
        }
        log.info("【停止生成话术】，id={}", chatAgentDto.getId());
        BuChatAgentLog update = new BuChatAgentLog();
        update.setId(chatAgentDto.getId());
        update.setStopAsk("1");
        update.setSendAnswer(chatAgentDto.getSendAnswer());
        return AjaxResult.success(buChatAgentLogService.updateById(update));
    }
}
