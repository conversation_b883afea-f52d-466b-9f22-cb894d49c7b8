package com.cenker.scrm.service.impl.message;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.constants.WeConstants;
import com.cenker.scrm.event.SynGroupMsgEvent;
import com.cenker.scrm.mapper.message.TbWxCorpMassMessageInfoMapper;
import com.cenker.scrm.pojo.dto.message.*;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.vo.message.*;
import com.cenker.scrm.service.message.*;
import com.cenker.scrm.service.tag.TbWxExtFollowUserTagService;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.external.WxCpMsgTemplate;
import me.chanjar.weixin.cp.bean.external.WxCpMsgTemplateAddResult;
import me.chanjar.weixin.cp.bean.external.msg.Attachment;
import me.chanjar.weixin.cp.bean.external.msg.Image;
import me.chanjar.weixin.cp.bean.external.msg.Link;
import me.chanjar.weixin.cp.bean.external.msg.Text;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.ExternalContact.*;


/**
 * <AUTHOR> 即将废弃
 */
@Service
@Slf4j
@Deprecated
public class TbWxCorpMassMessageInfoServiceImpl extends ServiceImpl<TbWxCorpMassMessageInfoMapper, TbWxCorpMassMessageInfo> implements TbWxCorpMassMessageInfoService {

    @Autowired
    private MessageSendTargetConditionService messageSendTargetConditionService;
    @Autowired
    private TbWxCorpMassMessageDetailService tbWxCorpMassMessageDetailService;
    @Resource
    private WxCpFeign wxCpFeign;
    @Autowired
    private TbWxCorpMassMessageLogService tbWxCorpMassMessageLogService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private TbWxExtFollowUserTagService tbWxExtFollowUserTagService;

    @Autowired
    private ITbWxMassMessageSenderRecordService tbWxMassMessageSenderRecordService;
    @Autowired
    private ITbWxMassMessageDetailService  tbWxMassMessageDetailService;

    // private String uri = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateMassMessage(MassMessageDTO dto) throws Exception {
        String errMsg;
        if (dto.getId() == null) {
            TbWxCorpMassMessageInfo massMessageInfo = baseMapper.selectById(dto.getId());
            if (massMessageInfo == null) {
                massMessageInfo = new TbWxCorpMassMessageInfo();
                TbWxCorpMassMessageDetail detail = new TbWxCorpMassMessageDetail();
                errMsg = buildMessageInfoAndSend(dto, massMessageInfo, detail, false);
            } else {
                // QueryWrapper wrapper = new QueryWrapper();
                // wrapper.eq("message_id", massMessageInfo.getId());
                TbWxCorpMassMessageDetail detail = tbWxCorpMassMessageDetailService.getOne(Wrappers.lambdaQuery(TbWxCorpMassMessageDetail.class).eq(TbWxCorpMassMessageDetail::getMessageInfoId,massMessageInfo.getId()));
                errMsg = buildMessageInfoAndSend(dto, massMessageInfo, detail, true);
            }
        } else {
            TbWxCorpMassMessageInfo massMessageInfo = new TbWxCorpMassMessageInfo();
            TbWxCorpMassMessageDetail detail = new TbWxCorpMassMessageDetail();
            errMsg = buildMessageInfoAndSend(dto, massMessageInfo, detail, true);
        }
        return errMsg;
    }

    @Override
    public List<MassMessageInfoVO> queryMassMessageInfo(QueryMassMessageVo queryMassMessageVo) {
        return baseMapper.queryMassMessageInfo(queryMassMessageVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTbWxCorpMassMessageInfo(TbWxCorpMassMessageInfo info) {
        return baseMapper.insertTbWxCorpMassMessageInfo(info);
    }

    @Override
    public List<MassMessageSenderVO> querySendDetailByMessageInfoId(QueryMassMessageVo vo) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("corpId", vo.getCorpId());
        params.put("messageInfoId", vo.getId());
        params.put("checkStatus", vo.getCheckStatus());
        if (Constants.CHAT_TYPE_SINGLE.equals(vo.getChatType())) {
            List<MassMessageSenderVO> list = baseMapper.queryMassMessageSenderCustDetail(params);
            if (list.size() > 0) {
                params.remove("messageInfoId");
                params.remove("checkStatus");
                List<Long> ids = new ArrayList<>();
                for (MassMessageSenderVO vo1 : list) {
                    Long minId = vo1.getMinId();
                    ids.add(minId);
                    Long maxId = vo1.getMaxId();
                    if (maxId.compareTo(minId) > 0) {
                        ids.add(maxId);
                        if (minId + 1 < maxId) {
                            ids.add(minId + 1);
                        }
                        if (maxId - minId > 1) {
                            vo1.setToUserName("等");
                        }
                    }
                }
                params.put("list", ids);
                List<MassMessageSenderVO> top3List = baseMapper.queryTop3ToUserName(params);
                Map<String, String> map = top3List.stream().collect(Collectors.toMap(x -> x.getUserId(), x -> x.getToUserName()));
                for (MassMessageSenderVO vo1 : list) {
                    if (StringUtils.isNotEmpty(vo1.getToUserName())) {
                        vo1.setToUserName(map.get(vo1.getUserId()) + vo1.getToUserName());
                    } else {
                        vo1.setToUserName(map.get(vo1.getUserId()));
                    }
                }
                return list;
            }
        } else if (Constants.CHAT_TYPE_GROUP.equals(vo.getChatType())) {
            return baseMapper.queryMassMessageSenderChatDetail(params);
        }
        return new ArrayList<>();
    }

    @Override
    public List<MassMessageExportVo> exportSendDetailByMessageInfoId(QueryMassMessageVo vo) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("corpId", vo.getCorpId());
        params.put("messageInfoId", vo.getId());
        params.put("checkStatus", vo.getCheckStatus());
        if (Constants.CHAT_TYPE_SINGLE.equals(vo.getChatType())) {
            return baseMapper.exportMassMessageSenderCustDetail(params);
        }
        return new ArrayList<>();
    }

    @Override
    public void batchSendMassMessage(String corpId) {
        //查询需要定时发送的群数据
        List<TbWxCorpMassMessageDetail> list = baseMapper.queryWaitToSendMassInfoByCorpId(corpId);
        if (list.size() <= 0) {
            log.info("corpId:{}没有需要定时发送的群数据", corpId);
            return;
        }
        Map<Long, TbWxCorpMassMessageDetail> detailMap = list.stream().collect(Collectors.toMap(x -> x.getMessageInfoId(), x -> x));
        Map<String, Object> params = new HashMap<>(8);
        params.put("corpId", corpId);
        List<Long> ids = list.stream().map(TbWxCorpMassMessageDetail::getMessageInfoId).distinct().
                collect(Collectors.toList());
        params.put("list", ids);
        List<MassWxSenderVO> massWxSenderVos = baseMapper.queryWaitToSendMessages(params);
        log.info("corpId:{}查询需要定时发送的群数据的大小为", massWxSenderVos.size());
        if (massWxSenderVos.size() > 0) {
            Map<Long, List<MassWxSenderVO>> mapSenders =
                    massWxSenderVos.stream().collect(Collectors.groupingBy(x -> x.getMessageInfoId()));
            for (Long id : mapSenders.keySet()) {
                List<MassWxSenderVO> voList = mapSenders.get(id);
                if (voList.size() < 1) {
                    log.info("messageInfoId:{} 没有要发送的数据");
                    continue;
                }
                //
                if (Constants.CHAT_TYPE_SINGLE.equals(voList.get(0).getChatType())) {
                    try {
                        WxCpMsgTemplateAddResult result = sendMassSingleMessage(voList, corpId, detailMap.get(id));
                        updateMessageInfoAndMessageInfo(corpId, result.getMsgId(), result.getFailList(), id, null);
                    } catch (WxErrorException e) {
                        log.error("企业群发失败:{}", e.getError().getErrorMsg(), e);
                    }
                } else {
                    //群发
                    WxCpMsgTemplateAddResult result = new WxCpMsgTemplateAddResult();
                    for (MassWxSenderVO vo : voList) {
                        WxCpMsgTemplate cpMsgTemplate = genWxCpMsgTemplate(detailMap.get(id), Constants.CHAT_TYPE_GROUP);
                        cpMsgTemplate.setSender(vo.getUserId());
                        try {
                            result = sendWxCpMsgTemplateAddResult(result, cpMsgTemplate,corpId);
                            updateMessageInfoAndMessageInfo(corpId, result.getMsgId(), result.getFailList(), id, vo.getUserId());
                        } catch (WxErrorException e) {
                            log.error("企业userId:{} 群发失败:{}", e.getError().getErrorMsg(), e);
                        }
                    }
                    //更新messageInfo表
                    TbWxCorpMassMessageInfo info = new TbWxCorpMassMessageInfo();
                    info.setId(id);
                    info.setCheckStatus(Constants.SENDED);
                    updateById(info);
                }
            }
        }
    }

    @Override
    public void batchSynWxMassMessage(String corpId, Long startTime, Long endTime) {
        //获取当天群发记录列表
        List<WxCpGroupMsgList.GroupMember> groupMembers = new ArrayList<>();
        List<WxCpGroupMsgList.GroupMember> groupSingleMembers = new ArrayList<>();

        try {
            WxCpGroupMsgList groupMsgList = getGroupMsgListFormWx(corpId, startTime, endTime, null, Constants.CHAT_TYPE_SINGLE);
            if (groupMsgList.getGroupMsgList().size() > 0) {
                groupSingleMembers.addAll(groupMsgList.getGroupMsgList());
            }
            while (StringUtils.isNotEmpty(groupMsgList.getNextCursor())) {
                String nextCursor = groupMsgList.getNextCursor();
                groupMsgList = getGroupMsgListFormWx(corpId, startTime, endTime, nextCursor, Constants.CHAT_TYPE_SINGLE);
                groupSingleMembers.addAll(groupMsgList.getGroupMsgList());
            }

            groupMsgList = getGroupMsgListFormWx(corpId, startTime, endTime, null, Constants.CHAT_TYPE_GROUP);
            if (groupMsgList.getGroupMsgList().size() > 0) {
                groupMembers.addAll(groupMsgList.getGroupMsgList());
            }
            while (StringUtils.isNotEmpty(groupMsgList.getNextCursor())) {
                String nextCursor = groupMsgList.getNextCursor();
                groupMsgList = getGroupMsgListFormWx(corpId, startTime, endTime, nextCursor, Constants.CHAT_TYPE_GROUP);
                groupMembers.addAll(groupMsgList.getGroupMsgList());
            }
            //发送事件
            if (groupMembers.size() > 0) {
                applicationEventPublisher.publishEvent(new SynGroupMsgEvent(groupMembers, corpId, Constants.CHAT_TYPE_GROUP));
            }
            if (groupSingleMembers.size() > 0) {
                applicationEventPublisher.publishEvent(new SynGroupMsgEvent(groupSingleMembers, corpId, Constants.CHAT_TYPE_SINGLE));
            }
        } catch (WxErrorException e) {
            log.error("调用企业微信群发接口externalcontact/get_groupmsg_list失败{},参数信息:{}", e.getError().getErrorMsg(),
                    corpId);
        }
    }


    @Override
    public WxCpGroupMsgList getGroupMsgListFormWx(String corpId, Long startTime, Long endTime, String cursor, String chatType) throws WxErrorException {
        // 代自建应用获取
        // WxCpServiceImpl wxCpService = workCorpCpService.getWxCpAgentServiceByCorpId(corpId);
        WxCpServiceImpl wxCpService = null;
        JsonObject json = new JsonObject();
        json.addProperty("chat_type", chatType);
        json.addProperty("start_time", startTime);
        json.addProperty("end_time", endTime);
        json.addProperty("limit", 100);
        json.addProperty("cursor", cursor);
        String result = wxCpService.post( wxCpService.getWxCpConfigStorage().getApiUrl(GET_GROUP_MSG_LIST_V2), json);
        return WxCpGroupMsgList.fromJson(result);
    }

    @Override
    public WxCpGroupTaskList getGroupTaskListFormWx(String corpId, String cursor, String msgId) throws WxErrorException {
        // 代自建应用获取
        // WxCpServiceImpl wxCpService = workCorpCpService.getWxCpAgentServiceByCorpId(corpId);
        WxCpServiceImpl wxCpService = null;
        JsonObject json = new JsonObject();
        json.addProperty("msgid", msgId);
        json.addProperty("limit", 1000);
        json.addProperty("cursor", cursor);
        String result = wxCpService.post(wxCpService.getWxCpConfigStorage().getApiUrl(GET_GROUP_MSG_TASK), json);
        return WxCpGroupTaskList.fromJson(result);
    }

    @Override
    public WxCpGroupMessageSendResultList getGroupMessageResultFormWx(String corpId, String cursor, String msgId, String userId) throws WxErrorException {
        // WxCpService wxCpService = wxMultiCorpService.getWxCpServiceByCorpId(corpId, WX_APP_EXT_CUSTOMER);
        // 代自建应用获取
        WxCpServiceImpl wxCpService = null;
        JsonObject json = new JsonObject();
        json.addProperty("msgid", msgId);
        json.addProperty("userid", userId);
        json.addProperty("limit", 1000);
        json.addProperty("cursor", cursor);
        String result = wxCpService.post(wxCpService.getWxCpConfigStorage().getApiUrl(GET_GROUP_MSG_SEND_RESULT), json);
        return WxCpGroupMessageSendResultList.fromJson(result);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageInfoAndMessageInfo(String corpId, String wxMsgId, List<String> failList, Long messInfoId, String userId) {
        if (StringUtils.isNotEmpty(userId)) {
            //更新messageInfo表
            TbWxCorpMassMessageInfo info = new TbWxCorpMassMessageInfo();
            info.setId(messInfoId);
            info.setCheckStatus(Constants.SENDED);
            updateById(info);
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("id", messInfoId);
            wrapper.eq("corp_id", corpId);
            wrapper.eq("user_id", userId);
            TbWxCorpMassMessageLog log = new TbWxCorpMassMessageLog();
            log.setMessageId(wxMsgId);
            log.setStatus(Constants.WX_MASS_MSG_SUCCESS);
            tbWxCorpMassMessageLogService.update(log, wrapper);
        } else {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("id", messInfoId);
            wrapper.eq("corp_id", corpId);
            wrapper.notIn("sender_id", failList);
            //更新发送成功的数据
            TbWxCorpMassMessageLog log = new TbWxCorpMassMessageLog();
            log.setMessageId(wxMsgId);
            log.setStatus(Constants.WX_MASS_MSG_SUCCESS);
            tbWxCorpMassMessageLogService.update(log, wrapper);
            //更新发送失败的数据
            log.setStatus(Constants.WX_MASS_MSG_FAIL);
            wrapper.clear();
            wrapper.eq("id", messInfoId);
            wrapper.eq("corp_id", corpId);
            wrapper.in("sender_id", failList);
            tbWxCorpMassMessageLogService.update(log, wrapper);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public String buildMessageInfoAndSend(MassMessageDTO dto, TbWxCorpMassMessageInfo info,
                                          TbWxCorpMassMessageDetail detail,
                                          boolean updateFlag) throws Exception {
        String userName = dto.getUserName();
        // 条件构造
        List<TbWxCorpMassMessageSendTargetCondition> conditions = new ArrayList<>();
        BeanUtils.copyProperties(dto, info);
        info.setCorpId(dto.getCorpId());
        detail.setCorpId(dto.getCorpId());
        if (dto.getDelFlag() == 1) {
            detail.setDelFlag(1);
            dto.setTargetDTO(null);
        }
        if (dto.getDetailDTO() != null) {
            BeanUtils.copyProperties(dto.getDetailDTO(), detail);
        }

        // 设置更新人、创建人
        if (!updateFlag) {
            info.setCreateBy(userName);
            detail.setCreateBy(userName);
        } else {
            info.setUpdateBy(userName);
            detail.setUpdateBy(userName);
        }

        // 设置发送时间
        if (StringUtils.isNotEmpty(dto.getSetTime())) {
            info.setSettingTime(DateUtils.parseDate(dto.getSetTime(), com.cenker.scrm.util.DateUtils.YYYY_MM_DD_HH_MM_SS));
            info.setTimedTask(1);
        }

        //更新target、detail,再更新tb_wx_corp_mass_message_info
        if (updateFlag) {
            tbWxCorpMassMessageDetailService.updateById(detail);
            updateById(info);
        } else {
            // 设置为已发送
            info.setCheckStatus("1");
            if (StringUtils.isNotEmpty(dto.getSetTime())) {
                info.setCheckStatus("0");
            }
            // 记录发送消息
            insertTbWxCorpMassMessageInfo(info);
            // 记录发送内容
            detail.setMessageInfoId(info.getId());
            tbWxCorpMassMessageDetailService.save(detail);
        }
        if (dto.getTargetDTO() != null) {
            dto.setId(info.getId());
            conditions = dealMassMessageCondition(dto);
        }

        // 发送对象数据入库
        if (conditions.size() > 0 && dto.getDelFlag() == 0) {
            messageSendTargetConditionService.saveBatch(conditions, 100);
        }

        List<MassWxSenderVO> list = new ArrayList<>();
        //判断是发送群还是发送给客户
        if (Constants.CHAT_TYPE_SINGLE.equals(dto.getChatType())) {
            // 判断是否全部客户 PushRange 为 0表示全部 1 表示指定
            if (DefaultConstants.PUSH_ALL_CUSTOMER.equals(dto.getPushRange())) {
                Map<String, Object> params = new HashMap<>(2);
                params.put("corpId", dto.getCorpId());
                list = baseMapper.queryExtUserIdByCondition(params);
            } else {
                //指定客户发送
                if (conditions.size() > 0) {
                    list = queryExtUserIdByCondition(conditions, dto.getCorpId());
                }
            }
        } else if (Constants.CHAT_TYPE_GROUP.equals(dto.getChatType())) {
            Map<String, Object> params = new HashMap<>(6);
            params.put("corpId", dto.getCorpId());
            params.put("userIds", buildConditionIdList(conditions, Constants.STAFF));
            params.put("deptIds", buildConditionIdList(conditions, Constants.DEPT));
            list = baseMapper.queryChatIdsByCorpInfo(params);
        }

        if (list.size() > 0) {
            //如果不是定时发送就直接发送,否则等待定时任务发送
            if (StringUtils.isNotEmpty(dto.getSetTime())) {
                info.setCheckStatus("0");
                //saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, new ArrayList<>(), userName);
            } else {
                info.setCheckStatus("1");
                if (Constants.CHAT_TYPE_SINGLE.equals(dto.getChatType())) {
                    WxCpMsgTemplateAddResult result = sendMassSingleMessage(list, dto.getCorpId(), detail);
                    saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, result.getFailList(), userName);
                    info.setExpectSend(list.stream().map(MassWxSenderVO::getSenderId).distinct().count());
                } else if (Constants.CHAT_TYPE_GROUP.equals(dto.getChatType())) {
                    info.setExpectSend(list.stream().map(MassWxSenderVO::getUserId).distinct().count());
                    List<WxCpMsgTemplateAddResult> results = sendMassGroupMessage(list, dto.getCorpId(), detail);
                    List<String> failList = new ArrayList<>();
                    if (results.size() > 0) {
                        for (WxCpMsgTemplateAddResult result : results) {
                            if (result.getFailList() != null) {
                                failList.addAll(result.getFailList());
                            }
                        }
                    }
                    int count = saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, failList, userName);
                    info.setExpectSend((long) count);
                }
            }
            updateById(info);
        }
        return "";
    }

    private int saveMessageLogs(String copId, String chatType, Long messageInfoId, List<MassWxSenderVO> list,
                                List<String> failList, String userName) {
        Date sendTime = new Date();
        List<TbWxCorpMassMessageLog> messageLogs = new ArrayList<>();
        //保存发送日志
        for (MassWxSenderVO followVo : list) {
            TbWxCorpMassMessageLog messageLog = new TbWxCorpMassMessageLog();
            messageLog.setCreateBy(userName);
            messageLog.setStatus("0");
            if (Constants.CHAT_TYPE_SINGLE.equals(chatType)) {
                messageLog.setMessageId(list.get(0).getWxMessageId());
                if (failList.size() > 0) {
                    if (failList.contains(followVo.getSenderId())) {
                        messageLog.setStatus("2");
                    }
                }
            } else {
                messageLog.setMessageId(followVo.getWxMessageId());
                if (failList.size() > 0) {
                    if (failList.contains(followVo.getUserId())) {
                        messageLog.setStatus("2");
                    }
                }
            }
            messageLog.setSendTime(sendTime);
            messageLog.setCorpId(copId);
            messageLog.setSendType(chatType);
            messageLog.setSenderId(followVo.getSenderId());
            messageLog.setUserId(followVo.getUserId());
            messageLog.setMessageInfoId(messageInfoId);
            messageLogs.add(messageLog);
        }
        tbWxCorpMassMessageLogService.saveBatch(messageLogs, 300);
        return messageLogs.size();
    }

    WxCpMsgTemplateAddResult sendMassSingleMessage(List<MassWxSenderVO> senders, String corpId, TbWxCorpMassMessageDetail detail) throws WxErrorException {
        WxCpMsgTemplateAddResult result = new WxCpMsgTemplateAddResult();
        WxCpMsgTemplate cpMsgTemplate = genWxCpMsgTemplate(detail, Constants.CHAT_TYPE_SINGLE);
        List<String> users = senders.stream().map(MassWxSenderVO::getSenderId).distinct().collect(Collectors.toList());
        cpMsgTemplate.setExternalUserid(users);
        result = sendWxCpMsgTemplateAddResult(result, cpMsgTemplate,corpId);
        senders.get(0).setWxMessageId(result.getMsgId());
        return result;
    }

    private WxCpMsgTemplateAddResult sendWxCpMsgTemplateAddResult(WxCpMsgTemplateAddResult result , WxCpMsgTemplate cpMsgTemplate,String corpId) throws WxErrorException {
        try {

            // work调用获取
            result = wxCpFeign.addMsgTemplate(cpMsgTemplate,corpId);
            // result = wxCpService.getExternalContactService().addMsgTemplate(cpMsgTemplate);
        } catch (Exception e) {
            log.error("调用群发消息接口失败:{} 参数:{}", e.getMessage(), JSON.toJSONString(cpMsgTemplate));
            result.setErrMsg(e.getMessage());
            throw e;
        }
        return result;
    }

    List<WxCpMsgTemplateAddResult> sendMassGroupMessage(List<MassWxSenderVO> senders, String corpId, TbWxCorpMassMessageDetail detail) throws WxErrorException {
        List<WxCpMsgTemplateAddResult> list = new ArrayList<>();
        WxCpMsgTemplate cpMsgTemplate = genWxCpMsgTemplate(detail, Constants.CHAT_TYPE_GROUP);
        List<String> senderList = senders.stream().map(MassWxSenderVO::getUserId).distinct().collect(Collectors.toList());
        Map<String, String> map = new HashMap<>(64);
        for (String str : senderList) {
            WxCpMsgTemplateAddResult result = new WxCpMsgTemplateAddResult();
            cpMsgTemplate.setSender(str);
            result = sendWxCpMsgTemplateAddResult(result, cpMsgTemplate,corpId);
            map.put(str, result.getMsgId());
            list.add(result);
        }
        for (MassWxSenderVO vo : senders) {
            vo.setWxMessageId(map.get(vo.getUserId()));
        }
        return list;
    }

    private WxCpMsgTemplate genWxCpMsgTemplate(TbWxCorpMassMessageDetail detail, String chatType) {
        WxCpMsgTemplate cpMsgTemplate = new WxCpMsgTemplate();
        cpMsgTemplate.setChatType(chatType);
        List<String> users;
        // 附件
        List<Attachment> attachments = Lists.newArrayList();
        // 文本消息
        if (StringUtils.isNotEmpty(detail.getContent())) {
            Text text = new Text();
            text.setContent(detail.getContent());
            cpMsgTemplate.setText(text);
        }
        // 图片
        if (StringUtils.isNotEmpty(detail.getPicUrl()) || StringUtils.isNotEmpty(detail.getMediaId())) {
            Image image = new Image();
            image.setPicUrl(detail.getPicUrl());
            image.setMediaId(detail.getMediaId());
            Attachment attachment = new Attachment();
            attachment.setImage(image);
            attachments.add(attachment);
            cpMsgTemplate.setAttachments(attachments);
        }
        if (StringUtils.isNotEmpty(detail.getLinkUrl())) {
            Link link = new Link();
            link.setPicUrl(detail.getLinkPicUrl());
            link.setUrl(detail.getLinkUrl());
            link.setTitle(detail.getLinkTitle());
            link.setDesc(detail.getLinDesc());
            Attachment attachment = new Attachment();
            attachment.setLink(link);
            attachments.add(attachment);
            cpMsgTemplate.setAttachments(attachments);
        }
        return cpMsgTemplate;
    }

    List<TbWxCorpMassMessageSendTargetCondition> dealMassMessageCondition(MassMessageDTO dto) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("corp_id", dto.getCorpId());
        wrapper.eq("message_info_id", dto.getId());
        messageSendTargetConditionService.remove(wrapper);

        List<TbWxCorpMassMessageSendTargetCondition> conditions = new ArrayList<>();
        if (dto.getTargetDTO() != null) {
            if (dto.getTargetDTO().getStaffId() != null && dto.getTargetDTO().getStaffId().size() > 0) {
                for (String str : dto.getTargetDTO().getStaffId()) {
                    buildCondition(dto, conditions, str, Constants.STAFF);
                }
            }
            if (dto.getTargetDTO().getDepartmentIds() != null && dto.getTargetDTO().getDepartmentIds().size() > 0) {
                for (String str : dto.getTargetDTO().getDepartmentIds()) {
                    buildCondition(dto, conditions, str, Constants.DEPT);
                }
            }
            if (dto.getTargetDTO().getTagIds() != null && dto.getTargetDTO().getTagIds().size() > 0) {
                for (String str : dto.getTargetDTO().getTagIds()) {
                    buildCondition(dto, conditions, str, Constants.IN_TAG);
                }
            }
            if (dto.getTargetDTO().getNotInTagIds() != null && dto.getTargetDTO().getNotInTagIds().size() > 0) {
                for (String str : dto.getTargetDTO().getNotInTagIds()) {
                    buildCondition(dto, conditions, str, Constants.NOT_IN_TAG);
                }
            }
            if (CollectionUtils.isNotEmpty(dto.getTargetDTO().getExternalUserId())) {
                for (String str : dto.getTargetDTO().getExternalUserId()) {
                    buildCondition(dto, conditions, str, "5");// 指定客户
                }
            }
        }
        return conditions;
    }

    private void buildCondition(MassMessageDTO dto, List<TbWxCorpMassMessageSendTargetCondition> conditions, String conditionId, String type) {
        TbWxCorpMassMessageSendTargetCondition condition = new TbWxCorpMassMessageSendTargetCondition();
        condition.setCorpId(dto.getCorpId());
        condition.setConditionId(conditionId);
        condition.setMessageInfoId(dto.getId());
        condition.setType(type);
        conditions.add(condition);
    }

    List<MassWxSenderVO> queryExtUserIdByCondition(List<TbWxCorpMassMessageSendTargetCondition> conditions, String corpId) {
        Map<String, Object> params = new HashMap<>(6);
        params.put("corpId", corpId);
        params.put("userIds", buildConditionIdList(conditions, Constants.STAFF));
        params.put("deptIds", buildConditionIdList(conditions, Constants.DEPT));
        params.put("tagIds", buildConditionIdList(conditions, Constants.IN_TAG));
        params.put("noTagIds", buildConditionIdList(conditions, Constants.NOT_IN_TAG));
        params.put("externalUserId", buildConditionIdList(conditions, "5"));
        return baseMapper.queryExtUserIdByCondition(params);
    }

    private List<String> buildConditionIdList(List<TbWxCorpMassMessageSendTargetCondition> conditions, String staff) {
        return conditions.stream().filter(x -> {
            if (staff.equals(x.getType())) {
                return true;
            } else {
                return false;
            }
        }).map(TbWxCorpMassMessageSendTargetCondition::getConditionId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void MassMessage4TimedTask(TbWxCorpMassMessageInfo info) {
        TbWxCorpMassMessageDetail detail = tbWxCorpMassMessageDetailService.getOne(
                new QueryWrapper<TbWxCorpMassMessageDetail>().eq("message_info_id", info.getId()));
        if (detail == null) {
            log.info("定时任务群发消息，查询发送详情为空,当前执行消息id{}", info.getId());
            return;
        }
        try {
            buildMessageInfoAndSend(info, detail);
        } catch (WxErrorException e) {
            log.error("构建信息并发送出现错误,info:{},detail:{}",info,detail);
            log.error("错误信息:{}",e);
        }
    }



    @Override
    public MassMessageOverviewVO getMassMessageOverview(String detailId) {
        LambdaQueryWrapper<TbWxMassMessageSenderRecord> senderRecordLambdaQueryWrapper = Wrappers.lambdaQuery(TbWxMassMessageSenderRecord.class);
        senderRecordLambdaQueryWrapper.eq(TbWxMassMessageSenderRecord::getMessageDetailId,detailId);
        List<TbWxMassMessageSenderRecord> senderRecordList = tbWxMassMessageSenderRecordService.list(senderRecordLambdaQueryWrapper);
        long unSendMemberNum = senderRecordList.stream().filter(item -> "0".equals(item.getSendStatus())).count();
        long sendMemberNum = senderRecordList.stream().filter(item -> "1".equals(item.getSendStatus())).count();
        long failSendCustomerNum = senderRecordList.stream().filter(item -> "2".equals(item.getSendStatus())).count();
        LambdaQueryWrapper<TbWxMassMessageDetail> messageDetailLambdaQueryWrapper = Wrappers.lambdaQuery(TbWxMassMessageDetail.class);
        messageDetailLambdaQueryWrapper.eq(TbWxMassMessageDetail::getId,detailId);
        TbWxMassMessageDetail tbWxMassMessageDetail = tbWxMassMessageDetailService.getOne(messageDetailLambdaQueryWrapper);
        Long actualSendCustomerNum = tbWxMassMessageDetail.getActualSend();
        Long expectSend = tbWxMassMessageDetail.getExpectSend();
        MassMessageOverviewVO massMessageOverviewVO = new MassMessageOverviewVO();
        massMessageOverviewVO.setSendMemberNum(sendMemberNum);
        massMessageOverviewVO.setUnSendMemberNum(unSendMemberNum);
        massMessageOverviewVO.setFailSendCustomerNum(failSendCustomerNum);
        massMessageOverviewVO.setActualSendCustomerNum(actualSendCustomerNum);
        massMessageOverviewVO.setUnSendCustomerNum(expectSend-actualSendCustomerNum);
        return massMessageOverviewVO;
    }

    @Override
    public List<MassMessageSendDetailVO> getSendUserDetailList(String detailId,String status) {
        List<MassMessageSendDetailVO> list=tbWxMassMessageSenderRecordService.getSendUserDetail(detailId);
        list.stream().map(item->{
            item.setFinishRate(getFinishRate(item.getActualSend(),item.getExpectSend()));
            if(item.getActualSend()>0){
                item.setSendStatus("1");
            }
            if(item.getFailSend()>0){
                item.setSendStatus("2");
            }
            if(item.getActualSend()==0 && item.getFailSend()==0){
                item.setSendStatus("0");
            }
            if(StringUtils.isEmpty(item.getSettingTime())){
                item.setSettingTime("未设置发送时间");
            }
            return item;
        }).filter(item->item.getSendStatus().equals(status)).collect(Collectors.toList());
        return list;
    }

    private String getFinishRate(Long actualSend, Long expectSend) {
        double v = new BigDecimal((float) actualSend / expectSend).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue();
        String format = new DecimalFormat("0").format(v)+"%";
        return format;
    }

    private void buildMessageInfoAndSend(TbWxCorpMassMessageInfo info, TbWxCorpMassMessageDetail detail) throws WxErrorException {
        String userName = info.getCreateBy();
        String corpId = info.getCorpId();
        String chatType = info.getChatType();

        // 条件构造
        List<TbWxCorpMassMessageSendTargetCondition> conditions = messageSendTargetConditionService.list(new QueryWrapper<TbWxCorpMassMessageSendTargetCondition>().eq("message_info_id", info.getId()));

        info.setUpdateBy(userName);
        info.setCreateTime(new Date());
        detail.setUpdateBy(userName);
        detail.setUpdateTime(new Date());

        // 设置为已发送
        info.setCheckStatus("1");

        List<MassWxSenderVO> list = new ArrayList<>();
        //判断是发送群还是发送给客户
        if (Constants.CHAT_TYPE_SINGLE.equals(chatType)) {
            // 判断是否全部客户 PushRange 为 0表示全部 1 表示指定
            Map<String, Object> params = new HashMap<>(2);
            if (DefaultConstants.PUSH_ALL_CUSTOMER.equals(info.getPushRange())) {
                params.put("corpId", corpId);
                list = baseMapper.queryExtUserIdByCondition(params);
            } else {
                //指定客户发送
                if (conditions.size() > 0) {
                    list = queryExtUserIdByCondition(conditions, corpId);
                }
            }
        } else if (Constants.CHAT_TYPE_GROUP.equals(chatType)) {
            Map<String, Object> params = new HashMap<>(6);
            params.put("corpId", corpId);
            params.put("deptIds", buildConditionIdList(conditions, Constants.DEPT));
            params.put("userIds", buildConditionIdList(conditions, Constants.STAFF));
            list = baseMapper.queryChatIdsByCorpInfo(params);
        }

        if (list.size() > 0) {
            if (Constants.CHAT_TYPE_SINGLE.equals(chatType)) {
                WxCpMsgTemplateAddResult result = sendMassSingleMessage(list, corpId, detail);
                saveMessageLogs(corpId, chatType, info.getId(), list, result.getFailList(), userName);
                info.setExpectSend(list.stream().map(MassWxSenderVO::getSenderId).distinct().count());
            } else if (Constants.CHAT_TYPE_GROUP.equals(info.getChatType())) {
                info.setExpectSend(list.stream().map(MassWxSenderVO::getUserId).distinct().count());
                List<WxCpMsgTemplateAddResult> results = sendMassGroupMessage(list, corpId, detail);
                List<String> failList = new ArrayList<>();
                if (results.size() > 0) {
                    for (WxCpMsgTemplateAddResult result : results) {
                        if (result.getFailList() != null) {
                            failList.addAll(result.getFailList());
                        }
                    }
                }
                int count = saveMessageLogs(corpId, chatType, info.getId(), list, failList, userName);
                info.setExpectSend((long) count);
            }
            updateById(info);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMessage4Fission(CustomerMessagePushDTO customerMessagePushDto) {

        //发送群发消息
        //发送类类型: 给单个客户发，群发
        //发给客户
        try {
            MassMessageDTO massMessageDTO = new MassMessageDTO();
            TbWxCorpMassMessageInfo massMessageInfo = new TbWxCorpMassMessageInfo();
            TbWxCorpMassMessageDetail tbWxCorpMassMessageDetail = new TbWxCorpMassMessageDetail();
            massMessageDTO.setTaskFissionId(customerMessagePushDto.getTaskFissionId());
            massMessageDTO.setCorpId(customerMessagePushDto.getCorpId());
            massMessageDTO.setUserName(customerMessagePushDto.getUserName());
            // 发送时间
            if (StringUtils.isNotEmpty(customerMessagePushDto.getSettingTime())) {
                massMessageDTO.setSetTime(customerMessagePushDto.getSettingTime());
            }
            // 发送客户
            if (customerMessagePushDto.getPushType().equals(WeConstants.SEND_MESSAGE_CUSTOMER)) {
                massMessageDTO.setChatType(Constants.CHAT_TYPE_SINGLE);
            }
            //发给客户群
            if (customerMessagePushDto.getPushType().equals(WeConstants.SEND_MESSAGE_GROUP)) {
                massMessageDTO.setChatType(Constants.CHAT_TYPE_GROUP);
            }
            // 消息详情
            MassMessageDetailDTO massMessageDetailDTO = new MassMessageDetailDTO();
            massMessageDetailDTO.setLinkUrl(customerMessagePushDto.getLinkMessage().getUrl());
            massMessageDetailDTO.setLinDesc(customerMessagePushDto.getLinkMessage().getDesc());
            massMessageDetailDTO.setLinkTitle(customerMessagePushDto.getLinkMessage().getTitle());
            massMessageDetailDTO.setLinkPicUrl(customerMessagePushDto.getLinkMessage().getPicurl());
            massMessageDTO.setDetailDTO(massMessageDetailDTO);

            // 指定还是全部
            massMessageDTO.setPushRange(customerMessagePushDto.getPushRange());

            MassSendTargetDTO massSendTargetDTO = new MassSendTargetDTO();
            // 指定需要员工id
            if (DefaultConstants.PUSH_RANGE_CUSTOMER.equals(customerMessagePushDto.getPushRange())) {
                //List<String> staffIds = customers.stream().map(TbWxExtCustomer::getExternalUserId).distinct().collect(Collectors.toList());
                String staffId = customerMessagePushDto.getStaffId();
                if (StringUtils.isNotEmpty(staffId)) {
                    // 可能存在只选了部门的情况
                    List<String> staffIds = Stream.of(staffId.split(",")).collect(Collectors.toList());
                    massSendTargetDTO.setStaffId(staffIds);
                }
            }
            // 指定标签？
            if (StringUtils.isNotEmpty(customerMessagePushDto.getTag()) && !"all".equals(customerMessagePushDto.getTag())) {
                String tagId = customerMessagePushDto.getTag();
                List<String> tagIds = Stream.of(tagId.split(",")).collect(Collectors.toList());
                massSendTargetDTO.setTagIds(tagIds);
            }
            // 指定部门
            if (StringUtils.isNotEmpty(customerMessagePushDto.getDepartment())) {
                String department = customerMessagePushDto.getDepartment();
                List<String> departmentIds = Stream.of(department.split(",")).collect(Collectors.toList());
                massSendTargetDTO.setDepartmentIds(departmentIds);
            }
            massMessageDTO.setTargetDTO(massSendTargetDTO);

            // 发送消息并记录
            buildMessageInfoAndSend4Fission(massMessageDTO, massMessageInfo, tbWxCorpMassMessageDetail, false);
        } catch (Exception e) {
            log.error("裂变任务发送消息异常");
            log.error("错误信息:{}",e);
        }
    }

    private String buildMessageInfoAndSend4Fission(MassMessageDTO dto, TbWxCorpMassMessageInfo info, TbWxCorpMassMessageDetail detail, boolean updateFlag) throws WxErrorException, ParseException {
        String userName = dto.getUserName();
        // 条件构造
        List<TbWxCorpMassMessageSendTargetCondition> conditions = new ArrayList<>();
        BeanUtils.copyProperties(dto, info);
        info.setCorpId(dto.getCorpId());
        detail.setCorpId(dto.getCorpId());
        if (dto.getDelFlag() == 1) {
            detail.setDelFlag(1);
            dto.setTargetDTO(null);
        }
        if (dto.getDetailDTO() != null) {
            BeanUtils.copyProperties(dto.getDetailDTO(), detail);
        }

        // 设置更新人、创建人
        if (!updateFlag) {
            info.setCreateBy(userName);
            detail.setCreateBy(userName);
        } else {
            info.setUpdateBy(userName);
            detail.setUpdateBy(userName);
        }

        // 设置发送时间
        if (StringUtils.isNotEmpty(dto.getSetTime())) {
            info.setSettingTime(DateUtils.parseDate(dto.getSetTime(), com.cenker.scrm.util.DateUtils.YYYY_MM_DD_HH_MM_SS));
            info.setTimedTask(1);
        }

        //更新target、detail,再更新tb_wx_corp_mass_message_info
        if (updateFlag) {
            tbWxCorpMassMessageDetailService.updateById(detail);
            updateById(info);
        } else {
            // 设置为已发送
            info.setCheckStatus("1");
            if (StringUtils.isNotEmpty(dto.getSetTime())) {
                info.setCheckStatus("0");
            }
            // 记录发送消息
            insertTbWxCorpMassMessageInfo(info);
            // 记录发送内容
            detail.setMessageInfoId(info.getId());
            tbWxCorpMassMessageDetailService.save(detail);
        }
        if (dto.getTargetDTO() != null) {
            dto.setId(info.getId());
            conditions = dealMassMessageCondition(dto);
        }

        // 发送对象数据入库
        if (conditions.size() > 0 && dto.getDelFlag() == 0) {
            messageSendTargetConditionService.saveBatch(conditions, 100);
        }

        List<MassWxSenderVO> list = new ArrayList<>();
        //判断是发送群还是发送给客户
        if (Constants.CHAT_TYPE_SINGLE.equals(dto.getChatType())) {
            // 判断是否全部客户 PushRange 为 0表示全部 1 表示指定
            if (DefaultConstants.PUSH_ALL_CUSTOMER.equals(dto.getPushRange())) {
                Map<String, Object> params = new HashMap<>(2);
                params.put("corpId", dto.getCorpId());
                if (conditions.size() > 0) {
                    // 选定标签
                    params.put("tagIds", buildConditionIdList(conditions, Constants.IN_TAG));
                }
                list = baseMapper.queryExtUserIdByCondition(params);
                /**
                 * 当选择全部客户-指定标签的时候 会存在都有这个客户但是标签情况不一样 导致没有给这个客户打这个标签的人也会收到群发消息
                 * 但sql如要修改则需要重构 在尽量不影响原有查询的情况下新增查询
                 */
                List<String> tagIds = (List<String>) params.get("tagIds");
                list = getMassWxSenderVos(dto, list, tagIds);
            } else {
                //指定客户发送
                if (conditions.size() > 0) {
                    list = queryExtUserIdByCondition(conditions, dto.getCorpId());
                    List<String> tagIds = buildConditionIdList(conditions, Constants.IN_TAG);
                    list = getMassWxSenderVos(dto, list, tagIds);
                }
            }
        } else if (Constants.CHAT_TYPE_GROUP.equals(dto.getChatType())) {
            Map<String, Object> params = new HashMap<>(6);
            params.put("corpId", dto.getCorpId());
            params.put("userIds", buildConditionIdList(conditions, Constants.STAFF));
            params.put("deptIds", buildConditionIdList(conditions, Constants.DEPT));
            list = baseMapper.queryChatIdsByCorpInfo(params);
        }

        if (list.size() > 0) {
            //如果不是定时发送就直接发送,否则等待定时任务发送
            if (StringUtils.isNotEmpty(dto.getSetTime())) {
                info.setCheckStatus("0");
                //saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, new ArrayList<>(), userName);
            } else {
                info.setCheckStatus("1");
                if (Constants.CHAT_TYPE_SINGLE.equals(dto.getChatType())) {
                    // WxCpMsgTemplateAddResult result = sendMassSingleMessage4Fission(list, dto.getCorpId(), detail);
                    List<String> msgIds = sendMassSingleMessage4Fission(list, dto.getCorpId(), detail);
                    // saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, result.getFailList(), userName);
                    info.setExpectSend(list.stream().map(MassWxSenderVO::getSenderId).distinct().count());
                    // 多条消息id 才发现原来的逻辑连消息id都没存.... -- 2022-03-02
                    info.setMsgId(String.join(",", msgIds));
                    if (StringUtils.isNotEmpty(dto.getTaskFissionId())) {
                        // 代表是裂变的
                        info.setTaskFissionId(dto.getTaskFissionId());
                    }
                } else if (Constants.CHAT_TYPE_GROUP.equals(dto.getChatType())) {
                    info.setExpectSend(list.stream().map(MassWxSenderVO::getUserId).distinct().count());
                    List<WxCpMsgTemplateAddResult> results = sendMassGroupMessage(list, dto.getCorpId(), detail);
                    List<String> failList = new ArrayList<>();
                    if (results.size() > 0) {
                        for (WxCpMsgTemplateAddResult result : results) {
                            if (result.getFailList() != null) {
                                failList.addAll(result.getFailList());
                            }
                        }
                    }
                    int count = saveMessageLogs(dto.getCorpId(), dto.getChatType(), info.getId(), list, failList, userName);
                    info.setExpectSend((long) count);
                }
            }
            updateById(info);
        }
        return "";
    }

    /**
     * 筛选共同客户但标签不同员工发送
     */
    private List<MassWxSenderVO> getMassWxSenderVos(MassMessageDTO dto, List<MassWxSenderVO> list, List<String> tagIds) {
        if (CollectionUtils.isNotEmpty(list) && CollectionUtils.isNotEmpty(tagIds)) {
            List<MassWxSenderVO> resultList = new ArrayList<>();
            for (MassWxSenderVO massWxSenderVo : list) {
                // 如果员工针对客户没有该标签 删除该条发送
                for (String tagId : tagIds) {
                    int count = tbWxExtFollowUserTagService.count(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                            .eq(TbWxExtFollowUserTag::getCorpId, dto.getCorpId())
                            .eq(TbWxExtFollowUserTag::getUserId, massWxSenderVo.getUserId())
                            .eq(TbWxExtFollowUserTag::getExternalUserId, massWxSenderVo.getSenderId())
                            .eq(TbWxExtFollowUserTag::getTagId, tagId)
                    );
                    if (count > 0) {
                        resultList.add(massWxSenderVo);
                    }
                }
            }
            list = resultList;
        }
        return list;
    }

    private List<String> sendMassSingleMessage4Fission(List<MassWxSenderVO> senders, String corpId, TbWxCorpMassMessageDetail detail) throws WxErrorException {
        //Map<String, Object> resultMap = Maps.newHashMap();
        // 群发选择成员其实是以多条发送的 因此需要聚合
        List<String> magIds = Lists.newArrayList();

        WxCpMsgTemplateAddResult result = new WxCpMsgTemplateAddResult();
        // 代自建应用获取
        WxCpMsgTemplate cpMsgTemplate = genWxCpMsgTemplate(detail, Constants.CHAT_TYPE_SINGLE);

        Map<String, List<MassWxSenderVO>> sendMap = senders.stream().collect(Collectors.groupingBy(MassWxSenderVO::getUserId));
        Set<String> userIds = sendMap.keySet();
        for (String userId : userIds) {
            List<MassWxSenderVO> massWxSenderVos = sendMap.get(userId);
            List<String> users = massWxSenderVos.stream().map(MassWxSenderVO::getSenderId).distinct().collect(Collectors.toList());
            cpMsgTemplate.setExternalUserid(users);
            cpMsgTemplate.setSender(userId);
            result = sendWxCpMsgTemplateAddResult(result, cpMsgTemplate,corpId);
            magIds.add(result.getMsgId());
        }
        senders.get(0).setWxMessageId(result.getMsgId());
        return magIds;
    }

}