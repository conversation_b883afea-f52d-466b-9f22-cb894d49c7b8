package com.cenker.scrm.service.impl.statistic;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatisticConstants;
import com.cenker.scrm.enums.DataScopeEnum;
import com.cenker.scrm.mapper.statistic.TbStatisticStaffMapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticStaff;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticStaffListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticStaffSummaryVo;
import com.cenker.scrm.service.statistic.ITbStatisticBaseService;
import com.cenker.scrm.service.statistic.ITbStatisticStaffService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据统计-员工数据 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Service
@Slf4j
@AllArgsConstructor
public class TbStatisticStaffServiceImpl extends ServiceImpl<TbStatisticStaffMapper, TbStatisticStaff> implements ITbStatisticStaffService, ITbStatisticBaseService {

    @Override
    public void saveStatisticData(String statDate) {
        log.info("【数据统计更新数据】开始统计员工数据, statDate={}", statDate);

        this.lambdaUpdate().eq(TbStatisticStaff::getStatisticDate, statDate).remove();

        baseMapper.saveStatisticDateByDay(statDate);

        log.info("【数据统计更新数据】统计员工数据完成, statDate={}", statDate);
    }

    @Override
    public StatisticStaffSummaryVo summary(StatisticSummaryQuery query) {
        StatisticStaffSummaryVo vo = baseMapper.summary(query);
        // 时间范围搜索，部分指标返回null
        if (StringUtils.isNotEmpty(query.getBeginTime()) && StringUtils.isNotEmpty(query.getEndTime())) {
            if (!query.getBeginTime().equals(query.getEndTime())) {
                vo.setStaffTotal(null);
                vo.setChatTimelyRate(null);
                vo.setAverageReplyTime(null);
                vo.setStaffGroupJoinTotal(null);
            }
        }
        return vo;
    }

    @Override
    public List<StatisticGraphVo> graph(StatisticGraphQuery query) {
        List<StatisticStaffSummaryVo> lstVo = baseMapper.graph(query);
        List<StatisticGraphVo> lstGraphVo = new ArrayList<>();

        switch (query.getType()){
            case StatisticConstants.STAFF_STAFFTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getStaffTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.STAFF_DELTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getDelTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.STAFF_ALONECHATTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getAloneChatTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.STAFF_CHATTIMELYRATE:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getChatTimelyRate().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.STAFF_AVERAGEREPLYTIME:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getAverageReplyTime().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.STAFF_STAFFGROUPJOINTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getStaffGroupJoinTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;

            case StatisticConstants.STAFF_STAFFGROUPCHATTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getStaffGroupChatTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            default:
                break;
        }
        return lstGraphVo;
    }

    @Override
    public List<TbStatisticStaff> list(StatisticStaffListQuery query) {
        LambdaQueryWrapper<TbStatisticStaff> queryWrapper = new LambdaQueryWrapper<>();

        if (query.getDataScope() == null || !DataScopeEnum.ALL.getValue().equals(query.getDataScope())) {
            // 用户有权限查看的部门
            if (CollectionUtil.isNotEmpty(query.getPermissionDeptIds())) {
                queryWrapper.in(TbStatisticStaff::getDeptId, query.getPermissionDeptIds());
            }

            // 用户为仅本人权限，只能查看员工为当前登录账号且主部门为归属部门的数据
            if (CollectionUtil.isEmpty(query.getPermissionDeptIds())) {
                queryWrapper.eq(TbStatisticStaff::getUserid, query.getWxUserId())
                        .eq(TbStatisticStaff::getDeptId, query.getDeptId());
            }
        }

        queryWrapper.ge(TbStatisticStaff::getStatisticDate, query.getBeginTime())
                .le(TbStatisticStaff::getStatisticDate, query.getEndTime())
                .like(StringUtils.isNotBlank(query.getUserName()),TbStatisticStaff::getUserName, query.getUserName())
                .eq(StringUtils.isNotBlank(query.getUserid()), TbStatisticStaff::getUserid, query.getUserid())
                .orderByDesc(TbStatisticStaff::getStatisticDate);
        List<TbStatisticStaff> list = this.list(queryWrapper);

        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        list.forEach(item -> {
            if (Objects.isNull(item.getTotalConversationNum()) || 0 == item.getTotalConversationNum()) {
                item.setAverageReplyTime(0);
                item.setChatTimelyRate(BigDecimal.ZERO);
            } else {
                Integer totalConversationDuration = Optional.ofNullable(item.getTotalConversationDuration()).orElse(0);
                Integer replyTimelyNum = Optional.ofNullable(item.getReplyTimelyNum()).orElse(0);
                replyTimelyNum*=100;
                int averageReplyTime = NumberUtil.ceilDiv(totalConversationDuration, item.getTotalConversationNum());
                BigDecimal chatTimelyRate = NumberUtil.div(replyTimelyNum, item.getTotalConversationNum(), 2);
                item.setAverageReplyTime(averageReplyTime);
                item.setChatTimelyRate(chatTimelyRate);
            }
        });

        return list;
    }

    @Override
    public void synData(Date statDate) {
        this.saveStatisticData(DateUtil.formatDate(statDate));
    }
}
