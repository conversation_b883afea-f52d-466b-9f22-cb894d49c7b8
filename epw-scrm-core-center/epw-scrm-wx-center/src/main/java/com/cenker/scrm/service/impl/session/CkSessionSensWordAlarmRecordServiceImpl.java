package com.cenker.scrm.service.impl.session;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.session.CkSessionSensWordAlarmRecordMapper;
import com.cenker.scrm.pojo.dto.session.QryWordAlarmDto;
import com.cenker.scrm.pojo.entity.session.CkSessionSensWordAlarmRecord;
import com.cenker.scrm.service.session.ICkSessionSensWordAlarmRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 热词审计人关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Service
public class CkSessionSensWordAlarmRecordServiceImpl extends ServiceImpl<CkSessionSensWordAlarmRecordMapper, CkSessionSensWordAlarmRecord> implements ICkSessionSensWordAlarmRecordService
{
    @Override
    public List<CkSessionSensWordAlarmRecord> selectCkSessionSensWordAlarmRecordList(QryWordAlarmDto qryWordAlarmDto) {
        return baseMapper.selectCkSessionSensWordAlarmRecordList(qryWordAlarmDto);
    }

    @Override
    public int saveSenWordAlarmRecord(String beginTime, String endTime) {
        return baseMapper.saveSenWordAlarmRecord(beginTime, endTime);
    }
}
