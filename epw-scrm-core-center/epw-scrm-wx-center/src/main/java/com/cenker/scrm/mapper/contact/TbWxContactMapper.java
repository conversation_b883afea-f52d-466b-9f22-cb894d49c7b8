package com.cenker.scrm.mapper.contact;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxContact;
import com.cenker.scrm.pojo.vo.contact.*;
import com.cenker.scrm.pojo.vo.external.CustomerFollowUserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工活码Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-25
 */
public interface TbWxContactMapper extends BaseMapper<TbWxContact> {
    /**
     * 查询员工活码
     *
     * @param configId 员工活码ID
     * @return 员工活码
     */
    TbWxContact selectTbWxContactByConfigId(String configId);
    TbWxContact selectTbWxContactByState(String state);


    /**
     * 查询员工活码列表
     *
     * @param contactVo 员工活码
     * @return 员工活码集合
     */
    List<ContactVO> selectTbWxContactList(ContactVO contactVo);

    /**
     * 新增员工活码
     *
     * @param tbWxContact 员工活码
     * @return 结果
     */
    int insertTbWxContact(TbWxContact tbWxContact);

    /**
     * 批量删除员工活码
     *
     * @param configIds 需要删除的数据ID
     * @return 结果
     */
    int deleteTbWxContactByConfigIds(String[] configIds);

    /**
     * 新增员工活码
     *
     * @param tbWxContact 员工活码
     * @return 结果
     */
    int updateTbWxContact(TbWxContact tbWxContact);

    /**
     * 查询工作台员工活码列表
     *
     * @param workContactVo
     * @return
     */
    List<WorkContactVO> selectTbWxContactListByWork(WorkContactVO workContactVo);

    String selectTbWxContactRemarkByState(String state);

    /**
     * 查询活码添加的客户
     */
    List<CustomerFollowUserVO> getAddCustomerInfo(TbWxContact tbWxContact);

    /**
     * 活码详情
     */
    ContactVO getInfo(ContactVO contactVo);

    /**
     * 获取生成工卡对象
     */
    TbWxContact getGenerateContact(ContactVO contactVo);

    /**
     * 数据统计
     */
    ContactStatisticsVO getDataStatistics(ContactVO contactVo);

    /**
     * 活码添加客户列表
     */
    List<ContactCustomerStatisticsVO> getAddCustomerInfo4Web(ContactVO contactVo);

    /**
     * 查询渠道活码添加累计客户总数
     */
    List<ContactStatisticsDailyVO> getBehaviorData4TotalAddCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("id") String id);

    /**
     * 查询渠道活码添加新增/流失客户总数
     */
    List<ContactStatisticsDailyVO> getBehaviorData4AddAndDelCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("id") String id, @Param("type") Integer type);
}
