package com.cenker.scrm.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;

import java.util.List;

/**
 * 素材信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface TbWxMaterialMapper extends BaseMapper<TbWxMaterial> {
    /**
     * 查询素材信息
     *
     * @param id 素材信息ID
     * @return 素材信息
     */
    TbWxMaterial selectTbWxMaterialById(Long id);

    /**
     * 查询素材信息列表
     *
     * @param param 素材信息
     * @return 素材信息集合
     */
    List<TbWxMaterial> selectTbWxMaterialList(MaterialListDto param);

    /**
     * 新增素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    int insertTbWxMaterial(TbWxMaterial tbWxMaterial);

    /**
     * 修改素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    int updateTbWxMaterial(TbWxMaterial tbWxMaterial);

    /**
     * 删除素材信息
     *
     * @param id 素材信息ID
     * @return 结果
     */
    int deleteTbWxMaterialById(Long id);

    /**
     * 批量删除素材信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTbWxMaterialByIds(Long[] ids);

    List<TbWxMaterial> selectTbWxMaterialTopList(TbWxMaterial tbWxMaterial);
}
