package com.cenker.scrm.controller.content.material;

import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.content.material.BuWelcomeContactTemplateDTO;
import com.cenker.scrm.pojo.entity.content.material.BuWelcomeContactTemplate;
import com.cenker.scrm.pojo.request.content.material.BuWelcomeContactTemplateRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.content.material.BuWelcomeContactTemplateVO;
import com.cenker.scrm.pojo.vo.content.material.BuWelcomeTemplateSenceVO;
import com.cenker.scrm.service.content.material.IBuWelcomeContactTemplateService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simpleframework.xml.core.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 欢迎语模板Controller
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/bu/welcome/template")
public class BuWelcomeContactTemplateController extends BaseController {

    private final IBuWelcomeContactTemplateService buWelcomeContactTemplateService;

    private final TokenParseUtil tokenService;

    /**
     * 查询欢迎语模板列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody BuWelcomeContactTemplateRequest queryRequest) {
        log.info("start - 查询欢迎语模板列表, 查询条件：{}", JSONUtil.toJsonStr(queryRequest));
        startPage();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, queryRequest);

        List<BuWelcomeContactTemplateVO> list = buWelcomeContactTemplateService.selectBuWelcomeContactTemplateList(queryRequest);
        log.info("end - 查询欢迎语模板列表，查询结果-数量：{}", list.size());
        return getDataTable(list);
    }

    /**
     * 获取欢迎语模板详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        log.info("start - 获取欢迎语模板详细信息，id：{}", id);
        BuWelcomeContactTemplateRequest queryRequest = new BuWelcomeContactTemplateRequest();
        queryRequest.setId(String.valueOf(id));

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, queryRequest);

        BuWelcomeContactTemplateVO buWelcomeContactTemplateVO = buWelcomeContactTemplateService.selectBuWelcomeContactTemplateById(queryRequest);
        log.info("end - 获取欢迎语模板详细信息，查询结果：{}", ObjectUtil.isNotEmpty(buWelcomeContactTemplateVO));
        return AjaxResult.success(buWelcomeContactTemplateVO);
    }

    /**
     * 新增欢迎语模板
     */
    @PostMapping
    public AjaxResult add(@RequestBody @Validate BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        log.info("start - 新增欢迎语模板");
        int cnt = buWelcomeContactTemplateService.count(new LambdaQueryWrapper<BuWelcomeContactTemplate>()
                .eq(BuWelcomeContactTemplate::getName, welcomeContactTemplateDTO.getName())
                .eq(BuWelcomeContactTemplate::getDelFlag, CommonConstants.NOT_DELETED));
        if (cnt > 0) {
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "该标题已存在，请重新输入");
        }
        if (StrUtil.isBlank(welcomeContactTemplateDTO.getContent())){
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "欢迎语内容不能为空");
        }
        if (CollectionUtil.isNotEmpty(welcomeContactTemplateDTO.getAttachments()) && welcomeContactTemplateDTO.getAttachments().size() > 9) {
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "最多只能添加9个附件");
        }

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, welcomeContactTemplateDTO);
        welcomeContactTemplateDTO.setCreateBy(loginUser.getUser().getUserId());
        int result = buWelcomeContactTemplateService.insertBuWelcomeContactTemplate(welcomeContactTemplateDTO);
        log.info("end - 新增欢迎语模板，操作结果：{}", result > 0);
        return toAjax(result);
    }

    /**
     * 修改欢迎语模板
     */
    @PutMapping
    public AjaxResult edit(@RequestBody @Validate BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        log.info("start - 修改欢迎语模板，id: {}", welcomeContactTemplateDTO.getId());
        int cnt = buWelcomeContactTemplateService.count(new LambdaQueryWrapper<BuWelcomeContactTemplate>()
                .eq(BuWelcomeContactTemplate::getName, welcomeContactTemplateDTO.getName())
                .ne(BuWelcomeContactTemplate::getId, welcomeContactTemplateDTO.getId())
                .eq(BuWelcomeContactTemplate::getDelFlag, CommonConstants.NOT_DELETED));
        if (cnt > 0) {
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "该标题已存在，请重新输入");
        }
        if (StrUtil.isBlank(welcomeContactTemplateDTO.getContent())){
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "欢迎语内容不能为空");
        }
        if (CollectionUtil.isNotEmpty(welcomeContactTemplateDTO.getAttachments()) && welcomeContactTemplateDTO.getAttachments().size() > 9) {
            return AjaxResult.error(HttpStatus.BAD_REQUEST, "最多只能添加9个附件");
        }

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, welcomeContactTemplateDTO);
        welcomeContactTemplateDTO.setUpdateBy(loginUser.getUser().getUserId());
        int result = buWelcomeContactTemplateService.updateBuWelcomeContactTemplate(welcomeContactTemplateDTO);
        log.info("end - 修改欢迎语模板，操作结果：{}", result > 0);
        return toAjax(result);
    }

    /**
     * 删除欢迎语模板
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        log.info("start - 删除欢迎语模板，ids: {}", ids);
        int result = buWelcomeContactTemplateService.deleteBuWelcomeContactTemplateByIds(ids);
        log.info("end - 删除欢迎语模板，操作结果：{}", result > 0);
        return toAjax(result);
    }

    /**
     * 修改欢迎语模板的状态
     */
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        log.info("start - 修改欢迎语模板状态，id: {}, status:{}", welcomeContactTemplateDTO.getId(), welcomeContactTemplateDTO.getStatus());
        // 补充停用状态的校验是否被渠道活码、获客链接、个人名片引用
        if (StatusConstants.CLOSE_FLAG.equals(welcomeContactTemplateDTO.getStatus())) {
           boolean isUsed = buWelcomeContactTemplateService.checkUsedCount(welcomeContactTemplateDTO.getId());
           if(isUsed){
               log.info("end - 修改欢迎语模板状态，操作结果：该欢迎语已被渠道活码、获客链接、个人名片引用，请勿停用");
               return AjaxResult.error(HttpStatus.BAD_REQUEST, "当前话术正在被系统中其他模块引用，无法禁用。请前往“详情”查看使用场景，并替换为其他话术后再进行禁用操作。");
           }
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        welcomeContactTemplateDTO.setUpdateBy(loginUser.getUser().getUserId());
        int result = buWelcomeContactTemplateService.updateWelcomeContactTemplateStatus(welcomeContactTemplateDTO);
        log.info("end - 修改欢迎语模板状态，操作结果：{}", result > 0);
        return toAjax(result);
    }

    /**
     * 查询欢迎语模板使用场景
     */
    @GetMapping("/sceneList")
    public TableDataInfo getUsedSceneList(@RequestBody BuWelcomeContactTemplateRequest queryRequest) {
        log.info("start - 获取已使用的场景列表, 查询条件：{}", JSONUtil.toJsonStr(queryRequest));
        startPage();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, queryRequest);
        List<BuWelcomeTemplateSenceVO> list = buWelcomeContactTemplateService.selectUsedSceneList(queryRequest);
        log.info("end - 获取已使用的场景列表，查询结果-数量：{}", list.size());
        return getDataTable(list);
    }

    /**
     * 查询启用状态欢迎语模板列表
     */
    @GetMapping("/enableList")
    public TableDataInfo enableList(@RequestBody BuWelcomeContactTemplateRequest queryRequest) {
        log.info("start - 查询启用状态欢迎语模板列表, 查询条件：{}", JSONUtil.toJsonStr(queryRequest));
        startPage();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, queryRequest);
        queryRequest.setStatus(StatusConstants.OPEN_FLAG);
        List<BuWelcomeContactTemplateVO> list = buWelcomeContactTemplateService.selectBuWelcomeContactTemplateList(queryRequest);
        log.info("end - 查询启用状态欢迎语模板列表，查询结果-数量：{}", list.size());
        return getDataTable(list);
    }
}
