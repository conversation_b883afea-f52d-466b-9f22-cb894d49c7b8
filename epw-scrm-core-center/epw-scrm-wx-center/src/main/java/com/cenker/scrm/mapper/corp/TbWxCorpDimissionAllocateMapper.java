package com.cenker.scrm.mapper.corp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.WxLeaveUserDto;
import com.cenker.scrm.pojo.entity.wechat.AllocateDetailInfoVO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpDimissionAllocate;
import com.cenker.scrm.pojo.vo.contact.WxLeaveUserVO;

import java.util.List;

/**
 * 企业成员离职分配记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface TbWxCorpDimissionAllocateMapper extends BaseMapper<TbWxCorpDimissionAllocate> {
    /**
     * 查看已离职人员已分配外部群和客户
     *
     * @param dto
     * @return
     */
    List<WxLeaveUserVO> leaveAllocatedUserList(WxLeaveUserDto dto);

    /**
     * 查看分配详细
     *
     * @param dto
     * @return
     */
    List<AllocateDetailInfoVO> queryAllocateDetailInfo(WxLeaveUserDto dto);
}
