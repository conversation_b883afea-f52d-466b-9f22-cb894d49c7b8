package com.cenker.scrm.pojo.dto.message;

import com.cenker.scrm.pojo.dto.condition.MessageConditionDTO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import me.chanjar.weixin.cp.bean.external.WxCpMsgTemplate;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/16
 * @Description 群发消息传递类
 */
@Data
public class MassMessageChainDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 群发任务名
     */
    private String massName;
    /**
     * 群发消息场景 1 企业群发 2 个人群发 3 sop企业群发
     */
    private Integer massScene;
    /**
     * 是否定时任务 0 立即发送 1 定时发送
     */
    private Integer timedTask;
    /**
     * 消息范围 0 全部客户/群  1 指定客户/群主 重构定义为全部选择为不筛选 2023-05-23
     */
    private Integer pushRange;
    /**
     * 状态: PENDING_EXEC 未开始  EXECUTING 执行中
     * CANCELED 已取消 EXEC_EXCEPTION 创建失败
     * FINISHED 已完成 PENDING_APPROVAL 待审核
     * REJECTED 已退回 REVOKED 已撤回
     */
    private String checkStatus;
    private String errorMsg;
    /**
     * 预计发送客户数
     */
    private Integer expectSendCnt;
    /**
     * 是否员工去重 是-单条走企微去重 否-多条指定员工
     */
    private Boolean userDistinct;
    /**
     * 发送时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settingTime;
    /**
     * 群发任务的类型，默认为single，表示发送给客户，group表示发送给客户群
     */
    private String chatType;
    /**
     * 企业主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long corpConfigId;
    /**
     * 创建来源1 web 2 侧边栏 3工作台
     */
    private Integer since;
    private String remark;
    private Integer delFlag;
    /**
     * 筛选发送条件json
     */
    private MessageConditionDTO messageCondition;
    /**
     * 群发消息文本
     */
    private String massContent;
    /**
     * 群发消息附件json
     */
    private List<WelcomeAttachmentVo> attachments;
    /**
     * 场景对应的id引用
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sceneId;
    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;

    private String corpId;
    /**
     * 群发消息内容
     */
    private List<MassMessageChainDetailDTO> details;

    /**
     * 企微发送模板
     */
    private WxCpMsgTemplate wxCpMsgTemplate;

    /**
     * 原始json
     */
    private String massJson;


    /**
     * 重构新增 标签、客户阶段、添加时间是否筛选 0 否 1 是
     */
    private Integer tagSelectType;
    private Integer stageSelectType;
    private Integer addSelectType;
    private Integer userSelectType;

    /**
     *  是否允许成员在待发送客户列表中重新进行选择，默认为false
     */
    private Boolean allowSelect=false;

    /**
     * 任务停止时间 单位小时
     */
    private Integer stopTaskHour;

    /**
     * 1v1群聊优化前端筛选用户传入的JSON串
     */
    private String condition;

    /**
     * 部门id
     */
    private Integer deptId;
    /**
     * 是否开启审核
     */
    private boolean enableApproval;
}
