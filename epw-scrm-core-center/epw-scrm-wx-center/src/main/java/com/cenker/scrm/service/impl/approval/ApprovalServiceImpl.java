package com.cenker.scrm.service.impl.approval;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cenker.scrm.biz.ApprovalMsgNotify;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.client.system.SysRoleFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.ApprovalWarningMsgDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.corp.ITbWxCorpConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 审核服务实现类
 */
@Slf4j
@AllArgsConstructor
@Service
public class ApprovalServiceImpl implements IApprovalService {


    private final ITbWxCorpConfigService tbWxCorpConfigService;

    private final SysRoleFeign sysRoleFeign;
    private final ApprovalMsgNotify approvalMsgNotify;
    private final MqSendMessageManager mqSendMessageManager;

    @Override
    public TbWxCorpConfig getTbWxCorpConfig(String corpId) {
        TbWxCorpConfig corpConfig = new TbWxCorpConfig();
        corpConfig.setCorpId(corpId);
        List<TbWxCorpConfig> list = tbWxCorpConfigService.selectTbWxCorpConfigList(corpConfig);
        TbWxCorpConfig config = null;
        if (CollectionUtils.isNotEmpty(list)) {
            config = list.get(0);
        }
        return config;
    }

    @Override
    public Result<List<String>> getApprovalUser(String type, String createBy) {
        return sysRoleFeign.getApprovalUser(type, createBy);
    }

    @Override
    public Result<Boolean> isApprovalUser(String type) {
        return sysRoleFeign.isApprovalUser(type);
    }

    @Override
    public String getApprovalKey(String id, String type) {
        return CacheKeyConstants.APPROVAL_KEY + type + ":" + id;
    }

    @Override
    public void sendPendingApprovalMsg(String sopName, String sopId, ApprovalTypeEnum approvalTypeEnum, boolean enableApproval, String status, String createBy) {
        sendPendingApprovalMsg(sopName, sopId, approvalTypeEnum, enableApproval, status, createBy, true, null);
    }

    @Override
    public void sendPendingApprovalMsg(String sopName, String sopId, ApprovalTypeEnum approvalTypeEnum, boolean enableApproval, String status, String createBy, boolean isTask, String businessJson) {
        // 新建或编辑时，发送通知消息
        if (enableApproval && Objects.equals(status, ApprovalStatusEnum.PENDING_APPROVAL.getStatus())) {
            log.info("新建或编辑{}时，发送消息：{}",approvalTypeEnum.getDesc(), sopName);
            String content = "您有新的" + approvalTypeEnum.getDesc() + (isTask? "任务" : "") +"【"+ sopName + "】待审核，请及时处理。";
            String msgContent = content;
            Result<List<String>> approvalUserResult = getApprovalUser(approvalTypeEnum.getType(), createBy);
            String approvalUser = "";
            List<String> approvalUserList = approvalUserResult.getData();
            if (approvalUserResult.isSuccess() && CollectionUtil.isNotEmpty(approvalUserList)) {
                approvalUser = approvalUserList.stream().collect(Collectors.joining(","));
            } else {
                log.info("新建或编辑{}时，审批人列表为空，不发送消息");
                return;
            }
            approvalMsgNotify.handlerMsgNotify("审核待办", content, msgContent, approvalUser, CorpInfoProperties.getCorpId(), approvalTypeEnum.getType(), sopId,"approval", businessJson);
        }
    }

    @Override
    public <T> void sendApprovalMsg(T info, String type, boolean success,
                                    Function<T, String> nameGetter,
                                    Function<T, String> creatorGetter,
                                    Function<T, String> idGetter) {
        sendApprovalMsg(info, type, success, nameGetter, creatorGetter, idGetter, true, null);
    }
    @Override
    public <T> void sendApprovalMsg(T info, String type, boolean success,
                                    Function<T, String> nameGetter,
                                    Function<T, String> creatorGetter,
                                    Function<T, String> idGetter,
                                    boolean isTask, String businessJson) {
        // 统一处理逻辑
        ApprovalTypeEnum approvalType = ApprovalTypeEnum.getType(type);
        String taskName = nameGetter.apply(info);
        String createBy = creatorGetter.apply(info);
        String id = idGetter.apply(info);

        log.info("审核{}时，发送消息：{}", approvalType.getDesc(), taskName);

        String title = success ? "审核通过" : "审核驳回";
        String statusDesc = success ? "已通过审核。" : "未通过审核，请修改后重新提交。";
        String content = StrUtil.format("您的{}"+(isTask? "任务" : "") +"【{}】{}",
                approvalType.getDesc(), taskName, statusDesc);

        approvalMsgNotify.handlerMsgNotify(
                title,
                content,
                content, // 保持 msgContent 与 content 一致
                createBy,
                CorpInfoProperties.getCorpId(),
                approvalType.getType(),
                id,
                null,
                businessJson);
    }

    @Override
    public <T> void sendAutoRejectedApprovalMsg(T info, String type, boolean success,
                                                Function<T, String> nameGetter,
                                                Function<T, String> creatorGetter,
                                                Function<T, String> idGetter) {
        // 统一处理逻辑
        ApprovalTypeEnum approvalType = ApprovalTypeEnum.getType(type);
        String taskName = nameGetter.apply(info);
        String createBy = creatorGetter.apply(info);
        String id = idGetter.apply(info);
        // 【审核超时】任务{任务名称}未在预设发送时间前通过审核，已自动退回。
        log.info("自动退回{}，发送消息：{}", approvalType.getDesc(), taskName);

        String title = "审核超时" ;
        String statusDesc = "未在预设发送时间前通过审核，已自动退回。";
        String content = StrUtil.format("任务【{}】{}",
                taskName,statusDesc);

        approvalMsgNotify.handlerMsgNotify(
                title,
                content,
                content, // 保持 msgContent 与 content 一致
                createBy,
                CorpInfoProperties.getCorpId(),
                approvalType.getType(),
                id,
                null,
                null);
    }

    @Override
    public <T> void sendWarnApprovalMsg(T info, String type, boolean timeCorn, boolean isCreator, Function<T, String> nameGetter, Function<T, String> creatorGetter, Function<T, String> idGetter) {
        // 统一处理逻辑
        ApprovalTypeEnum approvalType = ApprovalTypeEnum.getType(type);
        String taskName = nameGetter.apply(info);
        String createBy = creatorGetter.apply(info);
        String id = idGetter.apply(info);
        // 【审核预警】任务{任务名称}长时间未审核。
        // 【审核预警】任务{任务名称}剩余30分钟。
        log.info("审核预警{}，发送消息：{}", approvalType.getDesc(), taskName);

        String title = "审核预警" ;
        String statusDesc = !timeCorn ? "长时间未审核。" : "剩余30分钟。";
        String content = StrUtil.format("{}任务【{}】{}",approvalType.getDesc(),
                taskName,statusDesc);
        String noticeUser = createBy;
        if (!isCreator) {
            // 非通知创建人，通知审批人
            Result<List<String>> approvalUserResult = getApprovalUser(type, createBy);
            List<String> approvalUserList = approvalUserResult.getData();
            if (approvalUserResult.isSuccess() && CollectionUtil.isNotEmpty(approvalUserList)) {
                noticeUser = approvalUserList.stream().collect(Collectors.joining(","));
            } else {
                log.info("任务{}审核预警时，审批人列表为空，不发送消息");
                return;
            }
        }
        approvalMsgNotify.handlerMsgNotify(
                title,
                content,
                content, // 保持 msgContent 与 content 一致
                noticeUser,
                CorpInfoProperties.getCorpId(),
                approvalType.getType(),
                id,
                isCreator ? null : "approval",
                null);
    }

    @Override
    public Result<Object> validateApproval(LoginUser loginUser, String type, String taskName, String createBy, String checkStatus) {
        Result<Boolean> result = this.isApprovalUser(type);
        if (!result.isSuccess() || result.getData() == null || result.getData() == false) {
            return Result.error(500, "当前用户没有{" + taskName + "}的审核权限！");
        }
        if (Objects.equals(loginUser.getUser().getUserId(), createBy)) {
            return Result.error(500, "用户不能对自己创建的内容进行审核！");
        }
        if (!Objects.equals(checkStatus, ApprovalStatusEnum.PENDING_APPROVAL.getStatus())) {
            return Result.error(500, "任务状态已变更！");
        }
        return null;
    }

    @Override
    public <T> void sendAlarmDelayMsg(T task, ApprovalTypeEnum typeEnum, Function<T, Date> settingTimeGetter, Function<T, Integer> timeTaskGetter) {
        if (task == null) {
            return;
        }
        Date settingTime = settingTimeGetter.apply(task);
        Integer timeTask = timeTaskGetter.apply(task);
        if (settingTime == null || timeTask == null) {
            return;
        }
        ApprovalWarningMsgDTO dto = new ApprovalWarningMsgDTO();
        dto.setApprovalObj(task);
        dto.setApprovalType(typeEnum);
        int delayTime = 0;
        if (StatusConstants.TIMED_TASK_SEND_ONCE == timeTask) {
            /**
             *  如果“待审核”任务超过创建时间 1 小时（发送时间为立即发送），
             *  系统将会再次向审核员发送消息通知，同时也会通知创建者。
             */
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(settingTime);
            calendar.add(Calendar.HOUR_OF_DAY, 1);
            delayTime = (int) ((calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        } else {
            /**
             *  如果定时任务在距离外发时间 30 分钟时仍处于“待审核”的状态，
             *  那么将会同时向审核员和创建者发送消息通知以作提醒。
             */
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(settingTime);
            calendar.add(Calendar.MINUTE, -30);
            delayTime = (int) ((calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        }
        if (delayTime < 0) {
            return;
        }
        dto.setDelayTime(delayTime);
        mqSendMessageManager.sendDelayMsg(dto);
    }
}
