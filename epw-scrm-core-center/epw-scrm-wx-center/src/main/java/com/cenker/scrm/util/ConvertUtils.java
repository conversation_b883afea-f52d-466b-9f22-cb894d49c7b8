package com.cenker.scrm.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.pojo.entity.wechat.TbWxDepartment;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUserExtattr;
import com.cenker.scrm.pojo.entity.wechat.TbWxUserExternalProfile;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;

import java.util.ArrayList;
import java.util.List;

/**
 * 对象转换工具
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Slf4j
public class ConvertUtils {
    
    /**
     * 组织架构对象转换
     * @param wxCpDepartList        SDK组织架构
     * @return
     */
    public static List<TbWxDepartment> wxDepartmentConvert(String corpId, List<WxCpDepart> wxCpDepartList){
        log.info("开始转换组织架构对象！");
        List<TbWxDepartment> tbWxDepartmentList = null;
        // 判断是否存在需要转换的数据
        if(null != wxCpDepartList && wxCpDepartList.size() > 0){
            tbWxDepartmentList = new ArrayList<>();
            for(WxCpDepart wxCpDepart : wxCpDepartList){
                TbWxDepartment tbWxDepartment = new TbWxDepartment();
                tbWxDepartment.setCorpId(corpId);
                tbWxDepartment.setId(wxCpDepart.getId()+"");
                tbWxDepartment.setName(wxCpDepart.getName());
                tbWxDepartment.setParentId(wxCpDepart.getParentId()+"");
                tbWxDepartmentList.add(tbWxDepartment);
            }
        }
        log.info("转换组织架构对象结束！");
        return tbWxDepartmentList;
    }

    public static List<TbWxUser> wxUserConvert(String corpId, List<WxCpUser> wxCpUserList){
        return wxUserConvert(corpId, wxCpUserList, DefaultConstants.STAFF_DEFAULT_AVATAR);
    }

    /**
     * 组织成员对象转换
     * @param corpId        企业Id
     * @param wxCpUserList  SDF成员
     * @return
     */
    public static List<TbWxUser> wxUserConvert(String corpId, List<WxCpUser> wxCpUserList, String defaultAvatar){
        log.info("开始转换组织成员对象！");
        List<TbWxUser> tbWxUserList = null;
        // 判断是否存在需要转换的数据
        if(CollectionUtil.isNotEmpty(wxCpUserList)){
            tbWxUserList = new ArrayList<>();
            for(WxCpUser wxCpUser : wxCpUserList){
                TbWxUser tbWxUser = new TbWxUser();
                tbWxUser.setUserid(wxCpUser.getUserId());
                tbWxUser.setName(wxCpUser.getName());
                tbWxUser.setDepartment(JSONObject.toJSONString(wxCpUser.getDepartIds()));
                tbWxUser.setOrder(JSONObject.toJSONString(wxCpUser.getOrders()));
                tbWxUser.setPosition(wxCpUser.getPosition());
                tbWxUser.setIsLeaderInDept(JSONObject.toJSONString(wxCpUser.getIsLeaderInDept()));
                tbWxUser.setThumbAvatar(wxCpUser.getThumbAvatar());
                tbWxUser.setTelephone(wxCpUser.getTelephone());
                tbWxUser.setAlias(wxCpUser.getAlias());
                tbWxUser.setStatus(wxCpUser.getStatus());
                tbWxUser.setExternalPosition(wxCpUser.getExternalPosition());
                tbWxUser.setOpenUserid(wxCpUser.getOpenUserId());
                tbWxUser.setMainDepartment(wxCpUser.getMainDepartment());
                tbWxUser.setCorpId(corpId);

                /**
                 * 从2022年6月20号20点开始，除通讯录同步以外的基础应用（如客户联系、微信客服、会话存档、日程等），以及新创建的自建应用与代开发应用，调用该接口时，不再返回以下字段：头像、性别、手机、邮箱、企业邮箱、员工个人二维码、地址，应用需要通过oauth2手工授权的方式获取管理员与员工本人授权的字段。
                 */
                /*tbWxUser.setMobile(wxCpUser.getMobile());
                tbWxUser.setGender(wxCpUser.getGender().getCode());
                tbWxUser.setEmail(wxCpUser.getEmail());
                tbWxUser.setAvatar(StringUtils.isNotEmpty(wxCpUser.getAvatar()) ? wxCpUser.getAvatar() : defaultAvatar);
                tbWxUser.setQrCode(wxCpUser.getQrCode());
                tbWxUser.setAddress(wxCpUser.getAddress());*/

                List<TbWxUserExtattr> externalAttrList = new ArrayList<>();
                for(WxCpUser.ExternalAttribute externalAttribute : wxCpUser.getExternalAttrs()){
                    TbWxUserExtattr tbWxUserExtattr = new TbWxUserExtattr();
                    tbWxUserExtattr.setCorpId(corpId);
                    tbWxUserExtattr.setUserId(wxCpUser.getUserId());
                    tbWxUserExtattr.setOpenUserid(wxCpUser.getOpenUserId());
                    tbWxUserExtattr.setType(externalAttribute.getType());
                    tbWxUserExtattr.setName(externalAttribute.getName());
                    tbWxUserExtattr.setTextValue(externalAttribute.getValue());
                    tbWxUserExtattr.setWebUrl(externalAttribute.getUrl());
                    tbWxUserExtattr.setWebTitle(externalAttribute.getTitle());
                    externalAttrList.add(tbWxUserExtattr);
                }
                tbWxUser.setTbWxUserExtattrList(externalAttrList);

                List<TbWxUserExternalProfile> externalProfileList = new ArrayList<>();
                for(WxCpUser.Attr attr : wxCpUser.getExtAttrs()){
                    TbWxUserExternalProfile tbWxUserExternalProfile = new TbWxUserExternalProfile();
                    tbWxUserExternalProfile.setCorpId(corpId);
                    tbWxUserExternalProfile.setUserId(wxCpUser.getUserId());
                    tbWxUserExternalProfile.setOpenUserid(wxCpUser.getOpenUserId());
                    tbWxUserExternalProfile.setType(attr.getType());
                    tbWxUserExternalProfile.setName(attr.getName());
                    tbWxUserExternalProfile.setTextValue(attr.getTextValue());
                    tbWxUserExternalProfile.setWebUrl(attr.getWebUrl());
                    tbWxUserExternalProfile.setWebTitle(attr.getWebTitle());
                    externalProfileList.add(tbWxUserExternalProfile);
                }
                tbWxUser.setTbWxUserExternalProfileList(externalProfileList);
                tbWxUserList.add(tbWxUser);
            }
        }
        log.info("转换组织成员对象结束！");
        return tbWxUserList;
    }
}
