package com.cenker.scrm.client.system;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.pojo.vo.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ServiceNameConstants.SYSTEM_CENTER_SERVICE, path = "/system/role")
public interface SysRoleFeign {

    /**
     * 当前用户是否是审批员
     */
    @RequestMapping("/isApprovalUser")
    Result<Boolean> isApprovalUser(@RequestParam("type") String type);

    /**
     * 获取审批员列表
     * @param type
     * @return
     */
    @GetMapping("/getApprovalUser")
    Result<List<String>> getApprovalUser(@RequestParam("type") String type, @RequestParam("userId") String userId);
}
