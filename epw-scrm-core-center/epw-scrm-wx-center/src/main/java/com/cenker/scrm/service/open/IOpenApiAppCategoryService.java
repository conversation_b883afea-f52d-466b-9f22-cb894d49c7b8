package com.cenker.scrm.service.open;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.TreeSelect;
import com.cenker.scrm.pojo.dto.open.AppCategoryDTO;
import com.cenker.scrm.pojo.entity.open.store.OpenApiAppCategory;
import com.cenker.scrm.pojo.vo.open.store.AppCategoryVO;

import java.util.List;

public interface IOpenApiAppCategoryService extends IService<OpenApiAppCategory> {


    List<AppCategoryVO> listCategory(AppCategoryDTO appCategoryDTO);

    List<TreeSelect> buildCategoryTreeSelect(List<AppCategoryVO> list);
}
