package com.cenker.scrm.service.impl.subscr;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.mapper.subscr.BuSubscriptionMapper;
import com.cenker.scrm.mapper.subscr.BuSubscriptionMenuMapper;
import com.cenker.scrm.mapper.subscr.BuSubscriptionMenuSubMapper;
import com.cenker.scrm.mapper.subscr.BuSubscriptionSectionMapper;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.entity.subscr.BuSubscription;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenu;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenuSub;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionSection;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.pojo.vo.subscr.*;
import com.cenker.scrm.service.subscr.IBuSubscriptionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订阅服务实现类
 * 提供对订阅的CRUD操作及自定义业务方法
 */
@Slf4j
@AllArgsConstructor
@Service
public class BuSubscriptionServiceImpl extends ServiceImpl<BuSubscriptionMapper, BuSubscription> implements IBuSubscriptionService {

    private final BuSubscriptionMenuMapper menuMapper;

    private final BuSubscriptionMenuSubMapper menuSubMapper;

    private final BuSubscriptionSectionMapper sectionMapper;

    private final MqSendMessageManager mqSendMessageManager;

    @Override
    public Map<String, List<BuSubscriptionVO>> getSubscriptionList(String externalUserId) {
        // 查询客户的订阅信息
        List<BuSubscriptionVO> subscriptionList = getSubscriptionVOS(externalUserId);
        if (CollectionUtils.isEmpty(subscriptionList)) {
            return new HashMap<>();
        }

        // 按一级菜单分组
        return subscriptionList.stream()
                .filter(vo -> StrUtil.isNotEmpty(vo.getFirstSubMenuId()))
                .collect(Collectors.groupingBy(vo -> StrUtil.isNotEmpty(vo.getFirstSubMenuName()) ? vo.getFirstSubMenuName() : ""));
    }

    @Override
    public BuSubscriptionData getSubscrMenus(String externalUserId) {
        BuSubscriptionData data = new BuSubscriptionData();

        // 查询当前发布的订阅菜单
        BuSubscriptionMenu menu = menuMapper.selectOne(
                new LambdaQueryWrapper<BuSubscriptionMenu>()
                        .eq(BuSubscriptionMenu::getReleaseStatus, 1) // 已发布
                        .eq(BuSubscriptionMenu::getDelFlag, 0) // 未删除
                        .orderByDesc(BuSubscriptionMenu::getCreateTime)
                        .last("LIMIT 1")
        );

        if (menu == null) {
            return data;
        }

        // 构建菜单详情
        BuSubscriptionMenuDetail menuDetail = new BuSubscriptionMenuDetail();
        BeanUtils.copyProperties(menu, menuDetail);

        // 查询菜单对应的子菜单
        List<BuSubscriptionMenuSub> menuSubList = menuSubMapper.selectList(
                new LambdaQueryWrapper<BuSubscriptionMenuSub>()
                        .eq(BuSubscriptionMenuSub::getMenuId, menu.getId())
                        .eq(BuSubscriptionMenuSub::getDelFlag, 0)
                        .orderByAsc(BuSubscriptionMenuSub::getSubMenuSort)
        );

        // 构建菜单树结构
        List<BuSubscriptionMenuSubVO> menuTree = buildMenuTree(menuSubList);
        filterMenuTree(menuTree);
        menuDetail.setSubMenus(menuTree);

        data.setMenuDetail(menuDetail);

        // 如果有客户ID，查询客户的订阅信息
        if (!StringUtils.isEmpty(externalUserId)) {
            List<BuSubscriptionVO> subscriptionList = getSubscriptionVOS(externalUserId);
            Map<String, BuSubscriptionMenuSub> subMenuMap = menuSubList.stream().collect(Collectors.toMap(BuSubscriptionMenuSub::getId, sub -> sub));
            subscriptionList = subscriptionList.stream().filter(vo -> {
                String subMenuId = vo.getSubMenuId();
                BuSubscriptionMenuSub menuSub = subMenuMap.get(subMenuId);
                if (menuSub != null) {
                    // 如果该子菜单被选中，需要检查该子菜单关联的栏目是否被修改
                    if (StrUtil.isNotEmpty(vo.getSectionId()) && vo.getSectionId().equals(menuSub.getSectionId())) {
                        // 关联栏目被修改了，那么对应的选中项则不返回
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            data.setSubscriptionVOList(subscriptionList);
        }

        return data;
    }

    /**
     * 过滤掉叶子节点不关联栏目的分支
     * @param menuTree
     */
    private void filterMenuTree(List<BuSubscriptionMenuSubVO> menuTree) {
        if (CollectionUtils.isEmpty(menuTree)) {
            return;
        }
        // 循环处理每个节点，若其子节点为空，则删除该节点
        Iterator<BuSubscriptionMenuSubVO> iterator = menuTree.iterator();
        while (iterator.hasNext()) {
            BuSubscriptionMenuSubVO node = iterator.next();
            List<BuSubscriptionMenuSubVO> children = node.getChildren();
            filterMenuTree(children);
            if (CollectionUtils.isEmpty(children) && node.getSectionId() == null) {
                iterator.remove();
            }
        }
    }

    /**
     * 查询客户订阅信息
     * @param externalUserId
     * @return
     */
    @Override
    public List<BuSubscriptionVO> getSubscriptionVOS(String externalUserId) {
        // 查询客户订阅信息
        List<BuSubscriptionVO> subscriptionList = baseMapper.selectSubscriptionListByExternalUserId(externalUserId);
        return subscriptionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSubscrMenus(BuCustomerSubscrData buCustomerSubscrData) {
        if (buCustomerSubscrData == null || StringUtils.isEmpty(buCustomerSubscrData.getExternalUserId())
                || buCustomerSubscrData.getMenuId() == null) {
            return;
        }

        String externalUserId = buCustomerSubscrData.getExternalUserId();
        String menuId = buCustomerSubscrData.getMenuId();
        List<BuSectionSelect> selectSectionList = buCustomerSubscrData.getSelectSectionList();

        // 历史订阅
        List<BuSubscription> oldSubScriptions = baseMapper.selectList(new LambdaQueryWrapper<BuSubscription>()
                .eq(BuSubscription::getExternalUserId, externalUserId)
                .eq(BuSubscription::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
        );
        // 当前订阅菜单所有的订阅栏目
        List<BuSubscriptionMenuSub> buSubscriptionMenuSubs = menuSubMapper.selectList(new LambdaQueryWrapper<BuSubscriptionMenuSub>()
                .eq(BuSubscriptionMenuSub::getMenuId, menuId)
                .eq(BuSubscriptionMenuSub::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
        );

        List<String> sectionList = buSubscriptionMenuSubs.stream().filter(sub -> StrUtil.isNotEmpty(sub.getSectionId())).map(x -> x.getSectionId()).collect(Collectors.toList());
        // 历史订阅中包含在 sectionList 中的订阅栏目 都需要进行修改
        List<BuSubscription> needUpdateSubScriptions = oldSubScriptions.stream().filter(sub -> sectionList.contains(sub.getSectionId())).collect(Collectors.toList());

        // 如果没有选择栏目，直接返回
        if (CollectionUtils.isEmpty(selectSectionList)) {
            // 全部取消订阅
            cancleSubscription(needUpdateSubScriptions, buCustomerSubscrData);
            return;
        }
        // 必须修改的栏目，且未选中，即是需要取消订阅的栏目
        List<String> selectSectionIds = selectSectionList.stream().map(x -> x.getSectionId()).collect(Collectors.toList());
        List<BuSubscription> cancleSectionList = needUpdateSubScriptions.stream().filter(sub -> !selectSectionIds.contains(sub.getSectionId())).collect(Collectors.toList());
        cancleSubscription(cancleSectionList, buCustomerSubscrData);

        // 保存新的订阅信息
        List<BuSubscription> subscriptionList = new ArrayList<>();
        Date now = new Date();
        Map<String, BuSubscription> sectionMap = oldSubScriptions.stream().collect(Collectors.toMap(BuSubscription::getSectionId, sub -> sub));
        List<BuSubscription> addSectionList = new ArrayList<>();
        for (BuSectionSelect select : selectSectionList) {
            if (StringUtils.isEmpty(select.getSectionId()) || StringUtils.isEmpty(select.getSubMenuId())) {
                continue;
            }

            BuSubscription subscription = sectionMap.get(select.getSectionId());
            boolean isNew = subscription == null;
            if (isNew) {
                subscription = new BuSubscription();
                subscription.setExternalUserId(externalUserId);
                subscription.setSubscrTime(now);
                subscription.setDelFlag(StatusConstants.DEL_FLAG_FALSE_INT);
                subscription.setSectionId(select.getSectionId());
                addSectionList.add(subscription);
            }
            subscription.setSubMenuId(select.getSubMenuId());
            subscription.setFirstSubMenuId(select.getFirstSubMenuId());

            subscriptionList.add(subscription);
        }

        if (!subscriptionList.isEmpty()) {
            this.saveOrUpdateBatch(subscriptionList);
        }
        if (!addSectionList.isEmpty()) {
            // 发送新增客户订阅的消息动态
            DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_ADD.getSubEventType());
            RelatedResource relatedResource = new RelatedResource();
            relatedResource.setFromWechat(buCustomerSubscrData.getFromWechat());
            List<BuSubscriptionVO> sectionNames = baseMapper.selectSubscrSectionByIds(addSectionList.stream().map(x -> x.getSectionId()).collect(Collectors.toList()));
            relatedResource.setSubscrList(sectionNames.stream().map(x -> x.getFirstSubMenuName() + " - " + x.getSectionName()).collect(Collectors.toList()));
            OperTrackParams operTrackParams = OperTrackParams.builder()
                    .externalUserId(addSectionList.get(0).getExternalUserId())
                    .userId(buCustomerSubscrData.getUserId())
                    .corpId(CorpInfoProperties.getCorpId())
                    .relatedResource(relatedResource)
                    .build();
            BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
            mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
            log.info("{} 发送增加订阅的MQ消息成功", handler.eventType.getTitle());
        }
    }

    /**
     * 取消订阅
     *
     * @param needUpdateSubScriptions
     * @param buCustomerSubscrData
     */
    private void cancleSubscription(List<BuSubscription> needUpdateSubScriptions, BuCustomerSubscrData buCustomerSubscrData) {
        if (CollectionUtils.isEmpty(needUpdateSubScriptions)) {
            return;
        }
        log.info("取消订阅：{}", needUpdateSubScriptions.stream().map(sub -> sub.getId()).collect(Collectors.toList()));
        needUpdateSubScriptions.forEach(sub -> {
            sub.setDelFlag(StatusConstants.DEL_FLAG_TRUE_INT);
            sub.setCancelSubscrTime(new Date());
            baseMapper.updateById(sub);
        });
        // 发送取消客户订阅的消息动态
        DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_REMOVE.getSubEventType());
        RelatedResource relatedResource = new RelatedResource();
        relatedResource.setFromWechat(buCustomerSubscrData.getFromWechat());
        List<BuSubscriptionVO> sectionNames = baseMapper.selectSubscrSectionByIds(needUpdateSubScriptions.stream().map(x -> x.getSectionId()).collect(Collectors.toList()));
        relatedResource.setSubscrList(sectionNames.stream().map(x -> x.getFirstSubMenuName() + " - " + x.getSectionName()).collect(Collectors.toList()));
        OperTrackParams operTrackParams = OperTrackParams.builder()
                .externalUserId(needUpdateSubScriptions.get(0).getExternalUserId())
                .userId(buCustomerSubscrData.getUserId())
                .corpId(CorpInfoProperties.getCorpId())
                .relatedResource(relatedResource)
                .build();
        BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
        mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
        log.info("{} 发送取消订阅的MQ消息成功", handler.eventType.getTitle());
    }

    @Override
    public List<BuSubscriptionVO> getSubscrSectionList(BuSubscrQuery query) {
        if (StringUtils.isEmpty(query.getExternalUserId()) || StringUtils.isEmpty(query.getType())) {
            return new ArrayList<>();
        }
        // 客户订阅栏目 只查询 使用中 的物料
        List<BuSubscriptionVO> buSubscriptionVOS = baseMapper.selectSubscrSectionList(query);
        if (CollectionUtil.isNotEmpty(buSubscriptionVOS)) {
            buSubscriptionVOS.forEach(radar -> {
                TbWxRadarContent content = radar.getTbWxRadarContent();
                if (content!= null) {
                    StringBuilder urlBuilder = new StringBuilder();
                    urlBuilder.append(RadarConfig.getContentPage()).append("?id=").append(content.getId());
                    urlBuilder.append("&clickSource=8");
                    urlBuilder.append("&staffId=").append("");
                    content.setUrl(urlBuilder.toString());
                    radar.setRadarUrl(urlBuilder.toString());
                }
            });
        }
        return buSubscriptionVOS;
    }

    @Override
    public Boolean getUpgradeFlag(String externalUserId) {
        if (StringUtils.isEmpty(externalUserId)) {
            return false;
        }

        // 查询当前发布的订阅菜单
        BuSubscriptionMenu menu = menuMapper.selectOne(
                new LambdaQueryWrapper<BuSubscriptionMenu>()
                        .eq(BuSubscriptionMenu::getReleaseStatus, 1) // 已发布
                        .eq(BuSubscriptionMenu::getDelFlag, 0) // 未删除
                        .orderByDesc(BuSubscriptionMenu::getCreateTime)
                        .last("LIMIT 1")
        );

        if (menu == null || menu.getSubscrUpgradeTime() == null) {
            return false;
        }

        // 查询客户最新的订阅记录
        BuSubscription subscription = baseMapper.selectOne(
                new LambdaQueryWrapper<BuSubscription>()
                        .eq(BuSubscription::getExternalUserId, externalUserId)
                        .eq(BuSubscription::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
                        .orderByDesc(BuSubscription::getSubscrTime)
                        .last("LIMIT 1")
        );
        BuSubscription cancelSubscription = baseMapper.selectOne(
                new LambdaQueryWrapper<BuSubscription>()
                        .eq(BuSubscription::getExternalUserId, externalUserId)
                        .eq(BuSubscription::getDelFlag, StatusConstants.DEL_FLAG_TRUE_INT)
                        .orderByDesc(BuSubscription::getCancelSubscrTime)
                        .last("LIMIT 1")
        );
        // 比较subscription的订阅时间，和cancelSubscription的取消订阅时间，取较大的一个时间
        Date maxTime = null;
        if (subscription != null) {
            maxTime = subscription.getSubscrTime();
        }
        if (cancelSubscription != null) {
            if (maxTime == null || cancelSubscription.getCancelSubscrTime().after(maxTime)) {
                maxTime = cancelSubscription.getCancelSubscrTime();
            }
        }
        // 如果客户没有订阅记录，或者最后订阅时间早于升级时间，需要升级
        return maxTime == null || maxTime.before(menu.getSubscrUpgradeTime());
    }

    /**
     * 构建菜单树结构
     * @param menuSubList 子菜单列表
     * @return 菜单树
     */
    private List<BuSubscriptionMenuSubVO> buildMenuTree(List<BuSubscriptionMenuSub> menuSubList) {
        if (CollectionUtils.isEmpty(menuSubList)) {
            return new ArrayList<>();
        }

        // 将子菜单转换为VO
        List<BuSubscriptionMenuSubVO> voList = menuSubList.stream().map(sub -> {
            BuSubscriptionMenuSubVO vo = new BuSubscriptionMenuSubVO();
            BeanUtils.copyProperties(sub, vo);

            // 如果有栏目信息，查询栏目名称
            if (StrUtil.isNotEmpty(sub.getSectionId())) {
                BuSubscriptionSection section = sectionMapper.selectById(sub.getSectionId());
                if (section != null) {
                    vo.setSectionName(section.getSectionName());
                }
            }

            return vo;
        }).collect(Collectors.toList());

        // 根据父ID分组
        Map<String, List<BuSubscriptionMenuSubVO>> parentMap = voList.stream()
                .collect(Collectors.groupingBy(vo -> StrUtil.isNotEmpty(vo.getParentId()) ? vo.getParentId() : "0"));

        List<BuSubscriptionMenuSubVO> firstSubMenu = parentMap.get("0");
        buildMenuTree(firstSubMenu, null, parentMap);

        // 返回一级菜单
        return voList.stream()
                .filter(vo -> vo.getParentId() == null || Objects.equals(vo.getParentId(), "0"))
                .collect(Collectors.toList());
    }

    /**
     * 构建菜单树，并设置第一级菜单ID
     * @param firstSubMenu
     * @param parentVO
     * @param parentMap
     */
    private static void buildMenuTree(List<BuSubscriptionMenuSubVO> firstSubMenu, BuSubscriptionMenuSubVO parentVO, Map<String, List<BuSubscriptionMenuSubVO>> parentMap) {
        if (!CollectionUtils.isEmpty(firstSubMenu)) {
            firstSubMenu.forEach(vo -> {
                if ("0".equals(vo.getSectionId())) {
                    vo.setSectionId(null);
                    vo.setSectionName(null);
                }
                if (parentVO == null) {
                    vo.setFirstSubMenuId(vo.getId());
                } else {
                    vo.setFirstSubMenuId(parentVO.getFirstSubMenuId());
                }
                List<BuSubscriptionMenuSubVO> children = parentMap.get(vo.getId());
                vo.setChildren(children);
                buildMenuTree(children, vo, parentMap);
            });
        }
    }
}