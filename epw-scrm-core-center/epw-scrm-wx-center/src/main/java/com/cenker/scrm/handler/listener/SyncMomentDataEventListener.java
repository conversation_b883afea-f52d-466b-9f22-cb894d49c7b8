package com.cenker.scrm.handler.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.event.SyncMomentDataEvent;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.moment.MomentSyncDto;
import com.cenker.scrm.service.moment.ITbWxMomentService;
import com.cenker.scrm.service.moment.ITbWxMomentTaskInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 朋友圈数据同步事件监听器
 */
@Component
@Slf4j
@AllArgsConstructor
public class SyncMomentDataEventListener implements ApplicationListener<SyncMomentDataEvent> {
    private ITbWxMomentTaskInfoService tbWxMomentTaskInfoService;
    private final ITbWxMomentService momentService;

    @Async
    @Override
    public void onApplicationEvent(SyncMomentDataEvent event) {
        log.info("【同步朋友圈】接收到同步朋友圈数据事件！事件参数：{}", JSON.toJSONString(event.getData()));
        MomentSyncDto momentSyncDto = event.getData();

        if (Objects.isNull(momentSyncDto)) {
            return;
        }

        if (momentSyncDto.getFilterType() == 1) {
            syncEnterpriseMomentData(momentSyncDto);
        } else {
            syncPersonalMomentData(momentSyncDto);
        }
    }

    /**
     * 同步企业发表的朋友圈数据
     * @param momentSyncDto
     */
    private void syncEnterpriseMomentData(MomentSyncDto momentSyncDto) {
        int[] types = StrUtil.splitToInt(momentSyncDto.getSyncContentType(), StrUtil.COMMA);

        for (int type : types) {
            switch (type) {
                case 1:
                    momentService.synchronizeAllMoment(0, CorpInfoProperties.getCorpId(), null);
                    break;
                case 2:
                    if (StrUtil.isNotBlank(momentSyncDto.getMomentTaskId())) {
                        tbWxMomentTaskInfoService.synchronizeMomentTaskResult(momentSyncDto.getMomentTaskId());
                    } else {
                        tbWxMomentTaskInfoService.synchronizeMomentTaskResult(momentSyncDto.getStartTime(), momentSyncDto.getEndTime());
                    }
                    break;
                case 3:
                    if (StrUtil.isNotBlank(momentSyncDto.getMomentTaskId())) {
                        tbWxMomentTaskInfoService.syncMomentData(momentSyncDto.getMomentTaskId());
                    } else {
                        tbWxMomentTaskInfoService.syncMomentData(momentSyncDto.getStartTime(), momentSyncDto.getEndTime());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 同步个人发表的朋友圈数据
     * todo 待优化，先迁移代码，后续优化
     * @param momentSyncDto
     */
    private void syncPersonalMomentData(MomentSyncDto momentSyncDto) {
        int[] types = StrUtil.splitToInt(momentSyncDto.getSyncContentType(), StrUtil.COMMA);

        for (int type : types) {
            switch (type) {
                case 1:
                    momentService.synchronizeAllMoment(1, CorpInfoProperties.getCorpId(), momentSyncDto.getCreator());
                    break;
                case 3:
                    try {
                        momentService.synchronizeLikeAndCommentCount(new CurrentUserDTO(CorpInfoProperties.getCorpId(),momentSyncDto.getCreator()));
                    } catch (Exception e) {
                        log.error("【同步个人朋友圈】同步点赞和评论数据失败！", e);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
