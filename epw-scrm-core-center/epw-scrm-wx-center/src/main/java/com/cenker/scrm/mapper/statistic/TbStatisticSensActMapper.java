package com.cenker.scrm.mapper.statistic;

import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticSensActSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticSensAct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 数据统计-敏感行为 Mapper
*
* <AUTHOR>
* @since 2024-04-16 17:08
*/
@Mapper
public interface TbStatisticSensActMapper extends BaseMapper<TbStatisticSensAct> {
    /**
     * 汇总数据
     * @param query
     * @return
     */
    StatisticSensActSummaryVo summary(StatisticSummaryQuery query);

    /**
     * 图表
     * @param query
     * @return
     */
    List<StatisticSensActSummaryVo> graph(StatisticSummaryQuery query);

    /**
     * 保存统计数据
     * @param statDate
     */
    void saveStatisticDateByDay(@Param("statDate") String statDate);
}
