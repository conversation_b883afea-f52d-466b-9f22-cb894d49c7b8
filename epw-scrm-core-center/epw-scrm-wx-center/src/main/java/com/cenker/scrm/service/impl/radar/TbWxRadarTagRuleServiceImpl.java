package com.cenker.scrm.service.impl.radar;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.TagSource;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.mapper.external.TbWxExtCustomerMapper;
import com.cenker.scrm.mapper.radar.TbWxRadarTagRuleMapper;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryService;
import com.cenker.scrm.service.external.TbWxExtFollowUserService;
import com.cenker.scrm.service.radar.ITbWxRadarContentRecordService;
import com.cenker.scrm.service.radar.ITbWxRadarTagRuleService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupService;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.service.tag.TbWxExtFollowUserTagService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description
 */
@Service
@Slf4j
@AllArgsConstructor
public class TbWxRadarTagRuleServiceImpl extends ServiceImpl<TbWxRadarTagRuleMapper, TbWxRadarTagRule> implements ITbWxRadarTagRuleService {

    private TbWxExtFollowUserService followUserService;
    private final ITbWxRadarContentRecordService recordService;
    private final ITbWxCustomerTrajectoryService tbWxCustomerTrajectoryService;
    private final TbWxExtFollowUserTagService tbWxExtFollowUserTagService;
    private final ITbWxCorpTagService tbWxCorpTagService;
    private final ITbWxCorpTagGroupService tagGroupService;
    private final MqSendMessageManager mqSendMessageManager;
    private final TbWxExtCustomerMapper customerMapper;

    @Override
    public void addTagRule(List<TbWxRadarTagRule> tbWxRadarTagRuleList, String radarId) {
        if (CollectionUtils.isNotEmpty(tbWxRadarTagRuleList)) {
            tbWxRadarTagRuleList.forEach(t -> t.setRadarId(radarId));
            saveBatch(tbWxRadarTagRuleList);
        }
    }


    @Override
    public void addTagByRuleAndReadRecord(TbWxRadarInteract radar, MpWxUser mpWxUser, TbWxRadarContentRecord record, TbWxRadarContent tbWxRadarContent) {

        // 查询是否该微信用户是否客户
//        TbWxExtCustomer customer = followUserService.getCustomerByUnionId(radar.getCorpId(), record.getStaffId(), mpWxUser.getUnionId());
        List<TbWxExtCustomer> tbWxExtCustomers = customerMapper.selectList(new LambdaQueryWrapper<TbWxExtCustomer>().eq(TbWxExtCustomer::getUnionId, mpWxUser.getUnionId()).eq(TbWxExtCustomer::getCorpId, radar.getCorpId())
                .last("limit 1"));
        if (CollectionUtils.isEmpty(tbWxExtCustomers)) {
            log.warn("该微信用户非客户, unionId={}, nickName={}", mpWxUser.getUnionId(), mpWxUser.getNickName());
            return;
        }
        TbWxExtCustomer customer = tbWxExtCustomers.get(0);

        // 查询规则 理论必定存在
        List<TbWxRadarTagRule> list = list(new LambdaQueryWrapper<TbWxRadarTagRule>().eq(TbWxRadarTagRule::getRadarId, radar.getId()));
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Integer, List<TbWxRadarTagRule>> ruleGroupByTypeMap = list.stream().collect(Collectors.groupingBy(TbWxRadarTagRule::getType));
            Set<Integer> types = ruleGroupByTypeMap.keySet();
            for (Integer type : types) {
                List<TbWxRadarTagRule> ruleList = ruleGroupByTypeMap.get(type);
                if (CollectionUtils.isNotEmpty(ruleList)) {
                    // 统计点击次数
                    int clickNum = recordService.statisticClickNumByStaff(record);
                    // 统计阅读时长
                    int readNum = recordService.statisticReadNumByStaff(record);
                    // 统计转发次数
                    int forWordNum = recordService.statisticForWordNumByStaff(record);
                    // 1 点击次数 2 阅读时长 3 转发次数
                    switch (type) {
                        case TypeConstants.RADAR_ADD_TAG_RULE_TYPE_CLICK:
//                            addTagId2List(ruleList, tagIds, clickNum);
                            log.info("【智能物料】客户标签验证点击次数规则是否达标");
                            addTagForRuleList(radar, record, ruleList, clickNum, customer, tbWxRadarContent);
                            // 达标记录客户轨迹
//                            tbWxCustomerTrajectoryService.addCustomerTrajectory2RuleList(ruleList, clickNum, readNum, radar, mpWxUser, type, tbWxRadarContent, record);
                            break;
                        case TypeConstants.RADAR_ADD_TAG_RULE_TYPE_READ:
//                            addTagId2List(ruleList, tagIds, readNum);
                            addTagForRuleList(radar, record, ruleList, readNum, customer, tbWxRadarContent);
                            log.info("【智能物料】客户标签验证阅读时长规则是否达标");
                            // 达标记录客户轨迹
//                            tbWxCustomerTrajectoryService.addCustomerTrajectory2RuleList(ruleList, clickNum, readNum, radar, mpWxUser, type, tbWxRadarContent, record);
                            break;
                        case TypeConstants.RADAR_ADD_TAG_RULE_TYPE_FORWARD:
//                            addTagId2List(ruleList, tagIds, forWordNum);
                            log.info("【智能物料】客户标签验证转发次数规则是否达标");
                            addTagForRuleList(radar, record, ruleList, forWordNum, customer, tbWxRadarContent);
                            // 达标记录客户轨迹
//                            tbWxCustomerTrajectoryService.addCustomerTrajectory2RuleList(ruleList, forWordNum, readNum, radar, mpWxUser, type, tbWxRadarContent, record);
                            break;
                        default:
                            break;
                    }

                }
            }
            return;
        }
        log.warn("【智能物料】添加客户标签规则数据异常，找不到对应规则");
    }

    /**
     * 按规则判断是否需要打标签
     *
     * @param radar
     * @param record
     * @param ruleList
     * @param forWordNum
     * @param customer
     * @param tbWxRadarContent
     */
    private void addTagForRuleList(TbWxRadarInteract radar, TbWxRadarContentRecord record, List<TbWxRadarTagRule> ruleList, int forWordNum, TbWxExtCustomer customer, TbWxRadarContent tbWxRadarContent) {
        for (TbWxRadarTagRule tbWxRadarTagRule : ruleList) {
            if (Integer.valueOf(tbWxRadarTagRule.getRuleNum()) <= forWordNum) {
                // 达标，加入添加标签列表
                String[] tag = tbWxRadarTagRule.getTagId().split(",");
                List<String> tagList = Arrays.asList(tag);
                markTagForRadar(radar, record, tagList, customer, tbWxRadarTagRule, tbWxRadarContent);
            }
        }
    }

    /**
     * 打标签
     *
     * @param radar
     * @param record
     * @param tagIds
     * @param customer
     * @param tbWxRadarTagRule
     * @param tbWxRadarContent
     */
    private void markTagForRadar(TbWxRadarInteract radar, TbWxRadarContentRecord record, List<String> tagIds, TbWxExtCustomer customer, TbWxRadarTagRule tbWxRadarTagRule, TbWxRadarContent tbWxRadarContent) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            log.info("【智能物料】客户标签规则达标，添加标签：{}", tagIds);
            List<TagVO> tagVos = tbWxCorpTagService.selectValidTagByIds(tagIds);
            RelatedResource relatedResource = new RelatedResource();
            RelatedResource.LinkVo linkVo =  relatedResource.new LinkVo();
            linkVo.setClickSource(record.getClickSource());
            linkVo.setCover(tbWxRadarContent.getCover());
            linkVo.setUrl(RadarConfig.getContentPage() + "?id=" + tbWxRadarContent.getId());
            linkVo.setTitle(tbWxRadarContent.getTitle());
            linkVo.setDigest(tbWxRadarContent.getDigest());
            linkVo.setType(radar.getType());
            relatedResource.setLinkVo(linkVo);
            String corpId = radar.getCorpId();
            CustomerAddTagMsgDto msgContent = CustomerAddTagMsgDto.builder()
                    .eventTypeEnum(TrackEventTypeEnum.SMART_MATERIAL_AUTO_TAG)
                    .corpId(corpId)
                    .externalUserIds(Lists.newArrayList(customer.getExternalUserId()))
                    .userId(record.getStaffId())
                    .tagList(tagVos)
                    .tagSource(TagSource.MATERIAL.name())
                    .fromCallback(true)
                    .name(radar.getTitle())
                    .operTrackParams(OperTrackParams.builder().relatedResource(relatedResource).build())
                    .isRetry(false).build();
            msgContent.setRadarRuleNum(tbWxRadarTagRule.getRuleNum());
            msgContent.setRadarType(tbWxRadarTagRule.getType());
            mqSendMessageManager.sendCustomerAddTagMessage(msgContent);
            log.info("【智能物料】发送客户添加标签MQ消息成功");
        }
    }


    /**
     * 添加到待添加标签的列表
     *
     * @param ruleList 对应的规则
     * @param tagIds   待添加列表
     * @param ruleNum  规定数
     */
    private void addTagId2List(List<TbWxRadarTagRule> ruleList, Set<String> tagIds, int ruleNum) {
        for (TbWxRadarTagRule tbWxRadarTagRule : ruleList) {
            if (Integer.valueOf(tbWxRadarTagRule.getRuleNum()) <= ruleNum) {
                // 达标，加入添加标签列表
                String[] tag = tbWxRadarTagRule.getTagId().split(",");
                tagIds.addAll(Arrays.asList(tag));
            }
        }
    }
}
