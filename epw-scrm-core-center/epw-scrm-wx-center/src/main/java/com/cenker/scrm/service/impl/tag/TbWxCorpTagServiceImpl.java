package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.tag.TbWxCorpTagMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.util.DateUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 企业微信标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@Slf4j
public class TbWxCorpTagServiceImpl extends ServiceImpl<TbWxCorpTagMapper, TbWxCorpTag> implements ITbWxCorpTagService {
    @Autowired
    private TbWxCorpTagMapper tbWxCorpTagMapper;

    /**
     * 查询企业微信标签
     *
     * @param tagId 企业微信标签ID
     * @return 企业微信标签
     */
    @Override
    public TbWxCorpTag selectTbWxCorpTagById(String tagId) {
        return tbWxCorpTagMapper.selectTbWxCorpTagById(tagId);
    }

    /**
     * 查询企业微信标签列表
     *
     * @param tbWxCorpTag 企业微信标签
     * @return 企业微信标签
     */
    @Override
    public List<TbWxCorpTag> selectTbWxCorpTagList(TbWxCorpTag tbWxCorpTag) {
        return tbWxCorpTagMapper.selectTbWxCorpTagList(tbWxCorpTag);
    }

    /**
     * 新增企业微信标签
     *
     * @param tbWxCorpTag 企业微信标签
     * @return 结果
     */
    @Override
    public int insertTbWxCorpTag(TbWxCorpTag tbWxCorpTag) {
        tbWxCorpTag.setCreateTime(DateUtils.getNowDate());
        return tbWxCorpTagMapper.insertTbWxCorpTag(tbWxCorpTag);
    }

    /**
     * 修改企业微信标签
     *
     * @param tbWxCorpTag 企业微信标签
     * @return 结果
     */
    @Override
    public int updateTbWxCorpTag(TbWxCorpTag tbWxCorpTag) {
        return tbWxCorpTagMapper.updateTbWxCorpTag(tbWxCorpTag);
    }

    /**
     * 批量删除企业微信标签
     *
     * @param tagIds 需要删除的企业微信标签ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpTagByIds(String[] tagIds) {
        return tbWxCorpTagMapper.deleteTbWxCorpTagByIds(tagIds);
    }

    /**
     * 删除企业微信标签信息
     *
     * @param tagId 企业微信标签ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpTagById(String tagId) {
        return tbWxCorpTagMapper.deleteTbWxCorpTagById(tagId);
    }

    @Override
    public void syncUpdateCorpTag(String corpId, WxCpUserExternalTagGroupList list) {
        if (ObjectUtil.isNull(list)) {
            return;
        }
        List<TbWxCorpTag> tagList = Lists.newArrayList();
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtil.isNotEmpty(tagGroupList)) {
            WxCpUserExternalTagGroupList.TagGroup tagGroup = tagGroupList.get(0);
            if (tagGroup.getTag() == null || tagGroup.getTag().size() == 0) {
                return;
            }
            for (WxCpUserExternalTagGroupList.TagGroup.Tag tag1 : tagGroup.getTag()) {
                TbWxCorpTag tag = new TbWxCorpTag();
                tag.setStatus(UserStatus.OK.getCode());
                tag.setCreateBy(Constants.DEFAULT_USER);
                tag.setOrder(tag1.getOrder().intValue());
                tag.setCreateTime(new Date(tag1.getCreateTime() * 1000));
                tag.setName(tag1.getName());
                tag.setCorpId(corpId);
                tag.setGroupId(tagGroup.getGroupId());
                tag.setTagId(tag1.getId());
                if (tag1.getDeleted() != null && tag1.getDeleted()) {
                    tag.setStatus(UserStatus.DELETED.getCode());
                }
                tagList.add(tag);
            }
        }
        if (tagList.size() > 0) {
            LambdaUpdateWrapper<TbWxCorpTag> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TbWxCorpTag::getCorpId,corpId);
            // 一般一个事件只会有一个标签更新
            for (TbWxCorpTag tbWxCorpTag : tagList) {
                wrapper.eq(TbWxCorpTag::getGroupId,tbWxCorpTag.getGroupId());
                wrapper.eq(TbWxCorpTag::getTagId,tbWxCorpTag.getTagId());
                update(tbWxCorpTag,wrapper);
            }
        }
     /*  摒弃全量更新的方法
     QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("corp_id", corpId);
        if (tagList.size() > 0) {
            this.remove(wrapper);
            this.saveBatch(tagList, 100);
        }*/
    }

    @Override
    public List<TbWxCorpTag> selectTbWxCorpTagGroupList(String corpId, String[] groupIds, Integer status) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("corpId", corpId);
        param.put("groupIds", groupIds);
        param.put("status", status);
        return baseMapper.selectTbWxCorpTagGroupList(param);
    }

    @Override
    public List<String> selectTbWxCorpTagGroupIdList(String corpId, String[] tagIds) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("corpId", corpId);
        param.put("tagIds", tagIds);
        return baseMapper.selectTbWxCorpTagGroupIdList(param);
    }

    @Override
    public List<String> selectTbWxCorpTagNameList(String corpId, String[] tagIds) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("corpId", corpId);
        param.put("tagIds", tagIds);
        return baseMapper.selectTbWxCorpTagNameList(param);
    }

    @Override
    public List<TagVO> selectValidTagByIds(List<String> tagIds) {
        return baseMapper.selectValidTagByIds(tagIds);
    }
}
