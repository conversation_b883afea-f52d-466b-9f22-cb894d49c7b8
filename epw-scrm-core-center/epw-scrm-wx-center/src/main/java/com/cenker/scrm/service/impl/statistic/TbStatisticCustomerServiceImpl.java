package com.cenker.scrm.service.impl.statistic;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatisticConstants;
import com.cenker.scrm.enums.DataScopeEnum;
import com.cenker.scrm.mapper.statistic.TbStatisticCustomerDetailMapper;
import com.cenker.scrm.mapper.statistic.TbStatisticCustomerMapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomer;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomerDetail;
import com.cenker.scrm.pojo.request.statistic.StatisticCustomerListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticCustomerSummaryVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.service.statistic.ITbStatisticBaseService;
import com.cenker.scrm.service.statistic.ITbStatisticCustomerService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据统计-客户数据 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Service
@Slf4j
@AllArgsConstructor
public class TbStatisticCustomerServiceImpl extends ServiceImpl<TbStatisticCustomerMapper, TbStatisticCustomer> implements ITbStatisticCustomerService, ITbStatisticBaseService {

    private final TbStatisticCustomerMapper tbStatisticCustomerMapper;

    private final TbStatisticCustomerDetailMapper tbStatisticCustomerDetailMapper;

    @Override
    public StatisticCustomerSummaryVo summary(StatisticSummaryQuery query) {
        StatisticCustomerSummaryVo vo = tbStatisticCustomerMapper.summary(query);
        // 时间范围搜索，部分指标返回null
        if (StringUtils.isNotEmpty(query.getBeginTime()) && StringUtils.isNotEmpty(query.getEndTime())) {
            if (!query.getBeginTime().equals(query.getEndTime())) {
                vo.setCustomerTotal(null);
                vo.setCustomerAuthTotal(null);
            }
        }
        return vo;
    }

    @Override
    public List<StatisticGraphVo> graph(StatisticGraphQuery query) {
        List<StatisticCustomerSummaryVo> lstVo = tbStatisticCustomerMapper.graph(query);

        List<StatisticGraphVo> lstGraphVo = new ArrayList<>();

        switch (query.getType()){
            case StatisticConstants.CUSTOMER_CUSTOMERTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                            StatisticGraphVo graphVo = new StatisticGraphVo();
                            graphVo.setDate(summaryVo.getStatisticDate());
                            graphVo.setNum(summaryVo.getCustomerTotal().doubleValue());
                            return graphVo;
                        }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_CUSTOMERAUTHTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerAuthTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_CUSTOMERNEWAUTHTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerNewAuthTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_CUSTOMERNEWTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerNewTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_CUSTOMERLOSSTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerLossTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_CUSTOMERACTIVETOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerActiveTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_ALONECHATTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getAloneChatTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_RGROUPCHATTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerGroupChatTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.CUSTOMER_NETNEWTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getNetNewTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            default:
                break;
        }

        return lstGraphVo;
    }

    @Override
    public List<TbStatisticCustomer> list(StatisticCustomerListQuery query) {
        List<TbStatisticCustomer> lstCustomer = tbStatisticCustomerMapper.list(query);

        if (null != lstCustomer && lstCustomer.size() > 0) {
            // 查询服务客户的员工数据
            List<Long> lstStatisticCustomerId = lstCustomer.stream().map(t -> t.getId()).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<TbStatisticCustomerDetail> queryWrapper = new LambdaQueryWrapper<TbStatisticCustomerDetail>()
                    .in(TbStatisticCustomerDetail::getStatisticCustomerId, lstStatisticCustomerId);

            if (query.getDataScope() == null || !DataScopeEnum.ALL.getValue().equals(query.getDataScope())) {
                // 用户有权限查看的部门
                if (CollectionUtil.isNotEmpty(query.getPermissionDeptIds())) {
                    queryWrapper.in(TbStatisticCustomerDetail::getDeptId, query.getPermissionDeptIds());
                }

                // 用户为仅本人权限，只能查看群主为当前登录账号且主部门为归属部门的数据
                if (CollectionUtil.isEmpty(query.getPermissionDeptIds())) {
                    queryWrapper.eq(TbStatisticCustomerDetail::getUserid, query.getWxUserId())
                            .eq(TbStatisticCustomerDetail::getDeptId, query.getDeptId());
                }
            }

            List<TbStatisticCustomerDetail> lstCustomerDetail = tbStatisticCustomerDetailMapper.selectList(queryWrapper);

            if (null != lstCustomerDetail) {
                for (TbStatisticCustomer customer : lstCustomer) {
                    List<TbStatisticCustomerDetail> serverStaffList = lstCustomerDetail.stream().filter(t -> customer.getId().equals(t.getStatisticCustomerId())).collect(Collectors.toList());
                    List<String> serverStaffNameList = serverStaffList.stream().map(t -> t.getUserName()+"("+t.getUserid()+")").collect(Collectors.toList());
                    customer.setServerStaffList(serverStaffList);
                    customer.setServerStaffNames(String.join(",", serverStaffNameList));
                }
            }
        }
        return lstCustomer;
    }

    @Override
    public void saveStatisticData(String statDate) {
        log.info("【数据统计更新数据】开始统计客户数据，日期：{}", statDate);

        // 先删除统计日期的数据
        this.lambdaUpdate().eq(TbStatisticCustomer::getStatisticDate, statDate).remove();

        // 插入统计日期当天的数据
        baseMapper.saveStatisticDateByDay(statDate);

        // 删除统计日期当天的服务员工数据
        tbStatisticCustomerDetailMapper.removeStatisticDateByDay(statDate);

        // 插入统计日期当天的服务员工数据
        tbStatisticCustomerDetailMapper.saveStatisticDateByDay(statDate);

        log.info("【数据统计更新数据】结束统计客户数据，日期：{}", statDate);
    }

    @Override
    public void synData(Date statDate) {
        this.saveStatisticData(DateUtil.formatDate(statDate));
    }
}
