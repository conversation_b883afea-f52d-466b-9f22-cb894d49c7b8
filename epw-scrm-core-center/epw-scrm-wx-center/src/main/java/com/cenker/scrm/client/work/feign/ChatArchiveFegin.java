package com.cenker.scrm.client.work.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ServiceNameConstants.WORK_API_SERVICE, path = "/service/chatarchive")
public interface ChatArchiveFegin {

    @GetMapping("/getPermitUserList")
    List<String> getPermitUserList(@RequestParam("corpId") String corpId) throws WxErrorException;
}
