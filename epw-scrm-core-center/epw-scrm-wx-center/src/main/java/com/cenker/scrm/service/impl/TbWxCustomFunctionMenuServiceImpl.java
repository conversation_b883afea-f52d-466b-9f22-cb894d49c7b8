package com.cenker.scrm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.TbWxCustomFunctionMenuMapper;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomFunctionMenu;
import com.cenker.scrm.service.ITbWxCustomFunctionMenuService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/23
 * @Description
 */
@Service
public class TbWxCustomFunctionMenuServiceImpl extends ServiceImpl<TbWxCustomFunctionMenuMapper, TbWxCustomFunctionMenu> implements ITbWxCustomFunctionMenuService {
    @Override
    public List<SysMenu> getCustomMenuList(Long corpConfigId) {
        return baseMapper.getCustomMenuList(corpConfigId);
    }
}
