package com.cenker.scrm.controller.session;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.QryActAlarmDto;
import com.cenker.scrm.pojo.entity.enums.CkSessionEnum;
import com.cenker.scrm.pojo.entity.session.CkSessionSensActAlarmRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.vo.session.CkSessionSensActAlarmVO;
import com.cenker.scrm.pojo.vo.session.ExportCkSessionSensActAlarmVO;
import com.cenker.scrm.pojo.vo.session.RuleUserVO;
import com.cenker.scrm.service.corp.ITbWxCorpConfigService;
import com.cenker.scrm.service.session.ICkSessionSensActAlarmRecordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 热词信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/session/actalarm")
public class CkSessionSensActAlarmController extends BaseController {
    private ICkSessionSensActAlarmRecordService iCkSessionSensActAlarmRecordService;
    private ITbWxCorpConfigService tbWxCorpConfigService;

    /**
     * 查询热词信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QryActAlarmDto qryActAlarmDto)
    {
        String companyName = this.getCompanyName();
        startPage();
        PageHelper.orderBy(" o.create_time desc ");
        List<CkSessionSensActAlarmVO> listVo = new ArrayList<>();
        List<CkSessionSensActAlarmRecord> list = iCkSessionSensActAlarmRecordService.selectCkSessionSensActAlarmRecordList(qryActAlarmDto);
        for (CkSessionSensActAlarmRecord actInfo : list) {
            CkSessionSensActAlarmVO actInfoVO = buildCkSessionSensActAlarmVO(actInfo, companyName);
            listVo.add(actInfoVO);
        }
        TableDataInfo backTab = getDataTable(listVo);
        backTab.setTotal(new PageInfo(list).getTotal());
        return backTab;
    }
    @GetMapping("/export")
    public List<ExportCkSessionSensActAlarmVO> export(QryActAlarmDto qryActAlarmDto){
        String companyName = this.getCompanyName();
        PageHelper.orderBy(" o.create_time desc ");
        List<ExportCkSessionSensActAlarmVO> listVo = new ArrayList<>();
        List<CkSessionSensActAlarmRecord> list = iCkSessionSensActAlarmRecordService.selectCkSessionSensActAlarmRecordList(qryActAlarmDto);
        for ( CkSessionSensActAlarmRecord alarmVO:list) {
            ExportCkSessionSensActAlarmVO actAlarmVO = buildExportCkSessionSensActAlarmVO(alarmVO, companyName);
            listVo.add(actAlarmVO);
        }
        return listVo;
    }

    /**
     * 查询企业名称
     * @return
     */
    private String getCompanyName(){
        TbWxCorpConfig corpConfig = tbWxCorpConfigService.lambdaQuery()
                .eq(TbWxCorpConfig::getStatus, CommonConstants.STATUS_NORMAL)
                .eq(TbWxCorpConfig::getDelFlag, CommonConstants.STATUS_NORMAL)
                .last("LIMIT 1").one();
        return Objects.nonNull(corpConfig)? corpConfig.getCompanyName() : "";
    }

    private CkSessionSensActAlarmVO buildCkSessionSensActAlarmVO(CkSessionSensActAlarmRecord record, String companyName) {
        CkSessionSensActAlarmVO alarmVO = new CkSessionSensActAlarmVO();
        alarmVO.setId(record.getId());
        alarmVO.setActType(record.getActType());
        alarmVO.setTriggerTime(DateUtil.formatDateTime(record.getTriggerTime()));
        alarmVO.setRuleName(record.getRuleName());

        RuleUserVO sendUserObj = new RuleUserVO();
        sendUserObj.setUserId(record.getSendUserId());
        sendUserObj.setUserName(record.getSendUserName());
        sendUserObj.setType(record.getSendUserType());
        sendUserObj.setCorpName(record.getSendCorpName());

        // 本企业员工，企业名称统一显示
        if (CommonConstants.USER_TYPE_2.equals(record.getSendUserType())) {
            sendUserObj.setCorpName(companyName);
        }

        alarmVO.setSendUserObj(sendUserObj);

        RuleUserVO acceptUserObj = new RuleUserVO();
        acceptUserObj.setUserId(record.getAcceptUserId());
        acceptUserObj.setType(record.getAcceptUserType());
        acceptUserObj.setUserName(record.getAcceptUserName());
        acceptUserObj.setCorpName(record.getAcceptCorpName());
        // 群聊名称为空时，需显示默认名称
        if(CommonConstants.USER_TYPE_3.equals(record.getAcceptUserType()) && StrUtil.isBlank(record.getAcceptUserName())){
            acceptUserObj.setUserName("群聊（未命名）");
        }

        // 本企业员工，企业名称统一显示
        if (CommonConstants.USER_TYPE_2.equals(record.getAcceptUserType())) {
            acceptUserObj.setCorpName(companyName);
        }
        alarmVO.setAcceptUserObj(acceptUserObj);
        return alarmVO;
    }

    private ExportCkSessionSensActAlarmVO buildExportCkSessionSensActAlarmVO(CkSessionSensActAlarmRecord record, String companyName) {
        ExportCkSessionSensActAlarmVO alarmVO = new ExportCkSessionSensActAlarmVO();
        alarmVO.setActTypeName(CkSessionEnum.getDescByValue(record.getActType()));
        alarmVO.setRuleName(record.getRuleName());
        alarmVO.setTriggerTime(DateUtil.formatDateTime(record.getTriggerTime()));

        String sendCorpName = (CommonConstants.USER_TYPE_2.equals(record.getSendUserType())) ? companyName : record.getSendCorpName();
        alarmVO.setSendUserName(getUserName(record.getSendUserName(), record.getSendUserType(), sendCorpName));
        String acceptCorpName = (CommonConstants.USER_TYPE_2.equals(record.getAcceptUserType())) ? companyName : record.getAcceptCorpName();
        alarmVO.setAcceptUserName(getUserName(record.getAcceptUserName(), record.getAcceptUserType(), acceptCorpName));

        return alarmVO;
    }

    private String getUserName(String userName, String userType, String corpName) {
        if (CommonConstants.USER_TYPE_1.equals(userType) && StrUtil.isBlank(corpName)) {
            return userName + "@微信";
        }

        if (CommonConstants.USER_TYPE_3.equals(userType)) {
            String groupName = StrUtil.isBlank(userName) ? "群聊（未命名）" : userName;
            return groupName + "@群聊";
        }

        if (StrUtil.isNotBlank(corpName)) {
            return userName + "@" + corpName;
        }

        return userName;
    }
}
