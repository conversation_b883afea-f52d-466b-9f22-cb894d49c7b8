package com.cenker.scrm.service.approval;

import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.vo.base.Result;

import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * 审核服务接口
 */
public interface IApprovalService {
    TbWxCorpConfig getTbWxCorpConfig(String corpId);

    Result<List<String>> getApprovalUser(String type, String createBy);

    Result<Boolean> isApprovalUser(String type);

    String getApprovalKey(String id, String type);

    void sendPendingApprovalMsg(String sopName, String sopId, ApprovalTypeEnum approvalTypeEnum, boolean enableApproval, String status, String createBy);

    void sendPendingApprovalMsg(String sopName, String sopId, ApprovalTypeEnum approvalTypeEnum, boolean enableApproval, String status, String createBy, boolean isTask, String businessJson);

    /**
     * 发送审批消息
     * @param info
     * @param type
     * @param success
     * @param nameGetter
     * @param creatorGetter
     * @param idGetter
     * @param <T>
     */
    <T> void sendApprovalMsg(T info, String type, boolean success,
                             Function<T, String> nameGetter,
                             Function<T, String> creatorGetter,
                             Function<T, String> idGetter);
    <T> void sendApprovalMsg(T info, String type, boolean success,
                             Function<T, String> nameGetter,
                             Function<T, String> creatorGetter,
                             Function<T, String> idGetter,
                             boolean isTask, String businessJson);

    <T> void sendAutoRejectedApprovalMsg(T info, String type, boolean success,
                                         Function<T, String> nameGetter,
                                         Function<T, String> creatorGetter,
                                         Function<T, String> idGetter);

    Result<Object> validateApproval(LoginUser loginUser, String type, String taskName, String createBy, String checkStatus);

    <T> void sendWarnApprovalMsg(T task, String type, boolean timeCorn, boolean isCreator, Function<T, String> nameGetter,
                                 Function<T, String> creatorGetter,
                                 Function<T, String> idGetter);

    <T> void sendAlarmDelayMsg(T task, ApprovalTypeEnum typeEnum, Function<T, Date> settingTimeGetter, Function<T, Integer> timeTaskGetter);
}
