package com.cenker.scrm.handler.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.service.statistic.*;
import com.cenker.scrm.service.subscr.IBuSectionStatisticsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 数据统计相关定时任务
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class StaticsticDataTask {

    private final ITbStatisticCustomerService tbStatisticCustomerService;
    private final ITbStatisticSensWordService tbStatisticSensWordService;
    private final ITbStatisticSensActService tbStatisticSensActService;
    private final ITbStatisticRadarService tbStatisticRadarService;
    private final ITbStatisticHotWordService tbStatisticHotWordService;
    private final ITbStatisticCustomerGroupService tbStatisticCustomerGroupService;
    private final IWkChatConversationService wkChatConversationService;
    private final ITbStatisticReplyTimeoutService tbStatisticReplyTimeoutService;
    private final ITbStatisticStaffService tbStatisticStaffService;
    private final ITbWxBusinessTagService tbWxBusinessTagService;
    private final IBuSectionStatisticsService buSectionStatisticsService;

    /**
     * 保存当日客户统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_CUSTOMER_BY_DAY)
    public void saveCustomerStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticCustomerService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日客户统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日客户群统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_GROUP_BY_DAY)
    public void saveGroupStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticCustomerGroupService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日客户群统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日敏感词触发统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_SENS_WORD_BY_DAY)
    public void saveSensWordStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticSensWordService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日敏感词触发统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }


    /**
     * 保存当日敏感行为统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_SENS_ACT_BY_DAY )
    public void saveSensActionStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticSensActService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日敏感行为统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日物料统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_RADAR_BY_DAY)
    public void saveRadarStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticRadarService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日物料统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日热词统计数据
     * <AUTHOR>
     */
    @XxlJob(XxlJobContant.STAT_HOT_WORD_BY_DAY)
    public void saveHotWordStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticHotWordService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日热词统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日聊天会话统计数据
     */
    @XxlJob(XxlJobContant.STAT_CHAT_CONVERSATION_BY_DAY)
    public void saveChatConversationData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        wkChatConversationService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日聊天会话统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日员工统计数据
     */
    @XxlJob(XxlJobContant.STAT_STAFF_BY_DAY)
    public void saveStaffStatisticData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticStaffService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日员工统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }


    /**
     * 保存当日回复超时统计数据
     */
    @XxlJob(XxlJobContant.STAT_REPLY_TIMEOUT_BY_DAY)
    public void saveReplyTimeoutData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        tbStatisticReplyTimeoutService.saveStatisticData(statDate);
        stopWatch.stop();
        log.info("保存当日回复超时统计数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日企业标签数据
     */
    @XxlJob(XxlJobContant.STAT_WX_BUSINESS_TAG_DATA)
    public void statWxBusinessTagData() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        tbWxBusinessTagService.saveData();
        stopWatch.stop();
        log.info("保存当日企业标签数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存当日栏目订阅数据
     */
    @XxlJob(XxlJobContant.STAT_SECTION_DATA_STATISTIC)
    public void statSectionDataStatistic() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String statDate = getStatDate();
        buSectionStatisticsService.saveStatisticData(statDate);
        // 如果当前时间是凌晨0点至1点之间，且同步的是今天的数据，则再统计一次昨天的数据
        if (statDate.equals(DateUtil.today()) && DateUtil.hour(new Date(), true) <= 1) {
            log.info("当前时间是凌晨0点至1点之间，且同步的是今天的数据，则再统计一次昨天的数据");
            XxlJobHelper.log("当前时间是凌晨0点至1点之间，且同步的是今天的数据，则再统计一次昨天的数据");
            statDate = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
            buSectionStatisticsService.saveStatisticData(statDate);
        }
        stopWatch.stop();
        log.info("保存当日栏目订阅数据完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
        XxlJobHelper.log("保存当日栏目订阅数据完成，耗时：" + stopWatch.getTotalTimeMillis() + " ms");
    }

    /**
     * 获取参数
     * @return
     */
    private String getStatDate() {
        String param =XxlJobHelper.getJobParam();

        if (StrUtil.isBlank(param)) {
            return DateUtil.today();
        }

        if (!ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}", param)) {
            XxlJobHelper.log("参数格式错误，请确认参数格式为yyyy-MM-dd！参数为：" + param);
        }

        return param;
    }
}
