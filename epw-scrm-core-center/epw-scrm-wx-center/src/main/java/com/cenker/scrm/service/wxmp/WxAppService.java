
package com.cenker.scrm.service.wxmp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxOpenQuery;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxOpenQueryVo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizerInfo;

import java.util.List;

/**
 * 微信应用
 *
 * <AUTHOR>
 * @date 2019-03-15 10:26:44
 */
public interface WxAppService extends IService<WxApp> {

	/**
	 * 微信原始标识查找
	 * @param weixinSign
	 * @return
	 */
	WxApp findByWeixinSign(String weixinSign);

	/**
	 * 通过appId获取WxApp，无租户条件
	 * @param appId
	 * @return
	 */
	WxApp findByAppId(String appId);

	/**
	 * 查询授权列表，区分企业
	 * @param wxOpenQuery
	 * @return
	 */
	List<WxOpenQueryVo> selectWxOpenList(WxOpenQuery wxOpenQuery);

	/**
	 * 获取企业选择小程序信息
	 * @param corpId
	 * @return
	 */
	WxApp getInfoByCorpConfigIdAndVerify(String corpId, String shortValue);

	/**
	 * 根据appId和企业id获取相关配置
	 *
	 * @param wxOpenAuthorizerInfo
	 * @param appId
	 * @param corpId
	 * @return
	 */
    WxApp getWxAppByAppIdAndCorpIdSaveOrUpdate(WxOpenAuthorizerInfo wxOpenAuthorizerInfo, String appId, String corpId);

	void findInstallWxMaByCorpId(String valueOf);

	WxOAuth2AccessToken getWxUserByCode(String code)  throws Exception;
}
