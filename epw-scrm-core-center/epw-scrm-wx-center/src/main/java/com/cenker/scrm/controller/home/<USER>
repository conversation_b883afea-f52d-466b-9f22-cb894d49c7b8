package com.cenker.scrm.controller.home;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.mapper.corp.TbWxCorpConfigMapper;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomFunctionMenu;
import com.cenker.scrm.pojo.exception.BaseException;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.external.CorpRealTimeDataVO;
import com.cenker.scrm.service.ITbWxCustomFunctionMenuService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.group.ITbWxCustomerGroupService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.cenker.scrm.util.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @Date 2022/7/29
 * @Description 首页控制器
 */
@Slf4j
@RestController
@RequestMapping("/wx/home")
@RequiredArgsConstructor
public class HomeController extends BaseController {
    private final TbWxCorpConfigMapper tbWxCorpConfigMapper;
    private final TbWxExtCustomerService tbWxExtCustomerService;
    private final ITbWxCustomerGroupService tbWxCustomerGroupService;
    private final ITbWxCustomFunctionMenuService tbWxCustomFunctionMenuService;
    private final ITbWxUserService tbWxUserService;

    /**
     * 查询功能状态（临时）
     *
     * @return
     */
    @RequestMapping("/queryTaskStatus")
    public AjaxResult queryTaskStatus(@RequestParam("corpId") String corpId) {
        Map<String, Object> result = tbWxCorpConfigMapper.queryTaskStatus(corpId);
        return AjaxResult.success(result);
    }

    /**
     * 查询首页趋势图
     */
    @RequestMapping("/getCorpRealTimeData")
    public AjaxResult getCorpRealTimeData(@RequestBody StatisticQuery statisticQuery) {
        Date startTime;
        Date endTime = DateUtil.endOfDay(DateUtil.date());
        // 查询时间范围
        if (statisticQuery.getSeven()) {
            startTime = DateUtil.beginOfDay(DateUtils.addDays(endTime, -6));
        } else if (statisticQuery.getThirty()) {
            startTime = DateUtil.beginOfDay(DateUtils.addDays(endTime, -29));
        } else {
            if (StringUtils.isAnyBlank(statisticQuery.getBeginTime(), statisticQuery.getEndTime())) {
                throw new BaseException("参数错误");
            }

            startTime = DateUtil.beginOfDay(DateUtil.parseDate(statisticQuery.getBeginTime()));
            endTime = DateUtil.endOfDay(DateUtil.parseDate(statisticQuery.getEndTime()));
        }
        CorpRealTimeDataVO corpRealTimeDataVO = null;
        if (statisticQuery.getTrendType() == 1) {
            corpRealTimeDataVO = tbWxExtCustomerService.getBehaviorData(startTime, endTime, statisticQuery);
        } else if (statisticQuery.getTrendType() == 2) {
            corpRealTimeDataVO = tbWxCustomerGroupService.getBehaviorData(startTime, endTime, statisticQuery);
        }
        return AjaxResult.success(corpRealTimeDataVO);
    }

    /**
     * 已认证总数日线图
     */
    @RequestMapping("/getDailyTotalAuthCustomerCountGraph")
    public AjaxResult getCertifiedTotal(QueryRadarStatisticsRankDTO dto) {
        if(Objects.equals(dto.getToday(),true)){
            dto.setBeginTime(DateUtils.getDate());
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getSeven(),true)){
            // 最近七天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -6)));
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getThirty(),true)) {
            // 最近30天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -29)));
            dto.setEndTime(DateUtils.getDate());
        }
        CorpRealTimeDataVO result= tbWxExtCustomerService.getDailyTotalAuthCustomerCountGraph(dto);
        return AjaxResult.success(result);
    }

    /**
     * 活跃客户数1V1，日线图
     */
    @RequestMapping("/getActiveCustomerCount1v1DailyGraph")
    public AjaxResult getActiveCustomerCount1v1DailyGraph(QueryRadarStatisticsRankDTO dto) {
        if(Objects.equals(dto.getToday(),true)){
            dto.setBeginTime(DateUtils.getDate());
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getSeven(),true)){
            // 最近七天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -6)));
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getThirty(),true)) {
            // 最近30天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -29)));
            dto.setEndTime(DateUtils.getDate());
        }
        return AjaxResult.success(tbWxExtCustomerService.getActiveCustomerCount1v1DailyGraph(dto));
    }


    /**
     * 员工总数
     * @param dto
     * @return
     */
    @RequestMapping("/getDailyTotalUserCountGraph")
    public AjaxResult getDailyTotalUserCountGraph(QueryRadarStatisticsRankDTO dto) {
        if(Objects.equals(dto.getToday(),true)){
            dto.setBeginTime(DateUtil.today());
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else if (Objects.equals(dto.getSeven(),true)){
            // 最近七天,年月日字符串
            dto.setBeginTime(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6)));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else if (Objects.equals(dto.getThirty(),true)) {
            // 最近30天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -29)));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else {
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(dto.getEndTime()))));
        }
        return AjaxResult.success(tbWxUserService.getDailyTotalUserCountGraph(dto));
    }

    /**
     * 客户群总数
     */
    @RequestMapping("/getDailyTotalGroupCountGraph")
    public AjaxResult getDailyTotalGroupCountGraph(QueryRadarStatisticsRankDTO dto) {
        if(Objects.equals(dto.getToday(),true)){
            dto.setBeginTime(DateUtil.today());
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else if (Objects.equals(dto.getSeven(),true)){
            // 最近七天,年月日字符串
            dto.setBeginTime(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6)));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else if (Objects.equals(dto.getThirty(),true)) {
            // 最近30天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -29)));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.date())));
        } else {
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(dto.getEndTime()))));
        }
        return AjaxResult.success(tbWxCustomerGroupService.getDailyTotalGroupCountGraph(dto));
    }

    /**
     * 自定义功能区列表
     */
    @RequestMapping("/getCustomMenuList")
    public AjaxResult getCustomMenuList(@RequestParam("corpConfigId") Long corpConfigId) {
        List<SysMenu> sysMenuList = tbWxCustomFunctionMenuService.getCustomMenuList(corpConfigId);
        return AjaxResult.success(sysMenuList);
    }

    /**
     * 保存自定义功能
     */
    @RequestMapping("/saveCustomMenu")
    public AjaxResult saveCustomMenu(@RequestBody List<SysMenu> sysMenus, @RequestParam("corpConfigId") Long corpConfigId) {
        // 删除原先自定义
        tbWxCustomFunctionMenuService.remove(new LambdaQueryWrapper<TbWxCustomFunctionMenu>()
                .eq(TbWxCustomFunctionMenu::getCorpPriId, corpConfigId));
        List<TbWxCustomFunctionMenu> saveList = Lists.newArrayList();
        for (SysMenu sysMenu : sysMenus) {
            TbWxCustomFunctionMenu tbWxCustomFunctionMenu = new TbWxCustomFunctionMenu();
            tbWxCustomFunctionMenu.setCorpPriId(corpConfigId);
            tbWxCustomFunctionMenu.setMenuId(sysMenu.getMenuId());
            tbWxCustomFunctionMenu.setOrderNum(sysMenu.getOrderNum());
            saveList.add(tbWxCustomFunctionMenu);
        }
        tbWxCustomFunctionMenuService.saveBatch(saveList);
        return AjaxResult.success();
    }
}
