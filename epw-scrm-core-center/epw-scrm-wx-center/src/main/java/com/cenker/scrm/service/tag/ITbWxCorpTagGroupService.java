package com.cenker.scrm.service.tag;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;

import java.util.List;

/**
 * 企业客户标签组Service接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface ITbWxCorpTagGroupService extends IService<TbWxCorpTagGroup> {
    /**
     * 查询企业客户标签组
     *
     * @param groupId 企业客户标签组ID
     * @return 企业客户标签组
     */
    TbWxCorpTagGroup selectTbWxCorpTagGroupById(String groupId);

    /**
     * 查询企业客户标签组列表
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 企业客户标签组集合
     */
    List<TbWxCorpTagGroup> selectTbWxCorpTagGroupList(TbWxCorpTagGroup tbWxCorpTagGroup);

    /**
     * 新增企业客户标签组
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 结果
     */
    TbWxCorpTagGroup insertTbWxCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup, boolean synFlag);

    /**
     * 修改企业客户标签组
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 结果
     */
    int updateTbWxCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup);

    /**
     * 更新标签组、标签表并调用企业微信接口
     *
     * @param tbWxCorpTagGroup
     * @param synFlag
     */
    void updateTbWxCorpTagAndCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup, boolean synFlag) throws WxErrorException;

    /**
     * 批量删除企业客户标签组
     *
     * @param groupIds 需要删除的企业客户标签组ID
     * @return 结果
     */
    int deleteTbWxCorpTagGroupByIds(String[] groupIds);

    /**
     * 删除企业客户标签组信息
     *
     * @param groupId 企业客户标签组ID
     * @return 结果
     */
    int deleteTbWxCorpTagGroupById(String groupId);

    /**
     * 删除便签组
     *
     * @param groupId
     * @throws WxErrorException
     */
    void deleteTagGroup(String[] groupId, String username, String corpId) throws WxErrorException;

    /**
     * 批量同步企业标签数据
     *
     * @param corpId
     */
    void batchSynTags(String corpId);

    /**
     * 异步更新标签组
     *
     * @param corpId
     * @param groupList
     */
    void syncUpdateCorpTagGroup(String corpId, WxCpUserExternalTagGroupList groupList);

    /**
     * 获取企业的所有企业标签
     *
     * @param corpId
     * @return
     */
    WxCpUserExternalTagGroupList queryAllCorpTabGroupList(String corpId);

    /**
     * 获取当前企业所有标签组名
     * @param corpId
     * @return
     */
    List<String> selectTagGroupListName(String corpId);


    String selectTbWxCorpTagGroupNameByTagId(String tagId);

    /**
     * 查询下标签名及标签组名
     * @param tagId 标签id
     * @return 对象
     */
    ExternalUserTagVO selectTbWxCorpTagByTagId(String tagId, String corpId);

    /**
     * 新增标签/标签组
     * @param corpId
     * @param corpTagList
     */
    void syncAddCorpTagGroup(String corpId, WxCpUserExternalTagGroupList corpTagList);

    void deleteUserTagByGroupId(String[] groupIds, String userId, String corpId);
}
