package com.cenker.scrm.service.material;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.dto.material.WeMediaDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;

import java.io.IOException;
import java.util.List;

/**
 * 素材信息Service接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface ITbWxMaterialService extends IService<TbWxMaterial> {
    /**
     * 查询素材信息
     *
     * @param id 素材信息ID
     * @return 素材信息
     */
    TbWxMaterial selectTbWxMaterialById(Long id);

    /**
     * 查询素材信息列表
     *
     * @param param 素材信息
     * @return 素材信息集合
     */
    List<TbWxMaterial> selectTbWxMaterialList(MaterialListDto param);

    /**
     * 新增素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    int insertTbWxMaterial(TbWxMaterial tbWxMaterial);

    /**
     * 修改素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    int updateTbWxMaterial(TbWxMaterial tbWxMaterial);

    /**
     * 批量删除素材信息
     *
     * @param ids 需要删除的素材信息ID
     * @return 结果
     */
    int deleteTbWxMaterialByIds(Long[] ids);

    /**
     * 删除素材信息信息
     *
     * @param id 素材信息ID
     * @return 结果
     */
    int deleteTbWxMaterialById(Long id);

    /**
     * 上传素材至微信
     */
    WeMediaDTO uploadTemporaryMaterial(String picUrl, String mediaType, String fileType, String corpId);
    /**
     * 上传素材至微信
     * @param forceUpload 跳过redis缓存，强制上传至微信
     */
    WeMediaDTO uploadTemporaryMaterial(String picUrl, String mediaType, String fileType, String corpId, boolean forceUpload);

    /**
     * 上传附件资源
     */
    WeMediaDTO uploadTemporaryMaterial(String baseUrl, String mediaType, String picName, Integer attachmentType, String corpId) throws IOException;

    /**
     * 查询一级素材信息列表（多级需遍历分组）
     */
    List<TbWxMaterial> selectTbWxMaterialTopList(TbWxMaterial tbWxMaterial);

    Result approve(ApprovalVO approvalVO, LoginUser loginUser);

    Result revoked(ApprovalVO approvalVO, LoginUser loginUser);

    String getBusinessJson(TbWxMaterial tbWxMaterial);
}
