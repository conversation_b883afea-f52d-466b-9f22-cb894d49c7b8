package com.cenker.scrm.service.statistic;


import com.cenker.scrm.pojo.entity.statistic.TbStatisticHotWord;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticSensAct;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticHotWordListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticHotWordSummaryVo;

import java.util.List;

/**
 * 数据统计-热词统计 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
public interface ITbStatisticHotWordService {
    /**
     * 汇总数据
     * @param query
     * @return
     */
    StatisticHotWordSummaryVo summary(StatisticSummaryQuery query);

    /**
     * 图表
     * @param query
     * @return
     */
    List<StatisticGraphVo> graph(StatisticGraphQuery query);

    /**
     * 明细
     * @param query
     * @return
     */
    List<TbStatisticHotWord> list(StatisticHotWordListQuery query);

    /**
     * 保存统计数据
     * @param statDate
     */
    void saveStatisticData(String statDate);
}
