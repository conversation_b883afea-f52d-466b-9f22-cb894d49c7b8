package com.cenker.scrm.service.session;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.session.QryWordAlarmDto;
import com.cenker.scrm.pojo.entity.session.CkSessionSensWordAlarmRecord;

import java.util.List;

/**
 * 热词信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface ICkSessionSensWordAlarmRecordService extends IService<CkSessionSensWordAlarmRecord>
{
    /**
     * 查询敏感词警告记录
     *
     * @param qryWordAlarmDto 热词信息
     * @return 热词信息集合
     */
    public List<CkSessionSensWordAlarmRecord> selectCkSessionSensWordAlarmRecordList(QryWordAlarmDto qryWordAlarmDto);

    /**
     * 插入敏感词触发记录
     * @param beginTime
     * @param endTime
     * @return
     */
    int saveSenWordAlarmRecord(String beginTime, String endTime);

}
