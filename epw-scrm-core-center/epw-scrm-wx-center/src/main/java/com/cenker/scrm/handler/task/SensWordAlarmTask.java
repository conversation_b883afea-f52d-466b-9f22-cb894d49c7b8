package com.cenker.scrm.handler.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordTriggerRecord;
import com.cenker.scrm.pojo.entity.session.CkSessionSensActAlarmRecord;
import com.cenker.scrm.pojo.entity.session.CkSessionSensWordAlarmRecord;
import com.cenker.scrm.service.session.ICkSessionHotWordTriggerRecordService;
import com.cenker.scrm.service.session.ICkSessionSensActAlarmRecordService;
import com.cenker.scrm.service.session.ICkSessionSensWordAlarmRecordService;
import com.cenker.scrm.util.DateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;


@Component
@RequiredArgsConstructor
@Slf4j
public class SensWordAlarmTask {
    private final RedisCache redisCache;
    private final ICkSessionSensWordAlarmRecordService ckSessionSensWordAlarmRecordService;
    private final ICkSessionSensActAlarmRecordService ckSessionSensActAlarmRecordService;
    private final ICkSessionHotWordTriggerRecordService ckSessionHotWordTriggerRecordService;


    @XxlJob(XxlJobContant.CK_SESSION_SENS_ALARM)
    public void statSenWordAlarm() {
        try{
            String beginTimeStr = getBeginTimeStr(CacheKeyConstants.CK_SESSION_STATS_SENS_ALARM_END_TIME);
            String endTimeStr = DateUtil.formatDateTime(new Date());

            String logMsg =  String.format("【定时任务】敏感词、敏感行为触发记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            XxlJobHelper.log(logMsg);
            log.info(logMsg);
            ckSessionSensWordAlarmRecordService.lambdaUpdate()
                    .ge(CkSessionSensWordAlarmRecord::getTriggerTime, beginTimeStr)
                    .le(CkSessionSensWordAlarmRecord::getTriggerTime, endTimeStr).remove();
            logMsg =  String.format("【定时任务】删除敏感词记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            ckSessionSensWordAlarmRecordService.saveSenWordAlarmRecord(beginTimeStr, endTimeStr);
            logMsg =  String.format("【定时任务】新增敏感词记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);

            ckSessionSensActAlarmRecordService.lambdaUpdate()
                    .ge(CkSessionSensActAlarmRecord::getTriggerTime, beginTimeStr)
                    .le(CkSessionSensActAlarmRecord::getTriggerTime, endTimeStr).remove();
            logMsg =  String.format("【定时任务】删除敏感行为记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            ckSessionSensActAlarmRecordService.saveSenActAlarmRecord(beginTimeStr, endTimeStr);
            logMsg =  String.format("【定时任务】新增敏感行为记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            redisCache.setCacheObject(CacheKeyConstants.CK_SESSION_STATS_SENS_ALARM_END_TIME, endTimeStr);
        }catch (Exception e){
            log.error("【定时任务】敏感规则,敏感词警告统计失败",e);
            XxlJobHelper.log("【定时任务】敏感规则,敏感词警告统计失败", e);
        }
    }

    /**
     * 获取开始时间，以上次任务时间往前推2小时
     * @param ckSessionStatsSensAlarmEndTime
     * @return
     */
    private String getBeginTimeStr(String ckSessionStatsSensAlarmEndTime) {
        String lastEndTime = redisCache.getCacheObject(ckSessionStatsSensAlarmEndTime);
        if (StrUtil.isNotBlank(lastEndTime)) {
            Date date = DateUtils.parseDate(lastEndTime);
            // 往前推2小时
            return DateUtil.formatDateTime(DateUtils.addHour(date, -2));
        } else {
            return DateUtil.formatDateTime(DateUtils.addHour(new Date(), -2));
        }

    }

    /**
     * 定时任务：热词触发记录
     * 每10分钟执行一次
     */
    @XxlJob(XxlJobContant.CK_SESSION_HOT_WORD_TRIGGER)
    public void recordHotWordTrigger() {
        try {
            String beginTimeStr = getBeginTimeStr(CacheKeyConstants.RECORD_HOT_WORD_TRIGGER_LAST_END_TIME);
            String endTimeStr = DateUtil.formatDateTime(new Date());

            String logMsg =  String.format("【定时任务】热词触发记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            XxlJobHelper.log(logMsg);
            log.info(logMsg);

            ckSessionHotWordTriggerRecordService.lambdaUpdate()
                    .ge(CkSessionHotWordTriggerRecord::getMsgTime, beginTimeStr)
                   .le(CkSessionHotWordTriggerRecord::getMsgTime, endTimeStr).remove();
            logMsg =  String.format("【定时任务】删除热词触发记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            ckSessionHotWordTriggerRecordService.saveHotWordTriggerRecord(beginTimeStr, endTimeStr);
            logMsg =  String.format("【定时任务】新增热词触发记录,数据开始时间：%s, 数据结束时间：%s", beginTimeStr, endTimeStr);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            redisCache.setCacheObject(CacheKeyConstants.RECORD_HOT_WORD_TRIGGER_LAST_END_TIME, endTimeStr);
        } catch (Exception e) {
            log.error("【定时任务】热词触发记录失败", e);
            XxlJobHelper.log("【定时任务】热词触发记录失败", e);
        }
    }
}
