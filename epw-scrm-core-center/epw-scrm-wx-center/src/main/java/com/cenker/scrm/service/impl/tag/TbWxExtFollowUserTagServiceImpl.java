package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.OperTypeEnum;
import com.cenker.scrm.mapper.external.TbWxExtCustomerMapper;
import com.cenker.scrm.mapper.external.TbWxExtFollowUserTagMapper;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.entity.TbWxCustomerTagLog;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.service.external.TbWxExtFollowUserService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupService;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.service.tag.ITbWxCustomerTagLogService;
import com.cenker.scrm.service.tag.TbWxExtFollowUserTagService;
import com.cenker.scrm.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TbWxExtFollowUserTagServiceImpl extends ServiceImpl<TbWxExtFollowUserTagMapper, TbWxExtFollowUserTag> implements TbWxExtFollowUserTagService {
    @Autowired
    private TbWxExtFollowUserService followUserService;
    @Autowired
    private ITbWxCorpTagService tbWxCorpTagService;
    @Lazy
    @Autowired
    private ITbWxCorpTagGroupService tagGroupService;
    @Autowired
    private TbWxExtCustomerMapper customerMapper;
    @Autowired
    private WxCpFeign wxCpFeign;
    @Autowired
    private ITbWxCustomerTagLogService tbWxCustomerTagLogService;

    @Override
    @Async
    public void syncAllStaffTagByExternalUserId(List<TbWxCorpTag> addTag, WeMakeCustomerTag weMakeCustomerTag) {
        String corpId = weMakeCustomerTag.getCorpId();
        String externalUserId = weMakeCustomerTag.getExternalUserId();
        log.info("【客户画像】1、开始标签协同同步，{},{},{}", corpId, weMakeCustomerTag.getUserId(), externalUserId);
        // 查询所有添加员工(排除已经添加)
        List<TbWxUser> userList = followUserService.listUser4AddExternalCustomerExcludeUserId(weMakeCustomerTag);
        // 当存在其他添加员工时进行
        if (CollectionUtils.isNotEmpty(userList)) {
            log.info("【客户画像】2、存在协同员工，开始更新操作,{}", userList);
            for (TbWxUser tbWxUser : userList) {
                // 原先标签清除
                List<TbWxExtFollowUserTag> dataList = list(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                        .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                        .eq(TbWxExtFollowUserTag::getUserId, tbWxUser.getUserid())
                        .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                        .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE));
                // 剔除标签
                List<String> notTagIdList;
                if (CollectionUtils.isNotEmpty(dataList)) {
                    removeByIds(dataList.stream().map(TbWxExtFollowUserTag::getId).collect(Collectors.toSet()));
                    List<String> notTagIds = dataList.stream().map(TbWxExtFollowUserTag::getTagId).collect(Collectors.toList());
                    // 剔除标签
                    notTagIdList = tbWxCorpTagService.selectTbWxCorpTagGroupIdList(corpId, new ArrayList<>(notTagIds).toArray(new String[]{}));
                    String[] notTagParam = new ArrayList<>(notTagIdList).toArray(new String[]{});
                    log.info("【客户画像】剔除企微客户标签：{}", notTagIdList);
                    try {
                        wxCpFeign.markTag(tbWxUser.getUserid(), externalUserId, new String[]{}, notTagParam, corpId);
                    } catch (Exception e) {
                        log.error("【客户画像】剔除企微客户标签失败：{},错误信息：{}", notTagIdList, e);
                    }
                }
                // 添加企微客户标签
                if (CollectionUtils.isNotEmpty(addTag)) {
                    // 组装添加标签id
                    List<String> tagIds = addTag.stream().map(TbWxCorpTag::getTagId).collect(Collectors.toList());
                    // 查询标签是否已被删除(可能存在被删除的标签会报错)
                    List<String> tagIdList = tbWxCorpTagService.selectTbWxCorpTagGroupIdList(corpId, new ArrayList<>(tagIds).toArray(new String[]{}));

                    if (CollectionUtils.isNotEmpty(tagIdList)) {
                        log.info("【客户画像】添加企微客户标签：{}", tagIdList);
                        try {
                            wxCpFeign.markTag(tbWxUser.getUserid(), externalUserId, new ArrayList<>(tagIdList).toArray(new String[]{}), new String[]{}, corpId);
                        } catch (Exception e) {
                            log.error("【客户画像】添加企微客户标签失败：{}，错误信息：{}", tagIdList, e);
                        }
                    }
                    List<TbWxExtFollowUserTag> list = new ArrayList<>();
                    for (TbWxCorpTag tbWxCorpTag : addTag) {
                        TbWxExtFollowUserTag tbWxExtFollowUserTag = new TbWxExtFollowUserTag();
                        // 查询标签组名 临时
                        String groupName = tagGroupService.selectTbWxCorpTagGroupNameByTagId(tbWxCorpTag.getTagId());
                        if (StringUtils.isNotEmpty(groupName)) {
                            tbWxExtFollowUserTag.setGroupName(groupName);
                        }
                        tbWxExtFollowUserTag.setExternalUserId(externalUserId);
                        tbWxExtFollowUserTag.setCorpId(corpId);
                        tbWxExtFollowUserTag.setType(TypeConstants.CORP_TAG_TYPE);
                        tbWxExtFollowUserTag.setUserId(tbWxUser.getUserid());
                        tbWxExtFollowUserTag.setTag(tbWxCorpTag.getName());
                        tbWxExtFollowUserTag.setTagId(tbWxCorpTag.getTagId());
                        list.add(tbWxExtFollowUserTag);
                    }
                    saveBatch(list);
                }
            }
        }
        log.info("【客户画像】3、开始更新客户表标签,{}", userList);
        if (CollectionUtils.isNotEmpty(addTag)) {
            customerMapper.setTag(JSON.toJSONString(addTag.stream().map(TbWxCorpTag::getName).collect(Collectors.toList())), corpId, externalUserId);
        } else {
            customerMapper.setTag2Empty(weMakeCustomerTag);
        }
    }

    @Override
    public boolean saveBatchWithLog(List<TbWxExtFollowUserTag> entityList) {
        boolean saved = this.saveBatch(entityList, 1000);
        // 组装tagLog,并入库
        Collection<TbWxCustomerTagLog> logList = entityList.stream().map(tag -> {
            return getTbWxCustomerTagLog(tag, CorpInfoProperties.getCorpId(), OperTypeEnum.ADD);
        }).collect(Collectors.toList());
        tbWxCustomerTagLogService.saveBatch(logList);

        return saved;
    }

    @Override
    public void removeByTagIdsWithLog(List<String> tagIds, String externalUserId) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }

        // 查询待删除userTag，方便构建tagLog
        LambdaQueryWrapper<TbWxExtFollowUserTag> wrapper = new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .in(TbWxExtFollowUserTag::getTagId, tagIds)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId);
        removeByWrapperWithLog(wrapper);
    }

    @Override
    public void removeByIdsWithLog(List<String> userTagIds) {
        if (CollectionUtils.isEmpty(userTagIds)) {
            return;
        }
        // 查询待删除userTag，方便构建tagLog
        LambdaQueryWrapper<TbWxExtFollowUserTag> wrapper = new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .in(TbWxExtFollowUserTag::getId, userTagIds);
        removeByWrapperWithLog(wrapper);
    }

    @Override
    public void removeByWrapperWithLog(LambdaQueryWrapper<TbWxExtFollowUserTag> warpper) {
        // 查询待删除userTag，方便构建tagLog
        List<TbWxExtFollowUserTag> removeTags = list(warpper);
        removeUserTagWithLog(removeTags);
    }

    @Override
    public void removeUserTagWithLog(List<TbWxExtFollowUserTag> removeTags) {
        if (CollectionUtils.isEmpty(removeTags)) {
            return;
        }
        // 删除userTag
        remove(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .in(TbWxExtFollowUserTag::getId, removeTags.stream().map(TbWxExtFollowUserTag::getId).collect(Collectors.toSet()))
        );
        // 组装tagLog,并入库
        Collection<TbWxCustomerTagLog> logList = removeTags.stream().map(tag -> {
            return getTbWxCustomerTagLog(tag, CorpInfoProperties.getCorpId(), OperTypeEnum.DEL);
        }).collect(Collectors.toList());
        tbWxCustomerTagLogService.saveBatch(logList);
    }

    private TbWxCustomerTagLog getTbWxCustomerTagLog(TbWxExtFollowUserTag tag, String corpId, OperTypeEnum del) {
        TbWxCustomerTagLog log = TbWxCustomerTagLog.builder()
                .userId(tag.getUserId())
                .externalUserId(tag.getExternalUserId())
                .corpId(corpId)
                .tagId(tag.getTagId())
                .tag(tag.getTag())
                .groupName(tag.getGroupName())
                .type(tag.getType())
                .operType(del.getCode())
                .createBy(Constants.DEFAULT_USER)
                .createTime(DateUtil.date())
                .build();
        return log;
    }
}