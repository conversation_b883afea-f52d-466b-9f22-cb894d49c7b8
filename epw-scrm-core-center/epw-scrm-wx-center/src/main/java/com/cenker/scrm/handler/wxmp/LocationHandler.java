
package com.cenker.scrm.handler.wxmp;

import com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxUser;
import com.cenker.scrm.service.wxmp.WxAppService;
import com.cenker.scrm.service.wxmp.WxUserService;
import com.cenker.scrm.util.wxmp.CorpContextHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class LocationHandler extends AbstractHandler {

	private final WxUserService wxUserService;
	private final WxAppService wxAppService;

	@Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService wxMpService,
                                    WxSessionManager sessionManager) {
        if (wxMessage.getEvent().equals(WxConsts.EventType.LOCATION)) {
            try {
				WxApp wxApp = wxAppService.findByWeixinSign(wxMessage.getToUser());
				CorpContextHolder.setCorpId(wxApp.getCorpConfigId());//加入租户ID
				WxUser wxUser = wxUserService.getByOpenId(wxApp.getId(), wxMessage.getFromUser());
				wxUser.setLatitude(wxMessage.getLatitude());
				wxUser.setLongitude(wxMessage.getLongitude());
				wxUser.setPrecision(wxMessage.getPrecision());
				wxUserService.updateById(wxUser);
                return null;
            } catch (Exception e) {
                log.error("位置消息接收处理失败", e);
                return null;
            }
        }
        return null;
    }

}
