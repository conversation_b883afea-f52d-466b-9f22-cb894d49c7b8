package com.cenker.scrm.handler.task;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.DelayQueueConstant;
import com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageDetail;
import com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageInfo;
import com.cenker.scrm.service.message.ITbWxMassMessageDetailService;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.util.StringUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 停止群发任务
 * @date 2023/7/14 18:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancleSendMsgTask {
    private final RedissonClient redissonClient;
    private final WxCpFeign wxCpFeign;
    private final ITbWxMassMessageDetailService tbWxMassMessageDetailService;
    private final ITbWxMassMessageInfoService tbWxMassMessageInfoService;

    @XxlJob("cancleSendMsgTask")
    public void test() throws WxErrorException {
        RBlockingDeque<String> blockingDeque = redissonClient.getBlockingDeque(DelayQueueConstant.CANCLE_MSG_QUEUE);
        /**
         * peek：获取队列的head对象，但不是从队列中移除。如果队列空，返回空
         * poll ：获取并移出队列head对象，如果head没有超时，返回空
         * take ：获取并移出队列head对象，如果没有超时head对象，会wait当前线程直到有对象满足超时条件
         */
        String msgId = blockingDeque.poll();
        if (StringUtils.isNoneBlank(msgId)) {
            log.info("【群发消息】停止群发拿到了延时队列的：{}", msgId);
            // 停止群发消息
            wxCpFeign.stop(msgId, CorpInfoProperties.getCorpId());
            // 修改群发消息的状态
            TbWxMassMessageDetail detail = tbWxMassMessageDetailService.getOne(Wrappers.lambdaQuery(TbWxMassMessageDetail.class)
                    .eq(TbWxMassMessageDetail::getMsgId, msgId).select(TbWxMassMessageDetail::getMessageInfoId).last("limit 1"));
            if (ObjectUtil.isNotNull(detail)) {
                tbWxMassMessageInfoService.update(Wrappers.lambdaUpdate(TbWxMassMessageInfo.class)
                        .set(TbWxMassMessageInfo::getExpired,true).eq(TbWxMassMessageInfo::getId,detail.getMessageInfoId()));
            }
        }
    }
}
