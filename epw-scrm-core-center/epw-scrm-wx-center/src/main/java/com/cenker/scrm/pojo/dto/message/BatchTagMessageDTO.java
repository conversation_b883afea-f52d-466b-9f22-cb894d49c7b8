package com.cenker.scrm.pojo.dto.message;

import com.cenker.scrm.pojo.dto.external.TagDetailList;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/15
 * @Description 批量打标签消息类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchTagMessageDTO {
    private String nickName; // 管理员 nickname
    private String corpId;
    private String userId; // 选择的打标签的微信客服 id
    private String externalUserId; // 被打标签的客户
    private List<TagDetailList> tagDetailFilterList; // 实际打的标签
    private List<TbWxExtCustomer> customers; // tb_wx_ext_customer 表数据
    // 操作标签类型： 添加-BIND， 移除-UNBIND
    private String tagType;


}
