package com.cenker.scrm.service.impl.drainage;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.drainage.TbWxDrainageLinkClickMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxDrainageLinkClick;
import com.cenker.scrm.pojo.entity.wechat.TbWxDrainageShortLink;
import com.cenker.scrm.service.drainage.ITbWxDrainageLinkClickService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/5/9
 * @Description
 */
@Service
public class TbWxDrainageLinkClickServiceImpl extends ServiceImpl<TbWxDrainageLinkClickMapper, TbWxDrainageLinkClick> implements ITbWxDrainageLinkClickService {
    @Override
    @Async
    public void recordClickDataByDrainageShortLink(TbWxDrainageShortLink drainageShortLink, String ipAddress) {
        if (!StringUtils.isBlank(ipAddress)) {

            Date nowDate = DateUtils.getNowDate();
            Date todayStart = DateUtils.getTodayStart();
    /*    // 查询今天ip是否点击过该短链 有则不做记录
            TbWxDrainageLinkClick ipRecord = getOne(new LambdaQueryWrapper<TbWxDrainageLinkClick>()
                    .eq(TbWxDrainageLinkClick::getClickDate, todayStart)
                    .eq(TbWxDrainageLinkClick::getDrainageId, drainageShortLink.getId())
                    .eq(TbWxDrainageLinkClick::getIp, ipAddress)
                    .last("limit 1")
            );
            if (ipRecord == null) {
                TbWxDrainageLinkClick tbWxDrainageLinkClick = new TbWxDrainageLinkClick();
                tbWxDrainageLinkClick.setIp(ipAddress);
                tbWxDrainageLinkClick.setDrainageId(drainageShortLink.getId());
                tbWxDrainageLinkClick.setClickDate(todayStart);
                tbWxDrainageLinkClick.setClickAt(nowDate.getTime()/1000);
                save(tbWxDrainageLinkClick);
            }*/
            TbWxDrainageLinkClick tbWxDrainageLinkClick = new TbWxDrainageLinkClick();
            tbWxDrainageLinkClick.setIp(ipAddress);
            tbWxDrainageLinkClick.setDrainageId(drainageShortLink.getId());
            tbWxDrainageLinkClick.setClickDate(todayStart);
            tbWxDrainageLinkClick.setClickAt(nowDate.getTime() / 1000);
            save(tbWxDrainageLinkClick);
        }
    }
}
