package com.cenker.scrm.service.impl.material;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.material.TbWxMaterialMapper;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.dto.material.WeMediaDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.material.ITbWxMaterialService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.SnowflakeIdUtil;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.UrlReplaceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.cp.api.WxCpExternalContactService;
import me.chanjar.weixin.cp.api.WxCpMediaService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 素材信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TbWxMaterialServiceImpl extends ServiceImpl<TbWxMaterialMapper, TbWxMaterial> implements ITbWxMaterialService {

    private final TbWxMaterialMapper tbWxMaterialMapper;
    /**
     * 企微接口注入优化
     */
    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    // private final QcloudCosUtils qcloudCosUtils;
    private final RedisCache redisCache;
    private final IApprovalService approvalService;
    private final RedissonClient redissonClient;

    /**
     * 查询素材信息
     *
     * @param id 素材信息ID
     * @return 素材信息
     */
    @Override
    public TbWxMaterial selectTbWxMaterialById(Long id) {
        return tbWxMaterialMapper.selectTbWxMaterialById(id);
    }

    /**
     * 查询素材信息列表
     *
     * @return 素材信息
     */
    @Override
    public List<TbWxMaterial> selectTbWxMaterialList(MaterialListDto param) {
        List<TbWxMaterial> tbWxMaterials = tbWxMaterialMapper.selectTbWxMaterialList(param);
        dealData(param, tbWxMaterials);
        return tbWxMaterials;
    }

    private void dealData(MaterialListDto dto, List<TbWxMaterial> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 处理 canApproval 字段
        // 是审批人，且不是创建人
        ApprovalTypeEnum typeEnum = ApprovalTypeEnum.MATERIAL;

        Result<Boolean> approvalUser = approvalService.isApprovalUser(typeEnum.getType());
        if (approvalUser.isSuccess() && approvalUser.getData()) {
            boolean isApproval = approvalUser.getData();
            for (TbWxMaterial vo : list) {
                boolean canApproval = vo.isEnableApproval() && isApproval
                        && !vo.getCreateBy().equals(dto.getUserId())
                        && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(vo.getStatus());
                vo.setCanApproval(canApproval);
            }
        }
    }

    /**
     * 新增素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    @Override
    public int insertTbWxMaterial(TbWxMaterial tbWxMaterial) {
        tbWxMaterial.setCreateTime(DateUtils.getNowDate());
        if (StringUtils.isEmpty(tbWxMaterial.getId())) {
            tbWxMaterial.setId(SnowflakeIdUtil.getSnowIdStr());
        }

        setMaterialSize(tbWxMaterial);
        this.save(tbWxMaterial);
        return 1;
    }

    /**
     * 修改素材信息
     *
     * @param tbWxMaterial 素材信息
     * @return 结果
     */
    @Override
    public int updateTbWxMaterial(TbWxMaterial tbWxMaterial) {
        tbWxMaterial.setUpdateTime(DateUtils.getNowDate());
        setMaterialSize(tbWxMaterial);
        this.updateById(tbWxMaterial);
        return 1;
    }

    /**
     * 批量删除素材信息
     *
     * @param ids 需要删除的素材信息ID
     * @return 结果
     */
    @Override
    public int deleteTbWxMaterialByIds(Long[] ids) {
        return tbWxMaterialMapper.deleteTbWxMaterialByIds(ids);
    }

    /**
     * 删除素材信息信息
     *
     * @param id 素材信息ID
     * @return 结果
     */
    @Override
    public int deleteTbWxMaterialById(Long id) {
        return tbWxMaterialMapper.deleteTbWxMaterialById(id);
    }

    /**
     * @param picUrl   图片地址
     * @param mediaType 媒体类型
     * @param fileType  文件类型
     * @param corpId    企业id
     * @return 获取素材
     */
    @Override
    public WeMediaDTO uploadTemporaryMaterial(String picUrl, String mediaType, String fileType, String corpId) {
        return uploadTemporaryMaterial(picUrl, mediaType, fileType, corpId, false);
    }
    /**
     * @param picUrl   图片地址
     * @param mediaType 媒体类型
     * @param fileType  文件类型
     * @param corpId    企业id
     * @param forceUpload 跳过redis缓存，强制上传至微信 ，用于页面强制刷新的情况
     * @return 获取素材
     */
    @Override
    public WeMediaDTO uploadTemporaryMaterial(String picUrl, String mediaType, String fileType, String corpId, boolean forceUpload) {
        // 域名处理
        picUrl = UrlReplaceUtils.urlReplace(picUrl);

        String cacheKey = CacheKeyConstants.MATERIAL_PREFIX + picUrl;
        if(!forceUpload && redisCache.isKey(cacheKey)){
            WeMediaDTO weMediaDto = redisCache.getCacheObject(cacheKey);
            return weMediaDto;
        }

        // 获取企微接口对象
        WxCpServiceImpl service = workCorpCpService.getWxCpCustomerServiceByCorpId(corpId);
        WxCpMediaService mediaService = service.getMediaService();

        HttpURLConnection conn = null;
        try {
            URL materialUrl = new URL(picUrl);
            conn = (HttpURLConnection) materialUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(20 * 1000);
            InputStream inputStream = conn.getInputStream();
            WxMediaUploadResult upload = mediaService.upload(mediaType, fileType, inputStream);

            WeMediaDTO weMediaDto = new WeMediaDTO();
            weMediaDto.setCreated_at(upload.getCreatedAt());
            weMediaDto.setMedia_id(upload.getMediaId());
            weMediaDto.setType(upload.getType());

            // 设置缓存，缓存2天
            redisCache.setCacheObject(cacheKey, weMediaDto, 2, TimeUnit.DAYS);

            return weMediaDto;
        } catch (Exception e) {
            log.error("上传临时素材：corpId={}, picUrl={}, fileType={}", corpId, picUrl, fileType);
            log.error("上传临时素材：", e);
        } finally {
            if (conn != null) {
                conn = null;
            }
        }
        return null;
    }

    /**
     * 上传附件朋友圈或商品相册
     *
     * @param baseUrl        对象存储链接
     * @param mediaType      image video link
     * @param picName        文件名
     * @param attachmentType 1 朋友圈
     * @param corpId         企业id
     * @return
     * @throws IOException
     */
    @Override
    public WeMediaDTO uploadTemporaryMaterial(String baseUrl, String mediaType, String picName, Integer attachmentType, String corpId) throws IOException {
        WxCpServiceImpl service = workCorpCpService.getWxCpCustomerServiceByCorpId(corpId);
        WxCpExternalContactService externalContactService = service.getExternalContactService();

        // 域名处理
        baseUrl = UrlReplaceUtils.urlReplace(baseUrl);

        HttpURLConnection conn = null;
        InputStream inputStream = null;
        try {
            conn = (HttpURLConnection) new URL(baseUrl).openConnection();
            conn.setConnectTimeout(20 * 1000);
            conn.setRequestMethod("GET");
            inputStream = conn.getInputStream();
            WxMediaUploadResult upload = externalContactService.uploadAttachment(mediaType, attachmentType, inputStream);

            WeMediaDTO weMediaDto = new WeMediaDTO();
            weMediaDto.setCreated_at(upload.getCreatedAt());
            weMediaDto.setMedia_id(upload.getMediaId());
            weMediaDto.setType(upload.getType());
            return weMediaDto;
        } catch (Exception e) {
            log.error("上传临时素材：corpId={}, mediaType={}, baseUrl={}, attachmentType={}",
                    corpId, mediaType, baseUrl, attachmentType);
            log.error("上传临时素材：", e);

            WeMediaDTO weMediaDto = new WeMediaDTO();
            if (e.getMessage().contains(ErrCodeEnum.IMAGE_FORMAT_ERROR.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.IMAGE_FORMAT_ERROR.getMessage());
            } else if (e.getMessage().contains(ErrCodeEnum.IMAGE_RESOLUTION_ERROR.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.IMAGE_RESOLUTION_ERROR.getMessage());
            } else if (e.getMessage().contains(ErrCodeEnum.VIDEO_LENGTH_EXCEEDS_LIMIT.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.VIDEO_LENGTH_EXCEEDS_LIMIT.getMessage());
            } else if (e.getMessage().contains(ErrCodeEnum.VIDEO_FORMAT_INVALID.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.VIDEO_FORMAT_INVALID.getMessage());
            } else if (e.getMessage().contains(ErrCodeEnum.FILE_SIZE_EXCEEDS_LIMIT.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.FILE_SIZE_EXCEEDS_LIMIT.getMessage());
            } else if (e.getMessage().contains(ErrCodeEnum.VOICE_PLAYBACK_EXCEEDS_LIMIT.getCode().toString())) {
                weMediaDto.setMsg(ErrCodeEnum.VOICE_PLAYBACK_EXCEEDS_LIMIT.getMessage());
            } else {
                weMediaDto.setMsg(ErrCodeEnum.FILE_FORMAT_ERROR.getMessage());
            }
            return weMediaDto;
        } finally {
            if (conn != null) {
                conn = null;
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    @Override
    public List<TbWxMaterial> selectTbWxMaterialTopList(TbWxMaterial tbWxMaterial) {
        return tbWxMaterialMapper.selectTbWxMaterialTopList(tbWxMaterial);
    }

    /**
     * 获取素材大小尺寸
     *
     * @param tbWxMaterial
     */
    private void setMaterialSize(TbWxMaterial tbWxMaterial) {
        if (StrUtil.isBlank(tbWxMaterial.getMaterialUrl())) {
            return;
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HttpUtil.download(tbWxMaterial.getMaterialUrl(), outputStream, false);
        tbWxMaterial.setSize(Long.valueOf(outputStream.size()));

        // 获取图片尺寸
        if (0 == tbWxMaterial.getMediaType()) {
            BufferedImage image = ImgUtil.read(new ByteArrayInputStream(outputStream.toByteArray()));
            tbWxMaterial.setWidth(image.getWidth());
            tbWxMaterial.setHeight(image.getHeight());
        }
    }

    @Override
    public Result approve(ApprovalVO approvalVO, LoginUser loginUser) {
        String approvalId = approvalVO.getId();
        TbWxMaterial info = this.getById(approvalId);
        if (info == null) {
            return Result.error(500, "未找到营销素材信息！");
        }
        log.info("【审核营销素材】审核对象：{}", JSON.toJSONString(info));
        String type = ApprovalTypeEnum.MATERIAL.getType();
        String key = approvalService.getApprovalKey(approvalId, type);
        RLock lock = redissonClient.getLock(key);
        try {
            boolean cacheRes = lock.tryLock(5L, 30L, TimeUnit.SECONDS);
            if (!cacheRes) {
                return Result.error(500, "获取锁失败，其他用户正在执行审核操作！");
            }
            log.info("【审核营销素材】加锁成功：{}", key);
            if (!Objects.equals(0, info.getMediaType())) {
                return Result.error(500, "非图片素材不支持审核操作！");
            }
            Result<Object> validateApproval = approvalService.validateApproval(loginUser, type, info.getMaterialName(), info.getCreateBy()+"", info.getStatus());
            if (validateApproval != null) return validateApproval;
            if (Objects.equals(approvalVO.getAgree(), true)) {
                this.lambdaUpdate()
                        .set(TbWxMaterial::getStatus, ApprovalStatusEnum.EXECUTING.getStatus())
                        .set(TbWxMaterial::getApprovalUser, loginUser.getUser().getUserId())
                        .set(TbWxMaterial::getApprovalRemark, approvalVO.getRemark())
                        .eq(TbWxMaterial::getId, approvalId).update();
                log.info("【审核营销素材】审核通过，更新为使用中状态");

                // 发送通知
                approvalService.sendApprovalMsg(info, type, true,
                        TbWxMaterial::getMaterialName,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString(), false, getBusinessJson(info));
                log.info("【审核营销素材】审核通过，发送通知完成");
            } else {
                // 如果为 null 或者 "" 则替换为 "内容未满足审核标准，请与审核员沟通后修改并重新提交"
                if (StrUtil.isEmptyIfStr(approvalVO.getRemark())) {
                    approvalVO.setRemark("内容未满足审核标准，请与审核员沟通后修改并重新提交");
                }
                this.lambdaUpdate()
                        .set(TbWxMaterial::getStatus, ApprovalStatusEnum.REJECTED.getStatus())
                        .set(TbWxMaterial::getApprovalUser, loginUser.getUser().getUserId())
                        .set(TbWxMaterial::getApprovalRemark, approvalVO.getRemark())
                        .eq(TbWxMaterial::getId, approvalId).update();
                // 发送通知
                approvalService.sendApprovalMsg(info, type, false,
                        TbWxMaterial::getMaterialName,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString(), false, getBusinessJson(info));
                log.info("【审核营销素材】审核驳回，更新数据库后，发送通知完成");
            }
        } catch (InterruptedException e) {
            log.error("【审核营销素材】获取锁失败！", e);
            return Result.error(500, "获取锁失败！");
        } catch (Exception e1) {
            log.error("【审核营销素材】审批异常：{}", e1.getMessage(), e1);
            return Result.error(500, e1.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("【审核营销素材】释放锁成功！");
            }
        }

        return Result.success();
    }

    @Override
    public Result revoked(ApprovalVO approvalVO, LoginUser loginUser) {
        TbWxMaterial info = this.getById(approvalVO.getId());
        if (info == null) {
            return Result.error(500, "未找到审核营销素材信息！");
        }
        log.info("【撤回审核营销素材】撤回对象：{}", JSON.toJSONString(info));
        if (!Objects.equals(0, info.getMediaType())) {
            return Result.error(500, "非图片素材不支持撤回操作！");
        }
        if (!info.isEnableApproval()) {
            return Result.error(500, "该任务不支持撤回操作！");
        }
        if (!Objects.equals(info.getStatus(), ApprovalStatusEnum.PENDING_APPROVAL.getStatus())) {
            return Result.error(500, "不是待审核状态不支持撤回操作！");
        }
        if (!Objects.equals(loginUser.getUser().getUserId(), info.getCreateBy()+"")) {
            return Result.error(500, "用户不能对其他人创建的内容进行撤回！");
        }
        log.info("【撤回审核营销素材】检验完成");
        this.lambdaUpdate()
                .set(TbWxMaterial::getStatus, ApprovalStatusEnum.REVOKED.getStatus())
                .eq(TbWxMaterial::getId, approvalVO.getId())
                .eq(TbWxMaterial::getStatus, ApprovalStatusEnum.PENDING_APPROVAL.getStatus()).update();
        log.info("【撤回审核营销素材】更新为已撤回状态");
        return Result.success();
    }

    @Override
    public String getBusinessJson(TbWxMaterial info) {
        if (info!= null && info.isEnableApproval()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("status", info.getStatus());
            jsonObject.put("materialName", info.getMaterialName());
            jsonObject.put("mediaType", info.getMediaType());
            return jsonObject.toJSONString();
        }
        return "{}";
    }
}
