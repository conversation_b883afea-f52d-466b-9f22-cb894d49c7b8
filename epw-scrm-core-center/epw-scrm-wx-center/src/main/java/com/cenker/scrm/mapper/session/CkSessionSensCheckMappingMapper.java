package com.cenker.scrm.mapper.session;

import java.util.List;

import com.cenker.scrm.pojo.entity.session.CkSessionSensCheckMapping;

/**
 * 规则审计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface CkSessionSensCheckMappingMapper 
{
    /**
     * 查询规则审计
     * 
     * @param id 规则审计主键
     * @return 规则审计
     */
    public CkSessionSensCheckMapping selectCkSessionSensCheckMappingById(Long id);

    /**
     * 查询规则审计列表
     * 
     * @param ckSessionSensCheckMapping 规则审计
     * @return 规则审计集合
     */
    public List<CkSessionSensCheckMapping> selectCkSessionSensCheckMappingList(CkSessionSensCheckMapping ckSessionSensCheckMapping);

    /**
     * 新增规则审计
     * 
     * @param ckSessionSensCheckMapping 规则审计
     * @return 结果
     */
    public int insertCkSessionSensCheckMapping(CkSessionSensCheckMapping ckSessionSensCheckMapping);

    /**
     * 修改规则审计
     * 
     * @param ckSessionSensCheckMapping 规则审计
     * @return 结果
     */
    public int updateCkSessionSensCheckMapping(CkSessionSensCheckMapping ckSessionSensCheckMapping);

    /**
     * 删除规则审计
     * 
     * @param id 规则审计主键
     * @return 结果
     */
    public int deleteCkSessionSensCheckMappingById(Long id);
    public int deleteCkSessionSensCheckMappingByRuleId(Long ruleId);

    /**
     * 批量删除规则审计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCkSessionSensCheckMappingByIds(Long[] ids);
}
