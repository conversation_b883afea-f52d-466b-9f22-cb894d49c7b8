package com.cenker.scrm.controller.sop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.biz.sop.MassCustomerSopBiz;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.DateStyle;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.condition.SopMassSelectDTO;
import com.cenker.scrm.pojo.dto.sop.SopMassCustomerChainDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.request.sop.BaseSopCustomerRequest;
import com.cenker.scrm.pojo.request.sop.ConditionSopQueryRequest;
import com.cenker.scrm.pojo.request.sop.MassCustomerSopQueryRequest;
import com.cenker.scrm.pojo.request.sop.MassCustomerSopRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.message.MassMessageContentVO;
import com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO;
import com.cenker.scrm.pojo.vo.sop.*;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.service.sop.ISopContentInfoService;
import com.cenker.scrm.service.sop.ISopCustomerInfoService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description 1V1sop控制层
 */
@RestController
@RequestMapping("/sop/massCustomer")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MassCustomerSopController extends BaseController {

    private final MassCustomerSopBiz massCustomerSopBiz;
    private final ISopContentInfoService sopContentInfoService;
    private final ITbWxMassMessageInfoService tbWxMassMessageInfoService;
    private final ISopCustomerInfoService sopCustomerInfoService;
    private final TokenParseUtil tokenService;

    @RequestMapping("/addMassCustomerSop")
    public AjaxResult add(@RequestBody MassCustomerSopRequest request) {
        log.info("【新增1V1sop】添加事件接收json:{}", JSON.toJSONString(request));
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, request);
        // 校验是否启用了审批流程
        // 获取用户所在部门是否启用审批
        TbWxCorpConfig config = massCustomerSopBiz.getApprovalService().getTbWxCorpConfig(loginUser.getUser().getCorpId());
        boolean isApproval = getEnableApproval(loginUser.getDeptId()+"", config);
        request.setEnableApproval(isApproval);
        log.info("新增1V1sop】创建1V1SOP：设置是否需要审批：{}", isApproval);
        request.setSopType(TypeConstants.SOP_TYPE_OF_MASS_CUSTOMER);
        massCustomerSopBiz.saveSopInfo(request);
        log.info("【新增1V1sop】保存 sopInfo");
        // 条件信息存储
        massCustomerSopBiz.saveSopConditionInfo(request);
        log.info("【新增1V1sop】保存 sopConditionInfo");
        // 创建时先筛选好客户
        massCustomerSopBiz.startMassCustomerSopScan(request);
        log.info("【新增1V1sop】筛选客户");
        // 保存内容信息
        massCustomerSopBiz.saveSopContentInfo(request,false);
        log.info("【新增1V1sop】保存 sopContentInfo");

        // 发送消息
        massCustomerSopBiz.getApprovalService().sendPendingApprovalMsg(request.getSopName(), String.valueOf(request.getSopId()),
                ApprovalTypeEnum.ONESEPARATEONE, request.isEnableApproval(), request.getAlive(), request.getCreateBy()+"");
        log.info("【新增1V1sop】发送消息");
        return AjaxResult.success();
    }

    @RequestMapping("/listMassCustomerSop")
    public TableDataInfo<ConditionSopListVO> list(@RequestBody MassCustomerSopRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, request);
        request.setSopType(TypeConstants.SOP_TYPE_OF_MASS_CUSTOMER);

        startPage();
        log.info("【查询1V1sop列表】入参：{}", JSON.toJSONString(request));
        List<ConditionSopListVO> list = massCustomerSopBiz.listMassCustomerSop(request);
        log.info("【查询1V1sop列表】出参：{}", JSON.toJSONString(list));
        massCustomerSopBiz.dealData(list, loginUser, ApprovalTypeEnum.ONESEPARATEONE);
        log.info("【查询1V1sop列表】处理能否审批");
        return getDataTable(list);
    }

    @RequestMapping("/updateMassCustomerSop")
    public AjaxResult update(@RequestBody MassCustomerSopRequest request) {
        log.info("【修改1V1sop】编辑事件接收json:{}", JSON.toJSONString(request));
        // 校验sop状态 如未停用不可编辑
        massCustomerSopBiz.validSopAliveStatus(request);
        log.info("【修改1V1sop】校验 sop 状态");
        // 基础信息更新
        request.setSopType(TypeConstants.SOP_TYPE_OF_MASS_CUSTOMER);
        massCustomerSopBiz.saveSopInfo(request);
        log.info("【修改1V1sop】保存 sopInfo");
        // 内容序列更新
        massCustomerSopBiz.saveSopContentInfo(request, true);
        log.info("【修改1V1sop】保存 sopContentInfo");
        // 发送消息
        massCustomerSopBiz.getApprovalService().sendPendingApprovalMsg(request.getSopName(), String.valueOf(request.getSopId()),
                ApprovalTypeEnum.ONESEPARATEONE, request.isEnableApproval(), request.getAlive(), request.getCreateBy()+"");
        log.info("【修改1V1sop】发送消息");
        return AjaxResult.success();
    }

    @RequestMapping("/removeMassCustomerSop")
    public AjaxResult remove(@RequestBody MassCustomerSopRequest request) {
        request.setSopType(TypeConstants.SOP_TYPE_OF_MASS_CUSTOMER);
        // 校验sop状态 如未停用不可删除
        massCustomerSopBiz.validSopAliveStatus(request);
        // 删除内容序列及sop信息
        massCustomerSopBiz.removeSopContent(request);
        massCustomerSopBiz.removeSopInfo(request);
        // 删除定时任务?
        return AjaxResult.success();
    }

    @RequestMapping("/detailMassCustomerSop")
    public Result<MassCustomerSopDetailVO> detail(@RequestBody MassCustomerSopRequest request) {
        log.info("【1V1sop】查询详情入参：{}", JSON.toJSONString(request));
        MassCustomerSopDetailVO massCustomerSopDetailVO = massCustomerSopBiz.detailMassCustomerSop(request);
        massCustomerSopBiz.dealData(massCustomerSopDetailVO, tokenService.getLoginUser(ServletUtils.getRequest()), ApprovalTypeEnum.ONESEPARATEONE);
        log.info("【1V1sop】查询详情出参：{}", JSON.toJSONString(massCustomerSopDetailVO));
        return Result.success("操作成功", massCustomerSopDetailVO);
    }

    @RequestMapping("/changeMassCustomerSopStatus")
    @RedisLockAspect(key = CacheKeyConstants.SOP_MASS_CUSTOMER_UPDATE, value = "#request.sopId", waitTime = 20)
    public AjaxResult changeStatus(@RequestBody MassCustomerSopRequest request) {
        MassCustomerSopDetailVO massCustomerSopDetailVO = massCustomerSopBiz.detailMassCustomerSop(request);

        if (request.isEnableSop()) {
            request.setAlive(ApprovalStatusEnum.EXECUTING.getStatus());
        } else {
            request.setAlive(ApprovalStatusEnum.EXEC_INTERRUPTED.getStatus());
        }
        String statusDesc = ApprovalStatusEnum.isRunning(request.getAlive()) ? "启用" : "停用";
        LogUtil.logOperDesc(StrUtil.format("{}【{}】", statusDesc, massCustomerSopDetailVO.getSopName()));

        if (ApprovalStatusEnum.isRunning(request.getAlive())) {
            // 启用
            // 启用sop内容序列
            if (massCustomerSopDetailVO.isEnableApproval()) {
                if (!ApprovalStatusEnum.isStoped(massCustomerSopDetailVO.getAlive())) {
                    throw new CustomException(ErrCodeEnum.SOP_STATUS_START_ERROR);
                }
            } else {
                if (ApprovalStatusEnum.isRunning(massCustomerSopDetailVO.getAlive())) {
                    throw new CustomException(ErrCodeEnum.SOP_REPEAT_START_ERROR);
                }
            }
            massCustomerSopBiz.createOrStartContentTask(massCustomerSopDetailVO);
            // 修改sopInfo的状态
            massCustomerSopBiz.changeSopStatus(request);
            return AjaxResult.success();
        }
        // 停用
        if (massCustomerSopDetailVO.isEnableApproval()) {
            if (!ApprovalStatusEnum.isRunning(massCustomerSopDetailVO.getAlive())) {
                throw new CustomException(ErrCodeEnum.SOP_STATUS_STOP_ERROR);
            }
        } else {
            if (ApprovalStatusEnum.isStoped(massCustomerSopDetailVO.getAlive())) {
                throw new CustomException(ErrCodeEnum.SOP_REPEAT_STOP_ERROR);
            }
        }

        // 停用sop内容序列
        massCustomerSopBiz.stopContentTask(massCustomerSopDetailVO);
        // 修改sopInfo的状态
        massCustomerSopBiz.changeSopStatus(request);
        return AjaxResult.success();
    }

    @RequestMapping("/sopTaskList")
    public TableDataInfo sopTaskList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        request.setCalculateDay(false);
        List<ConditionSopTaskDataVO> list = sopContentInfoService.sopTaskList(request);
        return getDataTable(list);
    }

    @RequestMapping("/remindToSend")
    @SuppressWarnings(value = "Duplicates")
    public AjaxResult remindToSend(@RequestBody ConditionSopQueryRequest request) {
        MassMessageSenderListVO massMessageSenderListVO = new MassMessageSenderListVO();
        if (StringUtils.isNotEmpty(request.getUserId())) {
            MassMessageContentVO massMessageContentVO = tbWxMassMessageInfoService.getMassMessageInfoByRecordId(request.getMsgId());
            if (ObjectUtil.isNull(massMessageContentVO)) {
                log.error("【1V1sop】提醒成员失败，未找到对应消息信息，请求原文：{}", request);
                return AjaxResult.success();
            }
            String stopTime = ObjectUtil.isNotNull(massMessageContentVO.getStopTime()) ? DateUtils.getDate(massMessageContentVO.getStopTime(), DateStyle.YYYY_MM_DD_HH_MM) : "无";
            String content = "【1V1sop 群发任务提醒】\n你有一条群发消息还未发送\n任务触发时间："
                    + DateUtils.getDate(massMessageContentVO.getCreateTime(), DateStyle.YYYY_MM_DD_HH_MM) +
                    "\n任务截止时间：" + stopTime + "\n发送地址：工作台>客户联系与管理>群发助手中发送";
            massMessageSenderListVO.setUserId(request.getUserId());
            List<MassMessageSenderListVO> list = Lists.newArrayList(massMessageSenderListVO);
            tbWxMassMessageInfoService.remindUser2SendMessage(CorpInfoProperties.getCorpId(), list, content);
            return AjaxResult.success();
        }
        // 提醒全部要找出sop下所有未完成的
        request.setExecuteStatus(TypeConstants.SOP_TASK_EXECUTE_STATUS_UNFINISHED);
        List<ConditionSopDataVO> list = sopContentInfoService.taskExecuteList(request);
        if (CollectionUtils.isNotEmpty(list)) {
            // 这里应该拆分任务来循环 因为临时修改发送的文案 后续优化
            for (ConditionSopDataVO conditionSopDataVO : list) {
                MassMessageContentVO massMessageContentVO = tbWxMassMessageInfoService.getMassMessageInfoByRecordId(conditionSopDataVO.getMsgId());
                if (ObjectUtil.isNull(massMessageContentVO)) {
                    log.error("【1V1sop】提醒成员失败，未找到对应消息信息，请求原文：{}", request);
                    continue;
                }
                String executeTime = DateUtils.getDate(massMessageContentVO.getCreateTime(), DateStyle.YYYY_MM_DD_HH_MM);
                String stopTime = ObjectUtil.isNotNull(massMessageContentVO.getStopTime()) ? DateUtils.getDate(massMessageContentVO.getStopTime(), DateStyle.YYYY_MM_DD_HH_MM) : "无";
                String content = "【1V1sop 群发任务提醒】\n你有一条群发消息还未发送\n任务触发时间：" + executeTime + "\n任务截止时间：" + stopTime + "\n发送地址：工作台>客户联系与管理>群发助手中发送";
                massMessageSenderListVO.setUserId(conditionSopDataVO.getUserId());
                tbWxMassMessageInfoService.remindUser2SendMessage(CorpInfoProperties.getCorpId(), Lists.newArrayList(massMessageSenderListVO), content);
            }
        }
        return AjaxResult.success();
    }

    @RequestMapping("/sopCustomerList")
    public TableDataInfo sopCustomerList(@RequestBody MassCustomerSopQueryRequest request) {
        startPage();
        List<MassCustomerSopCustomerVO> list = sopCustomerInfoService.massCustomerSopCustomerList(request);
        return getDataTable(list);
    }

    @RequestMapping("/neverSopCustomer")
    public TableDataInfo neverSopCustomer(@RequestBody MassCustomerSopQueryRequest request) {
        startPage();
        List<ExternalSopCustomerVO> list = sopCustomerInfoService.neverSopCustomer(request);
        return getDataTable(list);
    }

    @RequestMapping("/addSopCustomer")
    public AjaxResult addSopCustomer(@RequestBody BaseSopCustomerRequest request) {
        sopCustomerInfoService.addSopCustomer(request);
        return AjaxResult.success();
    }

    @RequestMapping("/removeSopCustomer")
    public AjaxResult removeSopCustomer(@RequestBody BaseSopCustomerRequest request) {
        sopCustomerInfoService.removeSopCustomer(request);
        return AjaxResult.success();
    }

    @RequestMapping("/getMessagePredictedNum")
    public AjaxResult getMessagePredictedNum(@RequestBody SopMassSelectDTO sopMassSelectDTO){
        SopMassCustomerChainDTO sopMassCustomerChainDTO = sopCustomerInfoService.getMessagePredictedNum(sopMassSelectDTO);
        // 清空筛选客户结果不返回
        sopMassCustomerChainDTO.setCustomerList(null);
        return AjaxResult.success(sopMassCustomerChainDTO);
    }

    @RequestMapping("/synchronizeData")
    public AjaxResult synchronizeData(@RequestBody MassCustomerSopQueryRequest request){
        // 基础信息更新
        request.setSopType(TypeConstants.SOP_TYPE_OF_MASS_CUSTOMER);
        massCustomerSopBiz.synchronizeData(request);
        return AjaxResult.success();
    }
    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO) {
        log.info("【审核sop】审核1V1sop开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return massCustomerSopBiz.approve(approvalVO, loginUser);
    }
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO) {
        log.info("【撤回sop】撤回1V1sop开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return massCustomerSopBiz.revoked(approvalVO, loginUser);
    }
}
