package com.cenker.scrm.service.external;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.TStaff;

/**
* <AUTHOR>
* @description 针对表【t_staff(内部人员名单)】的数据库操作Service
* @createDate 2024-06-20 14:51:47
*/
public interface ITStaffService extends IService<TStaff> {

    /**
     * 根据客户号判断是否内部员工，1表示内部员工，0表示客户
     * @param custNo
     * @return
     */
    String getInternalEmployeeTag(String custNo);

}
