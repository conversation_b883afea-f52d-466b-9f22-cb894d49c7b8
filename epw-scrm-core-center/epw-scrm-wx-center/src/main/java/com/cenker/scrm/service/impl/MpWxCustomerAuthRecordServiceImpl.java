package com.cenker.scrm.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.entity.MpWxCustomerAuthRecord;
import com.cenker.scrm.mapper.MpWxCustomerAuthRecordMapper;
import com.cenker.scrm.service.IMpWxCustomerAuthRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class MpWxCustomerAuthRecordServiceImpl extends ServiceImpl<MpWxCustomerAuthRecordMapper, MpWxCustomerAuthRecord> implements IMpWxCustomerAuthRecordService {
    @Override
    public List<MpWxCustomerAuthRecord> selectAuthRecoreds(Date lastDate, Date now) {
        String lastDateStr = DateUtil.formatDateTime(lastDate);
        String nowStr = DateUtil.formatDateTime(now);
        return baseMapper.selectAuthRecoreds(lastDateStr, nowStr);
    }
}