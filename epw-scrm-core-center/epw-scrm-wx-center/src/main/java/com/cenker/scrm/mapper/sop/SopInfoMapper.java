package com.cenker.scrm.mapper.sop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.sop.SopInfo;
import com.cenker.scrm.pojo.request.sop.*;
import com.cenker.scrm.pojo.vo.journey.ExternalJourneyStageVO;
import com.cenker.scrm.pojo.vo.sop.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description sop基本信息
 */
public interface SopInfoMapper extends BaseMapper<SopInfo> {
    /**
     * 查询sop列表
     * @param request 请求参数
     * @return 条件sop列表
     */
    List<ConditionSopListVO> listConditionSop(BaseSopRequest request);

    /**
     * 查询条件sop详情（回显）
     * @param request 请求参数
     * @return 条件sop详情
     */
    ConditionSopDetailVO detailConditionSop(ConditionSopRequest request);

    /**
     * 查询总任务数
     * @param sopId sopId
     * @return 总任务数
     */
    Integer queryTotalTaskCntBySopId(@Param("sopId") Long sopId);

    /**
     * 查询完成任务数
     * @param sopId
     * @return
     */
    Integer queryCompleteTaskCntBySopId(@Param("sopId") Long sopId);

    /**
     * 查询条件sop数据概览
     * @param request
     * @return
     */
    ConditionSopDataStatisticsVO getDataStatistics(ConditionSopQueryRequest request);

    /**
     * 查询流失客户数
     * @param sopId
     * @return
     */
    Integer queryDelCustomerCntBySopId(@Param("sopId") Long sopId);

    /**
     * 查询成功触达客户消息数
     * @param sopId
     * @return
     */
    Integer querySendCustomerCntBySopId(@Param("sopId") Long sopId);

    /**
     * 查询触达客户总数
     * @param sopId
     * @return
     */
    Integer queryReachCustomerCntBySopId(@Param("sopId") Long sopId);

    /**
     * 查询执行任务成员-跑马灯
     * @param sopId
     * @return
     */
    List<ConditionSopDataVO> queryTaskCompleteDetailListBySopId(@Param("sopId") Long sopId);

    /**
     * 查询旅程sop详情
     * @param request
     * @return
     */
    JourneySopDetailVO detailJourneySop(JourneySopRequest request);


    /**
     * 查询旅程sop数据概览
     * @param request
     * @return
     */
    JourneySopDataStatisticsVO getJourneySopDataStatistics(JourneySopQueryRequest request);

    /**
     * 查询旅程sop列表
     * @param request
     * @return
     */
    List<JourneySopListVO> listJourneySop(JourneySopRequest request);

    /**
     * 获取旅程阶段数据
     * @param journeyId
     * @param sopId
     * @return
     */
    List<ExternalJourneyStageVO> getJourneySopStageList(@Param("journeyId") Long journeyId, @Param("sopId") Long sopId);

    /**
     * 查询1V1sop详情（回显）
     * @param request 请求参数
     * @return 1V1sop详情
     */
    MassCustomerSopDetailVO detailMassCustomerSop(MassCustomerSopRequest request);
}
