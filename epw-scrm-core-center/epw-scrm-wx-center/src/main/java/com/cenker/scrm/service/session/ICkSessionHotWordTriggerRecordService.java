package com.cenker.scrm.service.session;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.session.CkSessionHotWordTriggerRecord;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【ck_session_hot_word_trigger_record(热词触发记录表)】的数据库操作Service
* @createDate 2024-04-21 23:13:56
*/
public interface ICkSessionHotWordTriggerRecordService extends IService<CkSessionHotWordTriggerRecord> {

    /**
     * 保存触发记录
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    void saveTriggerRecord(Date beginTime, Date endTime);

    /**
     * 保存热词触发记录
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    int saveHotWordTriggerRecord(String beginTime, String endTime);
}
