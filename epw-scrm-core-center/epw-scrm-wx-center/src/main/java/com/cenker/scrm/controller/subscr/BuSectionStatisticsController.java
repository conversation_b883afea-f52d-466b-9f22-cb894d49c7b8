package com.cenker.scrm.controller.subscr;

import com.alibaba.fastjson.JSON;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsVO;
import com.cenker.scrm.service.subscr.IBuSectionStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * 统计控制器
 * 提供对统计的RESTful接口
 */
@RestController
@RequestMapping("/bu/section/statistics")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BuSectionStatisticsController  extends BaseController {

    @Autowired
    private IBuSectionStatisticsService buSectionStatisticsService;

    /**
     * 查询统计数据
     *
     * @param query 查询条件
     * @return 统计数据
     */
    @GetMapping("/list")
    public Result<BuSectionStatisticsVO> list(BuSectionStatisticsQuery query) {
        log.info("【查询统计数据】开始查询，参数：{}", query);
        BuSectionStatisticsVO result = buSectionStatisticsService.list(query);
        log.info("【查询统计数据】查询完成，结果：{}", JSON.toJSONString(result));
        return Result.success("操作成功", result);
    }

    /**
     * 同步数据
     *
     * @param query 同步数据查询条件
     * @return 同步结果
     */
    @PostMapping("/synData")
    public Result synData(@RequestBody BuSectionStatisticsQuery query) {
        log.info("【同步统计数据】开始同步，参数：{}", query);
        Result result = buSectionStatisticsService.synData(query);
        log.info("【同步统计数据】同步完成，结果：{}", result.isSuccess() ? "成功" : "失败");
        return result;
    }
}