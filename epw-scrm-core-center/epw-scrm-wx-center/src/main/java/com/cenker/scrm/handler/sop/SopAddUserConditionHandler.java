package com.cenker.scrm.handler.sop;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.pojo.dto.condition.SopTriggerDetailDTO;
import com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO;
import com.cenker.scrm.pojo.dto.sop.SopConditionChainDTO;
import com.cenker.scrm.service.external.TbWxExtFollowUserService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/7/17
 * @Description 筛选添加员工
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SopAddUserConditionHandler extends SopConditionChainHandler {

    private final TbWxExtFollowUserService tbWxExtFollowUserService;

    @Override
    protected boolean before(SopConditionChainDTO sopConditionChainDTO) {
        if (CollectionUtils.isEmpty(sopConditionChainDTO.getCustomerList())) {
            return false;
        }
        // 筛选出符合当前处理器的条件
        List<SopTriggerDetailDTO> andList = Optional.ofNullable(sopConditionChainDTO.getAndList()).orElse(Lists.newArrayList()).
                stream().filter(sopTriggerDetailDTO -> TypeConstants.SOP_TRIGGER_CONDITION_ADD_USER_TYPE == sopTriggerDetailDTO.getTriggerType())
                .collect(Collectors.toList());
        sopConditionChainDTO.setSpecificConditionList(andList);
        return CollectionUtils.isNotEmpty(andList);
    }

    @Override
    protected void doHandler(SopConditionChainDTO sopConditionChainDTO) {
        List<ConditionSopCustomerDTO> customerList = sopConditionChainDTO.getCustomerList();
        List<SopTriggerDetailDTO> specificConditionList = sopConditionChainDTO.getSpecificConditionList();
        log.info("【sop】链式处理-开始添加员工条件筛选，当前获取的客户数：【{}】，sopId:【{}】\n筛选条件：{}",
                customerList.size(),sopConditionChainDTO.getSopId(), specificConditionList);
        for (SopTriggerDetailDTO sopTriggerDetailDTO : specificConditionList) {
            // 这里可以使用枚举等方式来消除if/else 出于时间问题 考虑后续优化
            sopTriggerDetailDTO.getTriggerConditionValue().setCustomerList(customerList);
            if (sopTriggerDetailDTO.getTriggerConditionType() == TypeConstants.SOP_TRIGGER_CONDITION_ADD_USER_TYPE_1) {
                // 等于 不等于 包含 不包含
                customerList = tbWxExtFollowUserService.assignSelectAddUser4Sop(sopTriggerDetailDTO);
            }else if (sopTriggerDetailDTO.getTriggerConditionType() == TypeConstants.SOP_TRIGGER_CONDITION_ADD_USER_TYPE_2) {
                // 目前只有大于等于
                if (sopTriggerDetailDTO.getTriggerRelationType() == TypeConstants.SOP_TRIGGER_CONDITION_RELATION_GE) {
                    customerList = tbWxExtFollowUserService.ge4AddUserCount4Sop(sopTriggerDetailDTO);
                }
            }
            sopConditionChainDTO.setCustomerList(customerList);
            // 代表已经没有符合条件的客户了 直接退出该执行器
            if (CollectionUtils.isEmpty(customerList)) {
                log.info("【sop】链式处理-添加员工条件筛选，没有符合条件的客户，sopId：【{}】", sopConditionChainDTO.getSopId());
                return;
            }
        }
        log.info("【sop】链式处理-完成添加员工条件筛选，最后获取的客户数：【{}】，筛选条件：{}", customerList.size());
    }
}
