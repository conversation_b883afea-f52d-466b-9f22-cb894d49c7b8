package com.cenker.scrm.service.deprecated;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.statistics.WePageCountDto;
import com.cenker.scrm.pojo.entity.wechat.deprecated.TbWxGroupStatistic;
import com.cenker.scrm.pojo.request.data.WePageStateQueryVO;

import java.util.List;

/**
 * <AUTHOR> 即将废弃
 */
@Deprecated
public interface ITbWxGroupStatisticService extends IService<TbWxGroupStatistic> {
    /**
     * 客户群数据
     */
    WePageCountDto getCountDataByDay(String corpId, String dateTime, String day);

    /**
     * 按天维度查询数据统计
     */
    List<WePageCountDto> getDayCountData(WePageStateQueryVO wePageStateQueryVo);

    List<WePageCountDto> getWeekCountData(WePageStateQueryVO wePageStateQuery);

    List<WePageCountDto> getMonthCountData(WePageStateQueryVO wePageStateQuery);
}
