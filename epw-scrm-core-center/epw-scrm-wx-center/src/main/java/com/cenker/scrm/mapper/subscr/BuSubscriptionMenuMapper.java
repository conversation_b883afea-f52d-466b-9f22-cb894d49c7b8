package com.cenker.scrm.mapper.subscr;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenu;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuVO;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订阅菜单Mapper接口
 * 提供对订阅菜单的数据库操作
 */
@Mapper
public interface BuSubscriptionMenuMapper extends BaseMapper<BuSubscriptionMenu> {
    List<BuSubscriptionMenuVO> selectMenuList(BuSubscriptionQuery query);
}