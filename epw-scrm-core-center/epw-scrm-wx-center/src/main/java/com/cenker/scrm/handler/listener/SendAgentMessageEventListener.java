package com.cenker.scrm.handler.listener;


import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.event.SendAgentMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @Date 2021/12/2
 * @Description
 */
// @Component
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class SendAgentMessageEventListener implements ApplicationListener<SendAgentMessageEvent> {

    private final WxCpFeign wxCpFeign;

    @Override
    @Async
    public void onApplicationEvent(SendAgentMessageEvent sendAgentMessageEvent) {
        String corpId = sendAgentMessageEvent.getCorpId();
        WxCpMessage wxCpMessage = sendAgentMessageEvent.getWxCpMessage();
        try {
            wxCpFeign.agentSend(wxCpMessage, corpId);
        } catch (Exception e) {
            log.error("【应用】发送应用消息失败，错误信息：{}", e);
        }
    }
}
