package com.cenker.scrm.mapper.sop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.condition.SopMassSelectDTO;
import com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserDTO;
import com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO;
import com.cenker.scrm.pojo.entity.wechat.sop.SopCustomerInfo;
import com.cenker.scrm.pojo.request.sop.ConditionSopRequest;
import com.cenker.scrm.pojo.request.sop.MassCustomerSopQueryRequest;
import com.cenker.scrm.pojo.vo.contact.CorpUserInfoVO;
import com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO;
import com.cenker.scrm.pojo.vo.sop.ExternalSopCustomerVO;
import com.cenker.scrm.pojo.vo.sop.MassCustomerSopCustomerVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/17
 * @Description
 */
public interface SopCustomerInfoMapper extends BaseMapper<SopCustomerInfo> {
    /**
     * 从全部客户筛选出需要进行条件筛选的人群包
     * @param conditionSopRequest
     * @return
     */
    List<ConditionSopCustomerDTO> excludeHasSelectedCustomer4All(ConditionSopRequest conditionSopRequest);

    /**
     * 获取群发sop筛选客户
     * @param request
     * @return
     */
    List<MassCustomerSopCustomerVO> massCustomerSopCustomerList(MassCustomerSopQueryRequest request);

    /**
     * 查询客户添加员工
     * @param externalUserId
     * @return
     */
    List<CorpUserInfoVO>queryFollowUserByExtUserId(@Param("externalUserId") String externalUserId);

    /**
     * 未加入sop的客户
     * @param request
     * @return
     */
    List<ExternalSopCustomerVO> neverSopCustomer(MassCustomerSopQueryRequest request);

    /**
     * 全部客户
     * @param corpId 预留企业id
     * @return
     */
    List<ConditionSopCustomerDTO> selectCustomer4All(String corpId);

    /**
     * 根据条件查询出条件客户（员工去重，只需要查询客户id）
     * @param condition
     * @return
     */
    List<ConditionSopCustomerDTO> getUserDistinctCustomer(SopMassSelectDTO condition);

    public List<MassMessageSenderListVO> qrySendMsgCustUserList(QryListSendMsgCustUserDTO listQry);

    }
