package com.cenker.scrm.service.impl.bu;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.bu.BuOperTrackMapper;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackSearchVO;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackVO;
import com.cenker.scrm.service.bu.IBuOperTrackService;
import com.cenker.scrm.util.DateUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 客户动态记录表 服务实现类
 */
@Service
public class BuOperTrackServiceImpl extends ServiceImpl<BuOperTrackMapper, BuOperTrack> implements IBuOperTrackService {

    @Override
    public List<BuOperTrackVO> getList(BuOperTrackSearchVO buOperTrack) {
        if (buOperTrack.getStartTime() != null) {
            // 格式化为 yyyy-MM-dd HH:mm:ss
            buOperTrack.setStartTimeStr(DateUtil.formatDateTime(buOperTrack.getStartTime()));
        }
        if (buOperTrack.getEndTime() != null) {
            buOperTrack.setEndTime(DateUtils.getDayEndTime(buOperTrack.getEndTime()));
            // 格式化为 yyyy-MM-dd HH:mm:ss
            buOperTrack.setEndTimeStr(DateUtil.formatDateTime(buOperTrack.getEndTime()));
        }
        return baseMapper.selectList(buOperTrack);
    }

    @Override
    public Map<String, Object> getTrajectoryCountToday(BuOperTrackSearchVO buOperTrack) {
        return baseMapper.getTrajectoryCountToday(buOperTrack);
    }
}