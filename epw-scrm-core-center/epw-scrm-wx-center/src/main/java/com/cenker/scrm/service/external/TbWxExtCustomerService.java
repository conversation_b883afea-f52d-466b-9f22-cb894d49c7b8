package com.cenker.scrm.service.external;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.dto.external.CustomerAuthDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerTrajectory;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.export.ChurnCustomerExport;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.contact.CorpAddWayVO;
import com.cenker.scrm.pojo.vo.contact.CorpUserInfoVO;
import com.cenker.scrm.pojo.vo.external.*;
import com.cenker.scrm.pojo.vo.group.CustomerGroupVO;
import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 企业微信外部客户信息Service接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface TbWxExtCustomerService extends IService<TbWxExtCustomer> {
    /**
     * 创建外部联系信息
     *
     * @param externalContactInfo
     * @param corpId
     */
    void saveExtCustomer(WxCpExternalContactInfo externalContactInfo, String corpId, String userId);

    /**
     * 查询流失客户
     *
     * @param dto
     * @return
     */
    List<CustomerChurnVO> queryChurnCustomer(CustomerChurnDTO dto);

    /**
     * 查询企业成员信息
     *
     * @param dto
     * @return
     */
    List<CorpUserInfoVO> queryFollowUserByExtUserId(CustomerChurnDTO dto);

    /**
     * 查询外部客户
     *
     * @param dto
     * @return
     */
    List<CustomerInfoVo> queryCustomerInfo(CustomerChurnDTO dto);

    /**
     * 查询客户详情
     *
     * @param extUserId
     * @param corpId
     * @return
     */
    CustomerInfoVo queryCustomerInfoDetail(String extUserId, String corpId);

    /**
     * 批量同步获取用户
     *
     * @param corpId
     */
    void batchSynCustomerInfos(String corpId);

    void remindTags(String corpId);

    /**
     * 查询流失客户
     *
     * @param dto
     * @return
     */
    List<CustomerExportVo> exprotChurnCustomer(CustomerChurnDTO dto);


    /**
     * 查询流失客户
     *
     * @param dto
     * @return
     */
    List<ChurnCustomerExport> exprotChurnCustomerLost(CustomerChurnDTO dto);

    /**
     * 查询外部联系人详情
     *
     * @param tbWxExtCustomer
     * @return
     */
    List<TbWxExtCustomer> selectWeCustomerAllList(TbWxExtCustomer tbWxExtCustomer);

    /**
     * 根据客户id和当前企业员工id获取客户详细信息
     */
    CustomerPortraitVo findWeCustomerInfo(TbWxCustomerTrajectory tbWxCustomerTrajectory);

    /**
     * 根据客户id获取客户添加人
     */
    List<CustomerAddUserVO> findAddEmployers(String externalUserId, String corpId);
    /**
     * 更新用户个人标签
     */
    void updateWeCustomerPorTraitTag(WeMakeCustomerTag weMakeCustomerTag);

    /**
     * 更新画像资料
     * @param weCustomerPortrait
     */
    void updateWeCustomerPortrait(CustomerPortraitVo weCustomerPortrait);

    /**
     * 工作台查询个人客户总数
     * @param mobileUser 用户信息
     * @return 客户总数
     */
    int countTotalCustomer(MobileUser mobileUser);

    /**
     * 查询某天某人的新增或流失客户数量
     * @param day 指定日期
     * @param status 新增或流失
     * @param mobileUser 用户信息
     * @return 客户数量
     */
    int countAddOrDecreaseByDay(String day, String status, MobileUser mobileUser);

    /**
     * 昵称匹配客户数量
     */
    int countCustomerByCustomerIdsAndCorpId(Set<String> customerIds, String corpId);

    /**
     * 根据微信名昵称匹配客户id
     */
    String selectExtCustomerByCorpIdAndNickName(String corpId, String nickName);

    /**
     * 查询客户详情2.0 2022-02-18
     */
    CustomerInfoVo queryCustomerInfoDetail(CustomerChurnDTO dto);

    /**
     * 获取企业微信外部客户社交关系 所在群聊
     */
    List<CustomerGroupVO> queryCustomerGroupList(CustomerChurnDTO dto);

    /**
     * 查询客户列表 2022-02-21
     */
    List<CustomerInfoVo> getExtCustomerList(CustomerChurnDTO dto);

    List<CustomerInfoVo> qryExtCustomerList(CustomerChurnDTO dto);
    /**
     * 查询企业所有的添加渠道列表
     */
    List <CorpAddWayVO> getAddWayList(CustomerChurnDTO dto);

    /**
     * 根据客户id和员工id获取客户基本信息
     * @param tbWxCustomerTrajectory 企业、用户、员工id
     * @return 客户基本信息
     */
    CustomerPortraitVo getCustomerInfoByIdAndUserId(TbWxCustomerTrajectory tbWxCustomerTrajectory);

    /**
     * 查询客户趋势数据
     * @param startTime
     * @param endTime
     * @param statisticQuery
     * @return
     */
    CorpRealTimeDataVO getBehaviorData(Date startTime, Date endTime, StatisticQuery statisticQuery);

    /**
     * 获取客户标签信息（所有员工版本
     * @param tbWxCustomerTrajectory
     * @return 标签
     */
    List<ExternalUserTagVO> getCustomerTag(TbWxCustomerTrajectory tbWxCustomerTrajectory);

    /**
     * 批量打标签
     */
    void batchSetTag(CustomerChurnDTO dto);

    /**
     * 统计活跃客户数
     */
    int countActiveCustomerByDay(String day,String corpId);

    CorpRealTimeDataVO getDailyTotalAuthCustomerCountGraph(QueryRadarStatisticsRankDTO dto);

    CorpRealTimeDataVO getActiveCustomerCount1v1DailyGraph(QueryRadarStatisticsRankDTO dto);

    WxCustomerBindingVO checkBindingByUnionId(String unionId) throws Exception;

    WxCustomerBindingVO checkEsbBindingByUnionId(String unionId);
    void unbindingByUnionId(String unionId);
    void bindingByUnionId(CustomerAuthDTO dto);

    WxCustomerBindingVO checkAuthStatusByUnionId(String unionId);

    void accessAuthCallBack(CustomerAuthDTO esbDTO);

    WxCustomerBindingVO checkAccessAuthStatus(CustomerAuthDTO dto);

    /**
     * 批量移除标签
     */
    void batchUnSetTag(CustomerChurnDTO dto);

    /**
     * 获取无有效关系的客户ID
     * @return
     */
    List<String> getNullRelationshipIds();

    void updateAuthCustomers(List<TbWxExtCustomer> authList, Date now);

    void updateUnAuthCustomers(List<TbWxExtCustomer> unAuthList, Date now);

    void updateCustomers(List<TbWxExtCustomer> updateList, Date now);
}
