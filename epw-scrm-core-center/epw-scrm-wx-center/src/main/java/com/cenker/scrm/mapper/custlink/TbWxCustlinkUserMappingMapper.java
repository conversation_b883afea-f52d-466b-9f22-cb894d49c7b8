package com.cenker.scrm.mapper.custlink;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustlinkUserMapping;

import java.util.List;

/**
 * 员工活码Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-25
 */
public interface TbWxCustlinkUserMappingMapper extends BaseMapper<TbWxCustlinkUserMapping> {

    List<UserConditionDTO> qryUserListByLinkId(Long linkId);

}
