package com.cenker.scrm.controller.workbench;

import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.vo.external.CustomerFollowUserVO;
import com.cenker.scrm.pojo.vo.group.CustomerGroupVO;
import com.cenker.scrm.service.external.TbWxExtFollowUserService;
import com.cenker.scrm.service.group.ITbWxCustomerGroupService;
import com.cenker.scrm.util.TokenParseUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/5
 * @Description 客户控制器
 */
@RequestMapping("/work/customer")
@RestController
public class WorkCustomerController extends BaseController {
    @Autowired
    private TokenParseUtil tokenService;
    @Autowired
    private TbWxExtFollowUserService followUserService;
    @Autowired
    private ITbWxCustomerGroupService groupService;

    @RequestMapping("/getCustomerList")
    public TableDataInfo getCustomerList(HttpServletRequest request, String name) {
        startPage();
        MobileUser mobileUser = tokenService.getLoginUserH5(request).getMobileUser();
        Map<String, Object> params = Maps.newHashMap();
        params.put("userId", mobileUser.getUserId());
        params.put("corpId", mobileUser.getCorpId());
        params.put("name", name);
        List<CustomerFollowUserVO> list = followUserService.getCustomerListByWork(params);
        return getDataTable(list);
    }

    @RequestMapping("/getGroupList")
    public TableDataInfo getGroupList(@RequestBody MobileUser mobileUser, String ownerName,Integer type) {
        // type 0/null 全部 1 我创建的 2 非我创建的
        Map<String, Object> params = Maps.newHashMap();
        params.put("corpId", mobileUser.getCorpId());
        params.put("userId", mobileUser.getUserId());
        params.put("administrator", mobileUser.getAdministrator());
        params.put("ownerName", ownerName);
        params.put("type", type);
        startPage();
        List<CustomerGroupVO> list = groupService.getGroupListByWork(params);
        return getDataTable(list);
    }
}
