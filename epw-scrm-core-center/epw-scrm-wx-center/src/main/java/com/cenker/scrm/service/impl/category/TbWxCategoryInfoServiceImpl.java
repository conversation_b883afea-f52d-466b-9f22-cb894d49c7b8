package com.cenker.scrm.service.impl.category;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.mapper.category.TbWxCategoryInfoMapper;
import com.cenker.scrm.pojo.TreeSelect;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategoryInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.service.category.ITbWxCategoryInfoService;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/4/11
 * @Description
 */
@Service
public class TbWxCategoryInfoServiceImpl extends ServiceImpl<TbWxCategoryInfoMapper, TbWxCategoryInfo> implements ITbWxCategoryInfoService {
    @Override
    public List<TbWxCategoryInfo> selectCategoryInfoList(TbWxCategoryInfo tbWxCategoryInfo) {
        return baseMapper.selectList(new LambdaQueryWrapper<TbWxCategoryInfo>()
                .eq(TbWxCategoryInfo::getCorpConfigId,tbWxCategoryInfo.getCorpConfigId())
                .select(TbWxCategoryInfo::getName,TbWxCategoryInfo::getId)
                .orderByAsc(TbWxCategoryInfo::getOrderNum)
        );
    }

    @Override
    public List<TreeSelect> buildCategoryTreeSelect(List<TbWxCategoryInfo> list) {
        List<TbWxCategoryInfo> categoryTrees = buildDeptTree(list);
        return categoryTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(TbWxCategoryInfo tbWxCategoryInfo) {
        for (TbWxCategoryInfo wxCategoryInfo : tbWxCategoryInfo.getOrderList()) {
            TbWxCategoryInfo info = new TbWxCategoryInfo();
            info.setOrderNum(wxCategoryInfo.getOrderNum());
            int update = baseMapper.update(info, new LambdaUpdateWrapper<TbWxCategoryInfo>()
                    .eq(TbWxCategoryInfo::getId, wxCategoryInfo.getId()));
            if (update < 1) {
                throw new CustomException("分组数据已更新，请刷新页面重试");
            }
        }
    }

    @Override
    public void delete(TbWxCategoryInfo tbWxCategoryInfo) {
        TbWxCategoryInfo categoryInfo = this.getById(tbWxCategoryInfo.getId());
        LogUtil.logOperDesc(categoryInfo.getName());

        long count = count(new LambdaQueryWrapper<TbWxCategoryInfo>()
                .eq(TbWxCategoryInfo::getCorpConfigId, tbWxCategoryInfo.getCorpConfigId())
                .eq(TbWxCategoryInfo::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
        );
        if (count == 1) {
            throw new CustomException("删除失败，至少保留1个分组");
        }
        removeById(tbWxCategoryInfo.getId());
    }

    private List<TbWxCategoryInfo> buildDeptTree(List<TbWxCategoryInfo> list) {
        List<TbWxCategoryInfo> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (TbWxCategoryInfo tbWxCategoryInfo : list) {
            tempList.add(tbWxCategoryInfo.getId());
        }
        for (Iterator<TbWxCategoryInfo> iterator = list.iterator(); iterator.hasNext(); ) {
            TbWxCategoryInfo tbWxCategoryInfo = (TbWxCategoryInfo) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(tbWxCategoryInfo.getParentId())) {
                recursionFn(list, tbWxCategoryInfo);
                returnList.add(tbWxCategoryInfo);
            }
        }
        if (returnList.isEmpty()) {
            returnList = list;
        }
        return returnList;
    }

    private void recursionFn(List<TbWxCategoryInfo> list, TbWxCategoryInfo tbWxCategoryInfo) {
        // 得到子节点列表
        List<TbWxCategoryInfo> childList = getChildList(list, tbWxCategoryInfo);
        tbWxCategoryInfo.setChildren(childList);
        for (TbWxCategoryInfo tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    private boolean hasChild(List<TbWxCategoryInfo> list, TbWxCategoryInfo tChild) {
        return getChildList(list, tChild).size() > 0;
    }

    private List<TbWxCategoryInfo> getChildList(List<TbWxCategoryInfo> list, TbWxCategoryInfo tbWxCategoryInfo) {
        List<TbWxCategoryInfo> infos = new ArrayList<>();
        for (TbWxCategoryInfo n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && tbWxCategoryInfo.getId().equals(n.getParentId())) {
                infos.add(n);
            }
        }
        return infos;
    }
}
