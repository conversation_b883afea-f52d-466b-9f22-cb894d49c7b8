package com.cenker.scrm.service.fission;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.corp.CorpDTO;
import com.cenker.scrm.pojo.dto.fission.WeTaskFissionStatisticDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxFission;
import com.cenker.scrm.pojo.vo.fission.TaskFissionDataVO;
import com.cenker.scrm.pojo.vo.fission.TaskFissionProgressVO;
import com.cenker.scrm.pojo.vo.fission.TaskFissionRecordStatisticVO;
import com.cenker.scrm.pojo.vo.fission.TaskFissionStatisticVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/10
 * @Description
 */
public interface ITbWxFissionService extends IService<TbWxFission> {

    /**
     * 查询裂变活动裂变
     */
    List<TbWxFission> selectTbWxFissionList(TbWxFission tbWxFission);

    /**
     * 添加裂变
     */
    String add(TbWxFission tbWxFission);

    /**
     * 根据id查询裂变任务
     */
    TbWxFission selectTbWxFissionById(String id);

    /**
     * 更新裂变任务
     */
    void updateTbWxFission(TbWxFission tbWxFissionData);

    /**
     * 发送消息
     * @param id 裂变任务id
     */
    void sendTbWxFission(String id, String username);

    /**
     * 裂变统计数据
     */
    TaskFissionStatisticVO taskFissionStatistic(String taskFissionId, Date startTime, Date endTime);


    TaskFissionProgressVO getCustomerTaskProgress(TbWxFission tbWxFissionData, String unionId);

    /**
     * 查询裂变的状态
     * @return // 1 正常 2 已结束 3 未开始
     */
    Integer getFissionStatus(TbWxFission tbWxFission);

    /**
     * 获取裂变数据趋势
     */
    TaskFissionStatisticVO getFissionStatistics(WeTaskFissionStatisticDTO dto, Date startTime, Date endTime);

    /**
     * 查询客户参与明细
     */
    List<TaskFissionRecordStatisticVO> getFissionRecordStatistics(WeTaskFissionStatisticDTO dto);

    /**
     * 助力详情
     */
    List<TaskFissionRecordStatisticVO> getFissionRecordDetail(WeTaskFissionStatisticDTO dto);

    TaskFissionDataVO statistics(TbWxFission tbWxFission);

    /**
     * 工作台工具
     * @param corpDTO
     * @return
     */
    List<Map<String, Object>> getWorkFissionList(CorpDTO corpDTO);
    List<Map<String, Object>> getWorkFissionGroupList(CorpDTO corpDTO);
}
