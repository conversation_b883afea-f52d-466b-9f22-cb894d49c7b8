package com.cenker.scrm.service.impl.moment;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.moment.TbWxMomentStatisticsMapper;
import com.cenker.scrm.mapper.moment.TbWxMomentTaskInfoMapper;
import com.cenker.scrm.pojo.entity.moment.TbWxMomentStatistics;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentTaskInfo;
import com.cenker.scrm.pojo.vo.moment.MomentStatisticsVO;
import com.cenker.scrm.pojo.vo.moment.SendMomentVo;
import com.cenker.scrm.service.moment.ITbWxMomentStatisticsService;
import com.cenker.scrm.util.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/1/8
 * @Description 朋友圈指标数据统计
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxMomentStatisticsServiceImpl extends ServiceImpl<TbWxMomentStatisticsMapper, TbWxMomentStatistics> implements ITbWxMomentStatisticsService {
    private final TbWxMomentTaskInfoMapper momentTaskInfoMapper;

    @Override
    public void calculateAndSaveStatisticsResult(Long momentTaskId) {
        TbWxMomentTaskInfo taskInfo = momentTaskInfoMapper.selectById(momentTaskId);
        this.calculateAndSaveStatisticsResult(taskInfo);
    }

    @Override
    public void calculateAndSaveStatisticsResult(TbWxMomentTaskInfo taskInfo) {
        if (taskInfo == null) {
            // 任务不存在，直接返回
            return;
        }
        log.info("【同步朋友圈】开始计算并保存朋友圈【任务ID：{}】的指标数据!", taskInfo.getId());
        // 计算各指标结果
        SendMomentVo vo = new SendMomentVo();
        vo.setId(taskInfo.getId());
        MomentStatisticsVO dataStatistics = momentTaskInfoMapper.getDataStatistics(vo);

        Long taskId = Long.valueOf(taskInfo.getId());
        // 查询统计数据是否存在
        TbWxMomentStatistics statistics = this.lambdaQuery()
                .select(TbWxMomentStatistics::getId, TbWxMomentStatistics::getTaskId, TbWxMomentStatistics::getMomentId)
                .eq(TbWxMomentStatistics::getTaskId, taskId).one();

        if (statistics == null) {
            statistics = new TbWxMomentStatistics();
            statistics.setCreateTime(new Date());

        }
        statistics.setTaskId(taskId);
        statistics.setMomentId(taskInfo.getMomentId());
        statistics.setUpdateTime(new Date());
        BeanUtils.copyProperties(dataStatistics, statistics);
        this.saveOrUpdate(statistics);
        log.info("【同步朋友圈】完成计算并保存朋友圈【任务ID：{}】的指标数据!", taskInfo.getId());
    }

}
