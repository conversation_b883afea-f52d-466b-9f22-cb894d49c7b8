package com.cenker.scrm.service.impl.subscr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.subscr.BuSubscriptionMenuSubMapper;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenuSub;
import com.cenker.scrm.service.subscr.IBuSubscriptionMenuSubService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 子菜单服务实现类
 * 提供对子菜单的CRUD操作及自定义业务方法
 */
@Slf4j
@AllArgsConstructor
@Service
public class BuSubscriptionMenuSubServiceImpl extends ServiceImpl<BuSubscriptionMenuSubMapper, BuSubscriptionMenuSub> implements IBuSubscriptionMenuSubService {
}