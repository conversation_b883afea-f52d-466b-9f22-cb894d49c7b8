package com.cenker.scrm.mapper.moment;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendUser;
import com.cenker.scrm.pojo.vo.moment.SendMomentUserListVo;
import com.cenker.scrm.pojo.vo.moment.SendMomentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbWxMomentSendUserMapper extends BaseMapper<TbWxMomentSendUser> {
    List<SendMomentUserListVo> getMomentSendUserList(SendMomentVo sendMomentVo);

    List<SendMomentUserListVo> getMomentSendCusList(SendMomentVo sendMomentVo);

    /**
     * 查询发表成员列表
     *
     * @param publishStatus 发布状态：0-未发布，1-已发布
     * @param queryCustomerStatus 查询客户状态：0-未查询，1-已查询
     * @return
     */
    List<TbWxMomentSendUser> selectSendUser(@Param("momentTaskId") String momentTaskId, @Param("publishStatus") Integer publishStatus, @Param("queryCustomerStatus") Integer queryCustomerStatus);
}
