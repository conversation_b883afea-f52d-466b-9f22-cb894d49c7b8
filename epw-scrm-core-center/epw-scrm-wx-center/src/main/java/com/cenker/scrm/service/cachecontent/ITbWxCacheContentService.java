package com.cenker.scrm.service.cachecontent;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.cachecontent.CacheContentDTO;
import com.cenker.scrm.pojo.entity.cachecontent.TbWxCacheContent;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @date 2023/5/23 10:25
 */
public interface ITbWxCacheContentService extends IService<TbWxCacheContent> {

    Boolean updateLink(String id, String value);

    Boolean updateMiniprogram(String id, String value);

    Boolean save(String userId, String corpId, List<WelcomeAttachmentVo> attachments);

    Boolean addCacheContent(SysUser sysUser, CacheContentDTO cacheContentDTO);
}
