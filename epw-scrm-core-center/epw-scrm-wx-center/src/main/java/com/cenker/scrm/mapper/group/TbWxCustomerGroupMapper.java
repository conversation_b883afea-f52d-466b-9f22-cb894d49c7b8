package com.cenker.scrm.mapper.group;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.group.CustomerGroupDto;
import com.cenker.scrm.pojo.dto.group.CustomerGroupSopDto;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup;
import com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO;
import com.cenker.scrm.pojo.vo.group.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 企业微信客户群Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Repository
public interface TbWxCustomerGroupMapper extends BaseMapper<TbWxCustomerGroup> {
    /**
     * 查询企业微信客户群
     *
     * @param chatId 企业微信客户群ID
     * @return 企业微信客户群
     */
    TbWxCustomerGroup selectTbWxCustomerGroupById(String chatId);

    /**
     * 查询企业微信客户群列表
     *
     * @param tbWxCustomerGroup 企业微信客户群
     * @return 企业微信客户群集合
     */
    List<TbWxCustomerGroup> selectTbWxCustomerGroupList(TbWxCustomerGroup tbWxCustomerGroup);

    /**
     * 新增企业微信客户群
     *
     * @param tbWxCustomerGroup 企业微信客户群
     * @return 结果
     */
    int insertTbWxCustomerGroup(TbWxCustomerGroup tbWxCustomerGroup);

    /**
     * 修改企业微信客户群
     *
     * @param tbWxCustomerGroup 企业微信客户群
     * @return 结果
     */
    int updateTbWxCustomerGroup(TbWxCustomerGroup tbWxCustomerGroup);

    /**
     * 删除企业微信客户群
     *
     * @param chatId 企业微信客户群ID
     * @return 结果
     */
    int deleteTbWxCustomerGroupById(String chatId);

    /**
     * 批量删除企业微信客户群
     *
     * @param chatIds 需要删除的数据ID
     * @return 结果
     */
    int deleteTbWxCustomerGroupByIds(String[] chatIds);

    /**
     * 查询客户群信息
     * @param dto
     * @return
     */
    List<CustomerGroupVO> queryCustomerGroupInfo(CustomerGroupDto dto);

    /**
     * 查询客户群信息
     * @param dto
     * @return
     */
    List<CustomerGroupMemberVo> queryCustomerGroupMemberInfo(CustomerGroupDto dto);

    /**
     * 按天统计离群人数（不去重）
     * @param corpId
     * @param startTime
     * @param endTime
     * @return
     */
    List<CustomerGroupStatisVO> queryDepartureMemberStatis(@Param("corpId") String corpId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 按天统计入群人数（不去重）
     * @param corpId
     * @param startTime
     * @param endTime
     * @return
     */
    List<CustomerGroupStatisVO> queryJoinMemberStatis(@Param("corpId") String corpId, @Param("startTime") String startTime, @Param("endTime") String endTime);


    List<CustomerAddGroupVO> findWeGroupByCustomer(@Param("externalUserId") String externalUserId, @Param("userId") String userId, @Param("corpId") String corpId);

    List<CustomerGroupVO> getGroupListByWork(Map<String, Object> params);

    List<CustomerGroupVO> listCustomerGroup(CustomerGroupDto dto);

    List<CustomerMemberVO> getCorpGroupData(CustomerGroupDto dto);

    /**
     * 查询群头像
     */
    List<String> getGroupHeadImgByGroupList(CustomerGroupDto customerGroupDto);

    /**
     * 分日查询每日群成员总数
     */
    List<ContactStatisticsDailyVO> statGroupMemberDailyData( @Param("corpId") String corpId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询每日新增群成员
     * @param corpId
     * @param startTime
     * @param endTime
     * @return
     */
    List<ContactStatisticsDailyVO> statNewGroupMemberDailyData( @Param("corpId") String corpId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询每日群成员流失数据
     * @param corpId
     * @param startTime
     * @param endTime
     * @return
     */
    List<ContactStatisticsDailyVO> statExitGroupMemberDailyData( @Param("corpId") String corpId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 活跃群聊数据
     * @param corpId
     * @param startTime
     * @param endTime
     * @return
     */
    List<ContactStatisticsDailyVO> statActiveGroupData(@Param("corpId") String corpId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询分日流失/新增群成员
     */
    List<ContactStatisticsDailyVO> getBehaviorData4AddAndDelCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("corpId") String corpId, @Param("type") Integer type);

    /**
     * 查询社群sop列表
     */
    List<CustomerGroupSopVO> getGroupSopList(CustomerGroupSopDto dto);

    List<ContactStatisticsDailyVO> getDailyTotalGroupCountGraph(@Param("dto") QueryRadarStatisticsRankDTO dto);

}
