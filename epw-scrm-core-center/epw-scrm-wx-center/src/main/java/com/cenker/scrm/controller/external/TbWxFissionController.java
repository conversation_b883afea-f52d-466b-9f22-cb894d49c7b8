package com.cenker.scrm.controller.external;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.corp.CorpDTO;
import com.cenker.scrm.pojo.dto.fission.WeTaskFissionStatisticDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxFission;
import com.cenker.scrm.pojo.entity.wechat.TbWxTaskFissionRecord;
import com.cenker.scrm.pojo.exception.BaseException;
import com.cenker.scrm.pojo.exception.WeComException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.fission.TaskFissionProgressVO;
import com.cenker.scrm.pojo.vo.fission.TaskFissionRecordStatisticVO;
import com.cenker.scrm.pojo.vo.fission.TaskFissionStatisticVO;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.fission.ITbWxFissionService;
import com.cenker.scrm.service.fission.ITbWxTaskFissionRecordService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2021/9/10
 * @Description 裂变任务相关
 */
@RestController
@RequestMapping("/tp/fission")
@Slf4j
@RequiredArgsConstructor
public class TbWxFissionController extends BaseController {

    private final ITbWxFissionService tbWxFissionService;
    private final TokenParseUtil tokenService;
    private final TbWxExtCustomerService customerService;
    private final ITbWxTaskFissionRecordService recordService;

    @RequestMapping("/list")
    public TableDataInfo list(TbWxFission tbWxFission, HttpServletRequest request) {
        // 当前登录人企业id
        String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
        tbWxFission.setCorpId(corpId);
        startPage();
        List<TbWxFission> list = tbWxFissionService.selectTbWxFissionList(tbWxFission);
        return getDataTable(list);
    }

    @RequestMapping("/add")
    @Transactional
    public AjaxResult add(@RequestBody TbWxFission tbWxFission, HttpServletRequest request) {
        String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
        String userId = tokenService.getLoginUser(request).getUser().getUserId();
        tbWxFission.setCorpId(corpId);
        tbWxFission.setCreateTime(DateUtils.getNowDate());
        tbWxFission.setCreateBy(userId);
        String id = tbWxFissionService.add(tbWxFission);
        tbWxFissionService.sendTbWxFission(id, userId);
        return AjaxResult.success();
    }

    @RequestMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(tbWxFissionService.selectTbWxFissionById(id));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody TbWxFission tbWxFission, HttpServletRequest request) {
        String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
        tbWxFission.setCorpId(corpId);
        String userName = tokenService.getLoginUser(request).getUser().getUserId();
        // 前端传了创建人
        tbWxFission.setCreateBy(null);
        tbWxFission.setUpdateBy(userName);
        if (StringUtils.isEmpty(tbWxFission.getId())) {
            return AjaxResult.error("数据id为空");
        }
        TbWxFission tbWxFissionData = tbWxFissionService.selectTbWxFissionById(tbWxFission.getId());
        if (ObjectUtils.isEmpty(tbWxFission)) {
            return AjaxResult.error("数据不存在");
        }
        if (tbWxFissionData.getFissStatus().equals(StatusConstants.FISSION_ACTIVITY_END)) {
            return AjaxResult.error("已结束的活动不允许编辑！");
        }
        CopyOptions options = CopyOptions.create();
        options.setIgnoreNullValue(true);
        BeanUtil.copyProperties(tbWxFission, tbWxFissionData, options);
        if (CollectionUtil.isNotEmpty(tbWxFission.getTbWxFissionStaffs())) {
            tbWxFissionData.setTbWxFissionStaffs(tbWxFission.getTbWxFissionStaffs());
        } else {
            // 代表是全部
            tbWxFissionData.setCustomerTagId("all");
        }
        tbWxFissionService.updateTbWxFission(tbWxFissionData);
        return AjaxResult.success();
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        List<String> idsList = Arrays.asList(ids);
        tbWxFissionService.removeByIds(idsList);
        return AjaxResult.success();
    }

    /**
     * 发送裂变任务
     */
    @RequestMapping("/send/{id}")
    public AjaxResult send(@PathVariable String id, HttpServletRequest request) {
        String username = tokenService.getLoginUser(request).getUsername();
        tbWxFissionService.sendTbWxFission(id, username);
        return AjaxResult.success();
    }

    /**
     * 查询统计信息 废弃
     */
    @RequestMapping("/stat")
    public AjaxResult statistics1(@RequestBody WeTaskFissionStatisticDTO taskFissionStatisticDTO) {
        Date startTime = null;
        Date endTime = DateUtils.getNowDate();
        // 查询时间范围
        if (taskFissionStatisticDTO.getSeven()) {
            startTime = DateUtils.addDays(endTime, -7);
        } else if (taskFissionStatisticDTO.getThirty()) {
            startTime = DateUtils.addDays(endTime, -30);
        } else {
            if (StringUtils.isNotBlank(taskFissionStatisticDTO.getBeginTime()) ^ StringUtils.isNotBlank(taskFissionStatisticDTO.getEndTime())) {
                throw new BaseException("开始或结束时间不能为空");
            }
            try {
                startTime = DateUtils.parseDate(taskFissionStatisticDTO.getBeginTime() + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                endTime = DateUtils.parseDate(taskFissionStatisticDTO.getEndTime() + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException e) {
                log.error("查询统计信息获取开始时间和结束时间失败,taskFissionStatisticDTO:{}",taskFissionStatisticDTO);
                log.error("错误信息:{}",e);
            }
        }
        TaskFissionStatisticVO vo = tbWxFissionService.taskFissionStatistic(taskFissionStatisticDTO.getTaskFissionId(), startTime, endTime);
        return vo != null ? AjaxResult.success(vo) : AjaxResult.error("裂变数据存在异常");
    }

    /**
     * 获取裂变状态
     */
    @RequestMapping("/getFissionStatus")
    public Integer getFissionStatus(String fissionId) {
        TbWxFission tbWxFission = tbWxFissionService.getById(fissionId);
        return tbWxFissionService.getFissionStatus(tbWxFission);
    }

    /**
     * 获取客户邀请列表和任务进度(微信端)
     */
    @GetMapping("/{id}/progress")
    public AjaxResult getCustomerListById(@PathVariable("id") String fissionId, HttpServletRequest request) {
        H5LoginUser loginUserH5 = tokenService.getLoginUserH5Wx(request);
        MpWxUser mpWxUser = loginUserH5.getMpWxUser();
        TbWxFission tbWxFissionData = tbWxFissionService.selectTbWxFissionById(fissionId);
        if (tbWxFissionData != null) {
            TaskFissionProgressVO customerTaskProgress = tbWxFissionService.getCustomerTaskProgress(tbWxFissionData, mpWxUser.getUnionId());
            return AjaxResult.success(customerTaskProgress);
        } else {
            throw new WeComException("任务不存在");
        }
    }

    /**
     * 记录客户任务并生成二维码(微信端并且是外部客户才能参加)
     */
    @RequestMapping("/contactGenerate")
    public AjaxResult contactGenerate(String fissionId, HttpServletRequest request) {
        // 当前登录对象
        MpWxUser mpWxUser = tokenService.getLoginUserH5Wx(request).getMpWxUser();
        TbWxTaskFissionRecord tbWxTaskFissionRecord;
        // 先看活动的信息是否存在或者过期
        TbWxFission tbWxFission = tbWxFissionService.getOne(new LambdaQueryWrapper<TbWxFission>()
                .eq(TbWxFission::getId, fissionId)
                .eq(TbWxFission::getDelFlag, StatusConstants.DEL_FLAG_FALSE)
                .eq(TbWxFission::getFissStatus, StatusConstants.FISSION_ACTIVITY_NORMAL)
        );
        Integer fissionStatus = tbWxFissionService.getFissionStatus(tbWxFission);
        log.info("【裂变活动H5】获取到任务裂变信息：{}", tbWxFission);
        if (StatusConstants.FISSION_ACTIVITY_NORMAL.equals(fissionStatus)) {
            // 进行中
            // 查看是否当前企业外部客户
            String corpId = tbWxFission.getCorpId();
            List<TbWxExtCustomer> extCustomers = customerService.list(new LambdaQueryWrapper<TbWxExtCustomer>()
                    .eq(TbWxExtCustomer::getCorpId, corpId)
                    // 暂时昵称匹配
                    .eq(TbWxExtCustomer::getName, mpWxUser.getNickName())
                    // 1 微信用户 2 企业用户
                    .eq(TbWxExtCustomer::getType, 1)
            );
            if (CollectionUtils.isNotEmpty(extCustomers)) {
                // 查看是否存在任务进度记录
                tbWxTaskFissionRecord = recordService.selectWeTaskFissionRecordByIdAndCustomerId(fissionId, mpWxUser.getUnionId());
                if (tbWxTaskFissionRecord != null) {
                    log.info("【裂变活动H5】获取到任务进度：{}", tbWxTaskFissionRecord);
                    // 返回原有的二维码
                    tbWxTaskFissionRecord.setPoster(tbWxFission.getPostersUrl());
                    tbWxTaskFissionRecord.setStartTime(tbWxFission.getStartTime());
                    tbWxTaskFissionRecord.setEndTime(tbWxFission.getEndTime());
                    tbWxTaskFissionRecord.setTaskName(tbWxFission.getTaskName());
                    return AjaxResult.success(tbWxTaskFissionRecord);
                }
                log.info("【裂变活动H5】开始创建新的任务记录：{}", mpWxUser);
                // 创建新的记录
                tbWxTaskFissionRecord = recordService.addTaskFissionRecord(tbWxFission, mpWxUser);
                tbWxTaskFissionRecord.setPoster(tbWxFission.getPostersUrl());
                tbWxTaskFissionRecord.setStartTime(tbWxFission.getStartTime());
                tbWxTaskFissionRecord.setTaskName(tbWxFission.getTaskName());
                tbWxTaskFissionRecord.setEndTime(tbWxFission.getEndTime());
                return AjaxResult.success(tbWxTaskFissionRecord);
            } else {
                // 您没有资格参加活动
                tbWxTaskFissionRecord = new TbWxTaskFissionRecord();
                tbWxTaskFissionRecord.setStatus(StatusConstants.FISSION_ACTIVITY_NOT_AUTHORITY);
                tbWxTaskFissionRecord.setQrCode(tbWxFission.getFissQrcode());
                tbWxTaskFissionRecord.setTaskName(tbWxFission.getTaskName());
                return AjaxResult.success(tbWxTaskFissionRecord);
            }
        } else {
            // 返回不同的活动状态
            tbWxTaskFissionRecord = new TbWxTaskFissionRecord();
            tbWxTaskFissionRecord.setStatus(fissionStatus);
            return AjaxResult.success(tbWxTaskFissionRecord);
        }
    }

    @RequestMapping("/statistics")
    public AjaxResult statistics(@RequestBody WeTaskFissionStatisticDTO dto) {
        String taskFissionId = dto.getTaskFissionId();
        TbWxFission tbWxFission = tbWxFissionService.getById(taskFissionId);
        if (tbWxFission == null) {
            return AjaxResult.error("裂变信息不存在");
        }
        return AjaxResult.success(tbWxFissionService.statistics(tbWxFission));
    }

    @RequestMapping("/getFissionStatistics")
    public AjaxResult getFissionStatistics(@RequestBody WeTaskFissionStatisticDTO dto) {
        Date startTime = null;
        Date endTime = DateUtils.getNowDate();
        // 查询时间范围
        if (dto.getSeven()) {
            startTime = DateUtils.addDays(endTime, -7);
        } else if (dto.getThirty()) {
            startTime = DateUtils.addDays(endTime, -30);
        } else {
            try {
                startTime = DateUtils.parseDate(dto.getBeginTime() + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
                endTime = DateUtils.parseDate(dto.getEndTime() + " 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS);
            } catch (ParseException e) {
                log.error("时间转换有误,dto:{}",dto);
                log.error("错误信息:{}",e);
            }
        }
        TaskFissionStatisticVO vo = tbWxFissionService.getFissionStatistics(dto, startTime, endTime);
        return AjaxResult.success(vo);
    }

    @RequestMapping("/getFissionRecordStatistics")
    public TableDataInfo getFissionRecordStatistics(@RequestBody WeTaskFissionStatisticDTO dto) {
        startPage();
        List<TaskFissionRecordStatisticVO> list = tbWxFissionService.getFissionRecordStatistics(dto);
        return getDataTable(list);
    }

    @RequestMapping("/getFissionRecordDetail")
    public TableDataInfo getFissionRecordDetail(@RequestBody WeTaskFissionStatisticDTO dto) {
        startPage();
        List<TaskFissionRecordStatisticVO> list = tbWxFissionService.getFissionRecordDetail(dto);
        return getDataTable(list);
    }

    /**
     * 工作台工具列表附带数据-任务裂变
     *
     * @return
     */
    @PostMapping("/getWorkFissionList")
    public TableDataInfo getWorkFissionList(@RequestBody CorpDTO corpDTO, String taskName) {
        startPage();
        corpDTO.setCorpName(taskName);
        List<Map<String, Object>> list = tbWxFissionService.getWorkFissionList(corpDTO);
        return getDataTable(list);
    }

    @PostMapping("/getWorkFissionGroupList")
    public TableDataInfo getWorkFissionGroupList(@RequestBody CorpDTO corpDTO,String taskName) {
        startPage();
        corpDTO.setCorpName(taskName);
        List<Map<String, Object>> list = tbWxFissionService.getWorkFissionGroupList(corpDTO);
        return getDataTable(list);
    }
}
