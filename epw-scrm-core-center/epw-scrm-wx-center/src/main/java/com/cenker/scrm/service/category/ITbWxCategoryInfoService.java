package com.cenker.scrm.service.category;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.TreeSelect;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategoryInfo;

import java.util.List;

public interface ITbWxCategoryInfoService extends IService<TbWxCategoryInfo> {
    /**
     * 查询企业分类信息
     * @param tbWxCategoryInfo
     * @return 企业所有分类信息
     */
    List<TbWxCategoryInfo> selectCategoryInfoList(TbWxCategoryInfo tbWxCategoryInfo);

    /**
     * 构建企业分类信息树状结构
     * @param list
     * @return
     */
    List<TreeSelect> buildCategoryTreeSelect(List<TbWxCategoryInfo> list);

    /**
     * 分组排序
     * @param tbWxCategoryInfo
     */
    void sort(TbWxCategoryInfo tbWxCategoryInfo);

    /**
     * 删除分组
     * @param tbWxCategoryInfo
     */
    void delete(TbWxCategoryInfo tbWxCategoryInfo);
}
