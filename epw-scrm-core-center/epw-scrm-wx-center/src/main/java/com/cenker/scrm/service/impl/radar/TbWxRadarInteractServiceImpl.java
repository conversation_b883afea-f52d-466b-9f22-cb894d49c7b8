package com.cenker.scrm.service.impl.radar;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.biz.manager.WxMqSendMessageManager;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.*;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.OssStoragePathEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.mapper.radar.TbWxRadarInteractMapper;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarStatisticsDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContentRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.*;
import com.cenker.scrm.service.ISysDictDataService;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryService;
import com.cenker.scrm.service.radar.ITbWxRadarContentRecordService;
import com.cenker.scrm.service.radar.ITbWxRadarContentService;
import com.cenker.scrm.service.radar.ITbWxRadarInteractService;
import com.cenker.scrm.service.radar.ITbWxRadarTagRuleService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.file.ImageUtils;
import com.cenker.scrm.util.http.WebInfoFetcher;
import com.cenker.scrm.util.http.WechatArticleInfoExtractor;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxRadarInteractServiceImpl extends ServiceImpl<TbWxRadarInteractMapper, TbWxRadarInteract> implements ITbWxRadarInteractService {
    private static final String DELIVERY_CHANNEL_KEY = "material_delivery_channel";

    private final ITbWxRadarContentService radarContentService;
    private final WxMqSendMessageManager mqSendMessageManager;
    private final ITbWxRadarContentRecordService contentRecordService;
    private final ITbWxCustomerTrajectoryService trajectoryService;
    private final ITbWxRadarTagRuleService tagRuleService;
    private final OssStorageFactory ossStorageFactory;
    private final ISysDictDataService dictDataService;

    private final RedisCache redisCache;
    private final IApprovalService approvalService;
    private final RedissonClient redissonClient;

    @Override
    public List<InteractRadarVo> getRadarList(InteractRadarVo radarVo) {
        List<InteractRadarVo> radarList = baseMapper.getRadarList(radarVo);
        radarList.forEach(radar -> {
            TbWxRadarContent content = radar.getTbWxRadarContent();
            if (content!= null) {
                StringBuilder urlBuilder = new StringBuilder();
                urlBuilder.append(RadarConfig.getContentPage()).append("?id=").append(content.getId());
                if (radarVo.getClickSource() != null) {
                    urlBuilder.append("&clickSource=").append(radarVo.getClickSource());
                }
                if (StrUtil.isNotBlank(radarVo.getStaffId())) {
                    urlBuilder.append("&staffId=").append(radar.getStaffId());
                }

                content.setUrl(urlBuilder.toString());
            }
        });
        dealData(radarVo, radarList);
        return radarList;
    }

    private void dealData(InteractRadarVo radarVo, List<InteractRadarVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 处理 canApproval 字段
        // 是审批人，且不是创建人
        ApprovalTypeEnum typeEnum = Constants.RADAR_SCOPE_ENTERPRISE.equals(radarVo.getScope()) ? ApprovalTypeEnum.INTELLIGENTMATERIALS_ENTERPRISE : ApprovalTypeEnum.INTELLIGENTMATERIALS_PERSON;

        Result<Boolean> approvalUser = approvalService.isApprovalUser(typeEnum.getType());
        if (approvalUser.isSuccess() && approvalUser.getData()) {
            boolean isApproval = approvalUser.getData();
            for (InteractRadarVo vo : list) {
                boolean canApproval = vo.isEnableApproval() && isApproval
                        && !vo.getCreateBy().equals(radarVo.getUserId())
                        && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(vo.getStatus());
                vo.setCanApproval(canApproval);
            }
        }
    }
    @Override
    @Async
    @RedisLockAspect(key = "radar-sendMessage",value = "#mpWxUser.id + #record.contentId")
    public void sendMessageByReadRecord(TbWxRadarContentRecord record, MpWxUser mpWxUser) {
        // 找到对应的智能物料
        try {
            TbWxRadarContent tbWxRadarContent = radarContentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>()
                    .eq(TbWxRadarContent::getId, record.getContentId())
                    .eq(TbWxRadarContent::getDelFlag, StatusConstants.DEL_FLAG_FALSE), false);
            if (tbWxRadarContent == null) {
                log.info("【智能物料】发送企微消息，对应文章或智能物料已删除");
                return;
            }

            TbWxRadarInteract radar = getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                    .eq(TbWxRadarInteract::getId, tbWxRadarContent.getRadarId())
                    .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE), false);
            if (radar == null) {
                log.info("【智能物料】发送企微消息，对应文章或智能物料已删除");
                return;
            }

            // 向员工发送客户浏览物料的行为通知
            if (StatusConstants.OPEN_FLAG.equals(radar.getBehaviorInform()) && StrUtil.isNotBlank(record.getStaffId())) {
                sendBehaviorInformations(record, mpWxUser, tbWxRadarContent,  radar.getType());
            }

            if (StatusConstants.OPEN_FLAG.equals(radar.getDynamicInform())) {
                trajectoryService.informationNewsForRadar(radar, record, mpWxUser, tbWxRadarContent);
            }

            if (StatusConstants.OPEN_FLAG.equals(radar.getCustomerTag())) {
                log.info("【智能物料】客户标签开启：{}", record);
                tagRuleService.addTagByRuleAndReadRecord(radar, mpWxUser, record, tbWxRadarContent);
            }
        } catch (NullPointerException e) {
            log.error("【智能物料】发送企微消息，对应文章或智能物料已删除");
            log.error("错误信息:{}",e);
        }
    }

    /**
     * 发送行为通知
     * @param record
     * @param mpWxUser
     * @param tbWxRadarContent
     * @param radarType
     */
    private void sendBehaviorInformations(TbWxRadarContentRecord record, MpWxUser mpWxUser, TbWxRadarContent tbWxRadarContent, Integer radarType) {
        log.info("【智能物料】行为通知开启：{}", record);
        WxCpMessage wxCpMessage = new WxCpMessage();

        String source = getSource(String.valueOf(record.getClickSource()));
        wxCpMessage.setMsgType("text");
        wxCpMessage.setToUser(record.getStaffId());
        String content = "";

        // 转发行为
        if (StatusConstants.OPEN_FLAG.equals(record.getForwardTo())) {
            content = buildForwordMessageContent(source, mpWxUser, tbWxRadarContent);
            // 普通浏览行为
        } else {
            content = buildReadMessageContent(record, mpWxUser, tbWxRadarContent, source, radarType);
        }

        wxCpMessage.setContent(content);

        // 2023-08-10 改为MQ发送
        mqSendMessageManager.sendAgentMessage(wxCpMessage);
    }

    /**
     * 转发行为
     *
     * @param source           渠道
     * @param mpWxUser         当前阅读用户
     * @param tbWxRadarContent 图文对象
     * @return 发送消息体
     */
    private String buildForwordMessageContent(String source, MpWxUser mpWxUser, TbWxRadarContent tbWxRadarContent) {
       return String.format("【智能物料】\n@%s 转发了%s《%s》", mpWxUser.getNickName(), source, tbWxRadarContent.getTitle());
    }

    private String getSource(String clickSource) {
        String deliveryChannel = dictDataService.selectDictLabel(DELIVERY_CHANNEL_KEY, clickSource);
        if (StrUtil.isBlank(deliveryChannel)) {
            deliveryChannel = "未知";
        }

        return String.format("通过【%s】渠道发送", deliveryChannel);
    }

    /**
     * 组装浏览行为通知消息体
     * @param record
     * @param mpWxUser
     * @param tbWxRadarContent
     * @param source
     * @param radarType
     * @return
     */
    private String buildReadMessageContent(TbWxRadarContentRecord record, MpWxUser mpWxUser, TbWxRadarContent tbWxRadarContent,
                                           String source, Integer radarType) {
        // 查询是否存在阅读记录 如果有返回记录次数和总阅读秒数
        TbWxRadarContentRecord readRecord = contentRecordService.getReadRecordByParamId(record.getContentId(), record.getCustomerId(), record.getStaffId());

        // 如果是转发的链接，则显示转发人
        if (StringUtils.isNotEmpty(record.getForwardUser())) {
            source = "@" + record.getForwardUser() + "转发的";
        }

        String content = "";

        // 链接类型不记录阅读时长
        if (TypeConstants.RADAR_TYPE_LINK == radarType) {
            content = String.format("【智能物料】\n@%s 打开了%s《%s》", mpWxUser.getNickName(), source, tbWxRadarContent.getTitle());
            if (readRecord.getTotalReadNum() > 1) {
                content = content + "\n\n第" + readRecord.getTotalReadNum() + "次查看";
            }

            return content;
        }

        // 阅读时长
        String readTime = DateUtils.getSecondToStr(record.getReadTime());

        content = String.format("【智能物料】\n@%s 打开了%s《%s》，\n时长%s，阅读完成率为%s。",
                mpWxUser.getNickName(), source, tbWxRadarContent.getTitle(), readTime, record.getReadRate());
        if (readRecord.getTotalReadNum() > 1) {
            String totalReadTime = DateUtils.getSecondToStr(readRecord.getTotalReadTime());
            content = content + "\n\n第" + readRecord.getTotalReadNum() + "次查看，累计" + totalReadTime;
        }

        return content;
    }

    @Override
    public RadarStatisticsVO getRadarStatistics(String radarId, Date startTime, Date endTime) {
        TbWxRadarInteract radar = getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, radarId)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        // 当前智能物料的图文
        TbWxRadarContent content = radarContentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>()
                .eq(TbWxRadarContent::getRadarId, radarId));
        // 统计数据 点击次数 点击人数 带来客户数
        Map<String, Object> param = Maps.newHashMap();
        param.put("radarPrefix", WeConstants.RADAR_PREFIX);
        param.put("radarId", radarId);
        param.put("corpId", radar.getCorpId());
        RadarStatisticsVO radarStatisticsVo = baseMapper.getRadarStatistics(param);
        // 当天数据
        String today = DateUtil.today();
        param.put("date", today);
        RadarStatisticsVO radarStatisticsVo2 = baseMapper.getRadarStatistics(param);
        radarStatisticsVo.setNewContactCnt(radarStatisticsVo2.getTotalNewContactCnt());
        radarStatisticsVo.setClickNCnt(radarStatisticsVo2.getTotalClickNCnt());
        radarStatisticsVo.setClickNum(radarStatisticsVo2.getTotalClickNum());
        // 如果没有总数据
        if (startTime == null && radarStatisticsVo.getTotalClickNum().equals(0)) {
            startTime = DateUtils.addDays(endTime, -15);
        } else if (startTime == null) {
            // 从有数据的最后一天倒退15天(默认规则)
            endTime = contentRecordService.getEndDateByData(content);
            startTime = DateUtils.addDays(endTime, -15);
        }
        // 点击记录
        List<TbWxRadarContentRecord> tbWxRadarContentRecords = contentRecordService.statisticRecords(content.getId(), startTime, endTime);
        Map<String, List<TbWxRadarContentRecord>> contentRecordMap = tbWxRadarContentRecords.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(item -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getCreateTime())));
        // 日期数据
        List<RadarStatisticsDailyVO> data = Lists.newArrayList();
        DateUtils.findDates(startTime, endTime).stream().map(d -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, d)).forEach(
                date -> {
                    RadarStatisticsDailyVO vo = new RadarStatisticsDailyVO();
                    vo.setDay(date);
                    List<TbWxRadarContentRecord> records = contentRecordMap.get(date);
                    if (CollectionUtils.isNotEmpty(records)) {
                        // 代表这天有数据
                        vo.setClickNum(records.size());
                        // 客户id集合
                        Set<String> customerIds = records.stream().map(TbWxRadarContentRecord::getCustomerId).collect(Collectors.toSet());
                        vo.setVisitorNum(customerIds.size());
                        // 带来客户数
                        // int customerCount = customerService.countCustomerByCustomerIdsAndCorpId(customerIds, radar.getCorpId());
                        param.put("date", date);
                        RadarStatisticsVO radarStatisticsVo3 = baseMapper.getRadarStatistics(param);
                        vo.setCustomerNum(radarStatisticsVo3.getTotalNewContactCnt());
                    }
                    data.add(vo);
                }
        );
        // 设置日期数据
        radarStatisticsVo.setStartTime(startTime);
        radarStatisticsVo.setEndTime(endTime);
        radarStatisticsVo.setData(data);
        return radarStatisticsVo;
    }

    @Override
    public List<RadarCustomerStatisticsVO> getRadarReadRecordStatistics(RadarStatisticsDTO radarStatisticsDTO) {
        List<RadarCustomerStatisticsVO> list = baseMapper.getRadarReadRecordStatistics(radarStatisticsDTO);
        list.forEach(item -> item.setReadTime(DateUtil.secondToTime(Integer.parseInt(item.getReadTime()))));
        return list;
    }

    @Override
    public List<InteractRadarChatVO> getRadarChatList(InteractRadarVo radarVo) {
        return baseMapper.getRadarChatList(radarVo);
    }

    @Override
    public RadarStatisticsVO getRadarChatStatistics(String radarId) {
        TbWxRadarInteract radar = getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, radarId)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        // 统计数据 点击次数 点击人数 带来客户数
        Map<String, Object> param = Maps.newHashMap();
        param.put("radarId", radarId);
        param.put("radarPrefix", WeConstants.RADAR_PREFIX);
        param.put("corpId", radar.getCorpId());
        RadarStatisticsVO radarStatisticsVo = baseMapper.getRadarStatistics(param);
        // 当天数据
        param.put("date", DateUtil.today());
        RadarStatisticsVO radarStatisticsVo2 = baseMapper.getRadarStatistics(param);
        radarStatisticsVo.setClickNCnt(radarStatisticsVo2.getTotalClickNCnt());
        radarStatisticsVo.setNewContactCnt(radarStatisticsVo2.getTotalNewContactCnt());
        radarStatisticsVo.setClickNum(radarStatisticsVo2.getTotalClickNum());
        return radarStatisticsVo;
    }

    @Override
    public TbWxRadarInteract getOneById(String radarId) {
        return baseMapper.getOneById(radarId);
    }

    @Override
    public String getForwardUserById(String forwardUser) {
        return baseMapper.getForwardUserById(forwardUser);
    }

    @Override
    public TbWxRadarInteract selectRadarByContentId(String contentId) {
        return baseMapper.selectRadarByContentId(contentId);
    }

    @Override
    public void extractRadarContent(TbWxRadarContent content,boolean extractContent) {
        extractWebContent(content, extractContent);
    }

    @Override
    public void linkData(TbWxRadarContent content) {
        extractWebContent(content, false);
    }

    /**
     * 提取网页信息
     *
     * @param content
     * @param extractContent 是否提取文章内容
     */
    private void extractWebContent(TbWxRadarContent content, boolean extractContent) {
        try {
            if (WechatArticleInfoExtractor.isWechatArticleUrl(content.getUrl())) {
                Document doc = Jsoup.connect(content.getUrl()).get();
                content.setTitle(WechatArticleInfoExtractor.getTitle(doc));
                content.setDigest(WechatArticleInfoExtractor.getAbstract(doc));
                content.setCover(WechatArticleInfoExtractor.getCover(doc));
                if (extractContent) {
                    content.setContent(getWechatArticleContent(doc));
                    content.setType(2);
                }

            } else if (WebInfoFetcher.isStaticPage(content.getUrl())) {
                Document doc = Jsoup.connect(content.getUrl()).get();
                content.setTitle(doc.title());
                content.setDigest(WebInfoFetcher.getDescription(doc));
                content.setCover(WebInfoFetcher.getIconUrl(doc, content.getUrl()));
            }

            String cover = this.uploadPictureToCdn(content.getCover(), FileTypeConstant.IMAGE_PNG);
            content.setCover(cover);

            if (StringUtils.isEmpty(cover)) {
                content.setCover(redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER));
            }
        } catch (Exception e) {
            log.error("【智能物料】提取网页信息失败，链接：{}", content.getUrl(), e);
        }

        if (StringUtils.isEmpty(content.getTitle())) {
            content.setTitle("链接标题");
        }

        if (StringUtils.isEmpty(content.getDigest())) {
            content.setDigest("链接描述");
        }
    }

    /**
     * 获取微信文章内容
     *
     * @param doc
     * @return
     */
    private String getWechatArticleContent(Document doc) {
        Element contentElement = doc.selectFirst(WechatArticleInfoExtractor.ARTICLE_CONTENT_SELECTOR);

        if (contentElement == null) {
            return null;
        }

        String contentText = contentElement.html();

        // 对微信图片进行处理
        Elements imgTag = contentElement.select("img");
        if (CollectionUtils.isEmpty(imgTag)) {
            return contentText;
        }

        for (Element element : imgTag) {
            String dataSrc = element.attr("data-src");
            String dataType = element.attr("data-type");
            // 将图片存储到自己的对象存储中
            String src = this.uploadPictureToCdn(dataSrc,dataType);
            contentText = contentText.replace(dataSrc, src);
        }

        contentText = contentText.replace("data-src", "src");
        return contentText;
    }

    @Override
    public List<InteractRadarVo> getRadarSource(InteractRadarVo radarVo) {
        return baseMapper.getRadarSource(radarVo);
    }

    @Override
    public List<RadarStatisticsRankVO> getRadarStatisticsRank(CurrentUserDTO currentUserDTO, QueryRadarStatisticsRankDTO dto) {
        return baseMapper.getRadarStatisticsRank(currentUserDTO.getCorpId(),dto);
    }

    /**
     * 转换网络图片为自己对象存储的图片 如果失败返回原有
     */
    private String extractOnlinePictureToCdn(String dataSrc, String dataType) {
        if (StringUtils.isNotEmpty(dataSrc)) {
            try {
                InputStream inputStream = ImageUtils.getFile(dataSrc);
                // 上传至服务器
                String src = ossStorageFactory.build().upload(inputStream, UUID.randomUUID().toString() + SeparatorEnum.DOT.getSeparator() + dataType,
                        OssStoragePathEnum.RADAR_CONTACT_PATH.getPath());
                assert inputStream != null;
                inputStream.close();
                return StringUtils.isNotEmpty(src) ? src : dataSrc;
            } catch (Exception e) {
                log.error("【智能物料】转换微信图片失败");
                log.error("错误信息:{}",e);
                return dataSrc;
            }
        }
        return dataSrc;
    }

    /**
     * 上传图片到对象存储
     *
     * @param srcUrl
     * @param dataType
     * @return
     */
    private String uploadPictureToCdn(String srcUrl, String dataType) {
        if (StringUtils.isBlank(srcUrl)) {
            return srcUrl;
        }

        ByteArrayOutputStream outputStream = null;
        InputStream inputStream = null;

        try {
            outputStream = new ByteArrayOutputStream();
            inputStream = ImageUtils.getFile(srcUrl);
            ImgUtil.convert(inputStream, dataType, outputStream);

            String cdnPicUrl = ossStorageFactory.build().upload(new ByteArrayInputStream(outputStream.toByteArray()), UUID.randomUUID().toString() + SeparatorEnum.DOT.getSeparator() + dataType,
                    OssStoragePathEnum.RADAR_CONTACT_PATH.getPath());
            return cdnPicUrl;
        } catch (Exception e) {
            log.error("【智能物料】上传图片失败，图片地址：{}", srcUrl, e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }

        return "";
    }

    private Map<String, Object> getRequestMap() {
        Map<String, Object> map = new HashMap<>(new LinkedHashMap<>());
        map.put("Accept", "text/html, application/xhtml+xml, image/jxr, */*");
        map.put("Accept-Encoding", "gzip, deflate");
        map.put("Accept-Language", "zh-Hans-CN, zh-Hans; q=0.8, en-US; q=0.5, en; q=0.3");
        map.put("Host", "mp.weixin.qq.com");
        map.put("If-Modified-Since", "Sat, 04 Jan 2020 12:23:43 GMT");
        map.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
        return map;
    }

    @Override
    public Result approve(ApprovalVO approvalVO, LoginUser loginUser) {
        String approvalId = approvalVO.getId();
        TbWxRadarInteract info = this.getById(approvalId);
        if (info == null) {
            return Result.error(500, "未找到智能物料信息！");
        }
        log.info("【审核智能物料】审核对象：{}", JSON.toJSONString(info));
        String type = Objects.equals(info.getScope(), Constants.RADAR_SCOPE_ENTERPRISE) ? ApprovalTypeEnum.INTELLIGENTMATERIALS_ENTERPRISE.getType() : ApprovalTypeEnum.INTELLIGENTMATERIALS_PERSON.getType();
        String key = approvalService.getApprovalKey(approvalId, type);
        RLock lock = redissonClient.getLock(key);
        try {
            boolean cacheRes = lock.tryLock(5L, 30L, TimeUnit.SECONDS);
            if (!cacheRes) {
                return Result.error(500, "获取锁失败，其他用户正在执行审核操作！");
            }
            log.info("【审核智能物料】加锁成功：{}", key);
            Result<Object> validateApproval = approvalService.validateApproval(loginUser, type, info.getTitle(), info.getCreateBy()+"", info.getStatus());
            if (validateApproval != null) return validateApproval;
            if (Objects.equals(approvalVO.getAgree(), true)) {
                this.lambdaUpdate()
                        .set(TbWxRadarInteract::getStatus, ApprovalStatusEnum.EXECUTING.getStatus())
                        .set(TbWxRadarInteract::getApprovalUser, loginUser.getUser().getUserId())
                        .set(TbWxRadarInteract::getApprovalRemark, approvalVO.getRemark())
                        .eq(TbWxRadarInteract::getId, approvalId).update();
                log.info("【审核智能物料】审核通过，更新为使用中状态");

                // 发送通知
                approvalService.sendApprovalMsg(info, type, true,
                        TbWxRadarInteract::getTitle,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString(), false, getBusinessJson(info));
                log.info("【审核智能物料】审核通过，发送通知完成");
            } else {
                // 如果为 null 或者 "" 则替换为 "内容未满足审核标准，请与审核员沟通后修改并重新提交"
                if (StrUtil.isEmptyIfStr(approvalVO.getRemark())) {
                    approvalVO.setRemark("内容未满足审核标准，请与审核员沟通后修改并重新提交");
                }
                this.lambdaUpdate()
                        .set(TbWxRadarInteract::getStatus, ApprovalStatusEnum.REJECTED.getStatus())
                        .set(TbWxRadarInteract::getApprovalUser, loginUser.getUser().getUserId())
                        .set(TbWxRadarInteract::getApprovalRemark, approvalVO.getRemark())
                        .eq(TbWxRadarInteract::getId, approvalId).update();
                // 发送通知
                approvalService.sendApprovalMsg(info, type, false,
                        TbWxRadarInteract::getTitle,
                        t -> t.getCreateBy().toString(),
                        t -> t.getId().toString(), false, getBusinessJson(info));
                log.info("【审核智能物料】审核驳回，更新数据库后，发送通知完成");
            }
        } catch (InterruptedException e) {
            log.error("【审核智能物料】获取锁失败！", e);
            return Result.error(500, "获取锁失败！");
        } catch (Exception e1) {
            log.error("【审核智能物料】审批异常：{}", e1.getMessage(), e1);
            return Result.error(500, e1.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("【审核智能物料】释放锁成功！");
            }
        }

        return Result.success();
    }

    /**
     * 构建系统通知需要的 json 参数
     * @param info
     * @return
     */
    @Override
    public String getBusinessJson(TbWxRadarInteract info) {
        if (info!= null && info.isEnableApproval()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("scope", info.getScope());
            jsonObject.put("status", info.getStatus());
            jsonObject.put("title", info.getTitle());
            jsonObject.put("type", info.getType());
            return jsonObject.toJSONString();
        }
        return "{}";
    }

    @Override
    public Result revoked(ApprovalVO approvalVO, LoginUser loginUser) {
        TbWxRadarInteract info = this.getById(approvalVO.getId());
        if (info == null) {
            return Result.error(500, "未找到审核智能物料信息！");
        }
        log.info("【撤回审核智能物料】撤回对象：{}", JSON.toJSONString(info));
        if (!info.isEnableApproval()) {
            return Result.error(500, "该任务不支持撤回操作！");
        }
        if (!Objects.equals(info.getStatus(), ApprovalStatusEnum.PENDING_APPROVAL.getStatus())) {
            return Result.error(500, "不是待审核状态不支持撤回操作！");
        }
        if (!Objects.equals(loginUser.getUser().getUserId(), info.getCreateBy()+"")) {
            return Result.error(500, "用户不能对其他人创建的内容进行撤回！");
        }
        log.info("【撤回审核智能物料】检验完成");
        this.lambdaUpdate()
                .set(TbWxRadarInteract::getStatus, ApprovalStatusEnum.REVOKED.getStatus())
                .eq(TbWxRadarInteract::getId, approvalVO.getId())
                .eq(TbWxRadarInteract::getStatus, ApprovalStatusEnum.PENDING_APPROVAL.getStatus()).update();
        log.info("【撤回审核智能物料】更新为已撤回状态");
        return Result.success();
    }

    public static void main(String[] args) {

    }
}
