package com.cenker.scrm.controller.external;

import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import com.cenker.scrm.constants.*;
import com.cenker.scrm.pojo.entity.wechat.TbWxDrainageShortLink;
import com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.drainage.DrainageShortLinkShowInfoVO;
import com.cenker.scrm.service.drainage.ITbWxDrainageLinkClickService;
import com.cenker.scrm.service.drainage.ITbWxDrainageShortLinkService;
import com.cenker.scrm.service.wxmp.WxAppService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.http.RequestUtil;
import com.google.gson.JsonObject;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonParser;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static cn.binarywang.wx.miniapp.constant.WxMaApiUrlConstants.Link.GENERATE_URLLINK_URL;

/**
 * <AUTHOR>
 * @Date 2022/5/9
 * @Description 引流链接外部接口
 */
@RequestMapping("/drainageLink/external")
@RestController
@Slf4j
@RequiredArgsConstructor
public class ExternalDrainageLinkController {

    private final ITbWxDrainageShortLinkService drainageShortLinkService;
    private final ITbWxDrainageLinkClickService clickService;
    private final WxAppService wxAppService;
    private final RedissonClient redissonClient;
    @Value("${wechat.open.mini_program_url}")
    private String miniProgramUrl;


    @GetMapping("/getWxShortLink/{shortValue}")
    public AjaxResult getWxShortLink(@PathVariable("shortValue") String shortValue) {
        try {
            /**
             * 由于web的限制 无法准确区分用户 所以由前端记录对应浏览器是否
             */
            // 获取设备号（ip + 系统版本/浏览器版本）
            String deviceId = getDeviceId();
            log.info("【引流短链】获取的短链参数；{},使用设备号:{}", shortValue, deviceId);
            // 根据参数获取短链信息并校验
            TbWxDrainageShortLink drainageShortLink = drainageShortLinkService.getInfoByShortValueAndVerify(shortValue);
            // 记录点击人数
            clickService.recordClickDataByDrainageShortLink(drainageShortLink, deviceId);
            // 获取小程序信息并校验
            WxApp wxApp = wxAppService.getInfoByCorpConfigIdAndVerify(drainageShortLink.getCorpId(), shortValue);

            // 私有化部署改为自研小程序获取
            WxMaServiceImpl wxMaService = new WxMaServiceImpl();
            WxMaRedissonConfigImpl wxMaRedisConfig = new WxMaRedissonConfigImpl(redissonClient, "miniapp");
            wxMaRedisConfig.setAppid(wxApp.getAppId());
            wxMaRedisConfig.setSecret(wxApp.getSecret());
            wxMaService.setWxMaConfig(wxMaRedisConfig);
            String openLink = generateUrlLink(wxMaService, GenerateUrlLinkRequest.builder()
                    .path(wxApp.getDefaultPage())
                    // 正式版为 "release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效
                    .envVersion("trial")
                    .query("url=" + miniProgramUrl + "&shortValue=" + shortValue)
                    .build());
            return new AjaxResult(HttpStatus.SUCCESS, "生成成功", openLink);
        } catch (WxErrorException e) {
            log.info("【引流短链】生成微信短链错误：{}", e.getMessage());
            if (e.getMessage().contains(String.valueOf(WeConstants.SCHEME_URL_LINK_COUNT_LIMIT))) {
                // 数量超过限制
                return new AjaxResult(WeConstants.SCHEME_URL_LINK_COUNT_LIMIT, "短链生成数量已达到上限", null);
            }
        }
        return AjaxResult.error();
    }

    /**
     * 获取用户设备信息
     */
    private String getDeviceId() {
        HttpServletRequest request = ServletUtils.getRequest();
        Integer platformByHead = RequestUtil.getPlatformByHead(request);
        if (platformByHead.equals(TypeConstants.LOGIN_IP_FROM_WEB)) {
            throw new CustomException("请在手机端打开");
        }
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        String ipAddress = RequestUtil.getIpAddress(ServletUtils.getRequest());
        return ipAddress + "_" + userAgent.getId();
    }

    @GetMapping("/getMiniProgramDrainageInfo/{shortValue}")
    public AjaxResult getMiniProgramDrainageInfo(@PathVariable("shortValue") String shortValue) {
        DrainageShortLinkShowInfoVO shortLinkShowInfoVo = new DrainageShortLinkShowInfoVO();
        // 体验版测试
        if (shortValue.equals(DefaultConstants.DRAINAGE_SHORT_LINK_TEST_SHORT_VALUE)) {
            // 体验版测试模式
            shortLinkShowInfoVo.setCurrentPage("/pages/index/ckylink");
            shortLinkShowInfoVo.setAvatar(DefaultConstants.CKY_LOGO);
            shortLinkShowInfoVo.setCodeUrl(DefaultConstants.CKY_CUSTOMER_SERVICE_STAFF_AVATAR);
            shortLinkShowInfoVo.setGuideContent("扫码添加我的企业微信为您定制更多专属服务");
            shortLinkShowInfoVo.setPageTitle("添加客服");
            shortLinkShowInfoVo.setCorpName("成客数科");
            shortLinkShowInfoVo.setNickName("成客客服");
            shortLinkShowInfoVo.setShowBaseInfo(StatusConstants.SHOW_FLAG_TRUE);
            shortLinkShowInfoVo.setShowCorpInfo(StatusConstants.SHOW_FLAG_TRUE);
            return AjaxResult.success(shortLinkShowInfoVo);
        }
        // 根据参数获取短链信息并校验
        TbWxDrainageShortLink drainageShortLink = drainageShortLinkService.getInfoByShortValueAndVerify(shortValue);
        // 获取小程序信息并校验
        WxApp wxApp = wxAppService.getInfoByCorpConfigIdAndVerify(drainageShortLink.getCorpId(), shortValue);
        BeanUtils.copyProperties(drainageShortLink, shortLinkShowInfoVo);
        shortLinkShowInfoVo.setCurrentPage(wxApp.getDefaultPage());
        return AjaxResult.success(shortLinkShowInfoVo);
    }

    /**
     * 获取短链
     *
     * @param wxMaService
     * @param request
     * @return
     * @throws WxErrorException
     */
    private String generateUrlLink(WxMaServiceImpl wxMaService, GenerateUrlLinkRequest request) throws WxErrorException {
        String result = wxMaService.post(GENERATE_URLLINK_URL, request);
        String linkField = "url_link";
        JsonObject jsonObject = GsonParser.parse(result);
        if (jsonObject.has(linkField)) {
            return jsonObject.get(linkField).getAsString();
        }
        throw new WxErrorException("无url_link");
    }
}
