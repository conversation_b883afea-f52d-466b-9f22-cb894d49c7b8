package com.cenker.scrm.handler.task;

import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.transfer.SyncTransferStatusDTO;
import com.cenker.scrm.service.corp.ITbWxTransferService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class TransferStatusTask  {
    private final ITbWxTransferService tbWxTransferService;
    @XxlJob(XxlJobContant.SYNCHRONIZE_ALL_TRANSFER)
    public void synchronizeAllTransfer() throws Exception{
        log.info("【定时任务】开始同步在职，离职转移资源状态数据");
        tbWxTransferService.syncOnJobTransferStatus(new CurrentUserDTO(CorpInfoProperties.getCorpId(), null),new SyncTransferStatusDTO());

    }
}
