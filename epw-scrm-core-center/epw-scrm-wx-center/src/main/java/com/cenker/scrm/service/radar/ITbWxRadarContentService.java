package com.cenker.scrm.service.radar;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.vo.radar.RadarContentVO;

public interface ITbWxRadarContentService extends IService<TbWxRadarContent> {
    /**
     * 添加智能物料图文类型
     * @param tbWxRadarContent
     */
    void addContent(TbWxRadarContent tbWxRadarContent);

    /**
     * 查询文章详情
     * @param id
     * @return
     */
    RadarContentVO getRadarContentH5(String id);

    /**
     * PDF文件转PNG图片，全部页数
     * @param tbWxRadarContent
     */
    void pdf2Image(TbWxRadarContent tbWxRadarContent);
}
