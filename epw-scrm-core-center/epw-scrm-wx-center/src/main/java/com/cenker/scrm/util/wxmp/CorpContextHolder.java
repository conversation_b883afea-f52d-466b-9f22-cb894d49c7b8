package com.cenker.scrm.util.wxmp;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.experimental.UtilityClass;


@UtilityClass
public class CorpContextHolder {

	private final ThreadLocal<String> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();


	/**
	 * TTL 设置企业ID
	 *
	 * @param corpId
	 */
	public void setCorpId(String corpId) {
		THREAD_LOCAL_TENANT.set(corpId);
	}

	/**
	 * 获取TTL中的企业ID
	 *
	 * @return
	 */
	public String getCorpId() {
		return THREAD_LOCAL_TENANT.get();
	}

	public void clear() {
		THREAD_LOCAL_TENANT.remove();
	}
}
