package com.cenker.scrm.service.corp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.wechat.AppWxCorpPermanentToken;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;

import java.util.List;

public interface IAppWxCorpPermanentTokenService extends IService<AppWxCorpPermanentToken> {
    /**
     * 处理收到代自建应用授权事件
     * @param authCode 授权码
     */
    void handleCorpInfo(String authCode);

    /**
     * 处理代自建取消授权
     * @param token
     */
    void saveOrUpdateCorpPermanentToken(AppWxCorpPermanentToken token);

    /**
     * 更新代自建永久授权码(即secret)并刷新缓存
     * @param authCode
     */
    void updateAppPermanentTokenAndRefreshCpService(String authCode);

    /**
     * 查询有效的服务客户企业id
     * @return
     */
    List<String> selectAppCorpIdList();

    /**
     * 查询是否企业安装人
     */
    boolean isCorpAppInstallUser(String corpId, String userId);

    /**
     * 查询企业安装人id
     */
    String selectAppCorpAdmin(String corpId);

    /**
     * 查询有效的企业配置信息
     */
    List<TbWxCorpConfig> selectAppCorpConfigList();

    /**
     * 根据企业id查询有效的代自建安装
     * @param corpId
     * @return
     */
    AppWxCorpPermanentToken getAppCorpPermanentInfo(String corpId);
}
