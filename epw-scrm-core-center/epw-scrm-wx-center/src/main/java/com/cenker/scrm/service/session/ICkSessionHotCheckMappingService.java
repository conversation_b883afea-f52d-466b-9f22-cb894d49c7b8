package com.cenker.scrm.service.session;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.session.CkSessionHotCheckMapping;

/**
 * 热词审计人关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface ICkSessionHotCheckMappingService extends IService<CkSessionHotCheckMapping>
{
    /**
     * 查询热词审计人关联
     * 
     * @param id 热词审计人关联主键
     * @return 热词审计人关联
     */
    public CkSessionHotCheckMapping selectCkSessionHotCheckMappingById(Long id);

    /**
     * 查询热词审计人关联列表
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 热词审计人关联集合
     */
    public List<CkSessionHotCheckMapping> selectCkSessionHotCheckMappingList(CkSessionHotCheckMapping ckSessionHotCheckMapping);

    /**
     * 新增热词审计人关联
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 结果
     */
    public int insertCkSessionHotCheckMapping(CkSessionHotCheckMapping ckSessionHotCheckMapping);

    /**
     * 修改热词审计人关联
     * 
     * @param ckSessionHotCheckMapping 热词审计人关联
     * @return 结果
     */
    public int updateCkSessionHotCheckMapping(CkSessionHotCheckMapping ckSessionHotCheckMapping);

    /**
     * 批量删除热词审计人关联
     * 
     * @param ids 需要删除的热词审计人关联主键集合
     * @return 结果
     */
    public int deleteCkSessionHotCheckMappingByIds(Long[] ids);

    /**
     * 删除热词审计人关联信息
     * 
     * @param id 热词审计人关联主键
     * @return 结果
     */
    public int deleteCkSessionHotCheckMappingById(Long id);
}
