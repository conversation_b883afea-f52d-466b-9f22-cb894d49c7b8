package com.cenker.scrm.service.impl.moment;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.manager.WxMqSendMessageManager;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.mapper.moment.TbWxMomentSendUserMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentTaskInfo;
import com.cenker.scrm.pojo.vo.moment.SendMomentUserListVo;
import com.cenker.scrm.pojo.vo.moment.SendMomentVo;
import com.cenker.scrm.service.moment.ITbWxMomentSendUserService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/1/18
 * @Description 朋友圈执行者
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxMomentSendUserServiceImpl extends ServiceImpl<TbWxMomentSendUserMapper, TbWxMomentSendUser> implements ITbWxMomentSendUserService {

    private final WxMqSendMessageManager mqSendMessageManager;


    @Override
    public String remindToSendById(String id) {
        TbWxMomentSendUser sendUser = getById(id);
        if (ObjectUtil.isNotNull(sendUser)) {
            if (sendUser.getPublishStatus() == StatusConstants.PUBLISH_MOMENT_TRUE) {
                return "该员工已发送，无需重复提醒";
            }
            sendMessage(sendUser.getUserId(), sendUser.getCorpId());
            return null;
        }
        return "该条数据不存在，请刷新重试";
    }

    private void sendMessage(String userId, String corpId) {
        WxCpMessage wxCpMessage = new WxCpMessage();
        wxCpMessage.setMsgType("text");
        wxCpMessage.setToUser(userId);
        String content = "【朋友圈任务提醒】\n\n" +
                "你有一条朋友圈消息还未发送，可在【企业微" +
                "信】-【工作台】\n-【客户朋友圈】中进行发送";
        wxCpMessage.setContent(content);
        mqSendMessageManager.sendAgentMessage(wxCpMessage);
    }

    @Override
    public void remindToSendAll(TbWxMomentTaskInfo taskInfo) {
        SendMomentVo sendMomentVo = new SendMomentVo();
        sendMomentVo.setId(taskInfo.getId());
        sendMomentVo.setPublishStatus(StatusConstants.PUBLISH_MOMENT_FALSE);
        List<SendMomentUserListVo> momentSendUserList = getMomentSendUserList(sendMomentVo);
        if (CollectionUtils.isNotEmpty(momentSendUserList)) {
            log.info("【发朋友圈】开始提醒未发表朋友圈成员，任务id{}", taskInfo.getId());
            if (momentSendUserList.size() < 100) {
                String userIds = momentSendUserList.stream().map(SendMomentUserListVo::getUserId).collect(Collectors.joining("|"));
                sendMessage(userIds, taskInfo.getCorpId());
                return;
            }
            /**
             * 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。
             * 特殊情况：指定为"@all"，则向该企业应用的全部成员发送
             */
            // 拆分多个100发送
            List<List<SendMomentUserListVo>> partition = Lists.partition(momentSendUserList, 100);
            for (List<SendMomentUserListVo> sendMomentUserListVos : partition) {
                String userIds = sendMomentUserListVos.stream().map(SendMomentUserListVo::getUserId).collect(Collectors.joining("|"));
                sendMessage(userIds, taskInfo.getCorpId());
            }
        }
    }

    @Override
    public List<SendMomentUserListVo> getMomentSendUserList(SendMomentVo sendMomentVo) {
        return baseMapper.getMomentSendUserList(sendMomentVo);
    }

    @Override
    public List<SendMomentUserListVo> getMomentSendCusList(SendMomentVo sendMomentVo) {
        return baseMapper.getMomentSendCusList(sendMomentVo);
    }

    @Override
    public List<TbWxMomentSendUser> selectSendUser(String momentTaskId, Integer publishStatus, Integer queryCustomerStatus) {
        return baseMapper.selectSendUser(momentTaskId, publishStatus, queryCustomerStatus);
    }
}
