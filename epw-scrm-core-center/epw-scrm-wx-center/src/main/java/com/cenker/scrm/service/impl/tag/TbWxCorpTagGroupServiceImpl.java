package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.enums.*;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.mapper.external.TbWxExtCustomerMapper;
import com.cenker.scrm.mapper.tag.TbWxCorpTagGroupMapper;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.exception.WeComException;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupService;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.service.tag.TbWxExtFollowUserTagService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupInfo;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企业客户标签组Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@Slf4j
public class TbWxCorpTagGroupServiceImpl extends ServiceImpl<TbWxCorpTagGroupMapper, TbWxCorpTagGroup> implements ITbWxCorpTagGroupService {

    @Autowired
    private TbWxCorpTagGroupMapper tbWxCorpTagGroupMapper;
    @Autowired
    private ITbWxCorpTagService tbWxCorpTagService;
    @Lazy
    @Autowired
    private TbWxExtFollowUserTagService tbWxExtFollowUserTagService;
    @Autowired
    private WxCpFeign wxCpFeign;
    @Autowired
    private MqSendMessageManager mqSendMessageManager;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private TbWxExtCustomerMapper tbWxExtCustomerMapper;

    /**
     * 查询企业客户标签组
     *
     * @param groupId 企业客户标签组ID
     * @return 企业客户标签组
     */
    @Override
    public TbWxCorpTagGroup selectTbWxCorpTagGroupById(String groupId) {
        return tbWxCorpTagGroupMapper.selectTbWxCorpTagGroupById(groupId);
    }

    /**
     * 查询企业客户标签组列表
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 企业客户标签组
     */
    @Override
    public List<TbWxCorpTagGroup> selectTbWxCorpTagGroupList(TbWxCorpTagGroup tbWxCorpTagGroup) {
        List<TbWxCorpTagGroup> groups = tbWxCorpTagGroupMapper.selectTbWxCorpTagGroupList(tbWxCorpTagGroup);
        if (CollectionUtils.isNotEmpty(groups)) {
            String[] ids = groups.stream().map(TbWxCorpTagGroup::getGroupId).toArray(String[]::new);
            // 标签列表支持查询删除状态的标签组，因此此接口需要返回删除状态的标签
            List<TbWxCorpTag> tags = tbWxCorpTagService.selectTbWxCorpTagGroupList(tbWxCorpTagGroup.getCorpId(), ids, Objects.equals(tbWxCorpTagGroup.getStatus(), "0") ? 0 : null);
            Map<String, List<TbWxCorpTag>> mapTags = new HashMap<>(6);
            if (CollectionUtils.isNotEmpty(tags)) {
                mapTags = tags.stream().collect(Collectors.groupingBy(TbWxCorpTag::getGroupId));
            }
            for (TbWxCorpTagGroup group : groups) {
                List<TbWxCorpTag> tbWxCorpTags = mapTags.get(group.getGroupId());
                if (CollectionUtils.isNotEmpty(tbWxCorpTags)) {
                    // tbWxCorpTags 按order从小到大排序
                    tbWxCorpTags.sort(Comparator.comparing(TbWxCorpTag::getOrder));
                    // group 的 tagList 的状态和 group 状态一致
                    group.setTagList(tbWxCorpTags.stream().filter(tag -> Objects.equals(tag.getStatus(), group.getStatus())).collect(Collectors.toList()));
                    String[] names = tbWxCorpTags.stream().filter(tag -> Objects.equals(tag.getStatus(), group.getStatus())).map(TbWxCorpTag::getName).toArray(String[]::new);
                    group.setName(Arrays.asList(names));
                }
            }
        }
        return groups;
    }

    /**
     * 新增企业客户标签组 并调用企业微信接口新增标签接口
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbWxCorpTagGroup insertTbWxCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup, boolean synFlag) {
        String corpId = tbWxCorpTagGroup.getCorpId();
        if (tbWxCorpTagGroup.getTagList().size() > 0) {
            log.info("新增企业客户标签完成，添加标签处理corpId={}", corpId);
            WxCpUserExternalTagGroupInfo groupInfo = new WxCpUserExternalTagGroupInfo();
            WxCpUserExternalTagGroupInfo.TagGroup tagGroup = new WxCpUserExternalTagGroupInfo.TagGroup();
            tagGroup.setGroupName(tbWxCorpTagGroup.getGroupName());
            groupInfo.setTagGroup(tagGroup);
            tagGroup.setTag(new ArrayList<>());
            Long order = 0L;
            for (TbWxCorpTag tbWxCorpTag : tbWxCorpTagGroup.getTagList()) {
                WxCpUserExternalTagGroupInfo.Tag tag = new WxCpUserExternalTagGroupInfo.Tag();
                tag.setName(tbWxCorpTag.getName());
                tag.setOrder(order++);
                tagGroup.getTag().add(tag);
                tbWxCorpTag.setCorpId(corpId);
                tbWxCorpTag.setStatus(UserConstants.NORMAL);
            }
            try {
                log.info("新增企业客户标签，同步数据至企微，{}", tbWxCorpTagGroup);
                // work调用获取
                WxCpUserExternalTagGroupInfo result = wxCpFeign.addCorpTag(groupInfo, corpId);
                if (result.getTagGroup() != null) {
                    tbWxCorpTagGroup.setSynStatus(Constants.YES);
                    tbWxCorpTagGroup.setType(Constants.CUSTOMER);
                    tbWxCorpTagGroup.setGroupId(result.getTagGroup().getGroupId());
                    tbWxCorpTagGroupMapper.insert(tbWxCorpTagGroup);
                    Map<String, WxCpUserExternalTagGroupInfo.Tag> tagMap = new HashMap<>(6);
                    if (result.getTagGroup().getTag().size() > 0) {
                        tagMap = result.getTagGroup().getTag().stream().
                                collect(Collectors.toMap(WxCpUserExternalTagGroupInfo.Tag::getName, Function.identity(), (x, x1) -> x1));
                    }
                    for (TbWxCorpTag tag : tbWxCorpTagGroup.getTagList()) {
                        WxCpUserExternalTagGroupInfo.Tag tag1 = tagMap.get(tag.getName());
                        tag.setOrder(tag1.getOrder().intValue());
                        tag.setTagId(tag1.getId());
                        tag.setCreateBy(tbWxCorpTagGroup.getCreateBy());
                        tag.setGroupId(result.getTagGroup().getGroupId());
                    }
                    tbWxCorpTagService.saveOrUpdateBatch(tbWxCorpTagGroup.getTagList(), 300);
                }
            } catch (WeComException e) {
                log.error("新增企业客户标签失败：标签同步失败原因:{},失败Exception：{}", e.getMessage(), e);
                tbWxCorpTagGroup.setRemark(e.getMessage());
                return tbWxCorpTagGroup;
            }
        }
        return tbWxCorpTagGroup;
    }

    /**
     * 修改企业客户标签组
     *
     * @param tbWxCorpTagGroup 企业客户标签组
     * @return 结果
     */
    @Override
    public int updateTbWxCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup) {
        return tbWxCorpTagGroupMapper.updateTbWxCorpTagGroup(tbWxCorpTagGroup);
    }

    /**
     * 通过前端传过来的tag进行过滤，如果有新增的tag或者删除的tag就调用微信接口
     *
     * @param tbWxCorpTagGroup
     * @param synFlag
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTbWxCorpTagAndCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup, boolean synFlag) throws WxErrorException {
        updateCorpTagGroup(tbWxCorpTagGroup);
        log.info("修改企业客户标签组,保存CorpTagGroup");
        updateCorpTag(tbWxCorpTagGroup);
        log.info("修改企业客户标签,保存CorpTag");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTagGroup(String[] groupId, String userName, String corpId) throws WxErrorException {
        try {
            WxCpBaseResp wxCpBaseResp = wxCpFeign.delCorpTag(new String[]{}, groupId, corpId);
        } catch (Exception e) {
            log.error("删除企业微信标签组失败: errmsg:{}", e);
            throw new WxErrorException("删除企业微信标签失败");
        }

        List<TbWxCorpTagGroup> tagGroupList = this.lambdaQuery().in(TbWxCorpTagGroup::getGroupId, groupId).list();
        List<String> groupNames = tagGroupList.stream().map(TbWxCorpTagGroup::getGroupName).collect(Collectors.toList());
        String operDesc = String.format("删除客户标签组[%s]", StrUtil.join(StrUtil.COMMA, groupNames));
        LogUtil.recordOperLog(ModuleEnum.CUSTOMER_TAG, BusinessType.DELETE, operDesc);

        //更新标签组表和标签表
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("group_id", groupId);
        TbWxCorpTagGroup tagGroup = new TbWxCorpTagGroup();
        TbWxCorpTag tbWxCorpTag = new TbWxCorpTag();
        tagGroup.setUpdateBy(userName);
        tagGroup.setStatus(UserConstants.DEL);
        tbWxCorpTag.setUpdateBy(userName);
        tbWxCorpTag.setStatus(UserConstants.DEL);
        tbWxCorpTagGroupMapper.update(tagGroup, wrapper);
        tbWxCorpTagService.update(tbWxCorpTag, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSynTags(String corpId) {
        try {
            WxCpUserExternalTagGroupList list = wxCpFeign.getCorpTagList(new String[]{}, new String[]{}, corpId);
            updateTagGroupList(corpId, list);
        } catch (Exception e) {
            log.error("批量获取企业标签数据失败: errmsg:{}", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncUpdateCorpTagGroup(String corpId, WxCpUserExternalTagGroupList list) {
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtils.isNotEmpty(tagGroupList)) {
            List<TbWxCorpTagGroup> groupExistList = Lists.newArrayList();
            for (WxCpUserExternalTagGroupList.TagGroup tagGroup : tagGroupList) {
                TbWxCorpTagGroup group = new TbWxCorpTagGroup();
                group.setCorpId(corpId);
                group.setOrder(tagGroup.getOrder().intValue());
                group.setGroupId(tagGroup.getGroupId());
                group.setGroupName(tagGroup.getGroupName());
                // 如果是存在的标签组 则进行更新
                group.setGroupName(tagGroup.getGroupName());
                group.setOrder(tagGroup.getOrder().intValue());
                group.setUpdateTime(DateUtils.getNowDate());
                groupExistList.add(group);
            }
            if (groupExistList.size() > 0) {
                LambdaUpdateWrapper<TbWxCorpTagGroup> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(TbWxCorpTagGroup::getCorpId, corpId);
                // 一般一个事件只会有一个标签组更新
                for (TbWxCorpTagGroup tbWxCorpTagGroup : groupExistList) {
                    wrapper.eq(TbWxCorpTagGroup::getGroupId, tbWxCorpTagGroup.getGroupId());
                    update(tbWxCorpTagGroup, wrapper);
                }
            }
        }
    }

    @Override
    @Deprecated
    public WxCpUserExternalTagGroupList queryAllCorpTabGroupList(String corpId) {
        // WxCpService wxCpService = wxMultiCorpService.getWxCpServiceByCorpId(corpId, WX_APP_EXT_CUSTOMER);
        WxCpService wxCpService = null;
        try {
            WxCpUserExternalTagGroupList list = wxCpService.getExternalContactService().getCorpTagList(null);
            return list;
        } catch (WxErrorException e) {
            log.error("批量获取企业标签应信息失败: errmsg:{}", e.getError().getErrorMsg(), e);
        } catch (Exception e) {
            log.error("批量获取企业标签数据失败: errmsg:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<String> selectTagGroupListName(String corpId) {
        return baseMapper.selectTagGroupListName(corpId);
    }

    @Override
    public String selectTbWxCorpTagGroupNameByTagId(String tagId) {
        return baseMapper.selectTbWxCorpTagGroupNameByTagId(tagId);
    }

    @Override
    public ExternalUserTagVO selectTbWxCorpTagByTagId(String tagId, String corpId) {
        return baseMapper.selectTbWxCorpTagByTagId(tagId, corpId);
    }

    @Override
    @RedisLockAspect(key = CacheKeyConstants.SYNC_ADD_CORP_TAG_GROUP, value = "#corpId")
    public void syncAddCorpTagGroup(String corpId, WxCpUserExternalTagGroupList list) {
        if (ObjectUtil.isNull(list)) {
            return;
        }
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtils.isNotEmpty(tagGroupList)) {
            List<TbWxCorpTagGroup> groupList = Lists.newArrayList();
            List<TbWxCorpTag> tagList = Lists.newArrayList();
            for (WxCpUserExternalTagGroupList.TagGroup tagGroup : tagGroupList) {
                TbWxCorpTagGroup group = new TbWxCorpTagGroup();
                group.setCorpId(corpId);
                group.setStatus(UserStatus.OK.getCode());
                group.setType("CUSTOMER");
                group.setSynStatus("Y");
                group.setOrder(tagGroup.getOrder().intValue());
                group.setGroupId(tagGroup.getGroupId());
                group.setGroupName(tagGroup.getGroupName());
                TbWxCorpTagGroup one = getOne(new LambdaQueryWrapper<TbWxCorpTagGroup>()
                        .eq(TbWxCorpTagGroup::getGroupId, tagGroup.getGroupId())
                        .eq(TbWxCorpTagGroup::getCorpId, corpId)
                        .eq(TbWxCorpTagGroup::getStatus, StatusConstants.DEL_FLAG_FALSE)
                        .last("limit 1")
                );
                if (ObjectUtil.isNull(one)) {
                    group.setCreateBy(Constants.DEFAULT_USER);
                    groupList.add(group);
                }
                if (CollectionUtils.isNotEmpty(tagGroup.getTag())) {
                    for (WxCpUserExternalTagGroupList.TagGroup.Tag tag1 : tagGroup.getTag()) {
                        TbWxCorpTag tag = new TbWxCorpTag();
                        tag.setOrder(tag1.getOrder().intValue());
                        tag.setStatus(UserStatus.OK.getCode());
                        tag.setCreateTime(new Date(tag1.getCreateTime() * 1000));
                        tag.setName(tag1.getName());
                        tag.setGroupId(tagGroup.getGroupId());
                        tag.setCorpId(corpId);
                        tag.setTagId(tag1.getId());
                        TbWxCorpTag one1 = tbWxCorpTagService.getOne(new LambdaQueryWrapper<TbWxCorpTag>()
                                .eq(TbWxCorpTag::getTagId, tag1.getId())
                                .eq(TbWxCorpTag::getCorpId, corpId)
                                .eq(TbWxCorpTag::getStatus, StatusConstants.DEL_FLAG_FALSE)
                                .last("limit 1")
                        );
                        if (ObjectUtil.isNull(one1)) {
                            tag.setCreateBy(Constants.DEFAULT_USER);
                            tagList.add(tag);
                        }
                    }
                }
            }
            if (groupList.size() > 0) {
                saveBatch(groupList);
            }
            if (tagList.size() > 0) {
                tbWxCorpTagService.saveBatch(tagList);
            }
        }

    }

    @Override
    public void deleteUserTagByGroupId(String[] groupIds, String userId, String corpId) {
        // 删除标签组的时候，移除标签
        if(groupIds == null || groupIds.length == 0){
            return;
        }
        /**
         * 1,使用 tbWxCorpTagService 查询groupIds对应的正常的的标签
         * 2,使用 TbWxExtFollowUserTagService 删除对应的数据，若删除成功，则使用TbWxCustomerTagLogService记录日志
         */
        List<TbWxCorpTag> list = tbWxCorpTagService.lambdaQuery()
                .select(TbWxCorpTag::getTagId)
                .eq(TbWxCorpTag::getStatus, UserStatus.OK.getCode()).in(TbWxCorpTag::getGroupId, groupIds).list();
        String msg = "【删除标签组】发送客户移除标签MQ消息成功！";
        deleteUserTagByTagList(userId, corpId, list, msg);
    }
    /**
     * 根据标签删除客户标签
     * @param userId
     * @param corpId
     */
    private void deleteUserTagByTagList(String userId, String corpId, List<TbWxCorpTag> list, String msg) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> tagIdList = list.stream().map(tag -> tag.getTagId()).collect(Collectors.toList());

        List<TagVO> tagListAll = tbWxCorpTagService.selectValidTagByIds(tagIdList);
        List<TbWxExtFollowUserTag> allList = tbWxExtFollowUserTagService.list(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .in(TbWxExtFollowUserTag::getTagId, tagIdList));
        if (CollectionUtil.isNotEmpty(allList)) {
            List<String> customerIds = allList.stream().map(x -> x.getExternalUserId()).distinct().collect(Collectors.toList());
            List<TbWxExtCustomer> customerList = tbWxExtCustomerMapper
                    .selectList(new LambdaQueryWrapper<TbWxExtCustomer>()
                    .select(TbWxExtCustomer::getExternalUserId, TbWxExtCustomer::getName)
                    .in(TbWxExtCustomer::getExternalUserId, customerIds));
            SysUser sysUser = sysUserService.selectUserByUserName(userId);
            // 验证 userId 是否是数字，如果是数字，则查询 SysUser
            if (sysUser == null && userId.matches("^[1-9][0-9]*$")) {
                sysUser = sysUserService.selectUserById(Long.valueOf(userId));
            }
            String operId = UUID.randomUUID().toString().replaceAll("-", "");
            TrackEventTypeEnum bulkEditTagRemove = TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE;
            // 记录批量移除标签的企业运营动态
            sendBulkEditTagRemoveMsg(corpId, bulkEditTagRemove, tagListAll, customerList, sysUser, operId);
            // 批量移除标签事件
            CustomerRemoveTagMsgDto msgDto = CustomerRemoveTagMsgDto.builder()
                    .corpId(corpId)
                    .externalUserIds(customerIds)
                    .tagList(tagListAll)
                    .tagSource(TagSource.OTHER.name())
                    .isRetry(false)
                    .eventTypeEnum(bulkEditTagRemove)
                    .operId(operId)
                    .userId(userId)
                    .nickName(sysUser.getNickName())
                    .name(tagListAll.stream().map(TagVO::getGroupName).distinct().collect(Collectors.joining(",")))
                    .isMsgNotify(false)
                    .build();
            mqSendMessageManager.sendCustomerRemoveTagMessage(msgDto);
            log.info(msg);
        }
    }

    /**
     * 记录批量移除标签的企业运营动态
     * @param corpId
     * @param bulkEditTagRemove
     * @param tagListAll
     * @param customerList
     * @param sysUser
     * @param operId
     */
    private void sendBulkEditTagRemoveMsg(String corpId, TrackEventTypeEnum bulkEditTagRemove, List<TagVO> tagListAll, List<TbWxExtCustomer> customerList, SysUser sysUser, String operId) {
        DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(bulkEditTagRemove.getSubEventType());
        RelatedResource relatedResource = new RelatedResource();
        relatedResource.setRemoveTagList(tagListAll.stream().map(x->x.getTagName()).collect(Collectors.toList()));
        relatedResource.setCustomerList(customerList.stream().map(x->x.getName()).collect(Collectors.toList()));
        OperTrackParams operTrackParams = OperTrackParams.builder()
                .relatedResource(relatedResource)
                .externalUserId("")
                .userId(sysUser.getUserName())
                .corpId(corpId)
                .operId(operId)
                .build();
        BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
        mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
        log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
    }

    /**
     * 根据结果，进行企业标签更新
     *
     * @param corpId
     * @param list
     */
    private void updateTagGroupList(String corpId, WxCpUserExternalTagGroupList list) {
        if (null == list) {
            return;
        }
        List<TbWxCorpTagGroup> groupList = Lists.newArrayList();
        List<TbWxCorpTagGroup> groupExistList = Lists.newArrayList();
        List<TbWxCorpTag> tagList = Lists.newArrayList();
        List<TbWxCorpTag> tagExistList = Lists.newArrayList();
        List<WxCpUserExternalTagGroupList.TagGroup> tagGroupList = list.getTagGroupList();
        if (CollectionUtils.isNotEmpty(tagGroupList)) {
            for (WxCpUserExternalTagGroupList.TagGroup tagGroup : tagGroupList) {
                TbWxCorpTagGroup group = new TbWxCorpTagGroup();
                group.setCorpId(corpId);
                group.setStatus(UserStatus.OK.getCode());
                group.setSynStatus("Y");
                group.setType("CUSTOMER");
                group.setOrder(tagGroup.getOrder().intValue());
                group.setGroupId(tagGroup.getGroupId());
                group.setGroupName(tagGroup.getGroupName());
                TbWxCorpTagGroup one = getOne(new LambdaQueryWrapper<TbWxCorpTagGroup>()
                        .eq(TbWxCorpTagGroup::getGroupId, tagGroup.getGroupId())
                        .eq(TbWxCorpTagGroup::getCorpId, corpId)
                        .eq(TbWxCorpTagGroup::getStatus, StatusConstants.DEL_FLAG_FALSE)
                );
                if (ObjectUtil.isNull(one)) {
                    group.setCreateBy(Constants.DEFAULT_USER);
                    groupList.add(group);
                } else {
                    // 如果是存在的标签组 则进行更新
                    one.setGroupName(tagGroup.getGroupName());
                    one.setOrder(tagGroup.getOrder().intValue());
                    one.setUpdateTime(DateUtils.getNowDate());
                    groupExistList.add(one);
                }
                if (CollectionUtils.isNotEmpty(tagGroup.getTag())) {
                    for (WxCpUserExternalTagGroupList.TagGroup.Tag tag1 : tagGroup.getTag()) {
                        TbWxCorpTag tag = new TbWxCorpTag();
                        tag.setStatus(UserStatus.OK.getCode());
                        tag.setOrder(tag1.getOrder().intValue());
                        tag.setCreateTime(new Date(tag1.getCreateTime() * 1000));
                        tag.setName(tag1.getName());
                        tag.setCorpId(corpId);
                        tag.setGroupId(tagGroup.getGroupId());
                        tag.setTagId(tag1.getId());
                        if (tag1.getDeleted() != null && tag1.getDeleted()) {
                            tag.setStatus(UserStatus.DELETED.getCode());
                        }
                        TbWxCorpTag one1 = tbWxCorpTagService.getOne(new LambdaQueryWrapper<TbWxCorpTag>()
                                .eq(TbWxCorpTag::getTagId, tag1.getId())
                                .eq(TbWxCorpTag::getCorpId, corpId)
                                .eq(TbWxCorpTag::getStatus, StatusConstants.DEL_FLAG_FALSE)
                        );
                        if (ObjectUtil.isNull(one1)) {
                            tag.setCreateBy(Constants.DEFAULT_USER);
                            tagList.add(tag);
                        } else {
                            // 如果是存在的标签组 则进行更新
                            one1.setName(tag1.getName());
                            one1.setUpdateTime(DateUtils.getNowDate());
                            tagExistList.add(one1);
                        }

                    }
                }
            }
        }
        // 删除所有标签组及标签
        tbWxCorpTagService.remove(new LambdaQueryWrapper<TbWxCorpTag>().eq(TbWxCorpTag::getCorpId, corpId));
        remove(new LambdaQueryWrapper<TbWxCorpTagGroup>().eq(TbWxCorpTagGroup::getCorpId, corpId));
        // 标签组
        if (CollectionUtils.isNotEmpty(groupExistList)) {
            this.saveBatch(groupExistList);
        }
        if (CollectionUtils.isNotEmpty(groupList)) {
            this.saveBatch(groupList, 100);
        }
        if (CollectionUtils.isNotEmpty(tagExistList)) {
            tbWxCorpTagService.saveBatch(tagExistList);
        }
        if (CollectionUtils.isNotEmpty(tagList)) {
            tbWxCorpTagService.saveBatch(tagList, 100);
        }
    }

    private void updateCorpTag(TbWxCorpTagGroup tbWxCorpTagGroup) throws WxErrorException {
        String userName = tbWxCorpTagGroup.getUserName();
        String corpId = tbWxCorpTagGroup.getCorpId();
        // 标签组下原有标签
        List<TbWxCorpTag> existsTags = tbWxCorpTagService.list(new LambdaQueryWrapper<TbWxCorpTag>()
                .eq(TbWxCorpTag::getGroupId, tbWxCorpTagGroup.getGroupId())
                .eq(TbWxCorpTag::getStatus, UserConstants.NORMAL)
                .eq(TbWxCorpTag::getCorpId, corpId)
        );
        List<TbWxCorpTag> deleteTags;
        // 和前端所传做比较 只修改名字改变的
        List<TbWxCorpTag> tagList = tbWxCorpTagGroup.getTagList();
        if (CollectionUtils.isNotEmpty(tagList)) {
            // 要删除的标签（没有传已存在id的）
            List<String> editTagIds = tagList.stream().map(TbWxCorpTag::getTagId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(editTagIds)) {
                // 代表全都要删除
                deleteTags = existsTags;
            } else {
                // 筛选出要删除的
                deleteTags = existsTags.stream().filter(e -> !editTagIds.contains(e.getTagId())).collect(Collectors.toList());
            }
            // 不可变标签的最大排序
            int maxOrder = 0;
            // 企微后台加的标签不能编辑删除 过滤出来不可以编辑删除的
            List<TbWxCorpTag> noPermissionTagList = existsTags.stream().filter(e -> Constants.DEFAULT_USER.equals(e.getCreateBy())).collect(Collectors.toList());
            int noPermissionTagListSize = noPermissionTagList.size();
            log.info("【修改企业客户标签组】更新标签，当前标签组不可编辑标签数量为{}", noPermissionTagListSize);
            // 检查是否篡改不可编辑标签
            if (CollectionUtils.isNotEmpty(noPermissionTagList)) {
                if (checkIllegality2EditTag(tagList, noPermissionTagList)) {
                    throw new CustomException("非法操作！");
                }
                List<String> tagIds = noPermissionTagList.stream().map(TbWxCorpTag::getTagId).collect(Collectors.toList());
                // 从前端传入值中排除不可编辑的
                tagList = tagList.stream().filter(t -> !tagIds.contains(t.getTagId())).collect(Collectors.toList());
                // 排除不可删除的
                if (CollectionUtils.isNotEmpty(deleteTags)) {
                    deleteTags = deleteTags.stream().filter(d -> !tagIds.contains(d.getTagId())).collect(Collectors.toList());
                }
                // 最大排序
                maxOrder = noPermissionTagList.stream().mapToInt(TbWxCorpTag::getOrder).max().orElse(0);
            }

            // 删除打上标签的客户的标签
            String msg = "【删除标签】发送客户移除标签MQ消息成功！";
            this.deleteUserTagByTagList(userName, corpId, deleteTags, msg);
            // 删除标签
            deleteTagByList(deleteTags, corpId, userName);
            if (CollectionUtils.isEmpty(tagList)) {
                log.info("【修改企业客户标签组】标签编辑均为企微后台添加，无法继续操作");
                return;
            }
            // 提前将 existsTags 转换为 Map，提高查找效率
            Map<String, TbWxCorpTag> tagIdToTagMap = existsTags.stream()
                    .collect(Collectors.toMap(TbWxCorpTag::getTagId, tag -> tag));
            List<TbWxCorpTag> newTags = new ArrayList<>();
            List<TbWxCorpTag> updateTags = new ArrayList<>();
            for (int i = 0; i < tagList.size(); i++) {
                TbWxCorpTag tag = tagList.get(i);
                tag.setOrder(maxOrder++);
                /**
                 * 1、如果标签id为空 则代表是新增标签，加入newTags
                 * 2、如果标签id不为空 则代表是修改标签，判断是否需要修改
                 *      2.1、如果标签id不为空 且名称发生改变 则加入updateTags
                 *      2.2、如果标签id不为空 且order发生改变 则加入updateTags
                 */
                // 判断是否为新增标签
                if (StringUtils.isEmpty(tag.getTagId())) {
                    newTags.add(tag);
                    continue;
                }
                // 根据 tagId 查找是否存在对应标签
                TbWxCorpTag existingTag = tagIdToTagMap.get(tag.getTagId());
                if (existingTag == null) {
                    log.warn("【修改企业客户标签组】标签不存在，TagId: {}", tag.getTagId());
                    continue;
                }

                // 判断是否需要更新标签
                if (!existingTag.getName().equals(tag.getName()) || existingTag.getOrder() != tag.getOrder()) {
                    updateTags.add(tag);
                }
            }
            //  程序走到这里 代表标签是可以随意操作的 新增标签
            addTagByList(corpId, newTags, tbWxCorpTagGroup);

            editTagByList(corpId, updateTags, tbWxCorpTagGroup);
            return;
        }
        // 前端传值错误处理
        throw new CustomException("请至少输入一个标签");
    }

    /**
     * 批量删除企业客户标签组
     *
     * @param groupIds 需要删除的企业客户标签组ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpTagGroupByIds(String[] groupIds) {
        return tbWxCorpTagGroupMapper.deleteTbWxCorpTagGroupByIds(groupIds);
    }

    /**
     * 删除企业客户标签组信息
     *
     * @param groupId 企业客户标签组ID
     * @return 结果
     */
    @Override
    public int deleteTbWxCorpTagGroupById(String groupId) {
        return tbWxCorpTagGroupMapper.deleteTbWxCorpTagGroupById(groupId);
    }

    /**
     * 检查是否操作了不可编辑删除的标签
     *
     * @param tagList             传入数据
     * @param noPermissionTagList 不可编辑标签
     */
    private boolean checkIllegality2EditTag(List<TbWxCorpTag> tagList, List<TbWxCorpTag> noPermissionTagList) {
        if (CollectionUtils.isNotEmpty(noPermissionTagList)) {
            // 把传入数据排除新增 然后剩下的来和不可编辑的比较 如果传入的和不可编辑的id及名字不一样 返回true
            Map<String, List<TbWxCorpTag>> map = tagList.stream().filter(corpTag -> StringUtils.isNotEmpty(corpTag.getTagId()))
                    .collect(Collectors.groupingBy(TbWxCorpTag::getTagId));
            List<TbWxCorpTag> list = noPermissionTagList.stream().filter(no ->
                    CollectionUtils.isNotEmpty(map.get(no.getTagId())) && map.get(no.getTagId()).get(0).getName().equals(no.getName())
            ).collect(Collectors.toList());
            return list.size() != noPermissionTagList.size();
        }
        return false;
    }

    /**
     * 删除标签
     */
    private void deleteTagByList(List<TbWxCorpTag> deleteTags, String corpId, String userName) {
        if (CollectionUtils.isNotEmpty(deleteTags)) {
            List<String> tagIdList = deleteTags.stream().map(TbWxCorpTag::getTagId).collect(Collectors.toList());
            String[] deleteTagIds = tagIdList.toArray(new String[]{});
            log.info("【修改企业客户标签组】删除标签:{}", Arrays.toString(deleteTagIds));
            try {
                wxCpFeign.delCorpTag(deleteTagIds, new String[]{}, corpId);
                TbWxCorpTag tbWxCorpTag = new TbWxCorpTag();
                tbWxCorpTag.setStatus(UserConstants.DEL);
                tbWxCorpTag.setUpdateBy(userName);
                tbWxCorpTag.setUpdateTime(DateUtils.getNowDate());
                tbWxCorpTagService.update(tbWxCorpTag, new LambdaQueryWrapper<TbWxCorpTag>()
                        .eq(TbWxCorpTag::getCorpId, corpId)
                        .in(TbWxCorpTag::getTagId, tagIdList));
            } catch (Exception e) {
                log.error("【修改企业客户标签组】删除标签失败:{}", e);
            }
        }
    }

    /**
     * 新增标签
     */
    private void addTagByList(String corpId, List<TbWxCorpTag> newTags, TbWxCorpTagGroup tbWxCorpTagGroup) {
        if (CollectionUtils.isNotEmpty(newTags)) {
            try {
                log.info("【修改企业客户标签组】更新标签,新增标签数量:{}", newTags.size());
                WxCpUserExternalTagGroupInfo groupInfo = new WxCpUserExternalTagGroupInfo();
                WxCpUserExternalTagGroupInfo.TagGroup tagGroup = new WxCpUserExternalTagGroupInfo.TagGroup();
                tagGroup.setGroupId(tbWxCorpTagGroup.getGroupId());
                groupInfo.setTagGroup(tagGroup);
                tagGroup.setTag(Lists.newArrayList());
                log.info("【修改企业客户标签组】新增标签:{}", newTags.stream().map(TbWxCorpTag::getName).collect(Collectors.toList()));
                for (TbWxCorpTag str : newTags) {
                    WxCpUserExternalTagGroupInfo.Tag tag = new WxCpUserExternalTagGroupInfo.Tag();
                    tag.setName(str.getName());
                    tag.setOrder(Long.valueOf(str.getOrder()));
                    tagGroup.getTag().add(tag);
                }
                WxCpUserExternalTagGroupInfo result = wxCpFeign.addCorpTag(groupInfo, corpId);
                if (result.getTagGroup() != null && CollectionUtils.isNotEmpty(result.getTagGroup().getTag())) {
                    List<TbWxCorpTag> tags = Lists.newArrayList();
                    Map<String, WxCpUserExternalTagGroupInfo.Tag> tagMap = result.getTagGroup().getTag().stream().
                            collect(Collectors.toMap(WxCpUserExternalTagGroupInfo.Tag::getName, Function.identity(), (x, x1) -> x1));
                    for (TbWxCorpTag newTag : newTags) {
                        WxCpUserExternalTagGroupInfo.Tag tag = tagMap.get(newTag.getName());
                        newTag.setGroupId(tbWxCorpTagGroup.getGroupId());
                        newTag.setStatus(UserConstants.NORMAL);
                        newTag.setCreateBy(tbWxCorpTagGroup.getUserName());
                        newTag.setOrder(tag.getOrder().intValue());
                        newTag.setTagId(tag.getId());
                        newTag.setCorpId(tbWxCorpTagGroup.getCorpId());
                        tags.add(newTag);
                    }
                    tbWxCorpTagService.saveBatch(tags, 100);
                }
            } catch (Exception e) {
                log.error("【修改企业客户标签组】新增标签失败:{}", e);
            }

        }
    }

    /**
     * 编辑标签
     * @param corpId
     * @param editTags
     * @param tbWxCorpTagGroup
     */
    private void editTagByList(String corpId, List<TbWxCorpTag> editTags, TbWxCorpTagGroup tbWxCorpTagGroup) {
        if (CollectionUtils.isNotEmpty(editTags)) {
            try {
                log.info("【修改企业客户标签组】更新标签,编辑标签数量:{}", editTags.size());
                for (int i = editTags.size() - 1; i >=0; i--) {
                    TbWxCorpTag editTag = editTags.get(i);
                    wxCpFeign.editCorpTag(editTag.getTagId(), editTag.getName(), editTag.getOrder(), corpId);
                    tbWxCorpTagService.updateTbWxCorpTag(editTag);
                }
            } catch (Exception e) {
                log.error("【修改企业客户标签组】新增标签失败:{}", e);
            }

        }
    }
    /**
     * 编辑标签组
     *
     * @param tbWxCorpTagGroup
     */
    private void updateCorpTagGroup(TbWxCorpTagGroup tbWxCorpTagGroup) {
        TbWxCorpTagGroup existsTbWxCorpTagGroup = tbWxCorpTagGroupMapper.selectOne(new LambdaQueryWrapper<TbWxCorpTagGroup>()
                .eq(TbWxCorpTagGroup::getStatus, UserConstants.NORMAL)
                .eq(TbWxCorpTagGroup::getGroupId, tbWxCorpTagGroup.getGroupId())
                .eq(TbWxCorpTagGroup::getCorpId, tbWxCorpTagGroup.getCorpId())
        );

        this.lambdaUpdate().set(TbWxCorpTagGroup::getGroupName, tbWxCorpTagGroup.getGroupName())
               .set(StrUtil.isNotBlank(tbWxCorpTagGroup.getCategoryId()), TbWxCorpTagGroup::getCategoryId, tbWxCorpTagGroup.getCategoryId())
               .set(TbWxCorpTagGroup::getUpdateTime, new Date())
               .set(TbWxCorpTagGroup::getGroupTagType, tbWxCorpTagGroup.getGroupTagType())
               .eq(TbWxCorpTagGroup::getGroupId, tbWxCorpTagGroup.getGroupId())
               .eq(TbWxCorpTagGroup::getCorpId, tbWxCorpTagGroup.getCorpId())
               .update();

        if (tbWxCorpTagGroup.getGroupName().equals(existsTbWxCorpTagGroup.getGroupName())) {
            return;
        }

        try {
            log.info("【企业标签】更新标签组：{}", tbWxCorpTagGroup);
            wxCpFeign.editCorpTag(tbWxCorpTagGroup.getGroupId(), tbWxCorpTagGroup.getGroupName(), ObjectUtil.isNull(tbWxCorpTagGroup.getOrder()) ? 1 : 0, tbWxCorpTagGroup.getCorpId());
        } catch (Exception e) {
            log.info("【企业标签】更新标签失败:{}", e);
        }
    }

}
