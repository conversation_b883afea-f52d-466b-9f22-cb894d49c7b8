package com.cenker.scrm.service.impl.content.material;

import java.util.Arrays;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.content.material.BuWelcomeContactTemplateMapper;
import com.cenker.scrm.pojo.dto.content.material.BuWelcomeContactTemplateDTO;
import com.cenker.scrm.pojo.entity.content.material.BuWelcomeContactTemplate;
import com.cenker.scrm.pojo.request.content.material.BuWelcomeContactTemplateRequest;
import com.cenker.scrm.pojo.vo.content.material.BuWelcomeContactTemplateVO;
import com.cenker.scrm.pojo.vo.content.material.BuWelcomeTemplateSenceVO;
import com.cenker.scrm.service.content.material.IBuWelcomeContactTemplateService;
import com.cenker.scrm.util.DateUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 欢迎语模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@AllArgsConstructor
@Service
public class BuWelcomeContactTemplateServiceImpl extends ServiceImpl<BuWelcomeContactTemplateMapper, BuWelcomeContactTemplate> implements IBuWelcomeContactTemplateService {

    /**
     * 查询欢迎语模板
     *
     * @param id 欢迎语模板主键
     * @return 欢迎语模板
     */
    @Override
    public BuWelcomeContactTemplateVO selectBuWelcomeContactTemplateById(BuWelcomeContactTemplateRequest request) {
        return baseMapper.selectBuWelcomeContactTemplateById(request);
    }

    /**
     * 查询欢迎语模板列表
     *
     * @param queryRequest 欢迎语模板
     * @return 欢迎语模板
     */
    @Override
    public List<BuWelcomeContactTemplateVO> selectBuWelcomeContactTemplateList(BuWelcomeContactTemplateRequest queryRequest) {
        return baseMapper.selectBuWelcomeContactTemplateList(queryRequest);
    }

    /**
     * 新增欢迎语模板
     *
     * @param welcomeContactTemplateDTO 欢迎语模板
     * @return 结果
     */
    @Override
    public int insertBuWelcomeContactTemplate(BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        BuWelcomeContactTemplate buWelcomeContactTemplate = new BuWelcomeContactTemplate();
        BeanUtils.copyProperties(welcomeContactTemplateDTO, buWelcomeContactTemplate);
        buWelcomeContactTemplate.setId(null);
        buWelcomeContactTemplate.setCreateTime(DateUtils.getNowDate());
        // 设置欢迎语附件
        if (CollectionUtils.isNotEmpty(welcomeContactTemplateDTO.getAttachments())) {
            buWelcomeContactTemplate.setAttachments(JSON.toJSONString(welcomeContactTemplateDTO.getAttachments()));
        } else {
            buWelcomeContactTemplate.setAttachments(null);
        }
        return baseMapper.insert(buWelcomeContactTemplate);
    }

    /**
     * 修改欢迎语模板
     *
     * @param welcomeContactTemplateDTO 欢迎语模板
     * @return 结果
     */
    @Override
    public int updateBuWelcomeContactTemplate(BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        BuWelcomeContactTemplate buWelcomeContactTemplate = new BuWelcomeContactTemplate();
        BeanUtils.copyProperties(welcomeContactTemplateDTO, buWelcomeContactTemplate);
        buWelcomeContactTemplate.setUpdateTime(DateUtils.getNowDate());
        buWelcomeContactTemplate.setId(Long.valueOf(welcomeContactTemplateDTO.getId()));
        // 设置欢迎语附件
        if (CollectionUtils.isNotEmpty(welcomeContactTemplateDTO.getAttachments())) {
            buWelcomeContactTemplate.setAttachments(JSON.toJSONString(welcomeContactTemplateDTO.getAttachments()));
        } else {
            buWelcomeContactTemplate.setAttachments(null);
        }
        return baseMapper.updateById(buWelcomeContactTemplate);
    }

    /**
     * 批量删除欢迎语模板
     *
     * @param ids 需要删除的欢迎语模板主键
     * @return 结果
     */
    @Override
    public int deleteBuWelcomeContactTemplateByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除欢迎语模板信息
     *
     * @param id 欢迎语模板主键
     * @return 结果
     */
    @Override
    public int deleteBuWelcomeContactTemplateById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateWelcomeContactTemplateStatus(BuWelcomeContactTemplateDTO welcomeContactTemplateDTO) {
        BuWelcomeContactTemplate buWelcomeContactTemplate = new BuWelcomeContactTemplate();
        buWelcomeContactTemplate.setStatus(welcomeContactTemplateDTO.getStatus());
        buWelcomeContactTemplate.setUpdateTime(DateUtils.getNowDate());
        buWelcomeContactTemplate.setUpdateBy(welcomeContactTemplateDTO.getUpdateBy());
        return baseMapper.update(buWelcomeContactTemplate, new LambdaQueryWrapper<>(BuWelcomeContactTemplate.class)
                .eq(BuWelcomeContactTemplate::getId, welcomeContactTemplateDTO.getId()));
    }

    @Override
    public boolean checkUsedCount(String id) {
        return baseMapper.getUsedCountById(id) > 0;
    }

    @Override
    public List<BuWelcomeTemplateSenceVO> selectUsedSceneList(BuWelcomeContactTemplateRequest queryRequest) {
        return baseMapper.selectUsedSceneList(queryRequest);
    }
}
