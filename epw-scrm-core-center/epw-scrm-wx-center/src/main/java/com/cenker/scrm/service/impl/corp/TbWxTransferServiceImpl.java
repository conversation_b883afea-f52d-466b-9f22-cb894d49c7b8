package com.cenker.scrm.service.impl.corp;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.contact.TbWxUserMapper;
import com.cenker.scrm.mapper.external.TbWxExtCustomerMapper;
import com.cenker.scrm.mapper.group.TbWxCustomerGroupMapper;
import com.cenker.scrm.mapper.transfer.TbWxTransferRecordItemMapper;
import com.cenker.scrm.mapper.transfer.TbWxTransferRecordMapper;
import com.cenker.scrm.model.base.PageDomain;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.group.CustomerGroupDto;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.transfer.*;
import com.cenker.scrm.pojo.entity.enums.EnumTransferType;
import com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxTransferRecordItem;
import com.cenker.scrm.pojo.vo.transfer.*;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.service.corp.ITbWxTransferService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalGroupChatTransferResp;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalUnassignList;
import me.chanjar.weixin.cp.bean.external.WxCpUserTransferCustomerResp;
import me.chanjar.weixin.cp.bean.external.WxCpUserTransferResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TbWxTransferServiceImpl extends ServiceImpl<TbWxTransferRecordMapper, TbWxTransferRecord> implements ITbWxTransferService {

    @Autowired
    private TbWxTransferRecordMapper tbWxTransferRecordMapper;
    @Autowired
    private TbWxTransferRecordItemMapper tbWxTransferRecordItemMapper;
    @Autowired
    private TbWxCustomerGroupMapper tbWxCustomerGroupMapper;
    @Autowired
    private TbWxUserMapper tbWxUserMapper;
    /**
     * 企微接口注入优化
     */
    @Resource
    private IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    //在职员工客户群转移接口
    private final static String ON_JOB_CHAT_TRANSFER = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/onjob_transfer";
    private final static String OFF_JOB_CHAT_TRANSFER = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/transfer";


    /**
     * 处理在职员工的客户和客户群的转移。(支持选择多个继承员工，用逗号分隔)
     *
     * @param currentUserDTO 当前操作人员的用户信息。
     * @param transferCustomerDTO 转移操作的数据传输对象，包含了所有必要的转移信息。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferActiveEmployeeResources(CurrentUserDTO currentUserDTO, TransferCustomerDTO transferCustomerDTO) throws Exception {

        if (StringUtils.isEmpty(transferCustomerDTO.getTakeoverUserId())) {
            return;
        }

        // 存在多个继承的员工，需要对客户和群进行分配（分配规则，按提交的继承员工顺序分配）
        String[] lstTakeoverUserId = transferCustomerDTO.getTakeoverUserId().split(",");
        if (lstTakeoverUserId.length > 1) {

            // 按继承员工分组
            List<TransferCustomerDTO> lstDtoNew = new ArrayList<>();
            for (String takeoverUserId : lstTakeoverUserId) {
                TransferCustomerDTO dto = new TransferCustomerDTO();
                dto.setTakeoverUserId(takeoverUserId);
                dto.setHandoverUserId(transferCustomerDTO.getHandoverUserId());
                dto.setTransferType(transferCustomerDTO.getTransferType());
                lstDtoNew.add(dto);
            }
            // 待分配的客户
            if (CollectionUtils.isNotEmpty(transferCustomerDTO.getExternalUserid())) {
                for (int i = 0; i < transferCustomerDTO.getExternalUserid().size(); i++) {
                    // 计算当前元素应该被分配到哪个分组
                    int groupIndex = i % lstDtoNew.size();
                    // 添加元素到对应的分组
                    List<String> externalUserid = lstDtoNew.get(groupIndex).getExternalUserid();
                    if (null == externalUserid) {
                        externalUserid = new ArrayList<>();
                    }
                    externalUserid.add(transferCustomerDTO.getExternalUserid().get(i));
                    lstDtoNew.get(groupIndex).setExternalUserid(externalUserid);
                }
            }

            // 待分配的群
            if (CollectionUtils.isNotEmpty(transferCustomerDTO.getChatIdList())) {
                for (int i = 0; i < transferCustomerDTO.getChatIdList().size(); i++) {
                    // 计算当前元素应该被分配到哪个分组
                    int groupIndex = i % lstDtoNew.size();
                    // 添加元素到对应的分组
                    List<String> chatIdList = lstDtoNew.get(groupIndex).getChatIdList();
                    if (null == chatIdList) {
                        chatIdList = new ArrayList<>();
                    }
                    chatIdList.add(transferCustomerDTO.getChatIdList().get(i));
                    lstDtoNew.get(groupIndex).setChatIdList(chatIdList);
                }
            }
            // 循环处理继承
            for (TransferCustomerDTO dto : lstDtoNew) {
                if (CollectionUtils.isNotEmpty(dto.getChatIdList()) || CollectionUtils.isNotEmpty(dto.getExternalUserid())) {
                    transferActiveEmployeeResourcesByOnly(currentUserDTO, dto);
                }
            }
        } else {
            transferActiveEmployeeResourcesByOnly(currentUserDTO, transferCustomerDTO);
        }
    }

    /**
     * 处理在职员工的客户和客户群的转移。（单个员工继承）
     * @param currentUserDTO 当前操作人员的用户信息。
     * @param dto 转移操作的数据传输对象，包含了所有必要的转移信息。
     * @throws Exception
     */
    private void transferActiveEmployeeResourcesByOnly(CurrentUserDTO currentUserDTO, TransferCustomerDTO dto) throws Exception {

        // 获取企业微信服务实例
        WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(currentUserDTO.getCorpId());
        if (wxCpService == null) {
            throw new IllegalStateException("无法获取企业微信服务实例");
        }
        if(CollectionUtils.isEmpty(dto.getChatIdList())&&CollectionUtils.isEmpty(dto.getExternalUserid())) {
            throw new IllegalArgumentException("客户群和外部联系人ID不能同时为空");
        }
        if(dto.getExternalUserid()!=null&&dto.getExternalUserid().size()>100){
            throw new IllegalArgumentException("每次转移的外部联系人不能超过100个");
        }
        if (dto.getChatIdList()!=null&&dto.getChatIdList().size()>100) {
            throw new IllegalArgumentException("每次转移的客户群不能超过100个");
        }
        //保存转移记录
        TbWxTransferRecord tbWxOnJobTransferRecord=dto.toEntity(currentUserDTO);
        this.save(tbWxOnJobTransferRecord);
        // 处理客户群转移
        if (!CollectionUtils.isEmpty(dto.getChatIdList())) {
            try {
                List<TbWxTransferRecordItem> groupsEntity = dto.toGroupEntity(tbWxOnJobTransferRecord.getTransferId(),dto.getTransferType(),currentUserDTO);
                //调用企微接口，执行群转移
                WxCpUserExternalGroupChatTransferResp resp = WxCpUserExternalGroupChatTransferResp.fromJson(
                        wxCpService.post(dto.getTransferType()==EnumTransferType.ON_JOB?
                                ON_JOB_CHAT_TRANSFER:OFF_JOB_CHAT_TRANSFER, dto.buildWxCpChatTransferGroupReq()));
                //更新群转移状态
                resp.getFailedChatList().forEach(a->{
                    //更新转移失败记录的状态和错误信息
                    groupsEntity.stream().filter(b-> Objects.equals(b.getResourceId(),a.getChatId())).findFirst().ifPresent(c->{
                        //如果微信错误码为40128，表示群转移次数限制，设置为继承过于频繁
                        if(a.getErrcode()==40128){
                            c.setStatus("3");
                        }else{
                            c.setStatus("2");
                        }
                        c.setErrCode(a.getErrcode().toString());
                        c.setErrorMsg(a.getErrmsg());
                    });
                });
                //保存群转移记录
                groupsEntity.forEach(a-> tbWxTransferRecordItemMapper.insert(a));
            } catch (WxErrorException ex){
                log.error("外部联系人转移失败: {}", ex.getMessage());
                if(ex.getError().getErrorCode()==40205){
                    throw new Exception("成员微信票据过期");
                }
                else if(ex.getError().getErrorCode()==41054){
                    throw new Exception("该用户尚未激活");
                }
                else{
                    throw new Exception("外部联系人转移失败");
                }
            } catch (Exception e) {
                log.error("客户群转移失败: {}", e.getMessage());
                throw e;
            }
        }

        // 处理外部联系人转移
        if (!CollectionUtils.isEmpty(dto.getExternalUserid())) {
            List<TbWxTransferRecordItem> customerEntity = dto.toCustomerEntity(tbWxOnJobTransferRecord.getTransferId(),dto.getTransferType(),currentUserDTO);
            //调用企微接口，执行外部联系人转移
            List<WxCpUserTransferCustomerResp.TransferCustomer> wxResult=new ArrayList<>();
            if(dto.getTransferType()==EnumTransferType.ON_JOB){
                //获取接手人姓名
                String userName=tbWxUserMapper.selectTbWxUserById(currentUserDTO.getCorpId(),dto.getTakeoverUserId()).getName();
                wxResult= wxCpService.getExternalContactService().transferCustomer(dto.buildWxCpUserTransferCustomerReq(userName))
                        .getCustomer();
            } else if (dto.getTransferType()==EnumTransferType.OFF_JOB) {
                wxResult= wxCpService.getExternalContactService().resignedTransferCustomer(dto.buildWxCpUserTransferCustomerReq()).getCustomer();
            }
            wxResult.forEach(a->{
                //更新转移状态和信息
                customerEntity.stream().filter(b-> Objects.equals(b.getResourceId(),a.getExternalUserid())).findFirst().ifPresent(c->{
                    //0表示成功发起接替,待24小时后自动接替,并不代表最终接替成功
                    if(a.getErrcode()==0){
                        c.setStatus("2");
                    } else if(a.getErrcode()==40128){
                        c.setStatus("6");
                    }
                    else {
                        c.setStatus("5");
                        c.setErrCode(a.getErrcode().toString());
                        c.setErrorMsg("发起接替失败");
                    }
                });
            });
            //保存外部联系人转移记录
            customerEntity.forEach(a-> tbWxTransferRecordItemMapper.insert(a));
        }
    }


    /**
     * 查询在职员工的客户和客户群的转移记录
     * @param currentUser 当前用户信息
     * @param dto 转移记录查询条件
     * @return
     */
    @Override
    public List<TransferRecordVO> getOnJobTransferRecords(CurrentUserDTO currentUser,
                                                          QueryTransferRecordsDTO dto) {
        return tbWxTransferRecordMapper.getTransferRecords(currentUser.getCorpId(),dto);
    }

    @Override
    public TransferDetailsVO getOnJobTransferRecordDetail(CurrentUserDTO currentUser, QueryTransferDetailsDTO dto) {
        // 获取转移记录
        TransferDetailsVO result = tbWxTransferRecordMapper.getTransferDetailsByTransferId(dto.getRecordId());
        // 统计转移数量
        List<TbWxTransferRecordItem> items = tbWxTransferRecordItemMapper.selectList(
                new QueryWrapper<TbWxTransferRecordItem>().eq("transfer_id", dto.getRecordId()));
        // 客户转移详情
        CustomersVO customers = CustomersVO.calculateCustomersDetails(items);
        result.setCustomers(customers);
        // 群转移详情
        GroupsVO groups = GroupsVO.calculateGroupsDetails(items);
        result.setGroups(groups);
        return result;
    }

    @Override
    public List<TransferDetailsPerCustomerVO> queryOnJobTransferPerUser(CurrentUserDTO currentUser, QueryTransferPerUserDTO dto) {
        return tbWxTransferRecordMapper.queryTransferDetailsPerCustomer(dto);
    }

    @Override
    public List<TransferDetailsPerGroupVO> queryOnJobTransferPerGroup(CurrentUserDTO currentUser, QueryTransferPerGroupDTO dto) {
        List<TransferDetailsPerGroupVO> result= tbWxTransferRecordMapper.queryTransferDetailsPerGroup(dto);
        result.forEach(a->{
            // 查询群头像
            CustomerGroupDto req=new CustomerGroupDto();
            req.setCorpId(currentUser.getCorpId());
            req.setGroupId(a.getGroupId());
            a.setGroupAvatar(tbWxCustomerGroupMapper.getGroupHeadImgByGroupList(req));
        });
       return result;
    }

    @Override
    public void syncOnJobTransferStatus(CurrentUserDTO currentUserDTO,SyncTransferStatusDTO dto) throws Exception {
        // 构建查询条件
        QueryWrapper<TbWxTransferRecordItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corp_id", currentUserDTO.getCorpId())
                .eq("resource_type", 0)
                .eq("status", 2); // 状态为2，表示待更新
        if (dto.getTransferId() != null) {
            queryWrapper.eq("transfer_id", dto.getTransferId());
        }
        // 查询待更新的记录
        List<TbWxTransferRecordItem> items = tbWxTransferRecordItemMapper.selectList(queryWrapper);

        // 使用Set去重
        Set<String> uniqueKeys = items.stream()
                .map(item -> item.getHandoverUserId() + ";" + item.getTakeoverUserId() + ";" + item.getTransferType())
                .collect(Collectors.toSet());

        // 获取企业微信服务实例
        WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(currentUserDTO.getCorpId());
        if (wxCpService == null) {
            throw new IllegalStateException("无法获取企业微信服务实例");
        }
        for (String key : uniqueKeys) {
            String[] parts = key.split(";");
            String handoverUserId = parts[0];
            String takeoverUserId = parts[1];
            EnumTransferType transferType = EnumTransferType.valueOf(parts[2]);

            WxCpUserTransferResultResp resp=null;
            try{
                // 根据transferType决定调用的企业微信接口
                if (transferType == EnumTransferType.ON_JOB) {
                    // 调用在职继承查询接口
                    resp= wxCpService.getExternalContactService().transferResult(handoverUserId, takeoverUserId,null);
                } else if (transferType == EnumTransferType.OFF_JOB) {
                    // 调用离职继承查询接口
                    resp= wxCpService.getExternalContactService().resignedTransferResult(handoverUserId, takeoverUserId,null);
                }
                if(resp.success()){
                    resp.getCustomer().forEach(a->{
                        //更新转移记录
                        items.forEach(b->{
                            if(Objects.equals(a.getExternalUserid(),b.getResourceId())
                                    &&Objects.equals(b.getHandoverUserId(),handoverUserId)
                                    &&Objects.equals(b.getTakeoverUserId(),takeoverUserId)){
                                b.setStatus(String.valueOf(a.getStatus().ordinal()+1));
                                b.setUpdateTime(new Date());
                                tbWxTransferRecordItemMapper.updateById(b);
                            }
                        });
                    });
                }
            }
            catch (WxErrorException e){
                if (dto!=null&&dto.getTransferId()!=null){
                    if(e.getError().getErrorCode()==60111){
                        throw new Exception("该员工已离职，同步失败。");
                    }
                }
            }
        }
        log.info("完成在职员工客户和客户群的转移状态同步，共处理{}条记录", items.size());
    }

    @Override
    public TableDataInfo getLeavingEmployeeList(CurrentUserDTO currentUser, QueryLeavingEmployeeListDTO dto, PageDomain pageDomain) {
        List<ExternalUnAssignListVO> result=new ArrayList<>();
        int total=0;
        try {
            List<WxCpUserExternalUnassignList.UnassignInfo> resp= getAllLeavingEmployeeList(currentUser);
            if(!Strings.isNullOrEmpty(dto.getBeginTime())) {
                //将字符串转换为日期，
                Date startTime = DateFormat.getDateInstance().parse(dto.getBeginTime());
                // 并转换成Unix时间戳
                long startTimeStamp = startTime.getTime() / 1000;
                resp=resp.stream().filter(a->a.getDimissionTime()>=startTimeStamp).collect(Collectors.toList());
            }
            if(!Strings.isNullOrEmpty(dto.getEndTime())) {
                //将字符串转换为日期，
                Date endTime = DateFormat.getDateInstance().parse(dto.getEndTime());
                // 并转换成Unix时间戳
                long endTimeStamp = endTime.getTime() / 1000;
                resp=resp.stream().filter(a->a.getDimissionTime()<=endTimeStamp).collect(Collectors.toList());
            }
            //获取所有员工Id
            List<String> userIds=resp.stream().map(WxCpUserExternalUnassignList.UnassignInfo::getHandoverUserid).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(userIds)) {
                result= tbWxTransferRecordMapper.getAllLeavingEmployeeList(currentUser.getCorpId(),dto,userIds);
                //填充离职时间
                List<WxCpUserExternalUnassignList.UnassignInfo> finalResp = resp;
                result.forEach(a->{
                    finalResp.stream().filter(b->Objects.equals(a.getHandoverUserId(),b.getHandoverUserid())).findFirst().ifPresent(c->{
                        if(a.getDimissionTime()==null){
                            //设置离职时间
                            a.setDimissionTime(new Date(c.getDimissionTime()*1000));
                        }

                    });
                });
                //按离职时间降序
                result.sort(Comparator.comparing(ExternalUnAssignListVO::getDimissionTime).reversed());
                total= result.size();
                if(pageDomain!=null){

                    int start=(pageDomain.getPageNum()-1)*pageDomain.getPageSize();
                    int end=pageDomain.getPageNum()*pageDomain.getPageSize();
                    if(end>result.size()){
                        end=result.size();
                    }
                    result=result.subList(start,end);
                }
            }

        }
        catch (Exception e) {
            log.error("获取离职员工列表失败: {}", e.getMessage());
        }
        //填充离职员工的详细信息
        return new TableDataInfo(result,total);
    }

    private List<WxCpUserExternalUnassignList.UnassignInfo> getAllLeavingEmployeeList(CurrentUserDTO currentUser) throws Exception{
        //从企业微信获取所有离职未分配的员工
        // 获取企业微信服务实例
        WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(currentUser.getCorpId());
        if (wxCpService == null) {
            throw new IllegalStateException("无法获取企业微信服务实例");
        }
        List<WxCpUserExternalUnassignList.UnassignInfo> result=new ArrayList<>();
        WxCpUserExternalUnassignList resp=null;
        String cursor=null;
        do {
            resp= wxCpService.getExternalContactService().listUnassignedList(null,cursor,null);
            result.addAll(resp.getUnassignInfos());
            cursor=resp.getNextCursor();
        }while (!StringUtils.isEmpty(cursor));
        return result;
    }



}
