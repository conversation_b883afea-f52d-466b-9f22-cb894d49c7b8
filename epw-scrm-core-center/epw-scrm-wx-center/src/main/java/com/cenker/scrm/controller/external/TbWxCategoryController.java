package com.cenker.scrm.controller.external;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategory;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.category.ITbWxCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 素材分类信息Controller
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@RestController
@RequestMapping("/tp/category")
@RequiredArgsConstructor
public class TbWxCategoryController extends BaseController {

    private final ITbWxCategoryService tbWxCategoryService;

    /**
     * 查询素材分类信息列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody TbWxCategory tbWxCategory) {
        startPage();
        List<TbWxCategory> list = tbWxCategoryService.selectTbWxCategoryList(tbWxCategory);
        return getDataTable(list);
    }

    /**
     * 获取素材分类信息详细信息
     */
    @RequestMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tbWxCategoryService.selectTbWxCategoryById(id));
    }

    /**
     * 新增素材分类信息
     */
    @RequestMapping("/add")
    public AjaxResult add(@RequestBody TbWxCategory tbWxCategory) {
        TbWxCategory data = tbWxCategoryService.getOne(new LambdaQueryWrapper<TbWxCategory>()
                .eq(TbWxCategory::getName, tbWxCategory.getName())
                .eq(TbWxCategory::getMediaType, tbWxCategory.getMediaType())
                .eq(TbWxCategory::getCorpId, tbWxCategory.getCorpId())
                .eq(TbWxCategory::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
                .select(TbWxCategory::getId)
        );
        if (data != null) {
            throw new CustomException("已存在相同分组名称");
        }
        return toAjax(tbWxCategoryService.insertTbWxCategory(tbWxCategory));
    }

    /**
     * 修改素材分类信息
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody TbWxCategory tbWxCategory) {
        TbWxCategory data = tbWxCategoryService.getOne(new LambdaQueryWrapper<TbWxCategory>()
                .eq(TbWxCategory::getName, tbWxCategory.getName())
                .eq(TbWxCategory::getCorpId, tbWxCategory.getCorpId())
                .eq(TbWxCategory::getMediaType, tbWxCategory.getMediaType())
                .eq(TbWxCategory::getDelFlag,StatusConstants.DEL_FLAG_FALSE_INT)
                .select(TbWxCategory::getId)
        );
        if (data != null && !data.getId().equals(tbWxCategory.getId())) {
            throw new CustomException("已存在相同分组名称");
        }
        return toAjax(tbWxCategoryService.updateTbWxCategory(tbWxCategory));
    }

    /**
     * 删除素材分类信息
     */
    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbWxCategoryService.deleteTbWxCategoryByIds(ids));
    }

    /**
     * 获取部门下拉树列表
     */
    @RequestMapping("/treeSelect")
    public AjaxResult treeSelect(@RequestBody TbWxCategory tbWxCategory) {
        List<TbWxCategory> tbWxDepartmentList = tbWxCategoryService.selectTbWxCategoryList(tbWxCategory);
        return AjaxResult.success(tbWxCategoryService.buildCategoryTreeSelect(tbWxDepartmentList));
    }

    @RequestMapping("/sort")
    public AjaxResult sort(@RequestBody TbWxCategory tbWxCategory){
        List<TbWxCategory> orderList = tbWxCategory.getOrderList();
        int size = orderList.size();
        int count = tbWxCategoryService.count(new LambdaUpdateWrapper<TbWxCategory>()
                .eq(TbWxCategory::getCorpId, tbWxCategory.getCorpId())
                .eq(TbWxCategory::getMediaType,tbWxCategory.getMediaType())
                .eq(TbWxCategory::getDelFlag, StatusConstants.DEL_FLAG_FALSE_INT)
                .eq(TbWxCategory::getParentId,0)
        );
        if (size != count) {
            return AjaxResult.error("分组数据已更新，请刷新页面重试");
        }
        // 查询数据是否存在及数量是否与数据库对应
        tbWxCategoryService.sort(tbWxCategory);
        return AjaxResult.success();
    }
}
