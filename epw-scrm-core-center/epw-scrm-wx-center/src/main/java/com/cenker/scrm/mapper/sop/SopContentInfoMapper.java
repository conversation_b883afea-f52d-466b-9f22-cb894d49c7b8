package com.cenker.scrm.mapper.sop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopContentInfo;
import com.cenker.scrm.pojo.request.sop.BaseSopRequest;
import com.cenker.scrm.pojo.request.sop.ConditionSopQueryRequest;
import com.cenker.scrm.pojo.vo.sop.ConditionSopDataVO;
import com.cenker.scrm.pojo.vo.sop.ConditionSopTaskDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description
 */
public interface SopContentInfoMapper extends BaseMapper<SopContentInfo> {
    /**
     * 查看今天版本创建号是多少（创建多少条内容）
     *
     * @param todaySign RWyyyyMMdd
     * @param sopId     sopId
     * @return 当前sop今天已经创建多少条内容
     */
    int getTodaySignCount(@Param("todaySign") String todaySign, @Param("sopId") Long sopId);

    /**
     * 查询sop任务执行情况
     *
     * @param request
     * @return
     */
    List<ConditionSopDataVO> taskExecuteList(ConditionSopQueryRequest request);

    /**
     * 查询sop触达消息列表
     *
     * @param request
     * @return
     */
    List<ConditionSopDataVO> taskReachMessageList(ConditionSopQueryRequest request);

    /**
     * 查询触达客户列表
     *
     * @param request
     * @return
     */
    List<ConditionSopDataVO> taskReachCustomerList(ConditionSopQueryRequest request);

    /**
     * 查询sop客户流失列表
     *
     * @param request
     * @return
     */
    List<ConditionSopDataVO> churnCustomerList(ConditionSopQueryRequest request);

    /**
     * 查询sop任务列表
     *
     * @param request
     * @return
     */
    List<ConditionSopTaskDataVO> sopTaskList(ConditionSopQueryRequest request);

    /**
     * 内容任务子版本查询
     *
     * @param sopId
     * @param contentId
     * @param contentSign
     * @param calculateDay
     * @return
     */
    List<ConditionSopDataVO> queryChildrenVersion(@Param("sopId") Long sopId,
                                                  @Param("contentId") Long contentId,
                                                  @Param("contentSign") Long contentSign,
                                                  @Param("stageId") Long stageId,
                                                  @Param("calculateDay") boolean calculateDay);

    /**
     * 查询总执行任务数
     *
     * @param contentId
     * @return
     */
    Integer queryTotalTaskCntByContentId(@Param("contentId") Long contentId);

    /**
     * 查询完成任务数
     *
     * @param contentId
     * @return
     */
    Integer queryCompleteTaskCntBySopId(@Param("contentId") Long contentId);

    /**
     * 查询sop所有未过期的群发消息
     * @param request
     * @return
     */
    List<TbWxMassMessageInfo> selectValidMassMessageList(BaseSopRequest request);
}
