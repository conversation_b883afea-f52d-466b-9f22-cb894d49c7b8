package com.cenker.scrm.service.bu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackSearchVO;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackVO;

import java.util.List;
import java.util.Map;

/**
 * 客户动态记录表 服务类
 */
public interface IBuOperTrackService extends IService<BuOperTrack> {

    List<BuOperTrackVO> getList(BuOperTrackSearchVO buOperTrack);

    Map<String, Object> getTrajectoryCountToday(BuOperTrackSearchVO buOperTrack);
}