package com.cenker.scrm.service.impl.kf;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.kf.WkWxKfMsgMiniProgramMapper;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgItemCommonResp;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgMiniProgram;
import com.cenker.scrm.pojo.entity.wechat.kf.WkWxKfMsgResp;
import com.cenker.scrm.service.kf.IWkWxKfMsgMiniProgramService;
import com.cenker.scrm.util.RedisIdWorker;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.cenker.scrm.constants.WkWxKfConstants.WK_KF_MSG_MINIPROGRAM_PREFIX;

@Service
@Transactional(rollbackFor = Exception.class)
public class WkWxKfMsgMiniProgramServiceImpl extends ServiceImpl<WkWxKfMsgMiniProgramMapper, WkWxKfMsgMiniProgram> implements IWkWxKfMsgMiniProgramService {

    @Autowired
    private RedisIdWorker redisIdWorker;

    @Override
    public boolean stock(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        WkWxKfMsgMiniProgram entity = new WkWxKfMsgMiniProgram();
        // 获取 id
        Long id = redisIdWorker.nextId(WK_KF_MSG_MINIPROGRAM_PREFIX);
        entity.setId(id);
        return save(entity.init(msgItem));
    }

    @Override
    public WkWxKfMsgItemCommonResp getOne(WkWxKfMsgResp wkWxKfMsgResp) {
        QueryWrapper<WkWxKfMsgMiniProgram> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("send_time", wkWxKfMsgResp.getSendTime()).eq("msg_id", wkWxKfMsgResp.getMsgId());
        return getOne(queryWrapper);
    }
}
