package com.cenker.scrm.controller.corp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.biz.customer.CustomerConditionBizHandler;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.message.*;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.request.ListRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.external.CustomerInfoVo;
import com.cenker.scrm.pojo.vo.message.*;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.cachecontent.ITbWxCacheContentService;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.group.ITbWxCustomerGroupService;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.service.message.ITbWxMassMessageSenderRecordService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 群发controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/tp/massMessage")
@RequiredArgsConstructor
public class TbWxMassMessageController extends BaseController {
    private final TokenParseUtil tokenService;
    private final ITbWxCacheContentService tbWxCacheContentService;
    private final ITbWxCustomerGroupService tbWxCustomerGroupService;
    private final CustomerConditionBizHandler customerConditionBizHandler;
    private final IApprovalService approvalService;

    /**
     * 新群发处理
     */
    private final ITbWxMassMessageInfoService tbWxMassMessageInfoService;

    private final ITbWxMassMessageSenderRecordService tbWxMassMessageSenderRecordService;

    private final TbWxExtCustomerService tbWxExtCustomerService;

    /**
     * 群发消息重构-群发社群列表查询
     */
    @GetMapping("/listGroup")
    public TableDataInfo<MassMessageInfoGroupVO> listGroup(@RequestBody QueryMassMessageDTO dto) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, dto);

        startPage();
        dto.setUserId(loginUser.getUser().getUserId());
        log.info("【查询社群群发列表】查询列表，入参：{}", dto);
        List<MassMessageInfoGroupVO> list = tbWxMassMessageInfoService.queryMassMessageInfoGroup(dto);
        return getDataTable(list);
    }

    /**
     * 群发消息重构-查询社群详情-群发内容
     */
    @GetMapping("/getInfoGroup")
    public Result<MassMessageContentVO> getInfoGroup(String infoId) {
        log.info("【查看社群群发详情】入参：{}", infoId);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        MassMessageContentVO massMessageContentVO = tbWxMassMessageInfoService.getInfoById(infoId, loginUser.getUser().getUserId());
        LambdaQueryWrapper<TbWxMassMessageSenderRecord> msQueryWrapper = Wrappers.lambdaQuery(TbWxMassMessageSenderRecord.class);
        msQueryWrapper.eq(TbWxMassMessageSenderRecord::getMessageInfoId, infoId);
        List<String> externalUserIds = tbWxMassMessageSenderRecordService.list(msQueryWrapper).stream().map(TbWxMassMessageSenderRecord::getExternalUserId).collect(Collectors.toList());
        QueryWrapper<TbWxExtCustomer> extQueryWrapper = Wrappers.query();
        massMessageContentVO.setUserList("无");
        if (CollectionUtil.isNotEmpty(externalUserIds)) {
            extQueryWrapper.select("distinct name").lambda().in(TbWxExtCustomer::getExternalUserId, externalUserIds);
            List<TbWxExtCustomer> list = tbWxExtCustomerService.list(extQueryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                String userList = String.join(",", list.stream().map(TbWxExtCustomer::getName).collect(Collectors.toList()));
                massMessageContentVO.setUserList(userList);
            }
        }
        log.info("【查看社群群发详情】出参：{}", massMessageContentVO);
        return Result.success("操作成功", massMessageContentVO);
    }

    /**
     * 群发消息重构-查询社群详情-数据概览
     */
    @GetMapping("/getDataStatisticsGroup")
    public AjaxResult getDataStatisticsGroup(ListRequest listRequest) {
        MassMessageGroupOverviewVO massMessageGroupOverviewVO = tbWxMassMessageInfoService.getMassMessageGroupOverview(listRequest);
        return AjaxResult.success(massMessageGroupOverviewVO);
//        if (StringUtils.isNotEmpty(listRequest.getChatType()) && listRequest.getChatType().equals(CHAT_TYPE_GROUP)) {
//            MassMessageGroupOverviewVO massMessageGroupOverviewVO = tbWxMassMessageInfoService.getMassMessageGroupOverview(listRequest);
//            return AjaxResult.success(massMessageGroupOverviewVO);
//        }
//        MassMessageOverviewVO massMessageOverview = tbWxMassMessageInfoService.getMassMessageOverview(listRequest);
//        return AjaxResult.success(massMessageOverview);
    }

    /**
     * 群发消息重构-查询社群详情-成员发送详情
     */
    @GetMapping("/sendUserDetailListGroup")
    public TableDataInfo sendUserDetailListGroup(@RequestBody ListRequest listRequest) {
      /*  List<MassMessageSendDetailVO> sendUserDetailList = tbWxMassMessageInfoService.getSendUserDetailList(id,type);
        return getDataTable(sendUserDetailList);*/
        startPage();
        List<MassMessageGroupSenderListVO> list = tbWxMassMessageInfoService.sendUserDetailListGroup(listRequest);
        return getDataTable(list);
    }

    @RequestMapping("/exportSendUserDetailListGroup")
    public List<MassMessageGroupSenderListVO> exportSendUserDetailListGroup(@RequestBody ListRequest listRequest) {
        return tbWxMassMessageInfoService.sendUserDetailListGroup(listRequest);
    }

    /**
     * 群发消息重构-创建/修改群发
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody MassMessageChainDTO messageChainDTO) throws SchedulerException {
        log.info("【新增/修改群发任务】开始");
        // 非群主列表
        List<String> noHasGroupOwner = Lists.newArrayList();
        // 社群群发需要校验员工中是否包含群主
        if (Constants.CHAT_TYPE_GROUP.equals(messageChainDTO.getChatType())) {
            // 筛选出所有的群主
            TbWxCustomerGroup tbWxCustomerGroup = new TbWxCustomerGroup();
            tbWxCustomerGroup.setCorpId(messageChainDTO.getCorpId());
            tbWxCustomerGroup.setStatus(StatusConstants.DEL_FLAG_FALSE_INT);
            List<TbWxCustomerGroup> tbWxCustomerGroups = tbWxCustomerGroupService.selectTbWxCustomerGroupList(tbWxCustomerGroup);
            // 是否存在群主
            boolean hasGroupOwner = false;
            if (CollectionUtil.isNotEmpty(tbWxCustomerGroups)) {
                List<String> userList = tbWxCustomerGroups.stream().map(TbWxCustomerGroup::getOwner).distinct().collect(Collectors.toList());
                // 存在群主
                for (UserConditionDTO dto : messageChainDTO.getMessageCondition().getUserConditionList()) {
                    if (userList.contains(dto.getUserId())) {
                        hasGroupOwner = true;
                    } else {
                        noHasGroupOwner.add(dto.getUserId());
                    }
                }
            }
            log.info("【新增/修改群发任务】社群群发校验员工中是否包含群主");
            // 不存在群主，直接返回错误
            if (!hasGroupOwner) {
                return AjaxResult.error("缺少有效群主，无法创建群发任务！");
            }
        }


        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String userId = loginUser.getUserId();
        messageChainDTO.setDeptId(loginUser.getDeptId());

        //保存历史附件
        tbWxCacheContentService.save(userId, messageChainDTO.getCorpId(), messageChainDTO.getAttachments());
        log.info("【新增/修改群发任务】保存历史附件");
        // 校验是否启用了审批流程
        // 获取用户所在部门是否启用审批
        if (Objects.isNull(messageChainDTO.getId())) {
            // 新增时，设置是否需要审批
            TbWxCorpConfig config = approvalService.getTbWxCorpConfig(loginUser.getUser().getCorpId());
            boolean enableApproval = getEnableApproval(loginUser.getDeptId()+"", config);
            messageChainDTO.setEnableApproval(enableApproval);
            log.info("【新增/修改群发任务】创建群发：设置是否需要审批：{}", enableApproval);
        } else {
            // 修改时，判断是否需要审批
            TbWxMassMessageInfo tbWxMassMessageInfo = tbWxMassMessageInfoService.getById(messageChainDTO.getId());
            messageChainDTO.setEnableApproval(tbWxMassMessageInfo.isEnableApproval());
            log.info("【新增/修改群发任务】修改群发：是否需要审批：{}", messageChainDTO.isEnableApproval());
        }

        tbWxMassMessageInfoService.createTask(messageChainDTO);
        log.info("【新增/修改群发任务】创建群发任务完成");
        ApprovalTypeEnum typeEnum = Constants.CHAT_TYPE_GROUP.equals(messageChainDTO.getChatType()) ? ApprovalTypeEnum.GROUPTOGROUP : ApprovalTypeEnum.MASSCUSTOMER;
        approvalService.sendPendingApprovalMsg(messageChainDTO.getMassName(), messageChainDTO.getId()+"",
                typeEnum, messageChainDTO.isEnableApproval(), messageChainDTO.getCheckStatus(), loginUser.getUser().getUserId());
        log.info("【新增/修改群发任务】发送消息完成");
        // 社群群发，并且员工包含非群主
        if (Constants.CHAT_TYPE_GROUP.equals(messageChainDTO.getChatType()) && CollectionUtil.isNotEmpty(noHasGroupOwner)) {
            return AjaxResult.success(StrUtil.format("创建成功，其中非群主的员工包含【{}】", String.join(",", noHasGroupOwner)));
        }
        return AjaxResult.success();
    }

    /**
     * 1v1群发-查询群发消息列表
     */
    @PostMapping("/selectMassMessageList")
    public TableDataInfo<MassMessageListVO> list(@RequestBody ListRequest listRequest) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, listRequest);

        startPage();
        listRequest.setUserId(loginUser.getUser().getUserId());
        log.info("【查询1V1群发列表】查询列表，入参：{}", listRequest);
        List<MassMessageListVO> list = tbWxMassMessageInfoService.selectMassMessageList(listRequest);
        return getDataTable(list);
    }

    /**
     * 群发消息重构-成员发送详情列表
     */
    @Deprecated
    @PostMapping("/sendUserDetailList")
    public TableDataInfo sendUserDetailList(@RequestBody ListRequest listRequest) {
        startPage();
        List<MassMessageSenderListVO> list = tbWxMassMessageInfoService.sendUserDetailList(listRequest);
        return getDataTable(list);
    }

    /**
     * 成员发送详情
     * @param req
     * @return
     */
    @PostMapping("/sender/detail")
    public TableDataInfo listSenderDetail(@RequestBody MassMessageSenderDetailDto req) {
        startPage();
        List<MassMessageSenderDetailVo> list = tbWxMassMessageInfoService.listSenderDetail(req);
        return getDataTable(list);
    }

    /**
     * 送达客户详情
     * @param req
     * @return
     */
    @PostMapping("/customer/detail")
    public TableDataInfo listCustomerDetail(@RequestBody MassMessageCustomerDetailDto req) {
        startPage();
        List<MassMessageCustomerDetailVo> list = tbWxMassMessageInfoService.listCustomerDetail(req);
        return getDataTable(list);
    }


    /**
     * 群发消息重构-提醒未发送成员
     */
    @PostMapping("/remind")
    public AjaxResult remind(@RequestBody ListRequest listRequest) {
        tbWxMassMessageInfoService.remindToSend(listRequest);
        return AjaxResult.success();
    }

    /**
     * 群发消息重构-数据统计
     */
    @PostMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody ListRequest listRequest) {
        return AjaxResult.success(tbWxMassMessageInfoService.getDataStatistics(listRequest));
    }

    /**
     * 群发消息重构-消息预计可见
     */
    @PostMapping("/getMessagePredictedNum")
    public AjaxResult getMessagePredictedNum(@RequestBody MassMessageChainDTO massMessageChainDTO) {
        int messagePredictedNum = tbWxMassMessageInfoService.getMessagePredictedNum(massMessageChainDTO);
        return AjaxResult.success(messagePredictedNum);
    }

    /**
     * 群发消息重构-群发消息详情
     */
    @PostMapping("/getInfo")
    public Result<MassMessageDetailVO> getMassMessageInfo(@RequestBody ListRequest listRequest) {
        log.info("【查看1V1群发详情】入参：{}", listRequest);
        MassMessageDetailVO massMessageDetailVO = tbWxMassMessageInfoService.getMassMessageInfo(listRequest);
        log.info("【查看1V1群发详情】出参：{}", massMessageDetailVO);
        return Result.success("操作成功",massMessageDetailVO);
    }

    /**
     * 群发消息重构-导出数据
     */
    @PostMapping("/exportSendUserDetailList")
    public List<MassMessageExportVo> exportSendUserDetailList(@RequestBody ListRequest listRequest) {
        return tbWxMassMessageInfoService.exportSendUserDetailList(listRequest);
    }

    /**
     * 群发消息重构-更新群发消息数据
     */
    @PostMapping("/synchronizeMassMessage")
    public AjaxResult synchronizeMassMessage(@RequestBody ListRequest listRequest) {
        tbWxMassMessageInfoService.synchronizeMassMessage(listRequest.getId(), listRequest.getCorpId());
        return AjaxResult.success();
    }

    /**
     * 群发消息重构-定时更新群发消息数据
     */
    @PostMapping("/synchronizeMassMessageList")
    public AjaxResult synchronizeMassMessageList(@RequestBody TbWxCorpConfig tbWxCorpConfig) {
        String corpId = tbWxCorpConfig.getCorpId();
        // 同步一个月内（发送时间/创建时间）的有效消息
        List<TbWxMassMessageInfo> massMessageList = tbWxMassMessageInfoService.selectValidMassMessageList(tbWxCorpConfig.getId());
        if (CollectionUtil.isNotEmpty(massMessageList)) {
            for (TbWxMassMessageInfo massMessageListVO : massMessageList) {
                tbWxMassMessageInfoService.synchronizeMassMessage(massMessageListVO.getId(), corpId);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 群发消息重构-取消任务
     */
    @PostMapping("/cancelMassMessageTask")
    public AjaxResult cancelMassMessageTask(@RequestBody ListRequest listRequest) throws SchedulerException {
        tbWxMassMessageInfoService.cancelMassMessageTask(listRequest);
        return AjaxResult.success();
    }

    /**
     * 群发消息重构-获取任务创建结果
     */
    @PostMapping("/getTaskResult")
    public AjaxResult getTaskResult(@RequestBody QueryMassMessageVo queryMassMessageVo) {
        List<MassMessageListVO> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(queryMassMessageVo.getIds())) {
            for (Long id : queryMassMessageVo.getIds()) {
                MassMessageListVO massMessageListVO = new MassMessageListVO();
                massMessageListVO.setId(id);
                // 查询数据库
                TbWxMassMessageInfo tbWxMassMessageInfo = tbWxMassMessageInfoService.getOne(new LambdaQueryWrapper<TbWxMassMessageInfo>()
                        .select(TbWxMassMessageInfo::getCheckStatus)
                        .eq(TbWxMassMessageInfo::getId, id)
                );
                if (ObjectUtil.isNull(tbWxMassMessageInfo)) {
                    massMessageListVO.setCheckStatus(ApprovalStatusEnum.EXEC_EXCEPTION.getStatus());
                } else {
                    massMessageListVO.setCheckStatus(tbWxMassMessageInfo.getCheckStatus());
                }
                list.add(massMessageListVO);
            }
        }
        return AjaxResult.success(list);
    }


    /**
     * 送达社群明细
     */
    @RequestMapping("/sendGroupDetailList")
    public TableDataInfo sendGroupDetailList(@RequestBody ListRequest listRequest) {
        startPage();
        List<MassMessageSenderListVO> list = tbWxMassMessageInfoService.sendGroupDetailList(listRequest);
        return getDataTable(list);
    }

    @RequestMapping("/exportSendGroupDetailList")
    public List<MassMessageSenderListVO> exportSendGroupDetailList(@RequestBody ListRequest listRequest) {
        return tbWxMassMessageInfoService.sendGroupDetailList(listRequest);
    }



    /**
     * 1v1群发根据条件查询发送用户
     * @param qrySendMsgCustUserDTO
     * @return
     */
    @PostMapping("/qrySendMsgCustUserList")
    public TableDataInfo qrySendMgCustUserList(@RequestBody QryListSendMsgCustUserDTO qrySendMsgCustUserDTO){
        List<String> extUserIdList =  new ArrayList<>();
        try {
           extUserIdList = customerConditionBizHandler.qrySendMsgCustUserList(qrySendMsgCustUserDTO.getCorpId(), qrySendMsgCustUserDTO.getCondition());
        }catch (Exception e){
            log.error("###########查询客户列表失败#########",e);
        }
        CustomerChurnDTO dto = new CustomerChurnDTO();
        BeanUtils.copyProperties(qrySendMsgCustUserDTO,dto);
        dto.setExternalUserIdList(extUserIdList);
        dto.setCustomerName(qrySendMsgCustUserDTO.getCustName());
        List<CustomerInfoVo> listUser = null;
        if (extUserIdList.size()>0) {
            PageHelper.startPage(qrySendMsgCustUserDTO.getPageNum(), qrySendMsgCustUserDTO.getPageSize());
            listUser = tbWxExtCustomerService.qryExtCustomerList(dto);
            return getDataTable(listUser);
        }else{
            listUser = new ArrayList<>();
            return getDataTable(listUser);
        }
    }

    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return tbWxMassMessageInfoService.approve(approvalVO, loginUser);
    }
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return tbWxMassMessageInfoService.revoked(approvalVO, loginUser);
    }

}
