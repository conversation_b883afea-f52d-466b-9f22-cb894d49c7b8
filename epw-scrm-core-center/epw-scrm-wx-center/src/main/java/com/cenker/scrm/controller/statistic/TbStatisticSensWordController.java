package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.enums.StatisticTypeEnum;
import com.cenker.scrm.event.StatisticUpdateEvent;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticSensWord;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSensWordListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticSensWordSummaryVo;
import com.cenker.scrm.service.statistic.ITbStatisticSensWordService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据统计-敏感词统计 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/statistic/sensWord")
public class TbStatisticSensWordController extends BaseController {

    private final ITbStatisticSensWordService tbStatisticSensWordService;
    private final TokenParseUtil tokenService;
    private final ApplicationEventPublisher applicationEventPublisher;


    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        StatisticSensWordSummaryVo vo = tbStatisticSensWordService.summary(query);
        return AjaxResult.success(vo);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(@RequestBody StatisticGraphQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<StatisticGraphVo> lstGraph = tbStatisticSensWordService.graph(query);
        return AjaxResult.success(lstGraph);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody StatisticSensWordListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        this.startPage();
        List<TbStatisticSensWord> lstSensWord = tbStatisticSensWordService.list(query);
        return getDataTable(lstSensWord);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public List<TbStatisticSensWord> export(@RequestBody StatisticSensWordListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<TbStatisticSensWord> lstSensWord = tbStatisticSensWordService.list(query);
        return lstSensWord;
    }

    /**
     * 更新数据
     */
    @RequestMapping("/synData")
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        StatisticUpdateEvent event = new StatisticUpdateEvent(StatisticTypeEnum.SENS_WORD, query.getBeginTime(), query.getEndTime(), ITbStatisticSensWordService.class, loginUser.getUserId());
        applicationEventPublisher.publishEvent(event);
        return Result.success("更新操作成功，数据同步中");
    }
}
