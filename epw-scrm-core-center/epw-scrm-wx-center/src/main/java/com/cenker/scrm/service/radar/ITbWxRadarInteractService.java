package com.cenker.scrm.service.radar;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarStatisticsDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContentRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.*;

import java.util.Date;
import java.util.List;

public interface ITbWxRadarInteractService extends IService<TbWxRadarInteract> {
    List<InteractRadarVo> getRadarList(InteractRadarVo radarVo);

    void sendMessageByReadRecord(TbWxRadarContentRecord record, MpWxUser mpWxUser);

    RadarStatisticsVO getRadarStatistics(String radarId, Date startTime, Date endTime);

    /**
     * 查询智能物料客户链接数据
     */
    List<RadarCustomerStatisticsVO> getRadarReadRecordStatistics(RadarStatisticsDTO radarStatisticsDTO);

    /**
     * 聊天侧边栏-智能物料数据
     * @param radarVo
     * @return
     */
    List<InteractRadarChatVO> getRadarChatList(InteractRadarVo radarVo);

    /**
     * 智能物料统计数据-聊天素材
     * @param radarId 内容id
     */
    RadarStatisticsVO getRadarChatStatistics(String radarId);

    /**
     * 根据id查询智能物料
     * @param radarId
     * @return
     */
    TbWxRadarInteract getOneById(String radarId);

    /**
     * 根据id查询微信用户
     */
    String getForwardUserById(String forwardUser);

    /**
     * 根据内容id查询雷达
     */
    TbWxRadarInteract selectRadarByContentId(String contentId);


    /**
     * 提取公众号文章
     */
    void extractRadarContent(TbWxRadarContent content, boolean extractContent);

    /**
     * 提取外部链接封面等
     */
    void linkData(TbWxRadarContent content);

    List<InteractRadarVo> getRadarSource(InteractRadarVo radarVo);

    List<RadarStatisticsRankVO> getRadarStatisticsRank(CurrentUserDTO currentUserDTO, QueryRadarStatisticsRankDTO dto);

    Result approve(ApprovalVO approvalVO, LoginUser loginUser);

    String getBusinessJson(TbWxRadarInteract info);

    Result revoked(ApprovalVO approvalVO, LoginUser loginUser);
}
