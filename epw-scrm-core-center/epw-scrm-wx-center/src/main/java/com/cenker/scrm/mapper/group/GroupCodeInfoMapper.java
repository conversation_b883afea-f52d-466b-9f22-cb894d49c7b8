package com.cenker.scrm.mapper.group;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.group.GroupCodeConfigDTO;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.request.group.GroupCodeStatisticsRequest;
import com.cenker.scrm.pojo.vo.group.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description 社群活码
 */
public interface GroupCodeInfoMapper extends BaseMapper<GroupCodeInfo> {
    /**
     * 社群活码列表
     * @param request
     * @return
     */
    List<GroupCodeListVO> listGroupCode(GroupCodeRequest request);

    /**
     * 查询社群活码详情
     * @param request
     * @return 社群活码详情
     */
    GroupCodeDetailVO detailGroupCode(GroupCodeRequest request);

    /**
     * 根据活码id查询社群列表
     * @param codeId 活码id
     * @return 社群列表
     */
    GroupCodeConfigDTO getGroupListByCodeId(@Param("codeId") Long codeId);

    /**
     * 社群活码数据概览
     * @param request
     * @return
     */
    GroupCodeDataStatisticsVO getDataStatistics(GroupCodeRequest request);

    /**
     * 社群活码统计明细-按群聊查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailGroupVO> getDataStatisticsDetailByGroup(GroupCodeStatisticsRequest request);

    /**
     * 社群活码统计明细-按日期查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetailByDate(GroupCodeStatisticsRequest request);

    /**
     * 社群活码统计明细-按小时查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetailByHour(GroupCodeRequest request);

    /**
     * 社群活码扫码次数-按小时查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetail4ScanByHour(GroupCodeRequest request);

    /**
     * 社群活码扫码次数-按日期查看
     * @param request
     * @return
     */
    List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetail4ScanByDate(GroupCodeRequest request);
}
