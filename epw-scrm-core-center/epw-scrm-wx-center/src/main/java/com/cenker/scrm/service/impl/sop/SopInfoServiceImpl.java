package com.cenker.scrm.service.impl.sop;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.sop.SopInfoMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.sop.SopInfo;
import com.cenker.scrm.pojo.exception.DataNotExistException;
import com.cenker.scrm.pojo.request.sop.*;
import com.cenker.scrm.pojo.vo.sop.*;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.sop.ISopInfoService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description sop
 */
@Service
@RequiredArgsConstructor
public class SopInfoServiceImpl extends ServiceImpl<SopInfoMapper, SopInfo> implements ISopInfoService {
    private final TbWxExtCustomerService tbWxExtCustomerService;

    @Override
    public List<ConditionSopListVO> listConditionSop(BaseSopRequest request) {
        // 因为产品默认排序需要把正常和停用分开 并有另外的排序条件（创建时间）
        // 所以这里无法使用分页插件的order by 只能使用自定义order by
        return baseMapper.listConditionSop(request);
    }

    @Override
    public ConditionSopDetailVO detailConditionSop(ConditionSopRequest request) {
        ConditionSopDetailVO conditionSopDetailVO = baseMapper.detailConditionSop(request);
        Optional.ofNullable(conditionSopDetailVO).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        return conditionSopDetailVO;
    }

    @Override
    public ConditionSopDataStatisticsVO getConditionSopDataStatistics(ConditionSopQueryRequest request) {
        ConditionSopDataStatisticsVO dataStatistics = baseMapper.getDataStatistics(request);
        calculateRate(dataStatistics);
        return dataStatistics;
    }

    /**
     * 计算各完成率
     *
     * @param dataStatistics
     */
    public void calculateRate(ConditionSopDataStatisticsVO dataStatistics) {
        Optional.ofNullable(dataStatistics).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        if (ObjectUtil.isNull(dataStatistics.getDelCustomerTotalCnt())) {
            dataStatistics.setDelCustomerTotalCnt(0);
        }
        BigDecimal hundred = BigDecimal.valueOf(100);
        if (dataStatistics.getTotalTaskCnt() != 0) {
            // 任务执行完成率
            dataStatistics.setCompleteTaskRate(BigDecimal.valueOf((double) dataStatistics.getCompleteTaskCnt() / dataStatistics.getTotalTaskCnt())
                    .multiply(hundred)
                    .setScale(2, BigDecimal.ROUND_DOWN).toString() + "%");
        }
        if (dataStatistics.getReachCustomerTotalCnt() != 0) {
            // 消息触达流失率
            dataStatistics.setMessageReachChurnRate(BigDecimal.valueOf((double) dataStatistics.getDelCustomerTotalCnt() / dataStatistics.getReachCustomerTotalCnt())
                    .multiply(hundred)
                    .setScale(2, BigDecimal.ROUND_DOWN).toString() + "%");
        }
        if (dataStatistics.getSendCustomerTotalCnt() != 0) {
            // 客户触达流失率
            dataStatistics.setCustomerReachChurnRate(BigDecimal.valueOf((double) dataStatistics.getDelCustomerTotalCnt() / dataStatistics.getSendCustomerTotalCnt())
                    .multiply(hundred)
                    .setScale(2, BigDecimal.ROUND_DOWN).toString() + "%");
        }
    }

    @Override
    public JourneySopDetailVO detailJourneySop(JourneySopRequest request) {
        JourneySopDetailVO journeySopDetailVO = baseMapper.detailJourneySop(request);
        Optional.ofNullable(journeySopDetailVO).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        return journeySopDetailVO;
    }

    @Override
    public JourneySopDataStatisticsVO getJourneySopDataStatistics(JourneySopQueryRequest request) {
        JourneySopDataStatisticsVO dataStatistics = baseMapper.getJourneySopDataStatistics(request);
        BigDecimal hundred = BigDecimal.valueOf(100);
        Optional.ofNullable(dataStatistics).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        if (dataStatistics.getTotalTaskCnt() != 0) {
            // 任务执行完成率
            dataStatistics.setCompleteTaskRate(BigDecimal.valueOf((double) dataStatistics.getCompleteTaskCnt() / dataStatistics.getTotalTaskCnt())
                    .multiply(hundred)
                    .setScale(2, BigDecimal.ROUND_DOWN).toString() + "%");
        }
        return dataStatistics;
    }

    @Override
    public List<JourneySopListVO> listJourneySop(JourneySopRequest request) {
        return baseMapper.listJourneySop(request);
    }

    @Override
    public List<String> getExternalUserIdListByIds(List<Long> idList) {
        List<String> externalIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idList)) {
            if (idList.size() > 1000) {
                List<List<Long>> partition = Lists.partition(idList, 1000);
                for (List<Long> longs : partition) {
                    externalIdList.addAll(Optional.ofNullable(tbWxExtCustomerService.listByIds(longs)).orElse(Lists.newArrayList()).stream().map(TbWxExtCustomer::getExternalUserId).distinct().collect(Collectors.toList()));
                }
                return externalIdList;
            }
            return Optional.ofNullable(tbWxExtCustomerService.listByIds(idList)).orElse(Lists.newArrayList()).stream().map(TbWxExtCustomer::getExternalUserId).collect(Collectors.toList());
        }
        return externalIdList;
    }

    @Override
    public MassCustomerSopDetailVO detailMassCustomerSop(MassCustomerSopRequest request) {
        MassCustomerSopDetailVO massCustomerSopDetailVO = baseMapper.detailMassCustomerSop(request);
        Optional.ofNullable(massCustomerSopDetailVO).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        return massCustomerSopDetailVO;
    }
}
