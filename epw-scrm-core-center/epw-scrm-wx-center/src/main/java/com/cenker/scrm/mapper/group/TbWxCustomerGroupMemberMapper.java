package com.cenker.scrm.mapper.group;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.dto.condition.SopTriggerDetailDTO;
import com.cenker.scrm.pojo.dto.group.CustomerGroupDto;
import com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroupMember;
import com.cenker.scrm.pojo.vo.group.CustomerMemberVO;
import com.cenker.scrm.pojo.vo.group.GroupCustomerUserTotalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
public interface TbWxCustomerGroupMemberMapper extends BaseMapper<TbWxCustomerGroupMember> {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】ID
     * @return 【请填写功能名称】
     */
    TbWxCustomerGroupMember selectTbWxCustomerGroupMemberById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tbWxCustomerGroupMember 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<TbWxCustomerGroupMember> selectTbWxCustomerGroupMemberList(TbWxCustomerGroupMember tbWxCustomerGroupMember);

    /**
     * 新增【请填写功能名称】
     *
     * @param tbWxCustomerGroupMember 【请填写功能名称】
     * @return 结果
     */
    int insertTbWxCustomerGroupMember(TbWxCustomerGroupMember tbWxCustomerGroupMember);

    /**
     * 修改【请填写功能名称】
     *
     * @param tbWxCustomerGroupMember 【请填写功能名称】
     * @return 结果
     */
    int updateTbWxCustomerGroupMember(TbWxCustomerGroupMember tbWxCustomerGroupMember);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】ID
     * @return 结果
     */
    int deleteTbWxCustomerGroupMemberById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTbWxCustomerGroupMemberByIds(Long[] ids);

    /**
     * 通过group_id 获取群成员信息
     *
     * @param groupId
     * @param corpId
     * @param memberName
     * @return
     */
    List<CustomerMemberVO> queryMemberInfoByGroupId(@Param("groupId") String groupId, @Param("corpId") String corpId,
                                                    @Param("memberName") String memberName);

    /**
     * 解散群，变更群成员状态
     * @param tbWxCustomerGroupMember
     * @return
     */
    int bathUpdateTbWxCustomerGroupMember(TbWxCustomerGroupMember tbWxCustomerGroupMember);

    List<CustomerMemberVO> queryMemberInfoByGroupId2(CustomerGroupDto dto);

    GroupCustomerUserTotalVo countGroupDataByWorkBenchHome(MobileUser mobileUser);

    GroupCustomerUserTotalVo countGroupMemberByDate(@Param("statDate") String statDate, @Param("owner") String owner);

    /**
     * 添加群数大于等于某个数字的
     * @param sopTriggerDetailDTO
     * @return
     */
    List<ConditionSopCustomerDTO> ge4AddGroupCount4Sop(SopTriggerDetailDTO sopTriggerDetailDTO);

    /**
     * 筛选指定群
     * @param sopTriggerDetailDTO
     * @return
     */
    List<ConditionSopCustomerDTO> assignSelectAddGroup4Sop(SopTriggerDetailDTO sopTriggerDetailDTO);
}
