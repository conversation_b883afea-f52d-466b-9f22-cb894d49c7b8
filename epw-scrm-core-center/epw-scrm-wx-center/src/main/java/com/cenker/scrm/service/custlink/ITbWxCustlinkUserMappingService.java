package com.cenker.scrm.service.custlink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustlink;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustlinkCustDetail;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustlinkUserMapping;
import com.cenker.scrm.pojo.vo.custlink.CustlinkVO;

import java.util.List;

/**
 * @description:
 * @author:znlian
 * @time:2024/4/5
 */
public interface ITbWxCustlinkUserMappingService extends IService<TbWxCustlinkUserMapping> {

    List<UserConditionDTO> qryUserListByLinkId(Long linkId);


}
