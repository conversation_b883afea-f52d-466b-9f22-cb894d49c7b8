package com.cenker.scrm.service.impl.moment;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.moment.TbWxMomentSendCustomerMapper;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentInteract;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.radar.CorpInteractVO;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryService;
import com.cenker.scrm.service.moment.ITbWxMomentInteractService;
import com.cenker.scrm.service.moment.ITbWxMomentSendCustomerService;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.external.WxCpGetMomentComments;
import me.chanjar.weixin.cp.bean.external.WxCpGetMomentCustomerList;
import me.chanjar.weixin.cp.bean.external.WxCpGetMomentSendResult;
import me.chanjar.weixin.cp.bean.external.moment.CustomerItem;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cenker.scrm.constants.StatusConstants.INVISIBLE_CUSTOMER;


/**
 * <AUTHOR>
 * @Date 2022/1/18
 * @Description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TbWxMomentSendCustomerServiceImpl extends ServiceImpl<TbWxMomentSendCustomerMapper, TbWxMomentSendCustomer> implements ITbWxMomentSendCustomerService {
    /**
     * 企微接口注入优化
     */
    private final IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    private final ITbWxMomentInteractService momentInteractService;
    private final ITbWxCustomerTrajectoryService trajectoryService;
    private final ITbWxUserService userService;

    @Override
    public void getMomentCustomerList(List<TbWxMomentSendUser> list, String momentId) {
        if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotEmpty(momentId)) {
            // 代自建应用方式获取
            WxCpServiceImpl wxCpService = workCorpCpService.getWxCpCustomerServiceByCorpId(list.get(0).getCorpId());
            for (TbWxMomentSendUser tbWxMomentSendUser : list) {
                // 执行者
                String userId = tbWxMomentSendUser.getUserId();

                // 如果执行者已存在可见范围 或者离职 不再查询
                TbWxUser tbWxUser = userService.getOne(new LambdaQueryWrapper<TbWxUser>()
                        .select(TbWxUser::getUserid)
                        .eq(TbWxUser::getUserid,userId)
                        .eq(TbWxUser::getCorpId,list.get(0).getCorpId())
                        // 1 正常 2 离职
                        .eq(TbWxUser::getDelFlag, StatusConstants.DEL_FLAG_TRUE)
                );
                if (tbWxUser == null) {
                    continue;
                }

                int count = count(new LambdaQueryWrapper<TbWxMomentSendCustomer>()
                        .eq(TbWxMomentSendCustomer::getCorpId, list.get(0).getCorpId())
                        .eq(TbWxMomentSendCustomer::getMomentTaskId, list.get(0).getMomentTaskId())
                        .eq(TbWxMomentSendCustomer::getUserId, userId)
                );
                if (count > 0) {
                    continue;
                }
                try {
                    // 	用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
                    String cursor = "";
                    WxCpGetMomentCustomerList momentCustomerList = wxCpService.getExternalContactService().getMomentCustomerList(momentId, userId, cursor, 1000);
                    if (momentCustomerList.getErrcode() == 0L) {
                        do {
                            cursor = momentCustomerList.getNextCursor();
                            List<CustomerItem> customerList = momentCustomerList.getCustomerList();
                            List<TbWxMomentSendCustomer> addList = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(customerList)) {
                                for (CustomerItem customerItem : customerList) {
                                    TbWxMomentSendCustomer tbWxMomentSendCustomer = new TbWxMomentSendCustomer();
                                    tbWxMomentSendCustomer.setCorpId(tbWxMomentSendUser.getCorpId());
                                    tbWxMomentSendCustomer.setUserId(customerItem.getUserId());
                                    tbWxMomentSendCustomer.setExternalUserId(customerItem.getExternalUserId());
                                    tbWxMomentSendCustomer.setMomentTaskId(tbWxMomentSendUser.getMomentTaskId());
                                    addList.add(tbWxMomentSendCustomer);
                                }
                                saveBatch(addList);
                            }
                        } while (StringUtils.isNotEmpty(cursor));
                    }
                } catch (WxErrorException e) {
                    log.error("【发朋友圈】获取选择的可见范围失败");
                }
            }
        }
    }

    @Override
    public void getMomentSendResult(List<TbWxMomentSendUser> list, String momentId) {
        if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotEmpty(momentId)) {
            // 代自建应用方式获取
            WxCpServiceImpl wxCpService = workCorpCpService.getWxCpCustomerServiceByCorpId(list.get(0).getCorpId());
            for (TbWxMomentSendUser tbWxMomentSendUser : list) {
                // 执行者
                String userId = tbWxMomentSendUser.getUserId();

                // 如果执行者离职 不再查询
                TbWxUser tbWxUser = userService.getOne(new LambdaQueryWrapper<TbWxUser>()
                        .select(TbWxUser::getUserid)
                        .eq(TbWxUser::getCorpId,list.get(0).getCorpId())
                        .eq(TbWxUser::getUserid,userId)
                        // 1 正常 2 离职
                        .eq(TbWxUser::getDelFlag, StatusConstants.DEL_FLAG_TRUE)
                );
                if (tbWxUser == null) {
                    continue;
                }

                // 更新可见范围中的客户为可见客户
                List<TbWxMomentSendCustomer> selectList = list(new LambdaQueryWrapper<TbWxMomentSendCustomer>()
                        .eq(TbWxMomentSendCustomer::getMomentTaskId, list.get(0).getMomentTaskId())
                        .eq(TbWxMomentSendCustomer::getCorpId, list.get(0).getCorpId())
                        .eq(TbWxMomentSendCustomer::getUserId, userId));
                if (CollectionUtils.isNotEmpty(selectList)) {
                    // 	用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
                    String cursor = "";
                    try {
                        WxCpGetMomentSendResult momentSendResult = wxCpService.getExternalContactService().getMomentSendResult(momentId, userId, cursor, 5000);
                        if (momentSendResult.getErrcode() == 0L) {
                            do {
                                cursor = momentSendResult.getNextCursor();
                                List<CustomerItem> customerList = momentSendResult.getCustomerList();
                                List<TbWxMomentSendCustomer> updateList = Lists.newArrayList();
                                if (CollectionUtils.isNotEmpty(customerList)) {
                                    for (CustomerItem customerItem : customerList) {
                                        String externalUserId = customerItem.getExternalUserId();
                                        // 过滤出来需要变更为可见客户的
                                        List<TbWxMomentSendCustomer> collect = selectList.stream()
                                                .filter(sl -> sl.getExternalUserId().equals(externalUserId) && sl.getVisible() == INVISIBLE_CUSTOMER).collect(Collectors.toList());
                                        if (CollectionUtils.isNotEmpty(collect)) {
                                            updateList.add(collect.get(0));
                                        }
                                    }
                                    if (CollectionUtils.isNotEmpty(updateList)) {
                                        updateList.forEach(ul -> ul.setVisible(StatusConstants.VISIBLE_CUSTOMER));
                                        updateBatchById(updateList, 5000);
                                    }
                                }
                                updateList.clear();
                            } while (StringUtils.isNotEmpty(cursor));
                        }
                    } catch (WxErrorException e) {
                        if (e.getMessage().contains("错误代码：41062")) {
                            log.error("【发朋友圈】获取可见客户列表失败,user didn't publish the moment（可能是没有可见客户）");
                        } else {
                            log.error("【发朋友圈】获取可见客户列表失败");
                        }
                        log.error("错误信息:{}",e);
                    }
                }
            }
        }
    }

    @Override
    public void getMomentComments(List<TbWxMomentSendUser> list, String momentId) {
        if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotEmpty(momentId)) {
            String corpId = list.get(0).getCorpId();
            String momentTaskId = list.get(0).getMomentTaskId();
            // 代自建应用方式获取
            WxCpServiceImpl wxCpService = workCorpCpService.getWxCpCustomerServiceByCorpId(corpId);
            for (TbWxMomentSendUser tbWxMomentSendUser : list) {
                // 执行者
                String userId = tbWxMomentSendUser.getUserId();
                // 删除该执行者所有点赞评论数据 重新插入
                LambdaQueryWrapper<TbWxMomentInteract> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TbWxMomentInteract::getCorpId, corpId)
                        .eq(TbWxMomentInteract::getMomentTaskId, momentTaskId)
                        .eq(TbWxMomentInteract::getUserId, userId);

                // 客户画像0.4 发朋友圈点赞评论动态
                List<TbWxMomentInteract> momentInteractList = momentInteractService.list(queryWrapper);
                Map<String, TbWxMomentInteract> likeExist = Maps.newHashMap();
                Map<String, TbWxMomentInteract> commentExist = Maps.newHashMap();
                if (CollectionUtil.isNotEmpty(momentInteractList)) {
                    // 数据库点赞
                    likeExist = momentInteractList.stream().filter(m -> m.getType() == TypeConstants.MOMENT_CUSTOMER_LIKE).collect(Collectors.toMap(TbWxMomentInteract::getExternalUserId, Function.identity(), (x1, x2) -> x2));
                    // 数据库评论
                    commentExist = momentInteractList.stream().filter(m -> m.getType() == TypeConstants.MOMENT_CUSTOMER_COMMENT).collect(Collectors.toMap(TbWxMomentInteract::getExternalUserId, Function.identity(), (x1, x2) -> x2));
                }

                momentInteractService.remove(queryWrapper);
                try {
                    WxCpGetMomentComments momentComments = wxCpService.getExternalContactService().getMomentComments(momentId, userId);
                    if (momentComments.getErrcode() == 0L) {
                        // 点赞
                        List<WxCpGetMomentComments.CommentLikeItem> likeList = momentComments.getLikeList();
                        List<TbWxMomentInteract> likeAddList = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(likeList)) {
                            for (WxCpGetMomentComments.CommentLikeItem commentLikeItem : likeList) {
                                TbWxMomentInteract tbWxMomentInteract = new TbWxMomentInteract();
                                tbWxMomentInteract.setCorpId(corpId);
                                Date likeDate = new Date(commentLikeItem.getCreateTime() * 1000);
                                tbWxMomentInteract.setInteractTime(likeDate);
                                // 如果外部客户id为空 代表自己给自己点赞
                                String externalUserId = commentLikeItem.getExternalUserId();
                                if (StringUtils.isNotEmpty(externalUserId)) {
                                    tbWxMomentInteract.setExternalUserId(commentLikeItem.getExternalUserId());

                                    // 客户画像0.4
                                    try {
                                        if (likeExist.get(externalUserId) == null) {
                                            // 证明之前没点赞 加入客户轨迹
                                            trajectoryService.addCustomerTrajectory2Moment(corpId,userId,externalUserId,momentTaskId,likeDate,TypeConstants.MOMENT_CUSTOMER_LIKE);
                                        }
                                    } catch (Exception e) {
                                        log.info("【发朋友圈】同步点赞评论数据记录客户轨迹异常，{},{}",corpId,externalUserId);
                                    }

                                }else {
                                    tbWxMomentInteract.setExternalUserId(userId);
                                }
                                tbWxMomentInteract.setMomentTaskId(Long.valueOf(momentTaskId));
                                tbWxMomentInteract.setType(TypeConstants.MOMENT_CUSTOMER_LIKE);
                                tbWxMomentInteract.setUserId(userId);
                                likeAddList.add(tbWxMomentInteract);
                            }
                            momentInteractService.saveBatch(likeAddList);
                        }
                        // 评论
                        List<WxCpGetMomentComments.CommentLikeItem> commentList = momentComments.getCommentList();
                        List<TbWxMomentInteract> commentAddList = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(commentList)) {
                            for (WxCpGetMomentComments.CommentLikeItem commentLikeItem : commentList) {
                                TbWxMomentInteract tbWxMomentInteract = new TbWxMomentInteract();
                                tbWxMomentInteract.setCorpId(corpId);
                                Date commentDate = new Date(commentLikeItem.getCreateTime() * 1000);
                                tbWxMomentInteract.setInteractTime(new Date(commentLikeItem.getCreateTime() * 1000));
                                String externalUserId = commentLikeItem.getExternalUserId();
                                if (StringUtils.isNotEmpty(externalUserId)) {
                                    tbWxMomentInteract.setExternalUserId(commentLikeItem.getExternalUserId());

                                    // 客户画像0.4
                                    try {
                                        if (commentExist.get(externalUserId) == null) {
                                            // 证明之前没评论 加入客户轨迹
                                            trajectoryService.addCustomerTrajectory2Moment(corpId,userId,externalUserId,momentTaskId,commentDate,TypeConstants.MOMENT_CUSTOMER_COMMENT);
                                        }
                                    } catch (Exception e) {
                                        log.info("【发朋友圈】同步点赞评论数据记录客户轨迹异常，{},{}",corpId,externalUserId);
                                    }

                                }else {
                                    tbWxMomentInteract.setExternalUserId(userId);
                                }

                                tbWxMomentInteract.setMomentTaskId(Long.valueOf(momentTaskId));
                                tbWxMomentInteract.setType(TypeConstants.MOMENT_CUSTOMER_COMMENT);
                                tbWxMomentInteract.setUserId(userId);
                                commentAddList.add(tbWxMomentInteract);
                            }
                            momentInteractService.saveBatch(commentAddList);
                        }
                    }
                } catch (WxErrorException e) {
                    if (e.getMessage().contains(ErrCodeEnum.INVALID_USERID.getCode().toString())) {
                        log.error(ErrCodeEnum.INVALID_USERID.getMessage());
                    }else if (e.getMessage().contains(ErrCodeEnum.USER_NOT_POSTED_MOMENTS.getCode().toString())) {
                        log.error(ErrCodeEnum.USER_NOT_POSTED_MOMENTS.getMessage());
                    }else {
                        log.error(ErrCodeEnum.FAILED_OBTAIN_INTERACTIVE_DATA.getMessage());
                    }
                }
            }
        }
    }

    @Override
    public CorpInteractVO countCorpInteractByExtUserId(CustomerChurnDTO dto) {
        return baseMapper.countCorpInteractByExtUserId(dto);
    }
}
