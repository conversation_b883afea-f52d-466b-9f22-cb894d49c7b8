package com.cenker.scrm.service.impl.sitecontact;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.sitecontact.ContactAreaMapper;
import com.cenker.scrm.mapper.sitecontact.ContactDeliveryUserMapper;
import com.cenker.scrm.mapper.sitecontact.ContactSiteMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxContactRel;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactArea;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactDeliveryUser;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactSite;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.request.ContactAreaRequest;
import com.cenker.scrm.pojo.request.SiteRequest;
import com.cenker.scrm.pojo.request.StoreImportRequest;
import com.cenker.scrm.pojo.vo.UserVO;
import com.cenker.scrm.pojo.vo.sitecontact.*;
import com.cenker.scrm.service.contact.ITbWxContactRelService;
import com.cenker.scrm.service.sitecontact.IContactAreaService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.UUID;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * @Description
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContactAreaServiceImpl extends ServiceImpl<ContactAreaMapper, ContactArea> implements IContactAreaService {

    private final ContactDeliveryUserMapper contactDeliveryUserMapper;
    private final ITbWxContactRelService tbWxContactRelService;
    private final ContactSiteMapper contactSiteMapper;


    @Override
    public List<ContactArea> treeAllAreaSelect(ContactAreaRequest contactAreaRequest) {
        LambdaQueryWrapper<ContactArea> wrapper = new LambdaQueryWrapper<>();
        // 只查询省市区
        wrapper.in(ContactArea::getAreaLevel, TypeConstants.CONTACT_PROVINCE, TypeConstants.CONTACT_CITY, TypeConstants.CONTACT_DISTRICT)
                .select(ContactArea::getAreaName, ContactArea::getParentId, ContactArea::getId, ContactArea::getAreaLevel)
                .orderByAsc(ContactArea::getOrderNum);
        List<ContactArea> contactAreas = baseMapper.selectList(wrapper);
        if (contactAreaRequest.getShowDefault()) {
            ContactArea contactArea = new ContactArea();
            contactArea.setAreaName("默认");
            contactArea.setId(-1L);
            contactArea.setParentId(0L);
            contactArea.setAreaLevel(TypeConstants.CONTACT_PROVINCE);
            contactAreas.add(contactArea);
            contactAreas = contactAreas.stream().sorted(Comparator.comparing(ContactArea::getId)).collect(Collectors.toList());
        }
        return contactAreas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSite(SiteRequest siteRequest) {
        // 校验重名
        LambdaQueryWrapper<ContactArea> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContactArea::getAreaName, siteRequest.getSiteName()).eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_SITE);
        if (count(wrapper) > 0) {
            throw new CustomException(ErrCodeEnum.CITY_CONTACT_SITE_NAME_REPEAT_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_SITE_NAME_REPEAT_ERROR.getCode());
        }
        // 新增站点
        ContactArea contactArea = new ContactArea();
        contactArea.setAreaName(siteRequest.getSiteName());
        contactArea.setParentId(siteRequest.getAreaId());
        contactArea.setAreaLevel(TypeConstants.CONTACT_SITE);
        contactArea.setSignId(UUID.tokenBuild(8));
        contactArea.setAreaStatus(StatusConstants.OPEN_FLAG);
        contactArea.setCreateBy(siteRequest.getCreateBy());
        contactArea.setCorpId(1L);
        save(contactArea);
        // 同步活码配置
        ContactSite contact = new ContactSite();
        siteRequest.setSiteId(contactArea.getId());
        // 设置站点活码配置信息
        setContact(siteRequest, contact);
        // 生成活码成功 插入数据库
        saveTbWxContact(contact, siteRequest.getUserList(), false);
    }

    @Override
    public List<ContactSiteVO> selectSiteList(SiteRequest siteRequest) {
        return contactSiteMapper.selectSiteList(siteRequest);
    }

    private void setContact(SiteRequest contactVo, ContactSite contactSite) {
        BeanUtils.copyProperties(contactVo, contactSite);
        String signId = UUID.tokenBuild(8);
        if (StringUtils.isBlank(contactSite.getSignId())) {
            // 站点活码唯一标识
            contactSite.setSignId(signId);
            contactSite.setRemark("配送员拉新");
        }
        if (StringUtils.isEmpty(contactSite.getState())) {
            // 设置默认state
            contactSite.setState(signId);
        }
        contactSite.setSkipVerify(contactVo.getSkipVerify() ? TypeConstants.SKIP_VERIFY_TRUE : TypeConstants.SKIP_VERIFY_FALSE);
        // 设置扫码标签
        if (CollectionUtils.isNotEmpty(contactVo.getTagList())) {
            contactSite.setWeEmpleCodeTags(JSON.toJSONString(contactVo.getTagList()));
        } else {
            contactSite.setWeEmpleCodeTags(null);
        }
        // 设置欢迎语附件
        if (CollectionUtils.isNotEmpty(contactVo.getAttachments())) {
            contactSite.setWelcomeAttachment(JSON.toJSONString(contactVo.getAttachments()));
        } else {
            contactSite.setWelcomeAttachment(null);
        }
    }

    private void saveTbWxContact(ContactSite contact, List<UserVO> userList, boolean update) {
        contact.setCorpId(CorpInfoProperties.getCorpId());
        if (update) {
            // 删除原先员工使用对象
            tbWxContactRelService.remove(new LambdaQueryWrapper<TbWxContactRel>().eq(TbWxContactRel::getContactId, contact.getId()));
            contactSiteMapper.updateById(contact);
        } else {
            contactSiteMapper.insert(contact);
        }
        // 保存活码使用员工对象
        List<TbWxContactRel> tbWxContactRelList = new ArrayList<>();
        for (UserVO userVo : userList) {
            TbWxContactRel tbWxContactRel = new TbWxContactRel();
            tbWxContactRel.setContactId(contact.getId());
            tbWxContactRel.setUserId(userVo.getUserId());
            // 代表站点活码
            tbWxContactRel.setType(2);
            tbWxContactRelList.add(tbWxContactRel);
        }
        // 员工Id入库
        tbWxContactRelService.saveBatch(tbWxContactRelList);
    }

    @Override
    public List<String> selectAllSiteNameList(SiteRequest siteRequest) {
        return contactSiteMapper.selectAllSiteNameList(siteRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfo(SiteRequest siteRequest) {
        // 活码id
        Long siteContactId = siteRequest.getSiteContactId();
        // 查询原来活码对象
        ContactSite contact = contactSiteMapper.selectOne(new LambdaQueryWrapper<ContactSite>()
                .eq(ContactSite::getId, siteContactId)
                .eq(ContactSite::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        Optional.ofNullable(contact).orElseThrow(() -> new CustomException(ErrCodeEnum.DATA_NOT_EXIST.getMessage(), ErrCodeEnum.DATA_NOT_EXIST.getCode()));
        // 站点id
        Long siteId = contact.getSiteId();
        LambdaQueryWrapper<ContactArea> wrapper = new LambdaQueryWrapper<ContactArea>()
                .eq(ContactArea::getId, siteId);
        ContactArea baseContactArea = getById(siteId);
        Optional.ofNullable(baseContactArea).orElseThrow(() -> new CustomException(ErrCodeEnum.CITY_CONTACT_SITE_NOT_EXIST_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_SITE_NOT_EXIST_ERROR.getCode()));
        // 校验站点名称重复
        wrapper.clear();
        wrapper = new LambdaQueryWrapper<ContactArea>()
                .eq(ContactArea::getAreaName, siteRequest.getSiteName())
                .ne(ContactArea::getId, siteId).select(ContactArea::getId);
        ContactArea sameNameContactArea = getOne(wrapper);
        if (ObjectUtil.isNotNull(sameNameContactArea)) {
            throw new CustomException(ErrCodeEnum.CITY_CONTACT_SITE_NAME_REPEAT_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_SITE_NAME_REPEAT_ERROR.getCode());
        }
        LambdaUpdateWrapper<ContactArea> updateWrapper = new LambdaUpdateWrapper<ContactArea>().eq(ContactArea::getId, siteId);
        // 修改站点、地区
        updateWrapper.set(!baseContactArea.getAreaName().equals(siteRequest.getSiteName()), ContactArea::getAreaName, siteRequest.getSiteName());
        updateWrapper.set(!baseContactArea.getParentId().equals(siteRequest.getAreaId()), ContactArea::getParentId, siteRequest.getAreaId());
        updateWrapper.set(ContactArea::getUpdateTime, siteRequest.getUpdateTime());
        updateWrapper.set(ContactArea::getUpdateBy, siteRequest.getUpdateBy());
        boolean update = update(updateWrapper);
        if (update) {
            // 设置活码信息
            setContact(siteRequest, contact);
            contact.setId(String.valueOf(siteContactId));
            // 更新活码成功 插入数据库
            saveTbWxContact(contact, siteRequest.getUserList(), true);
        }
    }

    @Override
    public void removeSite(SiteRequest siteRequest) {
        // 活码id
        Long siteContactId = siteRequest.getSiteContactId();
        // 查询原来活码对象
        ContactSite contact = contactSiteMapper.selectOne(new LambdaQueryWrapper<ContactSite>()
                .eq(ContactSite::getId, siteContactId)
                .eq(ContactSite::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (ObjectUtil.isNotNull(contact)) {
            Long siteId = contact.getSiteId();
            // 校验底下是否还存在门店
            ContactArea store = getOne(new LambdaQueryWrapper<ContactArea>().eq(ContactArea::getParentId, siteId).eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE).last("limit 1"));
            if (ObjectUtil.isNotNull(store)) {
                throw new CustomException(ErrCodeEnum.CITY_CONTACT_SITE_CAN_NOT_DELETE_EXIST_STORE_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_SITE_CAN_NOT_DELETE_EXIST_STORE_ERROR.getCode());
            }
            contactSiteMapper.deleteById(contact.getId());
            removeById(siteId);
        }
    }

    @Override
    public List<ContactSiteTreeVO> selectAllSiteList(SiteRequest siteRequest) {
        return baseMapper.selectAllSiteList(siteRequest);
    }

    @Override
    public List<ContactSiteTreeVO> selectSiteByTree(SiteRequest siteRequest) {
        return baseMapper.selectSiteByTree(siteRequest);
    }

    @Override
    public List<AreaTreeSelect> buildAreaTreeSelect(List<ContactArea> list) {
        List<ContactArea> areaTrees = buildDeptTree(list);
        return areaTrees.stream().map(AreaTreeSelect::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addStore(ContactAreaRequest contactAreaRequest) {
        Long siteId = contactAreaRequest.getSiteId();
        // 校验站点
        ContactArea contactArea = getOne(new LambdaQueryWrapper<ContactArea>()
                .eq(ContactArea::getId, siteId)
                .eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_SITE));
        Optional.ofNullable(contactArea).orElseThrow(() -> new CustomException(ErrCodeEnum.CITY_CONTACT_SITE_NOT_EXIST_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_SITE_NOT_EXIST_ERROR.getCode()));
        // 校验门店重名
        ContactArea storeArea = getOne(new LambdaQueryWrapper<ContactArea>()
                .eq(ContactArea::getAreaName, contactAreaRequest.getStoreName())
                .eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE));
        if (ObjectUtil.isNotNull(storeArea)) {
            throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_NAME_REPEAT_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_NAME_REPEAT_ERROR.getCode());
        }
        ContactArea contactAreaStore = new ContactArea();
        contactAreaStore.setParentId(siteId);
        contactAreaStore.setAreaName(contactAreaRequest.getStoreName());
        contactAreaStore.setAreaStatus(contactAreaRequest.getAreaStatus());
        contactAreaStore.setAreaLevel(TypeConstants.CONTACT_STORE);
        contactAreaStore.setRemark(contactAreaRequest.getRemark());
        contactAreaStore.setCorpId(contactAreaRequest.getCorpConfigId());
        contactAreaStore.setCreateBy(contactAreaRequest.getCreateBy());
        contactAreaStore.setSignId(UUID.tokenBuild(8));
        save(contactAreaStore);
    }

    @Override
    public List<ContactStoreListVO> storeList(ContactAreaRequest contactAreaRequest) {
        return baseMapper.selectStoreList(contactAreaRequest);
    }

    @Override
    public ContactArea updateStoreStatus(ContactAreaRequest contactAreaRequest) {
        LambdaQueryWrapper<ContactArea> wrapper = new LambdaQueryWrapper<>();
        // 是否编辑处理看是否有传门店名
        boolean edit = StringUtils.isNotEmpty(contactAreaRequest.getStoreName());
        if (edit) {
            // 校验门店重名
            wrapper.eq(ContactArea::getAreaName, contactAreaRequest.getStoreName()).ne(ContactArea::getId, contactAreaRequest.getStoreId()).eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE).select(ContactArea::getId);
            ContactArea storeArea = getOne(wrapper);
            if (ObjectUtil.isNotNull(storeArea)) {
                throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_NAME_REPEAT_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_NAME_REPEAT_ERROR.getCode());
            }
        }
        wrapper.clear();
        ContactArea contactArea = getOne(wrapper.eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE).eq(ContactArea::getId, contactAreaRequest.getStoreId()));
        Optional.ofNullable(contactArea).orElseThrow(() -> new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getCode()));
        update(new LambdaUpdateWrapper<ContactArea>()
                .eq(ContactArea::getId, contactAreaRequest.getStoreId())
                .set(edit, ContactArea::getRemark, contactAreaRequest.getRemark())
                .set(edit, ContactArea::getAreaName, contactAreaRequest.getStoreName())
                // 修改站点id
                .set(edit, ContactArea::getParentId, contactAreaRequest.getSiteId())
                .set(ContactArea::getUpdateTime, DateUtils.getNowDate())
                .set(ContactArea::getAreaStatus, contactAreaRequest.getAreaStatus())
                .set(ContactArea::getUpdateBy, contactAreaRequest.getUpdateBy())
        );
        if (StatusConstants.OPEN_FLAG.equals(contactArea.getAreaStatus()) && StatusConstants.CLOSE_FLAG.equals(contactAreaRequest.getAreaStatus())) {
            return contactArea;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeStore(ContactAreaRequest contactAreaRequest) {
        String[] ids = contactAreaRequest.getId().split(",");
        for (String id : ids) {
            ContactArea contactArea = getOne(new LambdaQueryWrapper<ContactArea>().eq(ContactArea::getId, id).eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE));
            if (ObjectUtil.isNull(contactArea)) {
                throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getCode());
            }
            if (ObjectUtil.isNotNull(contactArea) && StatusConstants.OPEN_FLAG.equals(contactArea.getAreaStatus())) {
                throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_CAN_NOT_DELETE_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_CAN_NOT_DELETE_ERROR.getCode());
            }
            // 存在配送员不让删除
            if (contactDeliveryUserMapper.selectCount(new LambdaQueryWrapper<ContactDeliveryUser>().eq(ContactDeliveryUser::getStoreId, id)) > 0) {
                throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_CAN_NOT_DELETE_EXIST_USER_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_CAN_NOT_DELETE_EXIST_USER_ERROR.getCode());
            }
            update(new LambdaUpdateWrapper<ContactArea>().eq(ContactArea::getId, id)
                    .set(ContactArea::getDelFlag, StatusConstants.DEL_FLAG_TRUE_INT)
                    .set(ContactArea::getUpdateTime, DateUtils.getNowDate())
                    .set(ContactArea::getUpdateBy, contactAreaRequest.getUpdateBy())
            );
        }
    }

    @Override
    public ContactStoreInfoVO getStoreById(ContactAreaRequest contactAreaRequest) {
        LambdaQueryWrapper<ContactArea> wrapper = new LambdaQueryWrapper<ContactArea>()
                .eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE)
                .eq(ContactArea::getId, contactAreaRequest.getStoreId());
        ContactArea contactArea = getOne(wrapper);
        if (ObjectUtil.isNull(contactArea)) {
            throw new CustomException(ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getMessage(), ErrCodeEnum.CITY_CONTACT_STORE_NOT_EXIST_ERROR.getCode());
        }
        ContactStoreInfoVO contactAreaVO = new ContactStoreInfoVO();
        contactAreaVO.setSiteId(contactArea.getParentId());
        contactAreaVO.setStoreName(contactArea.getAreaName());
        contactAreaVO.setAreaStatus(contactArea.getAreaStatus());
        contactAreaVO.setRemark(contactArea.getRemark());
        // 校验使用
        contactAreaVO.setSignId(contactArea.getSignId());
        contactAreaVO.setStoreId(contactArea.getId());
        return contactAreaVO;
    }

    @Override
    public List<ContactArea> selectNormalAreaStoreList(ContactAreaRequest contactAreaRequest) {
        List<ContactArea> contactAreas = baseMapper.selectNormalAreaStoreList(contactAreaRequest);
        // 加入全部站点
        ContactArea contactAllArea = new ContactArea();
        contactAllArea.setId((long) TypeConstants.CONTACT_COUNTRY);
        contactAllArea.setAreaName("全部站点");
        contactAllArea.setAreaLevel(TypeConstants.CONTACT_COUNTRY);
        contactAllArea.setAreaStatus(StatusConstants.OPEN_FLAG);
        // 处理站点权限
     /*   if (!contactAreaRequest.getAdmin()) {
            if (contactAreaRequest.getSitePermissions().size() == 0) {
                contactAreas = Lists.newArrayList();
                contactAllArea.setDeliveryCnt(0);
                contactAreas.add(contactAllArea);
                return contactAreas;
            }
            List<Long> collect = contactAreaRequest.getSitePermissions().stream().map(Long::parseLong).collect(Collectors.toList());
            // 排除掉未在可见范围内的
            List<Long> invisibleIds = contactAreas.stream().filter(c -> !collect.contains(c.getId()) && c.getAreaLevel().equals(TypeConstants.CONTACT_SITE)).map(ContactArea::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(invisibleIds)) {
                contactAreas = contactAreas.stream().filter(c -> !invisibleIds.contains(c.getId())).collect(Collectors.toList());
            }
        }*/
        List<ContactArea> siteList = contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_SITE)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(siteList)) {
            contactAreas = Lists.newArrayList();
            contactAllArea.setDeliveryCnt(0);
            contactAreas.add(contactAllArea);
            return contactAreas;
        }

        List<ContactArea> storeList = contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_STORE)).collect(Collectors.toList());
        Map<Long, List<ContactArea>> storeMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(storeList)) {
            storeMap = storeList.stream().collect(Collectors.groupingBy(ContactArea::getParentId));
        }


        // 此时存在站点，将没有站点的省市区排除
        Map<Long, ContactArea> allContactAreasMap = contactAreas.stream().collect(Collectors.toMap(ContactArea::getId, Function.identity(), (x, x1) -> x1));
        List<ContactArea> contactAreaUnless = Lists.newArrayList();
        for (ContactArea site : siteList) {
            Long siteParentId = site.getParentId();
            // 门店加进去
            List<ContactArea> store = storeMap.get(site.getId());
            if (CollectionUtil.isNotEmpty(store)) {
                contactAreaUnless.addAll(store);
            }
            // 站点加进去
            contactAreaUnless.add(site);
            if (siteParentId == -1L) {
                continue;
            }
            // 找到属于哪个省/市/区的保留下来
            ContactArea parentContactArea = allContactAreasMap.get(siteParentId);
            if (parentContactArea.getAreaLevel() == TypeConstants.CONTACT_DISTRICT) {
                // 区级
                contactAreaUnless.add(parentContactArea);
                // 找到自己的市
                ContactArea contactCityArea = allContactAreasMap.get(parentContactArea.getParentId());
                contactAreaUnless.add(contactCityArea);
                // 找到自己的区
                contactAreaUnless.add(allContactAreasMap.get(contactCityArea.getParentId()));
            } else if (parentContactArea.getAreaLevel() == TypeConstants.CONTACT_CITY) {
                // 市级
                contactAreaUnless.add(parentContactArea);
                // 找到自己的省
                contactAreaUnless.add(allContactAreasMap.get(parentContactArea.getParentId()));
            } else {
                // 省级
                contactAreaUnless.add(parentContactArea);
            }
        }
        Map<Long, ContactArea> distinctMap = contactAreaUnless.stream().collect(Collectors.toMap(ContactArea::getId, Function.identity(), (x, x1) -> x1));
        contactAreaUnless.clear();
        for (Long key : distinctMap.keySet()) {
            contactAreaUnless.add(distinctMap.get(key));
        }
        contactAreas = contactAreaUnless;
        Map<Long, List<ContactArea>> siteMap = siteList.stream().collect(Collectors.groupingBy(ContactArea::getParentId));
        // 计算配送员人数
        // 区级(最小颗粒，不需要加入同级站点数据)
        List<ContactArea> districtList = contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_DISTRICT)).collect(Collectors.toList());
        contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_DISTRICT))
                .forEach(contactArea ->
                        contactArea.setDeliveryCnt(Optional.ofNullable(siteMap.get(contactArea.getId())).orElse(Lists.newArrayList())
                                .stream().mapToInt(ContactArea::getDeliveryCnt).sum()));
        Map<Long, List<ContactArea>> districtMap = districtList.stream().collect(Collectors.groupingBy(ContactArea::getParentId));
        // 市级
        List<ContactArea> cityList = contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_CITY)).collect(Collectors.toList());
        contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_CITY))
                .forEach(contactArea ->
                        // 加上同级站点
                        contactArea.setDeliveryCnt(Optional.ofNullable(districtMap.get(contactArea.getId())).orElse(Lists.newArrayList()).stream().mapToInt(ContactArea::getDeliveryCnt).sum() +
                                Optional.ofNullable(siteMap.get(contactArea.getId())).orElse(Lists.newArrayList()).stream().mapToInt(ContactArea::getDeliveryCnt).sum())
                );
        Map<Long, List<ContactArea>> cityMap = cityList.stream().collect(Collectors.groupingBy(ContactArea::getParentId));
        // 省级
        contactAreas.stream().filter(c -> c.getAreaLevel().equals(TypeConstants.CONTACT_PROVINCE))
                .forEach(contactArea -> {
                            int sum = 0;
                            if (ObjectUtil.isNotNull(cityMap.get(contactArea.getId()))) {
                                sum = cityMap.get(contactArea.getId()).stream().mapToInt(ContactArea::getDeliveryCnt).sum();
                            }
                            if (ObjectUtil.isNotNull(siteMap.get(contactArea.getId()))) {
                                sum += siteMap.get(contactArea.getId()).stream().mapToInt(ContactArea::getDeliveryCnt).sum();
                            }
                            contactArea.setDeliveryCnt(sum);
                        }
                );
        // 统计门店配送员数
        contactAllArea.setDeliveryCnt(siteList.stream().mapToInt(ContactArea::getDeliveryCnt).sum());
        contactAreas.add(contactAllArea);
        // 默认站点
        if (ObjectUtil.isNotNull(siteMap.get(-1L))) {
            ContactArea defaultSite = new ContactArea();
            defaultSite.setId(-1L);
            defaultSite.setParentId((long) TypeConstants.CONTACT_COUNTRY);
            defaultSite.setAreaLevel(TypeConstants.CONTACT_PROVINCE);
            defaultSite.setAreaName("默认");
            defaultSite.setDeliveryCnt(siteMap.get(-1L).stream().mapToInt(ContactArea::getDeliveryCnt).sum());
            contactAreas.add(defaultSite);
            // 默认分组在前
            contactAreas = contactAreas.stream().sorted(Comparator.comparing(ContactArea::getId)).collect(Collectors.toList());
        }
        return contactAreas;
    }

    @Override
    public List<ContactStoreVO> selectStoreByTree(ContactAreaRequest contactAreaRequest) {
        return baseMapper.selectStoreByTree(contactAreaRequest);
    }

    @Override
    public String storeImport(List<StoreImportRequest> importExcel, Long loginUserId, boolean isAdmin, Set<String> cityPermissions) {
        String result = "总共导入%s个门店，成功添加%s个，失败%s个";
        int successImportNum = 0;
        int failImportNum = 0;
        Date nowDate = DateUtils.getNowDate();
        for (StoreImportRequest storeImportRequest : importExcel) {
            // 查询站点id
            ContactArea site = getOne(new LambdaQueryWrapper<ContactArea>()
                    .eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_SITE)
                    .eq(ContactArea::getAreaName, storeImportRequest.getSite())
                    .in(!isAdmin, ContactArea::getId, cityPermissions)
                    .select(ContactArea::getId).last("limit 1"));
            if (ObjectUtil.isNull(site)) {
                failImportNum++;
                continue;
            }
            String store = storeImportRequest.getStore();
            if (StringUtils.isBlank(store) || store.length() > 10) {
                failImportNum++;
                continue;
            }
            // 同一城市下不能存在同名的门店
            // 校验门店重名
            ContactArea storeArea = getOne(new LambdaQueryWrapper<ContactArea>()
                    .eq(ContactArea::getAreaName, store)
                    .eq(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE)
                    .select(ContactArea::getId));
            if (ObjectUtil.isNotNull(storeArea)) {
                failImportNum++;
                continue;
            }
            // 添加门店到该城市
            ContactArea contactAreaStore = new ContactArea();
            contactAreaStore.setParentId(site.getId());
            contactAreaStore.setAreaName(storeImportRequest.getStore());
            contactAreaStore.setAreaStatus(StatusConstants.OPEN_FLAG);
            contactAreaStore.setRemark("批量导入生成");
            contactAreaStore.setCreateTime(nowDate);
            contactAreaStore.setAreaLevel(TypeConstants.CONTACT_STORE);
            contactAreaStore.setCorpId(1L);
            contactAreaStore.setCreateBy(loginUserId);
            contactAreaStore.setSignId(UUID.tokenBuild(8));
            save(contactAreaStore);
            successImportNum++;
        }
        return String.format(result, importExcel.size(), successImportNum, failImportNum);
    }

    @Override
    public List<ContactArea> selectAllAreaList() {
        List<ContactArea> contactAreas = baseMapper.selectList(new LambdaQueryWrapper<ContactArea>()
                .ne(ContactArea::getAreaLevel, TypeConstants.CONTACT_STORE)
                .select(ContactArea::getAreaName, ContactArea::getParentId, ContactArea::getId, ContactArea::getAreaLevel)
                .orderByAsc(ContactArea::getOrderNum));
        ContactArea defaultSite = new ContactArea();
        defaultSite.setId(-1L);
        defaultSite.setAreaLevel(TypeConstants.CONTACT_PROVINCE);
        defaultSite.setParentId((long) TypeConstants.CONTACT_COUNTRY);
        defaultSite.setAreaName("默认");
        contactAreas.add(defaultSite);
        // 默认分组在前
        contactAreas = contactAreas.stream().sorted(Comparator.comparing(ContactArea::getId)).collect(Collectors.toList());
        return contactAreas;
    }

    private List<ContactArea> buildDeptTree(List<ContactArea> list) {
        List<ContactArea> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (ContactArea contactArea : list) {
            tempList.add(contactArea.getId());
        }
        for (ContactArea contactArea : list) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(contactArea.getParentId())) {
                recursionFn(list, contactArea);
                returnList.add(contactArea);
            }
        }
        if (returnList.isEmpty()) {
            returnList = list;
        }
        return returnList;
    }

    private void recursionFn(List<ContactArea> list, ContactArea contactArea) {
        // 得到子节点列表
        List<ContactArea> childList = getChildList(list, contactArea);
        contactArea.setChildren(childList);
        for (ContactArea tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    private boolean hasChild(List<ContactArea> list, ContactArea tChild) {
        return getChildList(list, tChild).size() > 0;
    }

    private List<ContactArea> getChildList(List<ContactArea> list, ContactArea contactArea) {
        List<ContactArea> infos = new ArrayList<>();
        for (ContactArea n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && contactArea.getId().equals(n.getParentId())) {
                infos.add(n);
            }
        }
        return infos;
    }
}
