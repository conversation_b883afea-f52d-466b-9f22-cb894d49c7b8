package com.cenker.scrm.controller.session;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.session.QryWordAlarmDto;
import com.cenker.scrm.pojo.entity.session.CkSessionSensWordAlarmRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.vo.session.CkSessionSensWordAlarmVO;
import com.cenker.scrm.pojo.vo.session.ExportCkSessionSensWordAlarmVO;
import com.cenker.scrm.pojo.vo.session.RuleUserVO;
import com.cenker.scrm.service.corp.ITbWxCorpConfigService;
import com.cenker.scrm.service.session.ICkSessionSensWordAlarmRecordService;
import com.cenker.scrm.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 热词信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/session/wordalarm")
public class CkSessionSensWordAlarmController extends BaseController {
    private ICkSessionSensWordAlarmRecordService  iCkSessionSensWordAlarmRecordService;
    private ITbWxCorpConfigService tbWxCorpConfigService;


    /**
     * 查询热词信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QryWordAlarmDto qryWordAlarmDto)
    {
        String companyName = getCompanyName();

        startPage();
        PageHelper.orderBy(" o.create_time desc ");
        List<CkSessionSensWordAlarmVO> listVo = new ArrayList<>();
        List<CkSessionSensWordAlarmRecord> list = iCkSessionSensWordAlarmRecordService.selectCkSessionSensWordAlarmRecordList(qryWordAlarmDto);
        for (CkSessionSensWordAlarmRecord wordInfo:list) {
            CkSessionSensWordAlarmVO wordInfoVO = buildCkSessionSensWordAlarmVO(wordInfo, companyName);
            listVo.add(wordInfoVO);
        }
        TableDataInfo backTab = getDataTable(listVo);
        backTab.setTotal(new PageInfo(list).getTotal());
        return backTab;
    }



    @GetMapping("/export")
    public List<ExportCkSessionSensWordAlarmVO> export(QryWordAlarmDto qryWordAlarmDto){
        String companyName = getCompanyName();

        PageHelper.orderBy(" o.create_time desc ");
        List<ExportCkSessionSensWordAlarmVO> listVo = new ArrayList<>();
        List<CkSessionSensWordAlarmRecord> list = iCkSessionSensWordAlarmRecordService.selectCkSessionSensWordAlarmRecordList(qryWordAlarmDto);
        for ( CkSessionSensWordAlarmRecord alarmVO:list) {
            ExportCkSessionSensWordAlarmVO wordAlarmVO = buildExportCkSessionSensWordAlarmVO(alarmVO, companyName);

            listVo.add(wordAlarmVO);
        }
        return listVo;
    }

    /**
     * 查询企业名称
     * @return
     */
    private String getCompanyName(){
        TbWxCorpConfig corpConfig = tbWxCorpConfigService.lambdaQuery()
                .eq(TbWxCorpConfig::getStatus, CommonConstants.STATUS_NORMAL)
                .eq(TbWxCorpConfig::getDelFlag, CommonConstants.STATUS_NORMAL)
                .last("LIMIT 1").one();
        return Objects.nonNull(corpConfig)? corpConfig.getCompanyName() : "";
    }

    private CkSessionSensWordAlarmVO buildCkSessionSensWordAlarmVO(CkSessionSensWordAlarmRecord record, String companyName) {
        CkSessionSensWordAlarmVO alarmVO = new CkSessionSensWordAlarmVO();
        alarmVO.setId(record.getId());
        alarmVO.setSensitiveWord(record.getSensitiveWord());
        alarmVO.setTriggerTime(DateUtil.formatDateTime(record.getTriggerTime()));
        alarmVO.setRuleName(record.getRuleName());
        alarmVO.setContentValue(record.getContentValue());

        RuleUserVO sendUserObj = new RuleUserVO();
        sendUserObj.setUserId(record.getSendUserId());
        sendUserObj.setUserName(record.getSendUserName());
        sendUserObj.setType(record.getSendUserType());
        sendUserObj.setCorpName(record.getSendCorpName());

        // 本企业员工，企业名称统一显示
        if (CommonConstants.USER_TYPE_2.equals(record.getSendUserType())) {
            sendUserObj.setCorpName(companyName);
        }

        alarmVO.setSendUserObj(sendUserObj);

        RuleUserVO acceptUserObj = new RuleUserVO();
        acceptUserObj.setUserId(record.getAcceptUserId());
        acceptUserObj.setType(record.getAcceptUserType());
        acceptUserObj.setUserName(record.getAcceptUserName());
        acceptUserObj.setCorpName(record.getAcceptCorpName());
        // 群聊名称为空时，需显示默认名称
        if(CommonConstants.USER_TYPE_3.equals(record.getAcceptUserType()) && StrUtil.isBlank(record.getAcceptUserName())){
            acceptUserObj.setUserName("群聊（未命名）");
        }

        // 本企业员工，企业名称统一显示
        if (CommonConstants.USER_TYPE_2.equals(record.getAcceptUserType())) {
            acceptUserObj.setCorpName(companyName);
        }
        alarmVO.setAcceptUserObj(acceptUserObj);
        return alarmVO;
    }

    private ExportCkSessionSensWordAlarmVO buildExportCkSessionSensWordAlarmVO(CkSessionSensWordAlarmRecord record, String companyName) {
        ExportCkSessionSensWordAlarmVO alarmVO = new ExportCkSessionSensWordAlarmVO();
        alarmVO.setSensitiveWord(record.getSensitiveWord());
        alarmVO.setRuleName(record.getRuleName());
        alarmVO.setContentValue(record.getContentValue());
        alarmVO.setTriggerTime(DateUtil.formatDateTime(record.getTriggerTime()));

        String sendCorpName = (CommonConstants.USER_TYPE_2.equals(record.getSendUserType())) ? companyName : record.getSendCorpName();
        alarmVO.setSendUserName(getUserName(record.getSendUserName(), record.getSendUserType(), sendCorpName));
        String acceptCorpName = (CommonConstants.USER_TYPE_2.equals(record.getAcceptUserType())) ? companyName : record.getAcceptCorpName();
        alarmVO.setAcceptUserName(getUserName(record.getAcceptUserName(), record.getAcceptUserType(), acceptCorpName));

        return alarmVO;
    }

    private String getUserName(String userName, String userType, String corpName) {
        if (CommonConstants.USER_TYPE_1.equals(userType) && StrUtil.isBlank(corpName)) {
           return userName + "@微信";
        }

        if (CommonConstants.USER_TYPE_3.equals(userType)) {
            String groupName = StrUtil.isBlank(userName) ? "群聊（未命名）" : userName;
            return groupName + "@群聊";
        }

        if (StrUtil.isNotBlank(corpName)) {
            return userName + "@" + corpName;
        }

        return userName;
    }
}
