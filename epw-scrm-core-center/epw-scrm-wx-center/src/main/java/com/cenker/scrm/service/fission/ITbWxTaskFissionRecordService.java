package com.cenker.scrm.service.fission;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxFission;
import com.cenker.scrm.pojo.entity.wechat.TbWxTaskFissionRecord;


import java.util.List;

public interface ITbWxTaskFissionRecordService extends IService<TbWxTaskFissionRecord> {
    List<TbWxTaskFissionRecord> statisticRecords(String taskFissionId, String startTime, String endTime);

    TbWxTaskFissionRecord selectWeTaskFissionRecordByIdAndCustomerId(String fissionId, String unionId);

    /**
     * 开始新的裂变记录
     * @param tbWxFission 裂变任务
     * @param mpWxUser 登录用户信息
     * @return 生成的裂变记录
     */
    TbWxTaskFissionRecord addTaskFissionRecord(TbWxFission tbWxFission, MpWxUser mpWxUser);

    /**
     * 客户完成数
     */
    int countCompleteCnt(String taskFissionId);
}
