package com.cenker.scrm.mapper.custlink;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustlinkCustDetail;
import com.cenker.scrm.pojo.vo.custlink.CustlinkVO;
import com.cenker.scrm.pojo.vo.custlink.QryCustlinkCustDetailBakVO;
import com.cenker.scrm.pojo.vo.custlink.StatDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 员工活码Mapper接口
 *
 * <AUTHOR>
 * @date 2021-01-25
 */
public interface TbWxCustlinkCustDetailMapper extends BaseMapper<TbWxCustlinkCustDetail> {

    public List<QryCustlinkCustDetailBakVO> qryCustList(CustlinkVO custlinkVO);
    public List<TbWxCustlinkCustDetail>  selectTbWxCustlinkCustDetailVo(TbWxCustlinkCustDetail tbWxCustlinkCustDetail);

    public int insertTbWxCustlinkCustDetail(TbWxCustlinkCustDetail tbWxCustlinkCustDetail);
    public List<TbWxCustlinkCustDetail> qryAddChatStatData(CustlinkVO custlinkVO);
    public List<TbWxCustlinkCustDetail> qryLossChatStatData(CustlinkVO custlinkVO);

    public int updateTbWxCustlinkCustDetail(TbWxCustlinkCustDetail tbWxCustlinkCustDetail);

    /**
     * 统计获客链接累计添加客户数（不去重）
     * @param linkId
     * @return
     */
    Long countUserAddTimesByLinkId(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 统计获客链接累计流失客户数（不去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    Long countUserDelTimesByLinkId(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 统计获客链接累计聊天客户数（去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    Long countChatUserByLinkId(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按小时统计获客链接累计添加客户数（不去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statUserAddTimesGroupByHour(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按天统计获客链接累计添加客户数（不去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statUserAddTimesGroupByDay(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按小时统计获客链接累计流失客户数（不去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statUserDelTimesGroupByHour(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按天统计获客链接累计流失客户数（不去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statUserDelTimesGroupByDay(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按小时统计获客链接累计聊天客户数（去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statChatUserGroupByHour(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 按天统计获客链接累计聊天客户数（去重）
     * @param linkId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<StatDataVO> statChatUserGroupByDay(@Param("linkId") Long linkId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

}
