package com.cenker.scrm.controller.external;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.enums.*;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.base.Tree;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.material.MaterialListDto;
import com.cenker.scrm.pojo.dto.material.WeMediaDTO;
import com.cenker.scrm.pojo.dto.message.TemporaryMaterialDto;
import com.cenker.scrm.pojo.entity.system.SysDictData;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategory;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterial;
import com.cenker.scrm.pojo.entity.wechat.TbWxMaterialPoster;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.service.ISysDictTypeService;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.category.ITbWxCategoryService;
import com.cenker.scrm.service.material.ITbWxMaterialPosterService;
import com.cenker.scrm.service.material.ITbWxMaterialService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 素材信息Controller
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Slf4j
@RestController
@RequestMapping("/tp/material")
@RequiredArgsConstructor
public class TbWxMaterialController extends BaseController {
    private final ITbWxMaterialService tbWxMaterialService;
    private final TokenParseUtil tokenService;
    private final ITbWxCategoryService categoryService;
    private final ITbWxMaterialPosterService tbWxMaterialPosterService;
    private final ISysDictTypeService dictTypeService;
    private final IApprovalService approvalService;

    /**
     * 查询素材信息列表
     */
    @RequestMapping("/list")
    public TableDataInfo<TbWxMaterial> list(@RequestBody MaterialListDto param) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, param);

        startPage();
        List<TbWxMaterial> list = tbWxMaterialService.selectTbWxMaterialList(param);
        return getDataTable(list);
    }

    /**
     * 查询一级素材信息列表（多级需遍历分组）
     */
    @RequestMapping("/topList")
    public TableDataInfo topList(@RequestBody TbWxMaterial tbWxMaterial) {
        if (StringUtils.isNoneBlank(tbWxMaterial.getCategoryId())) {
            // 需要查询的分类id
            List<String> queryCategoryIds = Lists.newArrayList();
            queryCategoryIds.add(tbWxMaterial.getCategoryId());

            // 查询出来的子类id
            List<String> categoryIds = Lists.newArrayList();
            categoryIds.add(tbWxMaterial.getCategoryId());

            List<String> sonCategoryIds = Lists.newArrayList();
            do {
                queryCategoryIds.addAll(sonCategoryIds);
                // 遍历多级
                sonCategoryIds = Optional.ofNullable(categoryService.list(new LambdaQueryWrapper<TbWxCategory>()
                        .in(TbWxCategory::getParentId, categoryIds)
                        .eq(TbWxCategory::getDelFlag, StatusConstants.DEL_FLAG_FALSE)
                        .select(TbWxCategory::getId)
                        .eq(TbWxCategory::getCorpId, tbWxMaterial.getCorpId()))).orElse(Lists.newArrayList())
                        .stream().map(TbWxCategory::getId).collect(Collectors.toList());
                categoryIds.clear();
                categoryIds.addAll(sonCategoryIds);
            } while (CollectionUtil.isNotEmpty(sonCategoryIds));
            tbWxMaterial.setQueryCategoryIds(queryCategoryIds);
        }
        startPage();
        List<TbWxMaterial> list = tbWxMaterialService.selectTbWxMaterialTopList(tbWxMaterial);
        return getDataTable(list);
    }

    /**
     * 获取素材信息详细信息
     */
    @RequestMapping("/{id}")
    public Result<TbWxMaterial> getInfo(@PathVariable("id") Long id) {
        log.info("【查询营销素材】id:{}", id);
        TbWxMaterial tbWxMaterial = tbWxMaterialService.selectTbWxMaterialById(id);
        if (ObjectUtil.isNull(tbWxMaterial)) {
            log.warn("【查询营销素材】数据不存在:{}", id);
            throw new CustomException("数据已被删除或不存在");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String userId = null;
        if (loginUser != null) {
            userId = loginUser.getUser().getUserId();
        }
        log.info("【获取智能物料详情】当前用户id:{}", userId);
        // 处理 canApproval 字段
        // 是审批人，且不是创建人
        ApprovalTypeEnum typeEnum = ApprovalTypeEnum.MATERIAL;

        Result<Boolean> approvalUser = approvalService.isApprovalUser(typeEnum.getType());
        if (approvalUser.isSuccess() && approvalUser.getData()) {
            boolean isApproval = approvalUser.getData();
            boolean canApproval = tbWxMaterial.isEnableApproval() && isApproval
                    && !tbWxMaterial.getCreateBy().equals(userId)
                    && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(tbWxMaterial.getStatus());
            tbWxMaterial.setCanApproval(canApproval);
        }
        return Result.success("查询成功", tbWxMaterial);
    }

    /**
     * 新增素材信息
     */
    @RequestMapping("/add")
    public AjaxResult add(@RequestBody TbWxMaterial tbWxMaterial, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

        tbWxMaterial.setCorpId(loginUser.getTenantId());
        tbWxMaterial.setCreateBy(loginUser.getUserId());
        tbWxMaterial.setDeptId(loginUser.getDeptId());
        log.info("【新建营销素材】添加事件接收json:{}", JSONUtil.toJsonStr(tbWxMaterial));
        if (Objects.equals(0, tbWxMaterial.getMediaType())) {
            // 图片类型 需要支持审核
            // 新增时，设置是否需要审批
            TbWxCorpConfig config = approvalService.getTbWxCorpConfig(tbWxMaterial.getCorpId());
            boolean enableApproval = getEnableApproval(tbWxMaterial.getDeptId()+"", config);
            tbWxMaterial.setEnableApproval(enableApproval);
            log.info("【新建营销素材】设置是否需要审批：{}", enableApproval);
        } else {
            tbWxMaterial.setEnableApproval(false);
        }
        if (tbWxMaterial.isEnableApproval()) {
            // 待审核
            tbWxMaterial.setStatus(ApprovalStatusEnum.PENDING_APPROVAL.getStatus());
        } else {
            // 使用中
            tbWxMaterial.setStatus(ApprovalStatusEnum.EXECUTING.getStatus());
        }

        int insertResult = tbWxMaterialService.insertTbWxMaterial(tbWxMaterial);
        log.info("【新建营销素材】新增素材结果：{},id：{}", insertResult, tbWxMaterial.getId());
        ApprovalTypeEnum typeEnum = ApprovalTypeEnum.MATERIAL;
        approvalService.sendPendingApprovalMsg(tbWxMaterial.getMaterialName(), tbWxMaterial.getId()+"",
                typeEnum, tbWxMaterial.isEnableApproval(), tbWxMaterial.getStatus(), tbWxMaterial.getCreateBy(), false, tbWxMaterialService.getBusinessJson(tbWxMaterial));
        log.info("【新建营销素材】发送消息完成");
        return toAjax(insertResult);
    }

    /**
     * 修改素材信息
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody TbWxMaterial tbWxMaterial,HttpServletRequest request) {
        TbWxMaterial exists = tbWxMaterialService.getById(Long.valueOf(tbWxMaterial.getId()));
        if (ObjectUtil.isNull(exists)) {
            log.warn("【编辑营销素材】数据不存在:{}", tbWxMaterial.getId());
            throw new CustomException("数据已被删除或不存在");
        }
        tbWxMaterial.setEnableApproval(exists.isEnableApproval());
        if (tbWxMaterial.isEnableApproval() && ApprovalStatusEnum.PENDING_APPROVAL.getStatus().equals(exists.getStatus())) {
            // 待审核状态 不允许编辑
            log.warn("【编辑营销素材】“待审核”状态不可编辑");
            throw new CustomException("“待审核”状态不可编辑");
        }
        if (tbWxMaterial.isEnableApproval()) {
            tbWxMaterial.setStatus(ApprovalStatusEnum.PENDING_APPROVAL.getStatus());
        } else {
            tbWxMaterial.setStatus(ApprovalStatusEnum.EXECUTING.getStatus());
        }
        tbWxMaterial.setCorpId(tokenService.getLoginUser(request).getUser().getCorpId());
        tbWxMaterial.setUpdateBy(tokenService.getLoginUser(request).getUser().getUserId());
        int updateResult = tbWxMaterialService.updateTbWxMaterial(tbWxMaterial);
        log.info("【编辑营销素材】修改素材结果：{},id：{}", updateResult, tbWxMaterial.getId());
        ApprovalTypeEnum typeEnum = ApprovalTypeEnum.MATERIAL;
        approvalService.sendPendingApprovalMsg(tbWxMaterial.getMaterialName(), tbWxMaterial.getId()+"",
                typeEnum, tbWxMaterial.isEnableApproval(), tbWxMaterial.getStatus(), tbWxMaterial.getCreateBy(), false, tbWxMaterialService.getBusinessJson(tbWxMaterial));
        log.info("【编辑营销素材】发送消息完成");
        return toAjax(updateResult);
    }

    /**
     * 删除素材信息
     */
    @RequestMapping("/remove")
    public AjaxResult remove(@RequestBody TbWxMaterial tbWxMaterialRequest) {
        // 走逻辑删除
        List<String> idArray = StrUtil.split(tbWxMaterialRequest.getId(), StrUtil.COMMA);

        List<String> materialNames = tbWxMaterialService.lambdaQuery().in(TbWxMaterial::getId, tbWxMaterialRequest.getId())
                .list().stream().map(TbWxMaterial::getMaterialName).collect(Collectors.toList());
        LogUtil.logOperDesc(materialNames);

        tbWxMaterialService.lambdaUpdate()
                .set(TbWxMaterial::getUpdateTime, DateUtils.getNowDate())
                .set(TbWxMaterial::getDelFlag, 1)
                .in(TbWxMaterial::getId, idArray).update();
        return AjaxResult.success();
    }

    /**
     * 上传微信素材
     */
    @RequestMapping("/temporaryMaterialMediaId")
    public AjaxResult temporaryMaterialMediaIdForWeb(@RequestBody TemporaryMaterialDto temporaryMaterialDto){
        WeMediaDTO weMediaDto = tbWxMaterialService.uploadTemporaryMaterial(temporaryMaterialDto.getUrl(),
                temporaryMaterialDto.getType()
                ,temporaryMaterialDto.getName(),temporaryMaterialDto.getCorpId());
        return AjaxResult.success(weMediaDto);
    }

    @RequestMapping("/updateNamingRule")
    public RemoteResult updateNamingRule(@RequestBody Map<String, String> params) {
        String namingRule = params.get("namingRule");
        if (StrUtil.isNotBlank(namingRule)) {
            tbWxMaterialService.lambdaUpdate()
                    .set(TbWxMaterial::getNamingRule, namingRule)
                    .set(TbWxMaterial::getUpdateTime, new Date())
                    .eq(TbWxMaterial::getDelFlag, CommonConstants.NOT_DELETED)
                    .update();
        }

        return RemoteResult.success();
    }

    @GetMapping("/treeSelect")
    public AjaxResult treeSelect(HttpServletRequest request, @RequestParam("name")String name) {
        // 获取所有分类
        List<TbWxCategory> categoryInfoList = categoryService.lambdaQuery()
                .eq(TbWxCategory::getMediaType, MediaType.POSTER.getType())
                .list();
        Map<String, TbWxCategory> categoryMap = categoryInfoList.stream().collect(Collectors.toMap(c -> String.valueOf(c.getId()), Function.identity()));

        // 查询所有海报
        TbWxMaterial tbWxMaterial = new TbWxMaterial();
        tbWxMaterial.setMediaType(Integer.valueOf(MediaType.POSTER.getType()));
        tbWxMaterial.setMaterialName(name);
        tbWxMaterial.setCorpId(tokenService.getLoginUser(request).getUser().getCorpId());
        MaterialListDto materialListDto = new MaterialListDto();
        BeanUtils.copyProperties(tbWxMaterial, materialListDto);

        List<TbWxMaterial> lstPoster = tbWxMaterialService.selectTbWxMaterialList(materialListDto);


        List<Tree> leefNodeList = lstPoster.stream().map(poster -> {
            Tree node = Tree.builder()
                    .id(String.valueOf(poster.getId()))
                    .value(String.valueOf(poster.getId()))
                    .label(poster.getMaterialName())
                    .parentId(String.valueOf(poster.getCategoryId()))
                    .build();
            return node;
        }).collect(Collectors.toList());

        List<Tree> result = new ArrayList<>();
        Deque<Tree> deque = new ArrayDeque<>();
        deque.addAll(leefNodeList);

        while (!deque.isEmpty()) {
            Map<String, Tree> parentMap = new HashMap<>();
            while(!deque.isEmpty()) {
                Tree node = deque.poll();
                String parentId = node.getParentId();

                // 若没有父节点，则为顶级节点
                if (Objects.isNull(parentId)) {
                    result.add(node);
                }

                // 若有父节点，则将父节点的children中添加
                if (parentMap.containsKey(parentId)) {
                    parentMap.get(parentId).addChild(node);
                } else if (categoryMap.containsKey(parentId)) {
                    TbWxCategory category = categoryMap.get(node.getParentId());
                    Tree parentNode = Tree.builder()
                            .id(String.valueOf(category.getId()))
                            .value(String.valueOf(category.getId()))
                            .label(category.getName())
                            .parentId(String.valueOf(category.getParentId()))
                            .build();

                    parentNode.addChild(node);
                    parentMap.put(parentId, parentNode);
                } else {
                    result.add(node);
                }
            }
            deque.addAll(parentMap.values());
        }
        return AjaxResult.success(result);
    }

    /**
     * 根据链接类型获取海报信息
     * @param type
     * @return
     */
    @GetMapping("/poster/{type}")
    public AjaxResult poster(@PathVariable("type") String type) {
        TbWxMaterialPoster tbWxMaterialPoster = tbWxMaterialPosterService.lambdaQuery().eq(TbWxMaterialPoster::getLinkType, type).one();
        // 海报设置不为空，并且海报非删除状态
        if (null != tbWxMaterialPoster) {
            TbWxMaterial tbWxMaterial = tbWxMaterialService.getById(tbWxMaterialPoster.getMaterialId());
            if (null != tbWxMaterial && CommonConstants.NOT_DELETED == tbWxMaterial.getDelFlag()) {
                return AjaxResult.success(tbWxMaterialPoster);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 海报设置
     */
    @PutMapping("/posterSet")
    @Log(module = ModuleEnum.NORMAL_MATERIAL_POSTER, businessType = BusinessType.UPDATE)
    public AjaxResult posterSet(@RequestBody TbWxMaterialPoster tbWxMaterialPoster, HttpServletRequest request) {

        TbWxMaterialPoster posterEdit = tbWxMaterialPosterService.lambdaQuery().eq(TbWxMaterialPoster::getLinkType, tbWxMaterialPoster.getLinkType()).one();
        if (null != posterEdit) {
            posterEdit.setMaterialId(tbWxMaterialPoster.getMaterialId());
            posterEdit.setEnablePoster(tbWxMaterialPoster.getEnablePoster());
            posterEdit.setUpdateBy(tokenService.getLoginUser(request).getUser().getUserId());
            posterEdit.setUpdateTime(DateUtil.date());
            tbWxMaterialPosterService.updateById(posterEdit);
        } else {
            tbWxMaterialPoster.setCreateBy(tokenService.getLoginUser(request).getUser().getUserId());
            tbWxMaterialPoster.setCreateTime(DateUtil.date());
            tbWxMaterialPosterService.save(tbWxMaterialPoster);
        }
        TbWxMaterial tbWxMaterial = tbWxMaterialService.getById(tbWxMaterialPoster.getMaterialId());
        String status = tbWxMaterialPoster.getEnablePoster() ? "启用" : "禁用";
        String type = "";
        List<SysDictData> materData = dictTypeService.selectDictDataByType("material_poster");
        for (SysDictData data : materData) {
            if (data.getDictValue().equals(tbWxMaterialPoster.getLinkType())) {
                type = data.getDictLabel();
                break;
            }
        }
        LogUtil.logOperDesc(StrUtil.format("海报链接类型【{}】设置海报名称【{}】，状态为【{}】", type, tbWxMaterial.getMaterialName(), status));
        return AjaxResult.success();
    }

    /**
     * 根据链接类型获取海报信息
     * @param type
     * @return
     */
    @GetMapping("/posterByH5/{type}")
    public AjaxResult posterByH5(@PathVariable("type") String type) {
        TbWxMaterialPoster tbWxMaterialPoster = tbWxMaterialPosterService.lambdaQuery()
                .eq(TbWxMaterialPoster::getLinkType, type)
                .eq(TbWxMaterialPoster::getEnablePoster, true)
                .one();
        if (null != tbWxMaterialPoster) {
            TbWxMaterial tbWxMaterial = tbWxMaterialService.getById(tbWxMaterialPoster.getMaterialId());
            if (null != tbWxMaterial && CommonConstants.NOT_DELETED == tbWxMaterial.getDelFlag()) {
                return AjaxResult.success("操作成功", tbWxMaterial.getMaterialUrl());
            }
        }
        return AjaxResult.success();
    }


    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO) {
        log.info("【审核智能物料】审核智能物料开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return tbWxMaterialService.approve(approvalVO, loginUser);
    }
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO) {
        log.info("【撤回智能物料】撤回智能物料开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return tbWxMaterialService.revoked(approvalVO, loginUser);
    }
}
