<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.corp.TbWxCorpConfigMapper">

    <resultMap type="TbWxCorpConfig" id="TbWxCorpConfigResult">
        <result property="id" column="id" />
        <result property="corpId" column="corp_id" />
        <result property="companyName" column="company_name" />
        <result property="providerSecret" column="provider_secret" />
        <result property="contactSecret" column="contact_secret" />
        <result property="chatSecret" column="chat_secret" />
        <result property="pubKey" column="pub_key" />
        <result property="priKey" column="pri_key" />
        <result property="tencentCloudSecretId" column="tencent_cloud_secret_id" />
        <result property="tencentCloudSecretSecretKey" column="tencent_cloud_secret_secret_key" />
        <result property="toolAppId" column="tool_app_id" />
        <result property="toolAppSecret" column="tool_app_secret" />
        <result property="appletId" column="applet_id" />
        <result property="appletSecret" column="applet_secret" />
        <result property="qrAppId" column="qr_app_id" />
        <result property="qrLoginRedirectUri" column="qr_login_redirect_uri" />
        <result property="addBookCallBackUrl" column="add_book_call_back_url" />
        <result property="addBookCallBackToken" column="add_book_call_back_token" />
        <result property="addBookCallBackSecret" column="add_book_call_back_secret" />
        <result property="custMessageCallBackUrl" column="cust_message_call_back_url" />
        <result property="custMessageCallBackToken" column="cust_message_call_back_token" />
        <result property="custMessageCallBackSecret" column="cust_message_call_back_secret" />
        <result property="status" column="status" />
        <result property="delFlag" column="del_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="customerChurnNoticeSwitch" column="customer_churn_notice_switch" />
        <result property="editSuffix" column="edit_suffix" />
        <result property="accountSuffix" column="account_suffix" />
        <result property="contentAuditDepart" column="content_audit_depart" />
    </resultMap>

    <sql id="selectTbWxCorpConfigVo">
        select id, corp_id, company_name, provider_secret, contact_secret,
               chat_secret, pub_key, pri_key, tencent_cloud_secret_id,
               tencent_cloud_secret_secret_key, tool_app_id, tool_app_secret,
               applet_id, applet_secret, qr_app_id, qr_login_redirect_uri, account_suffix,
               add_book_call_back_url, add_book_call_back_token, add_book_call_back_secret,
               cust_message_call_back_url, cust_message_call_back_token, cust_message_call_back_secret,
               status, del_flag, create_by, create_time, update_by, update_time, customer_churn_notice_switch,
               edit_suffix,corp_avatar,content_audit_depart
        from tb_wx_corp_config
    </sql>

    <select id="selectTbWxCorpConfigList" parameterType="TbWxCorpConfig" resultMap="TbWxCorpConfigResult">
        <include refid="selectTbWxCorpConfigVo" />
        <where>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="companyName != null  and companyName != ''">
                and company_name like concat('%', #{companyName}, '%')
            </if>
            <if test="providerSecret != null  and providerSecret != ''">
                and provider_secret = #{providerSecret}
            </if>
            <if test="contactSecret != null  and contactSecret != ''">
                and contact_secret = #{contactSecret}
            </if>
            <if test="chatSecret != null  and chatSecret != ''">
                and chat_secret = #{chatSecret}
            </if>
            <if test="pubKey != null  and pubKey != ''">
                and pub_key = #{pubKey}
            </if>
            <if test="priKey != null  and priKey != ''">
                and pri_key = #{priKey}
            </if>
            <if test="tencentCloudSecretId != null  and tencentCloudSecretId != ''">
                and tencent_cloud_secret_id = #{tencentCloudSecretId}
            </if>
            <if test="tencentCloudSecretSecretKey != null  and tencentCloudSecretSecretKey != ''">
                and tencent_cloud_secret_secret_key = #{tencentCloudSecretSecretKey}
            </if>
            <if test="toolAppId != null  and toolAppId != ''">
                and tool_app_id = #{toolAppId}
            </if>
            <if test="toolAppSecret != null  and toolAppSecret != ''">
                and tool_app_secret = #{toolAppSecret}
            </if>
            <if test="appletId != null  and appletId != ''">
                and applet_id = #{appletId}
            </if>
            <if test="appletSecret != null  and appletSecret != ''">
                and applet_secret = #{appletSecret}
            </if>
            <if test="qrAppId != null  and qrAppId != ''">
                and qr_app_id = #{qrAppId}
            </if>
            <if test="qrLoginRedirectUri != null  and qrLoginRedirectUri != ''">
                and qr_login_redirect_uri = #{qrLoginRedirectUri}
            </if>
            <if test="addBookCallBackUrl != null  and addBookCallBackUrl != ''">
                and add_book_call_back_url = #{addBookCallBackUrl}
            </if>
            <if test="addBookCallBackToken != null  and addBookCallBackToken != ''">
                and add_book_call_back_token = #{addBookCallBackToken}
            </if>
            <if test="addBookCallBackSecret != null  and addBookCallBackSecret != ''">
                and add_book_call_back_secret = #{addBookCallBackSecret}
            </if>
            <if test="custMessageCallBackUrl != null  and custMessageCallBackUrl != ''">
                and cust_message_call_back_url = #{custMessageCallBackUrl}
            </if>
            <if test="custMessageCallBackToken != null  and custMessageCallBackToken != ''">
                and cust_message_call_back_token = #{custMessageCallBackToken}
            </if>
            <if test="custMessageCallBackSecret != null  and custMessageCallBackSecret != ''">
                and cust_message_call_back_secret = #{custMessageCallBackSecret}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="customerChurnNoticeSwitch != null  and customerChurnNoticeSwitch != ''">
                and customer_churn_notice_switch = #{customerChurnNoticeSwitch}
            </if>
        </where>
    </select>

    <select id="selectTbWxCorpConfigById" parameterType="Long" resultMap="TbWxCorpConfigResult">
        <include refid="selectTbWxCorpConfigVo" />
        where id = #{id}
    </select>

    <insert id="insertTbWxCorpConfig" parameterType="TbWxCorpConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_corp_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openCorpId != null and openCorpId != ''">open_corp_id,
            </if>
            <if test="corpId != null and corpId != ''">corp_id,
            </if>
            <if test="companyName != null and companyName != ''">company_name,
            </if>
            <if test="providerSecret != null and providerSecret != ''">provider_secret,
            </if>
            <if test="contactSecret != null and contactSecret != ''">contact_secret,
            </if>
            <if test="chatSecret != null">chat_secret,
            </if>
            <if test="pubKey != null">pub_key,
            </if>
            <if test="priKey != null">pri_key,
            </if>
            <if test="tencentCloudSecretId != null">tencent_cloud_secret_id,
            </if>
            <if test="tencentCloudSecretSecretKey != null">tencent_cloud_secret_secret_key,
            </if>
            <if test="toolAppId != null">tool_app_id,
            </if>
            <if test="toolAppSecret != null">tool_app_secret,
            </if>
            <if test="appletId != null">applet_id,
            </if>
            <if test="appletSecret != null">applet_secret,
            </if>
            <if test="qrAppId != null">qr_app_id,
            </if>
            <if test="qrLoginRedirectUri != null">qr_login_redirect_uri,
            </if>
            <if test="addBookCallBackUrl != null">add_book_call_back_url,
            </if>
            <if test="addBookCallBackToken != null">add_book_call_back_token,
            </if>
            <if test="addBookCallBackSecret != null">add_book_call_back_secret,
            </if>
            <if test="custMessageCallBackUrl != null">cust_message_call_back_url,
            </if>
            <if test="custMessageCallBackToken != null">cust_message_call_back_token,
            </if>
            <if test="custMessageCallBackSecret != null">cust_message_call_back_secret,
            </if>
            <if test="status != null">status,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="customerChurnNoticeSwitch != null">customer_churn_notice_switch,
            </if>
            <if test="accountSuffix != null and accountSuffix != ''">account_suffix,
            </if>
            <if test="contentAuditDepart != null and contentAuditDepart != ''">content_audit_depart,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openCorpId != null and openCorpId != ''">#{openCorpId},
            </if>
            <if test="corpId != null and corpId != ''">#{corpId},
            </if>
            <if test="companyName != null and companyName != ''">#{companyName},
            </if>
            <if test="providerSecret != null and providerSecret != ''">#{providerSecret},
            </if>
            <if test="contactSecret != null and contactSecret != ''">#{contactSecret},
            </if>
            <if test="chatSecret != null">#{chatSecret},
            </if>
            <if test="pubKey != null">#{pubKey},
            </if>
            <if test="priKey != null">#{priKey},
            </if>
            <if test="tencentCloudSecretId != null">#{tencentCloudSecretId},
            </if>
            <if test="tencentCloudSecretSecretKey != null">#{tencentCloudSecretSecretKey},
            </if>
            <if test="toolAppId != null">#{toolAppId},
            </if>
            <if test="toolAppSecret != null">#{toolAppSecret},
            </if>
            <if test="appletId != null">#{appletId},
            </if>
            <if test="appletSecret != null">#{appletSecret},
            </if>
            <if test="qrAppId != null">#{qrAppId},
            </if>
            <if test="qrLoginRedirectUri != null">#{qrLoginRedirectUri},
            </if>
            <if test="addBookCallBackUrl != null">#{addBookCallBackUrl},
            </if>
            <if test="addBookCallBackToken != null">#{addBookCallBackToken},
            </if>
            <if test="addBookCallBackSecret != null">#{addBookCallBackSecret},
            </if>
            <if test="custMessageCallBackUrl != null">#{custMessageCallBackUrl},
            </if>
            <if test="custMessageCallBackToken != null">#{custMessageCallBackToken},
            </if>
            <if test="custMessageCallBackSecret != null">#{custMessageCallBackSecret},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="customerChurnNoticeSwitch != null">#{customerChurnNoticeSwitch},
            </if>
            <if test="accountSuffix != null and accountSuffix != ''">#{accountSuffix},
            </if>
            <if test="contentAuditDepart != null and contentAuditDepart != ''">#{contentAuditDepart},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCorpConfig" parameterType="TbWxCorpConfig">
        update tb_wx_corp_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id =
                #{corpId},
            </if>
            <if test="corpAvatar != null and corpAvatar != ''">corp_avatar =
                #{corpAvatar},
            </if>
            <if test="companyName != null and companyName != ''">company_name =
                #{companyName},
            </if>
            <if test="providerSecret != null and providerSecret != ''">provider_secret =
                #{providerSecret},
            </if>
            <if test="contactSecret != null and contactSecret != ''">contact_secret =
                #{contactSecret},
            </if>
            <if test="chatSecret != null">chat_secret =
                #{chatSecret},
            </if>
            <if test="pubKey != null">pub_key =
                #{pubKey},
            </if>
            <if test="priKey != null">pri_key =
                #{priKey},
            </if>
            <if test="tencentCloudSecretId != null">tencent_cloud_secret_id =
                #{tencentCloudSecretId},
            </if>
            <if test="tencentCloudSecretSecretKey != null">tencent_cloud_secret_secret_key =
                #{tencentCloudSecretSecretKey},
            </if>
            <if test="toolAppId != null">tool_app_id =
                #{toolAppId},
            </if>
            <if test="toolAppSecret != null">tool_app_secret =
                #{toolAppSecret},
            </if>
            <if test="appletId != null">applet_id =
                #{appletId},
            </if>
            <if test="appletSecret != null">applet_secret =
                #{appletSecret},
            </if>
            <if test="qrAppId != null">qr_app_id =
                #{qrAppId},
            </if>
            <if test="qrLoginRedirectUri != null">qr_login_redirect_uri =
                #{qrLoginRedirectUri},
            </if>
            <if test="addBookCallBackUrl != null">add_book_call_back_url =
                #{addBookCallBackUrl},
            </if>
            <if test="addBookCallBackToken != null">add_book_call_back_token =
                #{addBookCallBackToken},
            </if>
            <if test="addBookCallBackSecret != null">add_book_call_back_secret =
                #{addBookCallBackSecret},
            </if>
            <if test="custMessageCallBackUrl != null">cust_message_call_back_url =
                #{custMessageCallBackUrl},
            </if>
            <if test="custMessageCallBackToken != null">cust_message_call_back_token =
                #{custMessageCallBackToken},
            </if>
            <if test="custMessageCallBackSecret != null">cust_message_call_back_secret =
                #{custMessageCallBackSecret},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="customerChurnNoticeSwitch != null">customer_churn_notice_switch =
                #{customerChurnNoticeSwitch},
            </if>
            <if test="accountSuffix != null and accountSuffix != ''">account_suffix =
                #{accountSuffix},
            </if>
            <if test="contentAuditDepart != null">content_audit_depart =
                #{contentAuditDepart},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxCorpConfigById" parameterType="Long">
        delete from tb_wx_corp_config where id = #{id}
    </delete>

    <delete id="deleteTbWxCorpConfigByIds" parameterType="String">
        delete from tb_wx_corp_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCorpConfigByCorpId" parameterType="String" resultType="java.lang.Integer">
        select count(1) from tb_wx_corp_config where open_corp_id = #{corpId}
    </select>
    <select id="queryWxSendMessageInfoByUserIdCorpId" resultType="CorpSendMessageInfoVO">
        select max(name) userName,(select agent_id from tb_wx_corp_permanent_token where t.corp_id  = corp_id and `status` = 0) agentId ,
        (select customer_churn_notice_switch from tb_wx_corp_config where t.corp_id  = corp_id and del_flag = 0) customerChurnNoticeSwitch
        from tb_wx_ext_customer t
         where t.external_user_id =#{userId}  and t.corp_id =#{corpId}
    </select>

    <select id="selectTbWxCorpConfigVoByCorpId" resultType="TbWxCorpConfig">
        select id,corp_id corpId, company_name companyName,account_suffix accountSuffix,provider_secret providerSecret,contact_secret contactSecret,corp_avatar corpAvatar,system_version systemVersion,content_audit_depart contentAuditDepart from tb_wx_corp_config where corp_id = #{corpId}
    </select>


    <select id="selectGroupUserCountByCorpId" parameterType="TbWxCustomerGroupMember" resultType="java.lang.Integer">
        select
          count(user_id)
        from
          tb_wx_customer_group_member m
        left join tb_wx_customer_group g on m.group_id = g.chat_id
        where
            m.corp_id = #{corpId}
          and m.type = 2
          and g.dismiss_status = 0
            <if test="status != null  and status != ''">
                and m.status = #{status}
            </if>
            <if test="departureTime != null  and departureTime != ''">
                and m.departure_time &gt;= concat(#{departureTime}, " 00:00:00")
                and m.departure_time &lt;= concat(#{departureTime}, " 23:59:59")
            </if>
            <if test="joinTime != null  and joinTime != ''">
                and m.join_time &gt;= concat(#{joinTime}, " 00:00:00")
                and m.join_time &lt;= concat(#{joinTime}, " 23:59:59")
            </if>
    </select>

    <select id="selectFollowUserCountByCorpId" parameterType="TbWxExtFollowUser" resultType="java.lang.Integer">
        select
          count(distinct external_user_id)
        from
        (
            SELECT
            u.external_user_id,
            max( u.del_time ) AS loss_time,
            min( u.create_time ) AS first_add_time
            FROM
            tb_wx_ext_customer c
            JOIN tb_wx_ext_follow_user u ON u.corp_id = c.corp_id and u.external_user_id = c.external_user_id
            where c.corp_id = #{corpId}
            <if test="status != null  and status != '' and status == '0'.toString()">
                and c.status = 0 AND u.status = 0
            </if>
            <if test="status != null  and status != '' and status == '2'.toString()">
                and c.status != 0
            </if>
            GROUP BY
                external_user_id
        ) t
        <where>
            <if test="delTime != null  and delTime != ''">
                and t.loss_time &gt;= concat(#{delTime}, " 00:00:00")
                and t.loss_time &lt;= concat(#{delTime}, " 23:59:59")
            </if>
            <if test="createTime != null  and createTime != ''">
                and t.first_add_time &gt;= concat(#{createTime},  " 00:00:00")
                and t.first_add_time &lt;= concat(#{createTime},  " 23:59:59")
            </if>
        </where>

    </select>

    <delete id="deleteCorpAllData" parameterType="String">
        delete from tb_wx_corp_config where corp_id = #{corpId};
        delete from tb_wx_corp_dimission_allocate where corp_id = #{corpId};
        delete from tb_wx_corp_dimission_info where corp_id = #{corpId};
        delete from tb_wx_corp_mass_message_detail where corp_id = #{corpId};
        delete from tb_wx_corp_mass_message_info where corp_id = #{corpId};

        delete from tb_wx_corp_mass_message_log where corp_id = #{corpId};
        delete from tb_wx_corp_mass_message_send_target_condition where corp_id = #{corpId};
        delete from tb_wx_corp_permanent_token where corp_id = #{corpId};
        delete from tb_wx_corp_tag where corp_id = #{corpId};
        delete from tb_wx_corp_tag_group where corp_id = #{corpId};

        delete from tb_wx_corp_welcome_tlp_scope where msg_tlp_id in (select id from tb_wx_corp_welcome_tlp where corp_id = #{corpId});
        delete from tb_wx_corp_welcome_tlp where corp_id = #{corpId};
        delete from tb_wx_customer_group where corp_id = #{corpId};
        delete from tb_wx_customer_group_member where corp_id = #{corpId};
        delete from tb_wx_department where corp_id = #{corpId};

        delete from tb_wx_ext_customer where corp_id = #{corpId};
        delete from tb_wx_ext_follow_user where corp_id = #{corpId};
        delete from tb_wx_ext_follow_user_tag where corp_id = #{corpId};
        delete from tb_wx_group_code where corp_id = #{corpId};

        delete from tb_wx_group_code_actual where group_code_id in (select id from tb_wx_group_code where corp_id = #{corpId});
        delete from tb_wx_material where corp_id = #{corpId};
        delete from tb_wx_user where corp_id = #{corpId};
        delete from tb_wx_user_rel where corp_id = #{corpId};

        delete from tb_wx_contact_user_rel where corp_id = #{corpId};
        delete from tb_wx_contact where corp_id = #{corpId};
        delete from tb_wx_category where corp_id = #{corpId};
        delete from tb_customer_follow_log where corp_id = #{corpId};

        delete from sys_user_post where user_id in (select user_id from sys_user where corp_id = #{corpId});
        delete from sys_user_role where user_id in (select user_id from sys_user where corp_id = #{corpId});
        delete from sys_dept where dept_id in (select dept_id from sys_user where corp_id = #{corpId});
        delete from sys_user where corp_id = #{corpId};
    </delete>

    <select id="selectAppCorpPermanentStatus" resultType="java.lang.Integer">
        select count(1) from app_corp_permanent_token where corp_id = #{corpId} and `status` = 1
    </select>

    <select id="getCorpIdByOpenCorpId" resultType="java.lang.String">
        select corp_id from tb_wx_corp_config where open_corp_id = #{authCorpId} limit 1
    </select>

    <select id="selectGroupUserCountByCorpIdNotDistinct" resultType="java.lang.Integer">
        select
        count(user_id)
        from
        tb_wx_customer_group_member
        where
        corp_id = #{corpId}
        and status = #{status}
    </select>

    <!--查询企业基本信息（不含密钥等敏感信息）-->
    <select id="selectBaseCorpInfoById" parameterType="TbWxCorpConfig" resultMap="TbWxCorpConfigResult">
        select
          id,corp_id,`open_corp_id`,company_name,`status`,`del_flag`,`customer_churn_notice_switch`,`edit_suffix`,account_suffix
        FROM
          tb_wx_corp_config
        <where>
            <if test="id != null  and id > 0">
                and id = #{id}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="openCorpId != null  and openCorpId != ''">
                and open_corp_id = #{openCorpId}
            </if>
        </where>
    </select>

    <select id="getCorpIdByConfigId" resultType="String">
        select ifnull(corp_id,'') from tb_wx_corp_config where id = #{corpConfigId} limit 1
    </select>

    <select id="selectTbWxConfigIdByCorpId" resultType="Long">
        select id from tb_wx_corp_config where corp_id = #{corpId} limit 1
    </select>

    <select id="selectSelfBuildAgentInfoById" resultType="TbWxCorpConfig">
        select
            c.id,
            c.corp_id,
            -- (select agent_id from app_corp_permanent_token where corp_id = c.corp_id and `status` = 0 limit 1)toolAppId saas
            c.tool_app_id toolAppId
        from tb_wx_corp_config c
        where c.id = #{corpId}
    </select>

    <select id="queryTaskStatus" resultType="java.util.Map">
        select
            (select
                 count(1) > 0
             from tb_wx_corp_tag_group
             where corp_id = #{corpId}
               and create_by != 'SYS'
               and `status` = 0)tag,
            (select count(1) > 0
             from tb_wx_radar_interact
             where corp_id = #{corpId}
               and del_flag = 0
               and scope = 1
            )radar,
            (select count(1) > 0
             from tb_wx_contact
             where corp_id = #{corpId}
               and `show` = 1
               and del_flag = 0
            )contact,
            (select count(1) > 0
             from tb_wx_quick_reply
             where corp_config_id = (select id from tb_wx_corp_config where corp_id = #{corpId} limit 1)
               and del_flag = 0
            )quickReply,
            (select count(1) > 0
             from tb_wx_corp_mass_message_info
             where corp_id = #{corpId}
               and task_fission_id is null
               and del_flag = 0
            )massMessage
    </select>

    <select id="selectTbWxUserCount" resultType="java.lang.Integer">
        select count(1) from tb_wx_ext_customer where corp_id = #{corpId} and status = 0
    </select>

    <select id="getCorpNameByCorpId" resultType="java.lang.String">
        select company_name from tb_wx_corp_config where corp_id = #{corpId} limit  1
    </select>


    <select id="countActiveCustomerTotal1v1" resultType="int">
        SELECT COUNT(DISTINCT wcai.from_id)
        FROM wk_chat_archive_info wcai
        JOIN tb_wx_ext_customer c ON wcai.from_id = c.external_user_id
        WHERE wcai.chat_type = 1 AND c.status = 0
          AND wcai.msg_day = #{today}
    </select>

    <select id="countActiveCustomerTotalGroup" resultType="int">
        SELECT COUNT(DISTINCT wcai.from_id)
        FROM wk_chat_archive_info wcai
                 LEFT JOIN tb_wx_user tbwu ON wcai.from_id = tbwu.userid
        WHERE wcai.chat_type = 2
          AND wcai.msg_day = #{today}
          AND tbwu.userid IS NULL
    </select>

    <select id="countActiveGroupCount" resultType="int">
        SELECT COUNT(DISTINCT room_id)
        FROM wk_chat_archive_info
        WHERE chat_type = 2
          AND msg_day = #{today}
          AND room_id IS NOT NULL
    </select>


</mapper>