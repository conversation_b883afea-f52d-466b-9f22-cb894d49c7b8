<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.kf.TbWxKfServicesMapper">

    <resultMap type="TbWxKfServices" id="TbWxKfServicesResult">
        <result property="id" column="id"/>
        <result property="kfId" column="kf_id"/>
        <result property="userId" column="user_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>
</mapper>