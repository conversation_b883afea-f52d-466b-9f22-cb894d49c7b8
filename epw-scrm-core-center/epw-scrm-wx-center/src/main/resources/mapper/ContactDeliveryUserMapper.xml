<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sitecontact.ContactDeliveryUserMapper">
    <select id="selectDeliveryUserList" resultType="ContactDeliveryUserVO">
        select
        du.id,
        wu.name user_name,
        wu.userid user_id,
        wu.avatar user_avatar,
        wu.mobile,
        (case when b.parent_id = -1 then '默认'
        when b.parent_id &lt; 35 then (select area_name from contact_area where id = b.parent_id)
        when b.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        when b.parent_id &lt; 10000 then concat(
        (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = b.parent_id)))
        ,(select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        else '默认' end)area_name,
        b.area_name site_name,
        a.area_name store_name,
        du.user_status
        from contact_delivery_user du
        join contact_area a on du.store_id = a.id
        join contact_area b on a.parent_id = b.id
        join tb_wx_user wu on du.user_pri_id = wu.id
        where du.del_flag = 0
        <if test="userStatus != null">
            and du.user_status = #{userStatus}
        </if>
        <if test="mobile != null and mobile != ''">
            and wu.mobile like concat('%',#{mobile},'%')
        </if>
        <if test="userName != null and userName != ''">
            and wu.name like concat('%',#{userName},'%')
        </if>
        <if test="storeIds != null and storeIds.length > 0">
            and du.store_id in
            <foreach item="item" collection="storeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by du.create_time desc
    </select>

    <resultMap id="getDeliveryUserByIdMap" type="ContactDeliveryUserEditVO">
        <id column="id" property="id"/>
        <result column="site_id" property="siteId"/>
        <result column="store_id" property="storeId"/>
        <result column="user_status" property="userStatus"/>
        <result column="area_name" property="areaName"/>
        <result column="site_name" property="siteName"/>
        <result column="store_name" property="storeName"/>
        <result column="sign_id" property="signId"/>
        <collection property="userList" ofType="UserVo">
            <id column="user_pri_id" property="id"/>
            <result column="avatar" property="avatar"/>
            <result column="name" property="name"/>
            <result column="userid" property="userId"/>
        </collection>
    </resultMap>

    <select id="getDeliveryUserById" resultMap="getDeliveryUserByIdMap">
        select du.id,
               du.sign_id,
               du.store_id,
               du.user_status,
               du.user_pri_id,
               u.userid,
               u.name,
               u.avatar,
               @siteName := (select area_name from contact_area where id = (select parent_id from contact_area a where a.id = du.store_id))site_name,
               @storeName := (select area_name from contact_area where id = du.store_id)store_name,
               (select parent_id from contact_area a where a.id = du.store_id)site_id,
               concat(@storeName,'-',@siteName)area_name
        from contact_delivery_user du
        join tb_wx_user u on du.user_pri_id = u.id
        join (select @siteName := null)n
        join (select @storeName := null)s
        where du.id = #{id}
          and du.del_flag = 0
          and u.del_flag = 1
    </select>

    <select id="selectDeliveryUserListBySignId" resultType="ContactDeliveryUser">
        select
            du.id
        from contact_delivery_user du
        <where>
            <if test="contactProvince == 1">
                and du.store_id in
                (select a.id from contact_area a where a.parent_id  =
                (select city_id from tb_wx_contact c where c.sign_id = #{signId} and c.del_flag = 0) and a.del_flag = 0)
            </if>
            <if test="contactProvince == 2">
                and du.store_id = (select a.id from contact_area a where a.sign_id  = #{signId})
            </if>
            <if test="contactProvince == 3">
                and du.sign_id = #{signId}
            </if>
        </where>
        and du.del_flag = 0
    </select>
</mapper>