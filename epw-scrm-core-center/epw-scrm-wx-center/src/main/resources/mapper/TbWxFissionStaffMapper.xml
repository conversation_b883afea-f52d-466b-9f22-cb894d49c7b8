<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.fission.TbWxFissionStaffMapper">
    <insert id="insertTbWxFissionStaffList" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_wx_fission_staff(id,fission_id,staff_type,staff_id,staff_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.fissionId},#{item.staffType},#{item.staffId},#{item.staffName})
        </foreach>
    </insert>
</mapper>