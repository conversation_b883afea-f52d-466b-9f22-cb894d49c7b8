<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.transfer.TbWxTransferRecordMapper">

    <select id="getTransferRecords" resultType="com.cenker.scrm.pojo.vo.transfer.TransferRecordVO">
        SELECT
        tr.transfer_id AS "recordId",
        hoUser.name AS "handoverUserName",
        hoUser.avatar AS "handoverUserAvatar",
        toUser.name AS "takeoverUserName",
        toUser.avatar AS "takeoverUserAvatar",
        tr.create_time AS "createTime",
        su.nick_name AS "creatorUserName",
        COUNT(DISTINCT CASE WHEN tri.resource_type = 1 THEN tri.resource_id END) AS "totalGroups",
        COUNT(DISTINCT CASE WHEN tri.resource_type = 0 THEN tri.resource_id END) AS "totalCustomers",
        COUNT(DISTINCT CASE WHEN tri.resource_type = 0 AND tri.status = '1' THEN tri.resource_id END) AS "successTransferredCustomers",
        COUNT(DISTINCT CASE WHEN tri.resource_type = 1 AND tri.status = '1' THEN tri.resource_id END) AS "successTransferredGroups"
        FROM
        tb_wx_transfer_record tr
        LEFT JOIN tb_wx_user hoUser ON tr.handover_user_id = hoUser.userid
        LEFT JOIN tb_wx_user toUser ON tr.takeover_user_id = toUser.userid
        LEFT JOIN tb_wx_transfer_record_item tri ON tr.transfer_id = tri.transfer_id
        LEFT JOIN sys_user su ON tr.create_by = su.user_id
        WHERE
        tr.corp_id = #{corpId} and tr.transfer_type = #{condition.transferType}
        <if test="condition.beginTime != null and condition.beginTime != ''">
            AND tr.create_time >= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and condition.endTime != ''">
            AND tr.create_time &lt;= CONCAT(#{condition.endTime}, ' 23:59:59')
        </if>
        <if test="condition.handoverUserId != null and condition.handoverUserId != ''">
            AND tr.handover_user_id = #{condition.handoverUserId}
        </if>
        <if test="condition.takeoverUserId != null and condition.takeoverUserId != ''">
            AND tr.takeover_user_id = #{condition.takeoverUserId}
        </if>
        <if test="condition.handoverUserName != null and condition.handoverUserName != ''">
            AND hoUser.name LIKE CONCAT('%', #{condition.handoverUserName}, '%')
        </if>
        <if test="condition.takeoverUserName != null and condition.takeoverUserName != ''">
            AND toUser.name LIKE CONCAT('%', #{condition.takeoverUserName}, '%')
        </if>
        <!-- 权限控制 -->
        <if test="condition.dataScope == null or condition.dataScope != '1'.toString()">
            <if test="condition.permissionDeptIds != null and condition.permissionDeptIds.size() > 0">
                and hoUser.main_department in
                <foreach item="item" collection="condition.permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.permissionDeptIds == null or condition.permissionDeptIds.size() == 0">
                and hoUser.userid = #{condition.wxUserId}
            </if>
        </if>
        GROUP BY tr.transfer_id, hoUser.name, hoUser.avatar, toUser.name, toUser.avatar, tr.create_time
        ORDER BY tr.create_time DESC
    </select>


    <select id="getTransferDetailsByTransferId" resultType="com.cenker.scrm.pojo.vo.transfer.TransferDetailsVO">
        SELECT
            hoUser.name AS handoverUserName,
            toUser.name AS takeoverUserName,
            tr.transfer_success_msg AS transferSuccessMsg,
            tr.create_time AS createTime,
            tr.transfer_type AS transferType,
            su.nick_name AS creatorUserName,
            (SELECT max(name) FROM tb_wx_department WHERE id= hoUser.main_department and corp_id = hoUser.corp_id ) as department,
            -- 离职时间
            hoUser.dimission_time AS dimissionTime
        FROM tb_wx_transfer_record tr
                 LEFT JOIN tb_wx_user hoUser ON tr.handover_user_id = hoUser.userid
                 LEFT JOIN tb_wx_user toUser ON tr.takeover_user_id = toUser.userid
                 LEFT JOIN sys_user su ON tr.create_by = su.user_id
        WHERE tr.transfer_id = #{transferId}
        limit 1
    </select>


    <select id="queryTransferDetailsPerCustomer" resultType="com.cenker.scrm.pojo.vo.transfer.TransferDetailsPerCustomerVO">
        SELECT
        c.external_user_id AS "customerId",
        c.name AS "customerName",
        c.avatar AS "customerAvatar",
        u.name AS "originalAdder",
        r.status AS "takeoverStatus",
        u.dimission_time AS "leaveTime",
        tu.name AS "takeoverUserName"
        FROM tb_wx_transfer_record_item r
        INNER JOIN tb_wx_ext_customer c ON r.resource_id = c.external_user_id
        INNER JOIN tb_wx_user u ON r.handover_user_id = u.userid
        Inner JOIN tb_wx_user tu ON r.takeover_user_id = tu.userid
        WHERE r.transfer_id = #{condition.recordId} and r.resource_type = 0
        <if test="condition.customerName != null and condition.customerName != ''">
            AND c.name LIKE CONCAT('%', #{condition.customerName}, '%')
        </if>
        <if test="condition.originalEmployee != null and condition.originalEmployee != ''">
            AND u.name LIKE CONCAT('%', #{condition.originalEmployee}, '%')
        </if>
        <if test="condition.takeoverStatus != null and condition.takeoverStatus >= 0">
            AND r.status = #{condition.takeoverStatus}
        </if>
    </select>

    <select id="queryTransferDetailsPerGroup" resultType="com.cenker.scrm.pojo.vo.transfer.TransferDetailsPerGroupVO">
        SELECT
        cg.chat_id AS "groupId",
        cg.group_name AS "groupName",
        u.name AS "originalOwner",
        u.avatar AS "originalOwnerAvatar",
        tri.status AS "takeoverStatus",
        tou.name AS "takeoverUserName",
        COUNT(gm.user_id) AS "groupMemberCount" -- 计算群成员数量
        FROM
        tb_wx_customer_group cg
        JOIN
        tb_wx_transfer_record_item tri ON cg.chat_id = tri.resource_id
        LEFT JOIN
        tb_wx_user u ON cg.owner = u.userid
        LEFT JOIN
        tb_wx_user tou ON tri.takeover_user_id = tou.userid
        LEFT JOIN -- 加入对群成员表的引用来计算每个群的成员数
        tb_wx_customer_group_member gm ON cg.chat_id = gm.group_id AND gm.status = 0 -- status=0表示有效成员
        WHERE
        tri.transfer_id = #{condition.recordId} and tri.resource_type = 1
        <if test="condition.groupName != null and condition.groupName != ''">
            AND cg.group_name LIKE CONCAT('%', #{condition.groupName}, '%')
        </if>
        <if test="condition.originalEmployee != null and condition.originalEmployee != ''">
            AND u.name LIKE CONCAT('%', #{condition.originalEmployee}, '%')
        </if>
        <if test="condition.takeoverStatus != null">
            AND tri.status = #{condition.takeoverStatus}
        </if>
        GROUP BY cg.chat_id -- 确保对每个群进行分组以正确计算成员数量
    </select>

    <select id="getAllLeavingEmployeeList" resultType="com.cenker.scrm.pojo.vo.transfer.ExternalUnAssignListVO">
        SELECT
            u.userid AS handoverUserId,
            u.NAME AS handoverUserName,
            u.avatar AS handoverUserAvatar,
            d.name AS handoverUserDepartment,
            u.dimission_time AS dimissionTime,
            COUNT(DISTINCT CASE WHEN efu.STATUS = '0' THEN efu.external_user_id END) AS unAssignCustomerCount,-- 待分配客户数
            COUNT(DISTINCT CASE WHEN cg.dismiss_status = '0' THEN cg.chat_id END) AS unAssignGroupChatCount -- 待分配群聊数
        FROM tb_wx_user u
        LEFT JOIN tb_wx_ext_follow_user efu ON efu.user_id = u.userid AND efu.corp_id = u.corp_id
        LEFT JOIN tb_wx_customer_group cg ON cg.OWNER = u.userid AND cg.corp_id = u.corp_id
        LEFT JOIN tb_wx_department d ON d.id = u.main_department
        WHERE u.corp_id = #{corpId} and (efu.status = '0' or cg.status = '0')
        <if test="userIds != null and userIds.size() > 0">
            AND u.userid IN
            <foreach item="userId" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <!-- 权限控制 -->
        <if test="condition.dataScope == null or condition.dataScope != '1'.toString()">
            <if test="condition.permissionDeptIds != null and condition.permissionDeptIds.size() > 0">
                and u.main_department in
                <foreach item="item" collection="condition.permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.permissionDeptIds == null or condition.permissionDeptIds.size() == 0">
                and u.userid = #{condition.wxUserId}
            </if>
        </if>
        <if test="condition.handoverUserName != null and condition.handoverUserName != ''">
            AND u.name LIKE CONCAT('%', #{condition.handoverUserName}, '%')
        </if>
        GROUP BY u.userid
    </select>


</mapper>
