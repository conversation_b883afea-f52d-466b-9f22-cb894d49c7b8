<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.group.TbWxCustomerGroupMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup" id="TbWxCustomerGroupResult">
        <result property="chatId" column="chat_id"/>
        <result property="groupName" column="group_name"/>
        <result property="createTime" column="create_time"/>
        <result property="notice" column="notice"/>
        <result property="owner" column="owner"/>
        <result property="status" column="status"/>
        <result property="corpId" column="corp_id"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="dismissDate" column="dismiss_date"/>
        <result property="dismissStatus" column="dismiss_status"/>
    </resultMap>

    <sql id="selectTbWxCustomerGroupVo">
        select chat_id,
               group_name,
               create_time,
               notice,
               owner,
               status,
               corp_id,
               create_by,
               update_time,
               update_by,
               dismiss_date,
               dismiss_status
        from tb_wx_customer_group
    </sql>

    <select id="selectTbWxCustomerGroupList" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup" resultMap="TbWxCustomerGroupResult">
        <include refid="selectTbWxCustomerGroupVo"/>
        <where>
            <if test="groupName != null  and groupName != ''">
                and group_name = #{groupName}
            </if>
            <if test="notice != null  and notice != ''">
                and notice = #{notice}
            </if>
            <if test="owner != null  and owner != ''">
                and owner = #{owner}
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="dismissDate != null ">
                and dismiss_date is not null
            </if>
        </where>
    </select>

    <select id="selectTbWxCustomerGroupById" parameterType="String"
            resultMap="TbWxCustomerGroupResult">
        <include refid="selectTbWxCustomerGroupVo"/>
        where chat_id = #{chatId}
    </select>

    <insert id="insertTbWxCustomerGroup" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup">
        insert into tb_wx_customer_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null">chat_id,
            </if>
            <if test="groupName != null">group_name,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="notice != null">notice,
            </if>
            <if test="owner != null">owner,
            </if>
            <if test="status != null">status,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="dismissDate != null">dismiss_date,
            </if>
            <if test="dismissStatus != null">dismiss_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null">#{chatId},
            </if>
            <if test="groupName != null">#{groupName},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="notice != null">#{notice},
            </if>
            <if test="owner != null">#{owner},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="dismissDate != null">#{dismissDate},
            </if>
            <if test="dismissStatus != null">#{dismissStatus},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCustomerGroup" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup">
        update tb_wx_customer_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name =
                #{groupName},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="notice != null">notice =
                #{notice},
            </if>
            <if test="owner != null">owner =
                #{owner},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="dismissDate != null">dismiss_date =
                #{dismissDate},
            </if>
            <if test="dismissStatus != null">dismiss_status =
                #{dismissStatus},
            </if>
        </trim>
        where chat_id = #{chatId}
    </update>

    <delete id="deleteTbWxCustomerGroupById" parameterType="String">
        delete
        from tb_wx_customer_group
        where chat_id = #{chatId}
    </delete>

    <delete id="deleteTbWxCustomerGroupByIds" parameterType="String">
        delete from tb_wx_customer_group where chat_id in
        <foreach item="chatId" collection="array" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </delete>

    <select id="queryCustomerGroupInfo" resultType="CustomerGroupVo">
        select
        if(t.group_name = '','群聊(未命名)', t.group_name) groupName,
        t.create_time,
        t.chat_id,
        t.notice,
        u.name as ownerName,
        t.status,
        t.dismiss_date,
        t.owner,
        (select count(1)
        from tb_wx_customer_group_member gm
        where gm.group_id = t.chat_id
        and gm.`status` != 0
        and date_format(gm.departure_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
        )today_departure_cnt,
        (select count(1)
        from tb_wx_customer_group_member gm
        where gm.group_id = t.chat_id
        and gm.`status` = 0
        and date_format(gm.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
        )today_join_cnt,
        (select count(id) from tb_wx_customer_group_member
        where group_id = t.chat_id and status ='0' and corp_id = #{corpId}) memberCnt,
        (select count(id) from tb_wx_customer_group_member
        where group_id = t.chat_id and status ='0' and corp_id = #{corpId} and type = 1) staff_cnt,
        (select count(ecc.external_user_id) from tb_wx_customer_group_member cgm
        left join tb_wx_ext_customer ecc on ecc.external_user_id = cgm.user_id and ecc.corp_id = cgm.corp_id
        where cgm.group_id = t.chat_id and cgm.status ='0' and cgm.corp_id = #{corpId} and cgm.type = 2) customer_cnt
        from tb_wx_customer_group t
        left join tb_wx_user u ON t.`owner` = u.userid
        where t.corp_id = #{corpId} and t.dismiss_date is null
        <if test="groupName != null and groupName != ''">
            and t.group_name like concat('%',#{groupName},'%')
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="owner != null  and owner.size() != 0">
            and t.owner in
            <foreach item="item" collection="owner" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupId != null  and groupId != ''">
            and t.chat_id = #{groupId}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryCustomerGroupMemberInfo" resultType="CustomerGroupMemberVo"
            parameterType="CustomerGroupDto">
        SELECT
        t.type type,
        CASE
        WHEN type = '2' THEN
        ( SELECT max( NAME ) FROM tb_wx_ext_customer WHERE external_user_id = t.user_id and t.corp_id = #{corpId}) ELSE
        ( SELECT max( NAME ) FROM tb_wx_user WHERE userid = t.user_id and t.corp_id = #{corpId} )
        END userName,
        DATE_FORMAT(t.join_time,'%Y-%m-%d') joinTime,
        t.join_scene joinScene,
        ( SELECT max( NAME ) FROM tb_wx_user WHERE userid = t1.`owner` ) OWNER
        FROM
        tb_wx_customer_group_member t,
        tb_wx_customer_group t1
        WHERE
        t.group_id = t1.chat_id
        AND t1.dismiss_date IS NULL
        and t1.corp_id = t.corp_id
        and t.corp_id = #{corpId}
        <if test="groupName != null and groupName != ''">
            and group_name = #{groupName}
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>

        <if test="owner != null  and owner.size() != 0">
            and owner in
            <foreach item="item" collection="owner" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupId != null  and groupId != ''">
            and chat_id = #{groupId}
        </if>
    </select>

    <select id="queryJoinMemberStatis" resultType="CustomerGroupStatisVo">
        SELECT DATE_FORMAT(join_time, '%m-%d') dayKey,
               COUNT(user_id)                  userCount
        FROM tb_wx_customer_group_member
        WHERE corp_id = #{corpId}
          AND join_time &gt;= concat(#{startTime}, " 00:00:00")
          AND join_time &lt;= concat(#{endTime}, " 23:59:59")
          AND `status` = 0
        group by DATE_FORMAT(join_time, '%m-%d')
        ORDER BY DATE_FORMAT(join_time, '%m-%d')
    </select>

    <select id="queryDepartureMemberStatis" resultType="CustomerGroupStatisVo">
        SELECT DATE_FORMAT(departure_time, '%m-%d') dayKey,
               COUNT(user_id)                       userCount
        FROM tb_wx_customer_group_member
        WHERE corp_id = #{corpId}
          AND departure_time &gt;= concat(#{startTime}, " 00:00:00")
          AND departure_time &lt;= concat(#{endTime}, " 23:59:59")
        group by DATE_FORMAT(departure_time, '%m-%d')
        ORDER BY DATE_FORMAT(departure_time, '%m-%d')
    </select>

    <select id="findWeGroupByCustomer" resultType="CustomerAddGroupVo">
        select g.chat_id,
               g.group_name,
               u.name                   as ownerName,
               (select count(1)
                from tb_wx_customer_group_member gm
                where g.chat_id = gm.group_id
                  and gm.`status` = 0
               )                           groupMemberNum,
               m.join_time,
               (SELECT if(count(1) > 1, '1', '0')
                FROM tb_wx_customer_group_member mm
                WHERE mm.group_id = g.chat_id
                  and (mm.user_id = #{externalUserId} or mm.user_id = #{userId})
                  and mm.`status` != 2) as commonGroup
        from tb_wx_customer_group g
                 left join tb_wx_customer_group_member m on g.chat_id = m.group_id
                 left join tb_wx_user u ON g.`owner` = u.userid and u.corp_id = g.corp_id
        where m.user_id = #{externalUserId}
          and g.corp_id = #{corpId}
          and m.`status` != 2
        group by g.chat_id
    </select>

    <select id="getGroupListByWork" resultType="CustomerGroupVo">
        select
        g.chat_id,
        g.create_time,
        count(m.user_id)member_cnt,
        concat(date_format(g.create_time, '%Y-%m-%d'),'创建') create_time_str,
        (select name from tb_wx_user where owner = userid and corp_id = g.corp_id)owner_name,
        if (g.group_name is null or g.group_name = '','群聊',g.group_name)group_name,
        sum(date_format(m.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and m.type = 2)today_join_cnt,
        (select count(1)
        from tb_wx_customer_group_member gm
        where gm.group_id = g.chat_id
        and gm.`status` != 0
        and gm.type = 2
        and date_format(gm.departure_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
        )today_departure_cnt
        from
        (
        select gg.chat_id,gg.create_time,gg.corp_id,gg.group_name,gg.`owner` from tb_wx_customer_group gg
        left join tb_wx_customer_group_member gmm on gmm.group_id = gg.chat_id
        where gg.corp_id = #{corpId}
        and gg.`status` = 0
        and gg.dismiss_status = 0
        and gmm.user_id = #{userId}
        and gmm.status = 0
        <if test="type != null and type == 1">
            and gg.owner = #{userId}
        </if>
        <if test="type != null and type == 2">
            and gg.owner != #{userId}
        </if>
        group by gg.chat_id
        )g
        left join tb_wx_customer_group_member m on m.group_id = g.chat_id and m.corp_id = g.corp_id and m.`status` = 0
        <where>
            <if test="ownerName != null and ownerName != ''">
                <choose>
                    <when test="ownerName == '群'.toString() or ownerName == '聊'.toString() or ownerName == '群聊'.toString()">
                        and (g.group_name like concat('%',#{ownerName},'%') or g.group_name = '')
                    </when>
                    <otherwise>
                        and g.group_name like concat('%',#{ownerName},'%')
                    </otherwise>
                </choose>
            </if>
        </where>
        group by g.chat_id
        order by g.create_time desc
    </select>

    <select id="listCustomerGroup" resultType="CustomerGroupVo">
        SELECT
        g.chat_id,
        u.name AS owner_name,
        IF (g.group_name is null or g.group_name = '', '群聊(未命名)', g.group_name) as group_name,
        g.create_time,
        SUM(CASE WHEN m.status = 0 THEN 1 ELSE 0 END) AS member_cnt,
        SUM(CASE WHEN m.status = 0 AND DATE(m.join_time) = CURDATE() THEN 1 ELSE 0 END) AS today_join_cnt,
        SUM(CASE WHEN m.status != 0 AND DATE(m.join_time) = CURDATE() THEN 1 ELSE 0 END) AS today_departure_cnt
        FROM tb_wx_customer_group g
        LEFT JOIN tb_wx_user u on g.owner = u.userid AND u.corp_id = g.corp_id
        LEFT JOIN tb_wx_customer_group_member m ON m.group_id = g.chat_id AND m.corp_id = g.corp_id
        where g.dismiss_status = 0 and g.corp_id = #{corpId}
        <if test="groupName != null and groupName != ''">
            and g.group_name like concat('%',#{groupName},'%')
        </if>
        <if test="owner != null and owner.size > 0">
            and g.owner in
            <foreach item="item" collection="owner" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and u.main_department in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and g.owner = #{wxUserId}
            </if>
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and DATE(g.create_time) >= STR_TO_DATE(#{beginTime}, '%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and DATE(g.create_time) <![CDATA[ <= ]]> STR_TO_DATE(#{endTime}, '%Y-%m-%d')
        </if>
        group by g.chat_id
        order by g.create_time desc
    </select>

    <select id="getCorpGroupData" resultType="CustomerMemberVo">
        select
        m.`name` user_name,
        if(g.group_name = '','群聊',g.group_name) group_name,
        m.user_id,
        (select name from tb_wx_user where userid= g.owner and corp_id = m.corp_id limit 1)owner_str,
        (case when m.join_scene = 1 and m.invitor_user_id is null then '由群成员邀请入群'
        when m.join_scene = 1 and m.invitor_user_id is not null then
        concat('由员工',IFNULL((select name from tb_wx_user where m.invitor_user_id = userid and m.corp_id =
        corp_id),''),'直接邀请入群')
        when m.join_scene = 2 then '由群成员发送邀请链接入群'
        when m.join_scene = 3 then '通过扫描群二维码入群'
        else '-' end
        )join_scene,
        (case when m.quit_scene = 0 then '成员自行退出'
        when m.quit_scene = 1 then '群主/群管理员移出'
        else '-' end
        )quit_scene,
        m.join_time,
        (select wu.`name` from tb_wx_ext_follow_user u
        left join tb_wx_user wu on wu.userid = u.user_id and wu.corp_id = u.corp_id
        where u.external_user_id = m.user_id
        and u.`status` = 0
        and u.corp_id = m.corp_id
        order by u.create_time desc limit 1)staff_name,
        m.departure_time,
        ifnull(c.avatar,#{avatar})avatar,
        c.type,
        c.corp_name
        from tb_wx_customer_group_member m
        left join tb_wx_customer_group g on m.group_id = g.chat_id
        left join tb_wx_ext_customer c on m.corp_id = c.corp_id and m.user_id = c.external_user_id
        where m.corp_id = #{corpId}
        and m.type = 2
        and g.dismiss_status = 0
        <if test="groupId != null and groupId  != ''">
            and m.group_id = #{groupId}
        </if>
        <if test="behaviorType == 1">
            and m.`status` = 0
            and g.`status` = 0
        <if test="beginTime != null ">
            <choose>
                <when test="timeScope != 3">
                    and date_format(#{beginTime}, '%Y-%m-%d') = date_format(m.join_time, '%Y-%m-%d')
                </when>
                <otherwise>
                    and date_format(m.join_time, '%Y-%m-%d') >= date_format(#{beginTime}, '%Y-%m-%d')
                </otherwise>
            </choose>
        </if>
            order by m.join_time desc
        </if>
        <if test="behaviorType == 2">
            and m.`status` = 2
            and g.`status` = 0
            <if test="beginTime != null ">
                <choose>
                    <when test="timeScope != 3">
                        and date_format(#{beginTime}, '%Y-%m-%d') = date_format(m.departure_time, '%Y-%m-%d')
                    </when>
                    <otherwise>
                        and date_format(m.departure_time, '%Y-%m-%d') >= date_format(#{beginTime}, '%Y-%m-%d')
                    </otherwise>
                </choose>
            </if>
            order by m.departure_time desc
        </if>
    </select>

    <select id="getGroupHeadImgByGroupList" resultType="String">
        select
           if (m.type = 1,u.avatar,c.avatar)avatar
        from tb_wx_customer_group_member m
        left join tb_wx_ext_customer c on m.user_id = c.external_user_id and m.corp_id = c.corp_id
        left join tb_wx_user u on u.userid = m.user_id and u.corp_id = m.corp_id
        where m.corp_id = #{corpId}
          and m.group_id = #{groupId}
          and m.`status` = 0
          and (u.avatar is not null or c.avatar is not null)
        order by m.join_time
        limit 9
    </select>

    <select id="statGroupMemberDailyData" resultType="ContactStatisticsDailyVo">
        SELECT
            c.date AS day,
        (SELECT COUNT(m.user_id) FROM tb_wx_customer_group_member m
            join tb_wx_customer_group g on m.group_id = g.chat_id
            WHERE m.status = '0' and g.del_flag = 0 and DATE(m.join_time) <![CDATA[<=]]> c.date) AS count
        FROM
        (
            SELECT DISTINCT DATE(join_time) AS date
            FROM tb_wx_customer_group_member
            WHERE corp_id = #{corpId}
            AND `status` = 0
            <if test="startTime != null">
              and join_time >= #{startTime}
            </if>
            <if test="endTime != null">
              and join_time &lt;= #{endTime}
            </if>
        ) as c
    </select>

    <select id="getBehaviorData4AddAndDelCount" resultType="ContactStatisticsDailyVo">
        select
        m.join_time date,
        count(distinct m.id) `count`
        from tb_wx_customer_group_member m
        where m.corp_id = #{corpId}
        <if test="type == 2">
            and m.status = 0
        </if>
        <if test="type == 3">
            and m.status != 0
        </if>
        and m.type = 2
        and date_format(m.join_time,'%Y-%m-%d') >= #{startTime}
        and date_format(m.join_time,'%Y-%m-%d') &lt;= #{endTime}
        group by date_format(m.join_time,'%Y-%m-%d')
    </select>
    <select id="getGroupSopList" resultType="com.cenker.scrm.pojo.vo.group.CustomerGroupSopVO">
        SELECT
            if(g.group_name is null or g.group_name = '','群聊',g.group_name)group_name,
            u.name AS owner,
            count(m.user_id) AS num,
            g.chat_id
        FROM
            tb_wx_customer_group g
        LEFT JOIN tb_wx_customer_group_member m ON g.chat_id = m.group_id
        LEFT JOIN tb_wx_user u ON g.owner = u.userid and u.del_flag ='1'
        WHERE
             g.corp_id=#{corpId}
          AND
             m.del_flag = 0
          AND m.`status` = 0
          AND m.quit_scene IS NULL
            <if test="groupName!=null and groupName !=''">
                and g.group_name like concat('%',#{groupName},'%')
            </if>
            <if test="ownerName!=null and ownerName!=''">
                and u.name like  concat('%',#{ownerName},'%')
            </if>
            <if test="beginTime!=null and beginTime!=''">
                and date_format(g.create_time, '%Y-%m-%d') >= #{beginTime}
            </if>
            <if test="endTime!=null and endTime!='' ">
                and date_format(g.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
            </if>
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and u.main_department in
                    <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and g.owner = #{wxUserId}
                </if>
            </if>
        GROUP BY
            g.chat_id
        ORDER BY
            g.create_time DESC,g.chat_id
    </select>

    <select id="getDailyTotalGroupCountGraph" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        SELECT
            d.date AS day,
            (SELECT COUNT(*) FROM tb_wx_customer_group g WHERE DATE(g.create_time) &lt;= d.date AND g.del_flag = '0') AS count
        FROM
            (SELECT DISTINCT DATE(create_time) AS date FROM tb_wx_customer_group WHERE create_time BETWEEN  #{dto.beginTime} AND #{dto.endTime} AND del_flag = '0') d
        ORDER BY
            d.date
    </select>

    <select id="statNewGroupMemberDailyData"
            resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
            date_format(join_time, '%Y-%m-%d') day,
            count(user_id) `count`
        from tb_wx_customer_group_member
        where corp_id = #{corpId}
          -- and status = 0
          and type = 2  -- 类型是客户，非员工
        <if test="startTime != null">
            and join_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and join_time &lt;= #{endTime}
        </if>
        group by day
    </select>

    <select id="statExitGroupMemberDailyData"
            resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
            date_format(departure_time, '%Y-%m-%d') day,
            count(user_id) `count`
        from tb_wx_customer_group_member
        where corp_id = #{corpId}
        and status != 0
        and type = 2  -- 类型是客户，非员工
        <if test="startTime != null">
            and departure_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and departure_time &lt;= #{endTime}
        </if>
        group by day
    </select>

    <select id="statActiveGroupData" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
        date_format( wcai.msg_time, '%Y-%m-%d' ) AS day,
        count( DISTINCT c.chat_id ) AS `count`
        from wk_chat_archive_info wcai
        JOIN tb_wx_customer_group c ON c.corp_id = wcai.corp_id AND c.chat_id = wcai.room_id
        where c.corp_id = #{corpId} and c.dismiss_status = 0 and wcai.chat_type = 2
        <if test="startTime != null">
            and wcai.msg_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wcai.msg_time &lt;= #{endTime}
        </if>
        group by day
    </select>


</mapper>