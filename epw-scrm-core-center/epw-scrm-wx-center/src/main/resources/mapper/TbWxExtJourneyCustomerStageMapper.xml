<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.journey.TbWxExtJourneyCustomerStageMapper">

    <select id="listStageCustomerInfoByJourneySop"
            resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            distinct(c.id),
            c.external_user_id,
                    c.name externalUserName
        from tb_wx_ext_journey_customer_stage cs
        join tb_wx_ext_customer c on cs.ext_customer_id = c.id
        where cs.stage_id = #{stageId}
        and cs.del_flag = 0
        and c.`status` = 0
    </select>

    <select id="excludeHasSelectedCustomer4All"
            resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            c.id,
            c.external_user_id,
            c.name externalUserName
        from tb_wx_ext_customer c
        where c.`status` = 0
          and c.id not in
              (
                  select
                      distinct ext_customer_id
                  from  tb_wx_ext_journey_customer_stage cs
                  where cs.journey_info_id = #{journeyId}
                    and cs.del_flag = 0)
        order by c.create_time desc
    </select>
</mapper>