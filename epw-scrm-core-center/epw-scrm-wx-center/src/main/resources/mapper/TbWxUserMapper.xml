<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.contact.TbWxUserMapper">

    <resultMap type="TbWxUser" id="TbWxUserResult">
        <result property="userid" column="userid" />
        <result property="name" column="name" />
        <result property="mobile" column="mobile" />
        <result property="department" column="department" />
        <result property="order" column="order" />
        <result property="position" column="position" />
        <result property="gender" column="gender" />
        <result property="email" column="email" />
        <result property="isLeaderInDept" column="is_leader_in_dept" />
        <result property="avatar" column="avatar" />
        <result property="thumbAvatar" column="thumb_avatar" />
        <result property="telephone" column="telephone" />
        <result property="alias" column="alias" />
        <result property="status" column="status" />
        <result property="qrCode" column="qr_code" />
        <result property="externalPosition" column="external_position" />
        <result property="address" column="address" />
        <result property="openUserid" column="open_userid" />
        <result property="mainDepartment" column="main_department" />
        <result property="corpId" column="corp_id" />
        <result property="remark" column="remark" />
        <result property="dimissionTime" column="dimission_time" />
        <result property="isAllocate" column="is_allocate" />
        <result property="customerTags" column="customer_tags" />
        <result property="mainDepartmentName" column="dept_name" />
        <result property="administrator" column="administrator" />
        <result property="sideAble" column="side_able" />
        <result property="corpName" column="company_name" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

    <resultMap id="TbWxUserTbWxUserExtattrResult" type="TbWxUser" extends="TbWxUserResult">
        <collection property="tbWxUserExtattrList" notNullColumn="corp_id" javaType="java.util.List" resultMap="TbWxUserExtattrResult" />
        <collection property="tbWxUserExtattrList" notNullColumn="open_userid" javaType="java.util.List" resultMap="TbWxUserExtattrResult" />
        <collection property="tbWxUserExtattrList" notNullColumn="userid" javaType="java.util.List" resultMap="TbWxUserExtattrResult" />
    </resultMap>

    <resultMap type="TbWxUserExtattr" id="TbWxUserExtattrResult">
        <result property="corpId" column="corp_id" />
        <result property="userId" column="user_id" />
        <result property="openUserid" column="open_userid" />
        <result property="type" column="type" />
        <result property="name" column="name" />
        <result property="textValue" column="text_value" />
        <result property="webUrl" column="web_url" />
        <result property="webTitle" column="web_title" />
    </resultMap>

    <resultMap id="TbWxUserTbWxUserExternalProfileResult" type="TbWxUser" extends="TbWxUserResult">
        <collection property="tbWxUserExtattrList" notNullColumn="corp_id" javaType="java.util.List" resultMap="TbWxUserExternalProfileResult" />
        <collection property="tbWxUserExtattrList" notNullColumn="open_userid" javaType="java.util.List" resultMap="TbWxUserExternalProfileResult" />
        <collection property="tbWxUserExtattrList" notNullColumn="userid" javaType="java.util.List" resultMap="TbWxUserExternalProfileResult" />
    </resultMap>

    <resultMap type="TbWxUserExternalProfile" id="TbWxUserExternalProfileResult">
        <result property="corpId" column="corp_id" />
        <result property="userId" column="user_id" />
        <result property="openUserid" column="open_userid" />
        <result property="type" column="type" />
        <result property="name" column="name" />
        <result property="textValue" column="text_value" />
        <result property="webUrl" column="web_url" />
        <result property="webTitle" column="web_title" />
        <result property="appid" column="appid" />
        <result property="pagepath" column="pagepath" />
    </resultMap>


    <sql id="selectTbWxUserVo">
        select u.userid, u.name, u.mobile, u.department, `order`, u.position, u.gender, u.email, u.is_leader_in_dept, u.avatar, u.thumb_avatar, u.telephone, u.alias, u.status, u.qr_code, u.external_position, u.address, u.open_userid, u.main_department, u.corp_id, u.remark, u.dimission_time, u.is_allocate, u.customer_tags from tb_wx_user u
    </sql>

    <select id="selectTbWxUserList" parameterType="TbWxUser" resultMap="TbWxUserResult">
        select u.userid, u.name, u.mobile, u.department, `order`, u.position, u.gender, u.email, u.is_leader_in_dept, u.avatar, u.thumb_avatar, u.telephone,
        u.alias, u.status, u.qr_code, u.external_position, u.address, u.open_userid, u.main_department, u.corp_id, u.remark, u.dimission_time, u.is_allocate, u.customer_tags, d.name as dept_name from tb_wx_user u left join tb_wx_department d on d.id = u.main_department and d.corp_id = u.corp_id
        where u.del_flag = '1'
            <if test="userid != null  and userid != ''">
                and u.userid = #{userid}
            </if>
            <if test="name != null  and name != ''">
                and u.name like concat('%', #{name}, '%')
            </if>
            <if test="mobile != null  and mobile != ''">
                and u.mobile = #{mobile}
            </if>
            <if test="department != null  and department != ''">
                and u.department = #{department}
            </if>
            <if test="order != null  and order != ''">
                and `order` = #{order}
            </if>
            <if test="position != null  and position != ''">
                and u.position = #{position}
            </if>
            <if test="gender != null  and gender != ''">
                and u.gender = #{gender}
            </if>
            <if test="email != null  and email != ''">
                and u.email = #{email}
            </if>
            <if test="isLeaderInDept != null  and isLeaderInDept != ''">
                and u.is_leader_in_dept = #{isLeaderInDept}
            </if>
            <if test="avatar != null  and avatar != ''">
                and u.avatar = #{avatar}
            </if>
            <if test="thumbAvatar != null  and thumbAvatar != ''">
                and u.thumb_avatar = #{thumbAvatar}
            </if>
            <if test="telephone != null  and telephone != ''">
                and u.telephone = #{telephone}
            </if>
            <if test="alias != null  and alias != ''">
                and u.alias = #{alias}
            </if>
            <if test="status != null ">
                and u.status = #{status}
            </if>
            <if test="qrCode != null  and qrCode != ''">
                and u.qr_code = #{qrCode}
            </if>
            <if test="externalPosition != null  and externalPosition != ''">
                and u.external_position = #{externalPosition}
            </if>
            <if test="address != null  and address != ''">
                and u.address = #{address}
            </if>
            <if test="openUserid != null  and openUserid != ''">
                and u.open_userid = #{openUserid}
            </if>
            <if test="mainDepartment != null  and mainDepartment != ''">
                and u.main_department = #{mainDepartment}
            </if>
            <if test="corpId != null  and corpId != ''">
                and u.corp_id = #{corpId}
            </if>
            <if test="dimissionTime != null ">
                and u.dimission_time = #{dimissionTime}
            </if>
            <if test="isAllocate != null ">
                and u.is_allocate = #{isAllocate}
            </if>
            <if test="customerTags != null  and customerTags != ''">
                and u.customer_tags = #{customerTags}
            </if>
    </select>

    <select id="selectTbWxUserById" parameterType="String" resultMap="TbWxUserTbWxUserExtattrResult">
        SELECT
            a.userid,
            a.NAME,
            a.mobile,
            a.department,
            a.ORDER,
            a.position,
            a.gender,
            a.email,
            a.is_leader_in_dept,
            a.avatar,
            a.thumb_avatar,
            a.telephone,
            a.alias,
            a.STATUS,
            a.qr_code,
            a.external_position,
            a.address,
            a.open_userid,
            a.main_department,
            a.corp_id,
            a.remark,
            a.dimission_time,
            a.is_allocate,
            a.customer_tags,
            a.administrator,
            c.company_name,
            a.del_flag,
            a.side_able
        FROM
          tb_wx_user a
        LEFT JOIN tb_wx_corp_config c on c.corp_id = a.corp_id and c.status = 0 and c.del_flag = 0
        WHERE
          a.userid = #{userId}
        and
          a.corp_id = #{corpId}
    </select>

    <insert id="insertTbWxUser" parameterType="TbWxUser">
        insert into tb_wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userid != null and userid != ''">userid,
            </if>
            <if test="name != null">name,
            </if>
            <if test="mobile != null">mobile,
            </if>
            <if test="department != null">department,
            </if>
            <if test="order != null">`order`,
            </if>
            <if test="position != null">position,
            </if>
            <if test="gender != null">gender,
            </if>
            <if test="email != null">email,
            </if>
            <if test="isLeaderInDept != null">is_leader_in_dept,
            </if>
            <if test="avatar != null">avatar,
            </if>
            <if test="thumbAvatar != null">thumb_avatar,
            </if>
            <if test="telephone != null">telephone,
            </if>
            <if test="alias != null">alias,
            </if>
            <if test="status != null">status,
            </if>
            <if test="qrCode != null">qr_code,
            </if>
            <if test="externalPosition != null">external_position,
            </if>
            <if test="address != null">address,
            </if>
            <if test="openUserid != null">open_userid,
            </if>
            <if test="mainDepartment != null">main_department,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="dimissionTime != null">dimission_time,
            </if>
            <if test="isAllocate != null">is_allocate,
            </if>
            <if test="customerTags != null">customer_tags,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userid != null and userid != ''">#{userid},
            </if>
            <if test="name != null">#{name},
            </if>
            <if test="mobile != null">#{mobile},
            </if>
            <if test="department != null">#{department},
            </if>
            <if test="order != null">#{order},
            </if>
            <if test="position != null">#{position},
            </if>
            <if test="gender != null">#{gender},
            </if>
            <if test="email != null">#{email},
            </if>
            <if test="isLeaderInDept != null">#{isLeaderInDept},
            </if>
            <if test="avatar != null">#{avatar},
            </if>
            <if test="thumbAvatar != null">#{thumbAvatar},
            </if>
            <if test="telephone != null">#{telephone},
            </if>
            <if test="alias != null">#{alias},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="qrCode != null">#{qrCode},
            </if>
            <if test="externalPosition != null">#{externalPosition},
            </if>
            <if test="address != null">#{address},
            </if>
            <if test="openUserid != null">#{openUserid},
            </if>
            <if test="mainDepartment != null">#{mainDepartment},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="dimissionTime != null">#{dimissionTime},
            </if>
            <if test="isAllocate != null">#{isAllocate},
            </if>
            <if test="customerTags != null">#{customerTags},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxUser" parameterType="TbWxUser">
        update tb_wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name =
                #{name},
            </if>
            <if test="mobile != null">mobile =
                #{mobile},
            </if>
            <if test="department != null">department =
                #{department},
            </if>
            <if test="order != null">`order` =
                #{order},
            </if>
            <if test="position != null">position =
                #{position},
            </if>
            <if test="gender != null">gender =
                #{gender},
            </if>
            <if test="email != null">email =
                #{email},
            </if>
            <if test="isLeaderInDept != null">is_leader_in_dept =
                #{isLeaderInDept},
            </if>
            <if test="avatar != null">avatar =
                #{avatar},
            </if>
            <if test="thumbAvatar != null">thumb_avatar =
                #{thumbAvatar},
            </if>
            <if test="telephone != null">telephone =
                #{telephone},
            </if>
            <if test="alias != null">alias =
                #{alias},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="qrCode != null">qr_code =
                #{qrCode},
            </if>
            <if test="externalPosition != null">external_position =
                #{externalPosition},
            </if>
            <if test="address != null">address =
                #{address},
            </if>
            <if test="openUserid != null">open_userid =
                #{openUserid},
            </if>
            <if test="mainDepartment != null">main_department =
                #{mainDepartment},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="dimissionTime != null">dimission_time =
                #{dimissionTime},
            </if>
            <if test="isAllocate != null">is_allocate =
                #{isAllocate},
            </if>
            <if test="customerTags != null">customer_tags =
                #{customerTags},
            </if>
        </trim>
        where userid = #{userid} and corp_id =#{corpId}
    </update>

    <insert id="batchTbWxUserExtattr">
        insert into tb_wx_user_extattr
        ( corp_id , user_id , open_userid , type , name , text_value , web_url , web_title) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.corpId}, #{item.userId}, #{item.openUserid}, #{item.type}, #{item.name}, #{item.textValue},
            #{item.webUrl}, #{item.webTitle})
        </foreach>
    </insert>



    <insert id="insertOrUpdateTbWxUser" parameterType="TbWxUser">
        insert into tb_wx_user(userid, name, mobile, department, `order`, position, gender, email, is_leader_in_dept, avatar, thumb_avatar,
        telephone, alias, status, qr_code, external_position, address, open_userid, main_department, corp_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userid}, #{item.name}, #{item.mobile}, #{item.department}, #{item.order}, #{item.position}, #{item.gender}, #{item.email}, #{item.isLeaderInDept}, #{item.avatar}, #{item.thumbAvatar},
            #{item.telephone}, #{item.alias}, #{item.status}, #{item.qrCode}, #{item.externalPosition}, #{item.address}, #{item.openUserid},  #{item.mainDepartment}, #{item.corpId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        NAME = VALUES( NAME ),
        mobile = VALUES( mobile ),
        department =VALUES( department ),
        `ORDER` =VALUES( `ORDER` ),
        position =VALUES( position ),
        gender =VALUES( gender ),
        email =VALUES( email ),
        is_leader_in_dept =VALUES( is_leader_in_dept ),
        avatar =VALUES( avatar ),
        thumb_avatar =VALUES( thumb_avatar ),
        telephone =VALUES( telephone ),
        alias =VALUES( alias ),
        STATUS =VALUES( STATUS ),
        qr_code =VALUES( qr_code ),
        external_position =VALUES( external_position ),
        address =VALUES( address ),
        main_department =VALUES( main_department )
    </insert>

    <select id="getTbWxUser" resultType="TbWxUser" >
        SELECT
            u.userid,
            u.NAME,
            u.mobile,
            u.department,
            `order`,
            u.position,
            u.gender,
            u.email,
            u.is_leader_in_dept,
            u.avatar,
            u.thumb_avatar,
            u.telephone,
            u.alias,
            u.STATUS,
            u.qr_code,
            u.external_position,
            u.address,
            u.open_userid,
            u.main_department,
            u.corp_id,
            u.remark,
            u.dimission_time,
            u.is_allocate,
            u.customer_tags
        FROM
            tb_wx_user u
        where
            u.corp_id = #{corpId}
        and u.del_flag = '1'
        and
            u.userid in
        <foreach item="item" collection="userIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="delExtattr">
        delete from tb_wx_user_extattr where (userid, corp_id) in
        <foreach item="item" collection="extattrList" open="(" separator="," close=")">
            (#{item.item}, #{item.corpId})
        </foreach>
    </delete>

    <delete id="delProfile">
        delete from tb_wx_user_external_profile where (userid, corp_id) in
        <foreach item="item" collection="profileList" open="(" separator="," close=")">
            (#{item.userId}, #{item.corpId})
        </foreach>
    </delete>

    <insert id="batchTbWxUserExternalProfile">
        insert into tb_wx_user_external_profile
        ( corp_id , user_id , open_userid , type , name , text_value , web_url , web_title, appid, pagePath) values
        <foreach item="item" index="index" collection="profileList" separator=",">
            ( #{item.corpId}, #{item.userId}, #{item.openUserid}, #{item.type}, #{item.name}, #{item.textValue},
            #{item.webUrl}, #{item.webTitle},#{item.appid}, #{item.pagePath})
        </foreach>
    </insert>

    <select id="getAllUserIdByCorpId" resultType="java.lang.String">
        select userid from tb_wx_user where corp_id = #{corpId} and del_flag ='1'
    </select>

    <select id="getWxUserByDeptId" parameterType="String" resultType="WechatUser">
        SELECT
            a.id,
            a.userid,
            a.NAME,
            CONCAT(LEFT(a.mobile,3), '*****' ,RIGHT(a.mobile,3)) mobile,
            a.department,
            a.ORDER,
            a.position,
            a.gender,
            a.email,
            a.is_leader_in_dept,
            a.avatar,
            a.thumb_avatar,
            a.telephone,
            a.alias,
            a.STATUS,
            a.qr_code,
            a.external_position,
            a.address,
            a.open_userid,
            a.main_department,
            -- a.corp_id,
            a.remark,
            a.dimission_time,
            a.is_allocate,
            a.customer_tags
        FROM
          tb_wx_user a
        WHERE
          a.main_department = #{deptId}
        and
          a.del_flag = '1'
        and
          a.corp_id = #{corpId}
        and
          a.status = '1'
        </select>
    <select id="getWxDimssionUserByDeptId" parameterType="String" resultType="WechatUser">
        SELECT
            a.userid,
            a.NAME,
            CONCAT(LEFT(a.mobile,3), '*****' ,RIGHT(a.mobile,3)) mobile,
            a.department,
            a.ORDER,
            a.position,
            a.gender,
            a.email,
            a.is_leader_in_dept,
            a.avatar,
            a.thumb_avatar,
            a.telephone,
            a.alias,
            a.STATUS,
            a.qr_code,
            a.external_position,
            a.address,
            a.open_userid,
            a.main_department,
            -- a.corp_id,
            a.remark,
            a.dimission_time,
            a.is_allocate,
            a.customer_tags
        FROM
          tb_wx_user a
        WHERE
          a.main_department = #{deptId}
        and
          a.del_flag = '2'
        and
          a.corp_id = #{corpId}
        </select>



    <select id="leaveNoAllocateUserList" resultType="WxLeaveUserVO" parameterType="WxLeaveUserDto">
        select * from (SELECT
        userid userId,
        name userName,
        dimission_time dimissionTime,
        (SELECT max(name) FROM tb_wx_department WHERE id= t.main_department and corp_id = t.corp_id ) as department,
        (SELECT COUNT(user_id) FROM tb_wx_ext_follow_user WHERE user_id=t.userid and corp_id = t.corp_id and status ='0') as
        allocateCustomerNum,
        (SELECT COUNT(chat_id) FROM tb_wx_customer_group WHERE `owner`=t.userid and corp_id = t.corp_id and dismiss_date is  null and status ='0') as
        allocateGroupNum
        FROM
        tb_wx_user t
        where t.corp_id = #{corpId}
        and t.del_flag != 1
        <if test="allocate != null">
            and is_allocate = #{allocate}
        </if>
        <if test="userIds != null and userIds.size() > 0  ">
           and t.userid in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="beginTime != null and beginTime != '' ">
            and DATE_FORMAT(t.dimission_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and DATE_FORMAT(t.dimission_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.dimission_time desc, t.is_allocate
        ) tt where (tt.allocateCustomerNum > 0 or tt.allocateGroupNum > 0)
    </select>

    <select id="getTbWxUserList" parameterType="TbWxUser" resultMap="TbWxUserResult">
        SELECT
            -- CONCAT(LEFT(u.userid,1), '*****') userid, 企业管理员修改需要改参数
            u.userid,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',CONCAT(LEFT(u.NAME,1), '***'),u.NAME) NAME,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',CONCAT(LEFT(u.mobile,3), '*****' ,RIGHT(u.mobile,3)),u.mobile) mobile,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',if(u.email is null or u.email = '','',CONCAT(LEFT(u.email,1), '***')),u.email) email,
            u.department,
            `order`,
            u.position,
            u.gender,
            u.is_leader_in_dept,
            u.avatar,
            u.thumb_avatar,
            u.telephone,
            u.alias,
            u.STATUS,
            u.qr_code,
            u.external_position,
            u.address,
            u.open_userid,
            u.main_department,
            -- u.corp_id,
            u.remark,
            u.dimission_time,
            u.is_allocate,
            u.customer_tags,
            d.NAME AS dept_name,
            -- 企业管理员
            u.administrator,
            -- 企微端权限
            u.side_able
        FROM
            tb_wx_user u
            LEFT JOIN tb_wx_department d ON d.id = u.main_department
            AND d.corp_id = u.corp_id
        WHERE
            u.del_flag = '1'
            <if test="mainDepartment != null and mainDepartment != ''">
                AND u.main_department = #{mainDepartment}
            </if>
            <if test="name != null and name != ''">
                AND u.name like concat('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND u.status = #{status}
            </if>
            AND u.corp_id = #{corpId}
        union
        SELECT
            -- CONCAT(LEFT(u.userid,1), '*****') userid,
            u.userid,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',CONCAT(LEFT(u.NAME,1), '***'),u.NAME) NAME,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',CONCAT(LEFT(u.mobile,3), '*****' ,RIGHT(u.mobile,3)),u.mobile) mobile,
            if (#{corpId}='wpQaM7BwAA8Eoplz3YIN7TLFXguef8Zw',if(u.email is null or u.email = '','',CONCAT(LEFT(u.email,1), '***')),u.email) email,
            u.department,
            `order`,
            u.position,
            u.gender,
            u.is_leader_in_dept,
            u.avatar,
            u.thumb_avatar,
            u.telephone,
            u.alias,
            u.STATUS,
            u.qr_code,
            u.external_position,
            u.address,
            u.open_userid,
            u.main_department,
            -- u.corp_id,
            u.remark,
            u.dimission_time,
            u.is_allocate,
            u.customer_tags,
            d.NAME AS dept_name,
            -- 企业管理员
            u.administrator,
            -- 企微端权限
            u.side_able
        FROM
            tb_wx_user u
            LEFT JOIN tb_wx_department d ON d.id = u.main_department
            AND d.corp_id = u.corp_id
        WHERE
            u.del_flag = '1'
            AND FIND_IN_SET(#{mainDepartment}, TRIM(TRAILING ']' FROM TRIM(LEADING '[' FROM u.department)))
            AND u.corp_id = #{corpId}
            <if test="name != null and name != ''">
                AND u.name = #{name}
            </if>
            <if test="status != null">
                AND u.status = #{status}
            </if>
    </select>

    <update id="setCorpAdminByUserId" >
        update tb_wx_user set administrator = #{administrator} where corp_id  = #{corpId} and userid = #{userid}
    </update>

    <select id="checkUserIsCorpAdmin" resultType="TbWxUser">
        select id,administrator,side_able,avatar from tb_wx_user where userid = #{userId} and corp_id = #{corpId}
    </select>

    <select id="querySysUserExist" resultType="TbWxUser">
        select
            *
        from  tb_wx_user u
                  left join sys_user su on u.userid = su.corp_user_id and u.corp_id = su.corp_id
        where u.corp_id = #{corpId}
          and u.del_flag =  1
          and u.`status` = 1
          and su.corp_user_id is null
    </select>

    <insert id="insertSysUser">
        INSERT INTO `sys_user`(`corp_id`, `dept_id`, `user_name`, `nick_name`,  `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`,`remark`, `corp_user_id`, `corp_open_user_id`)
        VALUES (#{corpId}, #{deptId}, #{userName}, #{nickName},#{avatar}, '$2a$10$ydYAw7IuZwgZHa3sBZiOP.TLm48SOxbjWnta4qJ.yLiTfuistQPlO', '0', '0', '', NULL, 'SYS', now(), '脚本生成', #{corpUserId},#{corpOpenUserId});
    </insert>

    <select id="selectTbWxUserByCorpIdAndOpenUserId" resultType="TbWxUser">
        select
            u.id,
            m.user_id userid,
            u.side_able
        from tb_wx_user u
                 left join tb_wx_user_map m on u.userid = m.user_id and u.corp_id = m.corp_id
        where u.del_flag = 1
          and u.corp_id = #{corpId}
          and m.open_user_id = #{openUserId}
        limit 1
    </select>

    <update id="setSideAbleByUserId" >
        update tb_wx_user set side_able = #{sideAble} where corp_id  = #{corpId} and userid = #{userid}
    </update>

    <select id="selectWorkLoginInfo" resultType="WorkbenchLoginInfo">
        select
            c.company_name corp_name,
            ifnull(u.`name`,#{userId})`name`,
            u.avatar,
            u.id,
            u.administrator,
            ifnull((select if(`status` = 0,1,3) from app_corp_permanent_token where corp_id = c.corp_id order by `status` limit 1),2)install_app
        from tb_wx_corp_config c
                 left join tb_wx_user u on c.corp_id = u.corp_id and u.userid = #{userId}
        where c.corp_id = #{corpId}
        order by c.status
        limit 1
    </select>

    <select id="selectAdmin4Corp" resultType="TbWxUser">
        select
            id,
            userid
        from tb_wx_user
        where del_flag = 1
          and `status` = 1
          and corp_id = (select corp_id from tb_wx_corp_config where id = #{corpConfigId})
          and administrator = 1
    </select>

    <select id="selectNoneDeptUserByCorpId" resultType="WechatUser">
        select id,userid,name,avatar,status from tb_wx_user where corp_id = #{corpId} and main_department is null and del_flag = 1
    </select>

    <select id="getTbWxUserNames" resultType="String">
        select
            name
        from tb_wx_user u
        where u.userid in
        <foreach item="item" collection="userList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and corp_id = #{corpId}
    </select>

    <select id="selectTbWxUserListByDepartmentList" resultType="TbWxUser">
        select userid
        from tb_wx_user
        where main_department in
        <foreach item="item" collection="departmentList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and corp_id = #{corpId}
    </select>

    <select id="getDailyTotalUserCountGraph" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        SELECT
            d.date AS day,
    (SELECT COUNT(*) FROM tb_wx_user u WHERE DATE(u.create_time) &lt;= d.date AND u.del_flag = '1') AS count
        FROM
            (SELECT DISTINCT DATE(create_time) AS date FROM tb_wx_user WHERE create_time BETWEEN  #{dto.beginTime} AND #{dto.endTime} AND del_flag = '1') d
        ORDER BY
            d.date
    </select>

</mapper>