<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.tag.TbWxCorpTagMapper">
    <resultMap type="TbWxCorpTag" id="TbWxCorpTagResult">
        <result property="groupId" column="group_id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="tagId" column="tag_id"/>
        <result property="order" column="order"/>
        <result property="createBy" column="create_by"/>
        <result property="corpId" column="corp_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTbWxCorpTagVo">
        select group_id,
               name,
               create_time,
               status,
               tag_id,
               `order`,
               create_by,
               corp_id,
               update_time,
               update_by,
               remark
        from tb_wx_corp_tag
    </sql>

    <select id="selectTbWxCorpTagList" parameterType="TbWxCorpTag" resultMap="TbWxCorpTagResult">
        <include refid="selectTbWxCorpTagVo"/>
        <where>
            <if test="groupId != null  and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="order != null ">
                and `order` = #{order}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
        </where>
    </select>

    <select id="selectTbWxCorpTagById" parameterType="String"
            resultMap="TbWxCorpTagResult">
        <include refid="selectTbWxCorpTagVo"/>
        where tag_id = #{tagId}
    </select>

    <insert id="insertTbWxCorpTag" parameterType="TbWxCorpTag">
        insert into tb_wx_corp_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null and groupId != ''">group_id,
            </if>
            <if test="name != null and name != ''">name,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="tagId != null">tag_id,
            </if>
            <if test="order != null">`order`,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="remark != null">remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null and groupId != ''">#{groupId},
            </if>
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="tagId != null">#{tagId},
            </if>
            <if test="order != null">#{order},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="remark != null">#{remark},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCorpTag" parameterType="TbWxCorpTag">
        update tb_wx_corp_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null and groupId != ''">group_id =
                #{groupId},
            </if>
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="order != null">`order` =
                #{order},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteTbWxCorpTagById" parameterType="String">
        delete
        from tb_wx_corp_tag
        where tag_id = #{tagId}
    </delete>

    <delete id="deleteTbWxCorpTagByIds" parameterType="String">
        delete from tb_wx_corp_tag where tag_id in
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>
    <delete id="deleteCorpTagByCorpId">
        delete
        from tb_wx_corp_tag
        where corp_id = #{corpId}
    </delete>

    <select id="selectTbWxCorpTagGroupList" resultType="TbWxCorpTag">
        select
        t.group_id, t.name, t.create_time, t.status, t.tag_id, t.`order`,
        t.create_by, t.corp_id, t.update_time, t.update_by, t.remark
        from tb_wx_corp_tag t
        where t.corp_id = #{corpId}
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>
          and t.group_id in
        <foreach item="groupId" collection="groupIds" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        order by t.create_time
    </select>

    <select id="selectTbWxCorpTagGroupIdList" resultType="java.lang.String">
        select
        t.tag_id
        from tb_wx_corp_tag t
        where t.corp_id = #{corpId}
        and t.status = 0
        and t.tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <select id="selectTbWxCorpTagNameList" resultType="java.lang.String">
        select
        t.name
        from tb_wx_corp_tag t
        where t.corp_id = #{corpId}
        and t.status = 0
        and t.tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and t.status = 0
    </select>

    <select id="selectValidTagByIds" resultType="com.cenker.scrm.pojo.vo.TagVO">
        SELECT
            t.tag_id,
            t.NAME AS tag_name,
            g.group_id,
            g.group_name
        FROM tb_wx_corp_tag t
        LEFT JOIN tb_wx_corp_tag_group g ON g.group_id = t.group_id
        WHERE t.STATUS = 0
        and t.tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>
</mapper>