<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionTimesetCheckMappingMapper">
    
    <resultMap type="CkSessionTimesetCheckMapping" id="CkSessionTimesetCheckMappingResult">
        <result property="id"    column="id"    />
        <result property="setId"    column="set_id"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />

    </resultMap>

    <sql id="selectCkSessionTimesetCheckMappingVo">
        select g.id, g.set_id, g.check_user_id, g.create_time, g.create_by,
               u.name,u.avatar
        from ck_session_timeset_check_mapping g,
             tb_wx_user u where   g.check_user_id = u.userid
    </sql>

    <select id="selectCkSessionTimesetCheckMappingList" parameterType="CkSessionTimesetCheckMapping" resultMap="CkSessionTimesetCheckMappingResult">
        <include refid="selectCkSessionTimesetCheckMappingVo"/>
        <if test="setId != null "> and g.set_id = #{setId}</if>
        <if test="checkUserId != null "> and g.check_user_id = #{checkUserId}</if>
    </select>
    
    <select id="selectCkSessionTimesetCheckMappingById" parameterType="Long" resultMap="CkSessionTimesetCheckMappingResult">
        <include refid="selectCkSessionTimesetCheckMappingVo"/>
        and g.id = #{id}
    </select>
        
    <insert id="insertCkSessionTimesetCheckMapping" parameterType="CkSessionTimesetCheckMapping" useGeneratedKeys="true" keyProperty="id">
        insert into ck_session_timeset_check_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="setId != null">set_id,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="setId != null">#{setId},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateCkSessionTimesetCheckMapping" parameterType="CkSessionTimesetCheckMapping">
        update ck_session_timeset_check_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="setId != null">set_id = #{setId},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCkSessionTimesetCheckMappingById" parameterType="Long">
        delete from ck_session_timeset_check_mapping where id = #{id}
    </delete>

    <delete id="deleteCkSessionTimesetCheckMappingByIds" parameterType="String">
        delete from ck_session_timeset_check_mapping where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCkSessionTimesetCheckMappingBySetId" parameterType="Long">
        delete from ck_session_timeset_check_mapping where set_id = #{setId}
    </delete>
</mapper>