<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.statistic.TbWxBusinessTagMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.statistic.TbWxBusinessTag">
            <id property="externalUserId" column="external_user_id" jdbcType="VARCHAR"/>
            <result property="statisticDate" column="statistic_date" jdbcType="DATE"/>
            <result property="messageSentCnt" column="message_sent_cnt" jdbcType="INTEGER"/>
            <result property="l7dMessageSentCnt" column="l7d_message_sent_cnt" jdbcType="INTEGER"/>
            <result property="l30dMessageSentCnt" column="l30d_message_sent_cnt" jdbcType="INTEGER"/>
            <result property="l7dActiveDays" column="l7d_active_days" jdbcType="INTEGER"/>
            <result property="l30dActiveDays" column="l30d_active_days" jdbcType="INTEGER"/>
            <result property="l7dMaterialReadCnt" column="l7d_material_read_cnt" jdbcType="INTEGER"/>
            <result property="l30dMaterialReadCnt" column="l30d_material_read_cnt" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        external_user_id, statistic_date, message_sent_cnt,l7d_message_sent_cnt,
        l30d_message_sent_cnt,l7d_active_days,l30d_active_days,
        l7d_material_read_cnt,l30d_material_read_cnt
    </sql>

    <insert id="saveData">
        INSERT INTO tb_wx_business_tag (external_user_id, statistic_date,message_sent_cnt,l7d_message_sent_cnt,
        l30d_message_sent_cnt,l7d_active_days,l30d_active_days,
        l7d_material_read_cnt,l30d_material_read_cnt, internal_employee)
        SELECT
            c.external_user_id,
            CURRENT_DATE AS statistic_date,
            COALESCE ( message_sent_cnt, 0 ) AS message_sent_cnt,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ),
                        NULL,
                        COALESCE ( l7d_message_sent_cnt, 0 )) AS l7d_message_sent_cnt,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ),
                        NULL,
                        COALESCE ( l30d_message_sent_cnt, 0 )) AS l30d_message_sent_cnt,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ),
                        NULL,
                        COALESCE ( l7d_active_days, 0 )) AS l7d_active_days,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ),
                        NULL,
                        COALESCE ( l30d_active_days, 0 )) AS l30d_active_days,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ),
                        NULL,
                        COALESCE ( l7d_material_read_cnt, 0 )) AS l7d_material_read_cnt,
            IF
                (
                        c.first_add_date > DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ),
                        NULL,
                        COALESCE ( l30d_material_read_cnt, 0 )) AS l30d_material_read_cnt,
            IF(c.custno IS NULL, NULL, IF(sc.custno IS NULL, 'N', 'Y')) AS internal_employee
        FROM
            tb_wx_ext_customer c
                LEFT JOIN (
                SELECT
                    from_id,
                    count( DISTINCT id ) AS message_sent_cnt,
                    count( CASE WHEN msg_time >= DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ) THEN id END ) AS l7d_message_sent_cnt,
                    count( CASE WHEN msg_time >= DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ) THEN id END ) AS l30d_message_sent_cnt,
                    count( DISTINCT CASE WHEN msg_time >= DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ) THEN msg_day END ) AS l7d_active_days,
                    count( DISTINCT CASE WHEN msg_time >= DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ) THEN msg_day END ) AS l30d_active_days
                FROM
                    wk_chat_archive_info
                GROUP BY
                    from_id
            ) wcai ON wcai.from_id = c.external_user_id
                LEFT JOIN (
                SELECT
                    wu.union_id,
                    count( CASE WHEN cr.create_time >= DATE_SUB( CURRENT_DATE, INTERVAL 7 DAY ) THEN cr.id END ) AS l7d_material_read_cnt,
                    count( CASE WHEN cr.create_time >= DATE_SUB( CURRENT_DATE, INTERVAL 30 DAY ) THEN cr.id END ) AS l30d_material_read_cnt
                FROM
                    tb_wx_radar_content_record cr
                        JOIN mp_wx_user wu ON wu.id = cr.customer_id
                GROUP BY
                    cr.customer_id
            ) r ON r.union_id = c.union_id
        LEFT JOIN t_staff sc ON sc.custno = c.custno
        WHERE
            c.STATUS = '0'
          AND c.del_flag = 0
    </insert>
</mapper>
