<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.moment.TbWxMomentSendUserMapper">

    <select id="getMomentSendUserList" resultType="SendMomentUserListVo">
        select
        u.id,
        u.moment_task_id,
        u.publish_status,
        wu.avatar,
        wu.`name`,
        u.user_id,
        (select count(1) from tb_wx_moment_send_customer where moment_task_id = u.moment_task_id and user_id = u.user_id
        and visible = 1)visible_cnt,
        (select count(1) from tb_wx_moment_interact i where i.moment_task_id = u.moment_task_id and i.user_id =
        u.user_id and i.type = 2)comment_cnt,
        (select count(1) from tb_wx_moment_interact i where i.moment_task_id = u.moment_task_id and i.user_id =
        u.user_id and i.type = 1)like_cnt
        from tb_wx_moment_send_user u
        left join tb_wx_user wu on u.user_id = wu.userid and u.corp_id = wu.corp_id
        where u.moment_task_id = #{id}
        <if test="publishStatus != null">
            and u.publish_status = #{publishStatus}
        </if>
        group by u.user_id
        order by u.publish_status desc
    </select>

    <select id="getMomentSendCusList" resultType="SendMomentUserListVo">
        select
           i.id,
           ifnull(c.name,u.name)name,
           <if test="sendScope == 1">
               i.interact_time like_time,
           </if>
           <if test="sendScope == 2">
               i.interact_time comment_time,
           </if>
           ifnull(c.avatar,u.avatar)avatar
        from tb_wx_moment_interact i
        left join tb_wx_ext_customer c on i.corp_id = c.corp_id and i.external_user_id = c.external_user_id
        left join tb_wx_user u on u.corp_id = i.corp_id and i.external_user_id = u.userid
        where i.moment_task_id = #{momentTaskId}
        and i.user_id = #{userId}
        <if test="sendScope != null">
            and i.type = #{sendScope}
        </if>
        order by i.interact_time desc
    </select>

    <select id="selectSendUser" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxMomentSendUser">
        SELECT DISTINCT su.*
        FROM tb_wx_moment_send_user su
        JOIN tb_wx_user u ON su.user_id = u.userid
        LEFT JOIN tb_wx_moment_send_customer sc ON su.moment_task_id = sc.moment_task_id AND su.user_id = sc.user_id
        where su.moment_task_id = #{momentTaskId}
         AND u.status = 1 AND u.del_flag = 1
            <if test="publishStatus != null">
                AND su.publish_status = #{publishStatus}
            </if>
            <if test="queryCustomerStatus != null">
                <choose>
                    <when test="queryCustomerStatus == 0">
                        AND sc.user_id IS NULL
                    </when>
                    <when test="queryCustomerStatus == 1">
                        AND sc.user_id IS NOT NULL
                    </when>
                </choose>
            </if>
    </select>
</mapper>