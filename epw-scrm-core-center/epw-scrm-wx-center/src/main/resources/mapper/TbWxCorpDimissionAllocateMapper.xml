<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.corp.TbWxCorpDimissionAllocateMapper">
    <select id="leaveAllocatedUserList" resultType="WxLeaveUserVO" parameterType="WxLeaveUserDto">
        select * from (SELECT
        userid userId,
        name userName,
        dimission_time dimissionTime,
        (SELECT max(name) FROM tb_wx_department WHERE id= t.main_department and corp_id = t.corp_id ) as department,
        (SELECT COUNT(id) FROM tb_wx_corp_dimission_allocate WHERE handover_userid=t.userid
        and corp_id = t.corp_id and type ='1' and status ='0') as
        allocateCustomerNum,
        (SELECT COUNT(id) FROM tb_wx_corp_dimission_allocate WHERE handover_userid=t.userid
        and corp_id = t.corp_id and type ='2' and status ='0') as
        allocateGroupNum
        FROM
        tb_wx_user t
        where t.corp_id = #{corpId}
        <if test="allocate != null">and is_allocate = #{allocate}
        </if>
        <if test="corpUserName != null and corpUserName !=''">
            and t.name =#{corpUserName}
        </if>
        <if test="userIds != null and userIds.size() > 0  ">
            and t.userid in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="beginTime != null and beginTime != '' ">
            and DATE_FORMAT(t.dimission_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and DATE_FORMAT(t.dimission_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        ) tt where (tt.allocateCustomerNum > 0 or tt.allocateGroupNum > 0)
    </select>
    <select id="queryAllocateDetailInfo" parameterType="WxLeaveUserDto"
            resultType="AllocateDetailInfoVO">
        select (select MAX(name) from tb_wx_ext_customer where external_user_id = t.allocate_id and corp_id =#{corpId}) name,
        t.allocate_id allocateId,t.type,
        (select max(name) from tb_wx_user where userid = t.take_over_user_id and corp_id =#{corpId}) corpUserName,
        (select max(t1.name) from tb_wx_user t2,tb_wx_department t1 where t2.userid = t.take_over_user_id
        and t2.corp_id = t1.corp_id  and t1.corp_id =#{corpId}) departmentName,
        t.allocate_time allocateTime,null
         from tb_wx_corp_dimission_allocate t
         where t.handover_userid =#{handOverUserId}
          and t.corp_id = #{corpId} and t.type ='1'
        <if test="beginTime != null and beginTime != '' ">
            and DATE_FORMAT(t.allocate_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and DATE_FORMAT(t.allocate_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        union all
        select (select MAX(group_name) from tb_wx_customer_group where chat_id = t.allocate_id and corp_id =#{corpId}) name,
        t.allocate_id allocateId,t.type,
        (select max(name) from tb_wx_user where userid = t.take_over_user_id and corp_id =#{corpId}) corpUserName,
        (select max(t1.name) from tb_wx_user t2,tb_wx_department t1 where t2.userid = t.take_over_user_id
        and t2.corp_id = t1.corp_id  and t1.corp_id =#{corpId}) departmentName,
        t.allocate_time allocateTime,
        (select count(id) from tb_wx_customer_group_member where group_id = t.allocate_id and status ='0') cnt
        from tb_wx_corp_dimission_allocate t
        where t.handover_userid =#{handOverUserId}
        and t.corp_id = #{corpId} and t.type ='2'
        <if test="beginTime != null and beginTime != '' ">
            and DATE_FORMAT(t.allocate_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and DATE_FORMAT(t.allocate_time, '%Y-%m-%d')<![CDATA[ <= ]]> #{endTime}
        </if>
    </select>
</mapper>