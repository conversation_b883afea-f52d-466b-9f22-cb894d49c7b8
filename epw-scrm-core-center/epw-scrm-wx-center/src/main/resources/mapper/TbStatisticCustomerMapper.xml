<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticCustomerMapper">
    <insert id="saveStatisticDateByDay">
        INSERT INTO tb_statistic_customer (
                                            statistic_date, custno, external_user_id, customer_name, customer_avatar,
                                            customer_gender, status, is_auth, add_time, auth_time, loss_time,
                                            alone_chat_num, group_chat_num, customer_group_num, radar_receive_times,
                                            radar_click_times, radar_forward_times, average_read_time
                                            )
        SELECT
        str_to_date(#{statDate}, '%Y-%m-%d') AS statistic_date,
        cb.custno,
        cb.external_user_id,
        cb.customer_name,
        cb.customer_avatar,
        cb.customer_gender,
        cb.status,
        (case when at.auth_time is not null then 1 else 0 end) as is_auth,
        rt.add_time,
        at.auth_time,
        rt.loss_time,
        IFNULL(cs.alone_chat_num, 0) AS alone_chat_num,
        IFNULL(cs.group_chat_num, 0) AS group_chat_num,
        IFNULL(gc.customer_group_num, 0) AS customer_group_num,
        rs.radar_receive_times,
        IFNULL(rs.radar_click_times, 0) AS radar_click_times,
        IFNULL(rs.radar_forward_times, 0) AS radar_forward_times,
        IFNULL(rs.average_read_time, 0) AS average_read_time
        FROM
        (
        SELECT
        a.custno,
        a.external_user_id,
        a.name AS customer_name,
        a.avatar AS customer_avatar,
        a.gender AS customer_gender,
        a.`status`,
        a.is_auth,
        a.union_id
        FROM
        tb_wx_ext_customer a
        WHERE
        date(a.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        AND EXISTS (
        SELECT 1 FROM tb_wx_ext_follow_user g
        WHERE g.external_user_id = a.external_user_id
        AND date(g.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        AND (g.`status` = 0 or date(g.del_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d') or date(g.update_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        )
        ) AS cb
        LEFT JOIN
        (
        SELECT
        c.from_id,
        COUNT(CASE WHEN c.chat_type = 1 THEN 1 END) AS alone_chat_num,
        COUNT(CASE WHEN c.chat_type = 2 THEN 1 END) AS group_chat_num
        FROM
        wk_chat_archive_info c
        WHERE
        date(c.msg_time) = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        c.from_id
        ) AS cs ON cs.from_id = cb.external_user_id
        LEFT JOIN
        (
        SELECT
        d.union_id,
        NULL AS radar_receive_times,
        COUNT(DISTINCT e.id) AS radar_click_times,
        IFNULL(SUM(e.forward_num), 0) AS radar_forward_times,
        FLOOR(IFNULL(SUM(e.read_time) / COUNT(DISTINCT d.id), 0)) AS average_read_time
        FROM
        mp_wx_user d
        LEFT JOIN
        tb_wx_radar_content_record e ON e.customer_id = d.id
        WHERE
        date(e.create_time) = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        d.union_id
        ) AS rs ON rs.union_id = cb.union_id
        LEFT JOIN
        (
        SELECT
        union_id,
        MAX(bind_time) AS auth_time
        FROM
        tb_wx_customer_auth_record
        WHERE
        date(bind_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (update_time IS NULL OR update_time &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        GROUP BY
        union_id
        ) AS at ON at.union_id = cb.union_id
        LEFT JOIN
        (
        SELECT
        external_user_id,
        MIN(
        case
        when status = 0 then create_time
        when status != 0 and del_time &gt;= str_to_date(#{statDate}, '%Y-%m-%d') then create_time
        else NULL
        end
        ) AS add_time,
        MAX(
        case
        when status != 0 and date(del_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d') then del_time
        else NULL
        end
        ) AS loss_time
        FROM
        tb_wx_ext_follow_user
        where date(create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        external_user_id
        ) AS rt ON rt.external_user_id = cb.external_user_id
        LEFT JOIN
        (
        SELECT
        user_id,
        COUNT(DISTINCT group_id) AS customer_group_num
        FROM
        tb_wx_customer_group_member
        WHERE
        date(join_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (departure_time IS NULL OR departure_time &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        GROUP BY
        user_id
        ) AS gc ON gc.user_id = cb.external_user_id;
    </insert>

    <select id="summary" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticCustomerSummaryVo">
        SELECT
            customerTotal,
            customerAuthTotal,
            customerNewAuthTotal,
            customerNewTotal,
            customerLossTotal,
            (t.customerNewTotal - t.customerLossTotal) AS netNewTotal,
            aloneChatTotal,
            customerGroupChatTotal,
            customerActiveTotal
        FROM
        (SELECT
            COUNT(DISTINCT CASE WHEN `status` = 0 THEN external_user_id END) AS customerTotal,
            COUNT(DISTINCT CASE WHEN is_auth = 1 THEN external_user_id END) AS customerAuthTotal,
            COUNT(DISTINCT CASE WHEN is_auth = 1 AND date(auth_time) BETWEEN str_to_date(#{beginTime},'%Y-%m-%d') AND str_to_date(#{endTime},'%Y-%m-%d') THEN external_user_id END) AS customerNewAuthTotal,
            COUNT(DISTINCT CASE WHEN date(add_time) BETWEEN str_to_date(#{beginTime},'%Y-%m-%d') AND str_to_date(#{endTime},'%Y-%m-%d')  THEN external_user_id END) AS customerNewTotal,
            COUNT(DISTINCT CASE WHEN `status` != 0 AND date(loss_time) BETWEEN str_to_date(#{beginTime},'%Y-%m-%d') AND str_to_date(#{endTime},'%Y-%m-%d') THEN external_user_id END) AS customerLossTotal,
            IFNULL(SUM(alone_chat_num), 0) AS aloneChatTotal,
            IFNULL(SUM(group_chat_num), 0) AS customerGroupChatTotal,
            COUNT(DISTINCT CASE WHEN alone_chat_num > 0 OR group_chat_num > 0 THEN external_user_id END) AS customerActiveTotal
        FROM
            tb_statistic_customer
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            and id in (SELECT distinct statistic_customer_id FROM tb_statistic_customer_detail
            <where>
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and userid = #{wxUserId}
                </if>
            </where>)
        </if>
        ) as t
    </select>

    <select id="graph" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticCustomerSummaryVo">
        SELECT
            statistic_date,
            customerTotal,
            customerAuthTotal,
            customerNewAuthTotal,
            customerNewTotal,
            customerLossTotal,
            (t.customerNewTotal - t.customerLossTotal) AS netNewTotal,
            aloneChatTotal,
            customerGroupChatTotal,
            customerActiveTotal
        FROM
            (SELECT
                 statistic_date,
                 COUNT(DISTINCT CASE WHEN `status` = 0 THEN external_user_id END) AS customerTotal,
                 COUNT(DISTINCT CASE WHEN is_auth = 1 THEN external_user_id END) AS customerAuthTotal,
                 COUNT(DISTINCT CASE WHEN is_auth = 1 AND date(auth_time) = statistic_date THEN external_user_id END) AS customerNewAuthTotal,
                 COUNT(DISTINCT CASE WHEN date(add_time) = statistic_date  THEN external_user_id END) AS customerNewTotal,
                 COUNT(DISTINCT CASE WHEN `status` != 0 AND date(loss_time) = statistic_date THEN external_user_id END) AS customerLossTotal,
                 IFNULL(SUM(alone_chat_num), 0) AS aloneChatTotal,
                 IFNULL(SUM(group_chat_num), 0) AS customerGroupChatTotal,
                 COUNT(DISTINCT CASE WHEN alone_chat_num > 0 OR group_chat_num > 0 THEN external_user_id END) AS customerActiveTotal
             FROM
                 tb_statistic_customer
             WHERE
                 statistic_date BETWEEN #{beginTime} AND #{endTime}
                <!-- 权限控制 -->
                <if test="dataScope == null or dataScope != '1'.toString()">
                    and id in (SELECT distinct statistic_customer_id FROM tb_statistic_customer_detail
                    <where>
                        <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                            and dept_id in
                            <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                        </if>
                        <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                            and userid = #{wxUserId}
                        </if>
                    </where>)
                </if>
             GROUP BY statistic_date
             ORDER BY statistic_date) as t
    </select>

    <select id="list" resultType="com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomer">
        SELECT
            DISTINCT
            c.id,
            c.statistic_date,
            c.custno,
            c.external_user_id,
            IFNULL(ec.`name`,c.customer_name) customer_name,
            IFNULL(ec.avatar,c.customer_avatar) customer_avatar,
            c.customer_gender,
            c.`status`,
            c.is_auth,
            c.add_time,
            c.auth_time,
            c.loss_time,
            c.alone_chat_num,
            c.group_chat_num,
            c.customer_group_num,
            c.radar_receive_times,
            c.radar_click_times,
            c.radar_forward_times,
            c.average_read_time
        FROM tb_statistic_customer c
        LEFT JOIN tb_statistic_customer_detail d ON c.id = d.statistic_customer_id
        LEFT JOIN tb_wx_ext_customer ec ON ec.external_user_id = c.external_user_id
        WHERE
            c.statistic_date BETWEEN #{beginTime} AND #{endTime}
            <if test="userName != null and userName != ''">
                AND d.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="customerId != null and customerId != ''">
                AND c.external_user_id = #{customerId}
            </if>
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and d.dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and d.userid = #{wxUserId}
                </if>
            </if>
        <if test="orderByColumn = null or orderByColumn = ''">
            ORDER BY c.statistic_date DESC
        </if>

    </select>
</mapper>
