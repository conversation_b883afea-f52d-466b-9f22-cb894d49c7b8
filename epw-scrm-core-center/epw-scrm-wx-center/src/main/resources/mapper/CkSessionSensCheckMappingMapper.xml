<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionSensCheckMappingMapper">
    
    <resultMap type="CkSessionSensCheckMapping" id="CkSessionSensCheckMappingResult">
        <result property="id"    column="id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />

    </resultMap>

    <sql id="selectCkSessionSensCheckMappingVo">
        select g.id, g.rule_id, g.check_user_id, g.create_time, g.create_by, g.update_time,
               g.update_by, u.name,u.avatar
        from ck_session_sens_check_mapping g,tb_wx_user u where g.check_user_id = u.userid


    </sql>

    <select id="selectCkSessionSensCheckMappingList" parameterType="CkSessionSensCheckMapping" resultMap="CkSessionSensCheckMappingResult">
        <include refid="selectCkSessionSensCheckMappingVo"/>

            <if test="ruleId != null "> and g.rule_id = #{ruleId}</if>
            <if test="checkUserId != null "> and g.check_user_id = #{checkUserId}</if>

    </select>
    
    <select id="selectCkSessionSensCheckMappingById" parameterType="Long" resultMap="CkSessionSensCheckMappingResult">
        <include refid="selectCkSessionSensCheckMappingVo"/>
        and id = #{id}
    </select>
        
    <insert id="insertCkSessionSensCheckMapping" parameterType="CkSessionSensCheckMapping">
        insert into ck_session_sens_check_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateCkSessionSensCheckMapping" parameterType="CkSessionSensCheckMapping">
        update ck_session_sens_check_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCkSessionSensCheckMappingById" parameterType="Long">
        delete from ck_session_sens_check_mapping where id = #{id}
    </delete>

    <delete id="deleteCkSessionSensCheckMappingByIds" parameterType="String">
        delete from ck_session_sens_check_mapping where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCkSessionSensCheckMappingByRuleId" parameterType="Long">
        delete from ck_session_sens_check_mapping where rule_id = #{ruleId}
    </delete>


</mapper>