<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sop.SopCustomerInfoMapper">


    <select id="excludeHasSelectedCustomer4All"
            resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        SELECT distinct(c.external_user_id),
                       c.name externalUserName
        FROM tb_wx_ext_customer c
        WHERE c.`status` = 0
          AND c.external_user_id NOT IN
              (SELECT sop_customer.external_user_id
               FROM ck_sop_customer_info sop_customer
               WHERE sop_customer.sop_id = #{sopId}
                 AND sop_customer.is_accord = 1)
    </select>

    <select id="queryFollowUserByExtUserId" resultType="com.cenker.scrm.pojo.vo.contact.CorpUserInfoVO">
        select t1.userName,
               t1.userid userId
        from (
                 select u.`name`   userName,
                        u.userid,
                        u.avatar,
                        (select fu.create_time
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id = t.external_user_id
                           and fu.user_id = u.userid
                         order by fu.`status`, fu.create_time desc
                         limit 1)  addDate,
                        (
                            select fu.`status`
                            from tb_wx_ext_follow_user fu
                            where fu.external_user_id = t.external_user_id
                              and fu.user_id = u.userid
                            order by fu.`status`
                            limit 1
                        )          status,
                        u.del_flag staff_status,
                        t.external_user_id,
                        t.corp_id
                 from tb_wx_ext_follow_user t
                          left join tb_wx_user u on t.corp_id = u.corp_id and t.user_id = u.userid
                 where t.external_user_id = #{externalUserId}
                 group by t.user_id
             ) t1
        order by t1.staff_status, t1.`status`, t1.addDate desc
    </select>

    <resultMap id="massCustomerSopCustomerListMap" type="com.cenker.scrm.pojo.vo.sop.MassCustomerSopCustomerVO">
        <id column="id" property="sopCustomerInfoId"/>
        <result column="name" property="customerName"/>
        <result column="type" property="customerType"/>
        <result column="gender" property="customerGender"/>
        <result column="avatar" property="customerAvatar"/>
        <result column="corp_name" property="customerCorpName"/>
        <result column="external_user_id" property="externalUserId"/>
        <result column="join_time" property="joinTime"/>
        <collection property="addUserList" column="{externalUserId=external_user_id}" select="queryFollowUserByExtUserId"/>
    </resultMap>

    <select id="massCustomerSopCustomerList" resultMap="massCustomerSopCustomerListMap">
        select t.* from (
             select sc.id,
                    c.name,
                    c.type,
                    c.gender,
                    c.avatar,
                    ifnull(c.corp_name,'')corp_name,
                    c.external_user_id,
                    sc.join_time,
                    (select group_concat(distinct fu.user_id) from tb_wx_ext_follow_user fu  where fu.external_user_id = c.external_user_id group by fu.external_user_id)user_ids
             from ck_sop_customer_info sc
                      join tb_wx_ext_customer c on sc.external_user_id = c.external_user_id
             where sop_id = #{sopId}
               and sc.is_deleted = 0
               <if test="customerName != null and customerName != ''">
                   and (c.name like concat('%',#{customerName},'%') or c.corp_name like concat('%',#{customerName},'%'))
               </if>
        )t
        <if test="userId != null and userId != ''">
            where t.user_ids like concat('%',#{userId},'%')
        </if>
        order by t.join_time desc
    </select>

    <select id="neverSopCustomer" resultType="com.cenker.scrm.pojo.vo.sop.ExternalSopCustomerVO">
        select
        t.*,
        (
        select create_time from
        tb_wx_ext_follow_user
        where user_id = t.first_add_user_id
        and external_user_id = t.external_user_id
        order by `status` desc limit 1)add_date,
        (select name from tb_wx_user where userid = t.first_add_user_id limit 1)add_user_name,
        (case when t.add_way_status = 1 then ifnull((select remark from tb_wx_contact where state = t.state limit 1),'扫描二维码')
        when t.add_way_status = 2 then '搜索手机号'
        when t.add_way_status = 3 then '名片分享'
        when t.add_way_status = 4 then '群聊'
        when t.add_way_status = 5 then '手机通讯录'
        when t.add_way_status = 6 then '微信联系人'
        when t.add_way_status = 7 then '来自微信的添加好友申请'
        when t.add_way_status = 8 then '安装应用时自动添加'
        when t.add_way_status = 9 then '搜索邮箱'
        when t.add_way_status = 10 then '视频号添加'
        when t.add_way_status = 11 then '通过日程参与人添加'
        when t.add_way_status = 12 then '通过会议参与人添加'
        when t.add_way_status = 13 then '添加微信好友对应的企业微信'
        when t.add_way_status = 14 then '通过智慧硬件专属客服添加'
        when t.add_way_status = 15 then '通过上门服务客服添加'
        when t.add_way_status = 16 then '通过获客链接添加'
        when t.add_way_status = 17 then '通过定制开发添加'
        when t.add_way_status = 18 then '通过需求回复添加'
        when t.add_way_status = 21 then '通过第三方售前客服添加'
        when t.add_way_status = 22 then '通过可能的商务伙伴添加'
        when t.add_way_status = 24 then '通过接受微信账号收到的好友申请添加'
        when t.add_way_status = 201 then '内部成员共享'
        when t.add_way_status = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way
        from (
        select
        c.id extCustomerId,
        c.external_user_id,
        c.name customerName,
        c.type customerType,
        c.corp_name customerCorpName,
        c.avatar customerAvatar,
        (select u.user_id from tb_wx_ext_follow_user u
        left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
        where u.external_user_id = c.external_user_id
        order by twu.del_flag,u.create_time
        limit 1
        )first_add_user_id,
        (select ifnull(fu.add_way, 0)
        from tb_wx_ext_follow_user fu
        where fu.external_user_id = c.external_user_id
        and fu.corp_id = c.corp_id
        and fu.user_id = (select u.user_id from tb_wx_ext_follow_user u
        left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
        where u.external_user_id = c.external_user_id
        order by twu.del_flag,u.create_time
        limit 1)
        and fu.`status` = 0
        limit 1)add_way_status,
        (select fu.state
        from tb_wx_ext_follow_user fu
        where fu.external_user_id = c.external_user_id
        and fu.corp_id = c.corp_id
        and fu.user_id = (select u.user_id from tb_wx_ext_follow_user u
        left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
        where u.external_user_id = c.external_user_id
        order by twu.del_flag,u.create_time
        limit 1)
        and fu.`status` = 0
        limit 1)state
        from tb_wx_ext_customer c
        where c.`status` = 0
        and c.id not in
        (
            select
            distinct external_user_id
            from  ck_sop_customer_info cs
            where cs.sop_id = #{sopId}
            and cs.is_deleted = 0
        )
        <if test="customerName != null and customerName != ''">
            and (c.name like concat('%',#{customerName},'%') or c.corp_name like concat('%',#{customerName},'%'))
        </if>
        order by c.create_time desc
        )t
    </select>

    <select id="selectCustomer4All" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        SELECT distinct(c.external_user_id) externalUserId,
                       c.name externalUserName
        FROM tb_wx_ext_customer c
        WHERE c.`status` = 0
    </select>

    <select id="getUserDistinctCustomer" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        SELECT
        *
        FROM tb_wx_ext_follow_user
        WHERE c.`status` = 0
    </select>




    <select id="qrySendMsgCustUserList" parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserDTO" resultType="com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO">
        select * from tb_wx_ext_customer  r where r.external_user_id in
        <if test="where != null and where != ''">
                                           ( ${where})
        </if>
    </select>


</mapper>