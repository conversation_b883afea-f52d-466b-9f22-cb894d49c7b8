<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.tag.VFScrmLabelMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.wechat.VFScrmLabel">
            <id property="custno" column="custno" jdbcType="INTEGER"/>
            <result property="cGenName" column="c_gen_name" jdbcType="VARCHAR"/>
            <result property="cAgeStra" column="c_age_stra" jdbcType="VARCHAR"/>
            <result property="cBthDate" column="c_bth_date" jdbcType="VARCHAR"/>
            <result property="cCityName" column="c_city_name" jdbcType="VARCHAR"/>
            <result property="cProvName" column="c_prov_name" jdbcType="VARCHAR"/>
            <result property="cOccoName" column="c_occo_name" jdbcType="VARCHAR"/>
            <result property="cIfDsCust" column="c_if_ds_cust" jdbcType="VARCHAR"/>
            <result property="cIfCsCust" column="c_if_cs_cust" jdbcType="VARCHAR"/>
            <result property="cIfIaCustRul" column="c_if_ia_cust_rul" jdbcType="VARCHAR"/>
            <result property="cDayHldAstStra" column="c_day_hld_ast_stra" jdbcType="VARCHAR"/>
            <result property="cL1yAvgHldAstStra" column="c_l1y_avg_hld_ast_stra" jdbcType="VARCHAR"/>
            <result property="cL3yAvgHldAstStra" column="c_l3y_avg_hld_ast_stra" jdbcType="VARCHAR"/>
            <result property="cCustOpenDate" column="c_cust_open_date" jdbcType="VARCHAR"/>
            <result property="cRiskTypeName" column="c_risk_type_name" jdbcType="VARCHAR"/>
            <result property="fL1yTracnt" column="f_l1y_tracnt" jdbcType="INTEGER"/>
            <result property="cL3mIfRfap" column="c_l3m_if_rfap" jdbcType="VARCHAR"/>
            <result property="cLastRfapCfmDate" column="c_last_rfap_cfm_date" jdbcType="VARCHAR"/>
            <result property="fHisTtlCfPrft" column="f_his_ttl_cf_prft" jdbcType="DECIMAL"/>
            <result property="fL3yTtlCfPrft" column="f_l3y_ttl_cf_prft" jdbcType="DECIMAL"/>
            <result property="fR1AstRatio" column="f_r1_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fR23AstRatio" column="f_r23_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fR45AstRatio" column="f_r45_ast_ratio" jdbcType="DECIMAL"/>
            <result property="cAstrRatioLabel" column="c_astr_ratio_label" jdbcType="VARCHAR"/>
            <result property="cIfVipCust" column="c_if_vip_cust" jdbcType="VARCHAR"/>
            <result property="cHisTtlCfPrftLabel" column="c_his_ttl_cf_prft_label" jdbcType="VARCHAR"/>
            <result property="cL3yTtlCfPrftLabel" column="c_l3y_ttl_cf_prft_label" jdbcType="VARCHAR"/>
            <result property="fMobileCnt" column="f_mobile_cnt" jdbcType="INTEGER"/>
            <result property="cIfComplaintCust" column="c_if_complaint_cust" jdbcType="VARCHAR"/>
            <result property="cIfNotdisturbCust" column="c_if_notdisturb_cust" jdbcType="VARCHAR"/>
            <result property="cDate" column="c_date" jdbcType="VARCHAR"/>
            <result property="cDayTtlHldFund" column="c_day_ttl_hld_fund" jdbcType="VARCHAR"/>
            <result property="fMmfAstRatio" column="f_mmf_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fBondAstRatio" column="f_bond_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fEquityAstRatio" column="f_equity_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fBlendAstRatio" column="f_blend_ast_ratio" jdbcType="DECIMAL"/>
            <result property="fFofAstRatio" column="f_fof_ast_ratio" jdbcType="DECIMAL"/>
            <result property="cIfYShareCust" column="c_if_y_share_cust" jdbcType="VARCHAR"/>
            <result property="cIfNotYPensCust" column="c_if_not_y_pens_cust" jdbcType="VARCHAR"/>
            <result property="cIfEtfCust" column="c_if_etf_cust" jdbcType="VARCHAR"/>
            <result property="cIfEtfCnetCust" column="c_if_etf_cnet_cust" jdbcType="VARCHAR"/>
            <result property="cIfIndexCust" column="c_if_index_cust" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
