<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cenker.scrm.mapper.tag.TbDcUserTagsMapper">


    <select id="getCustomerTagRecordByCustomerId" resultType="java.util.HashMap">
        SELECT
            v.*,
            bt.*
        FROM
            `tb_wx_ext_customer` t
                LEFT JOIN `v_f_scrm_label` v ON t.`custno` = v.`custno`
                LEFT JOIN tb_wx_business_tag bt ON bt.external_user_id = t.external_user_id
        WHERE t.`external_user_id` = #{externalUserId}
    </select>
</mapper>
