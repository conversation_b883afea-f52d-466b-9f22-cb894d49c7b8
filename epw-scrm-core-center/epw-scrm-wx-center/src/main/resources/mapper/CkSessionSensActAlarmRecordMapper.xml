<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionSensActAlarmRecordMapper">
    
    <resultMap type="CkSessionSensActAlarmRecord" id="CkSessionSensActAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="actType"    column="act_type"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="sendUserId"    column="send_user_id"    />
        <result property="acceptUserId"    column="accept_user_id"    />
        <result property="sendUserType"    column="send_user_type"    />
        <result property="acceptUserType"    column="accept_user_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="triggerTime"    column="trigger_time"    />
        <result property="msgId"    column="msg_id"    />
        <result property="chatType"    column="chat_type"    />
        <result property="roomId"    column="room_id"    />
        <result property="roomConsumeId"    column="room_consume_id"    />

        <result property="ruleName"    column="rule_name"    />
        <result property="sendUserName"    column="send_user_name"    />
        <result property="acceptUserName"    column="accept_user_name"    />
        <result property="sendCorpName"    column="send_corp_name"    />
        <result property="acceptCorpName"    column="accept_corp_name"    />


    </resultMap>

    <sql id="selectCkSessionSensActAlarmRecordVo">
        SELECT * FROM (
                          SELECT
                              d.*,
                              r.rule_name,
                              IF(d.send_user_type = '2', u1.name, c1.`name`) as send_user_name,
                              IF(d.send_user_type = '1', c1.corp_name, null) as send_corp_name,
                              IF(d.accept_user_type = '3', g.group_name, IFNULL(u2.name, c2.`name`)) as accept_user_name,
                              IF(d.accept_user_type = '3', '群聊', c2.corp_name) as accept_corp_name
                          FROM
                              ck_session_sens_act_alarm_record d
                                  LEFT JOIN ck_session_sens_rule_info r ON r.rule_id = d.rule_id
                                  LEFT JOIN tb_wx_user u1 ON u1.userid = d.send_user_id
                                  LEFT JOIN tb_wx_ext_customer c1 ON c1.external_user_id = d.send_user_id
                                  LEFT JOIN tb_wx_user u2 ON u2.userid = d.accept_user_id
                                  LEFT JOIN tb_wx_ext_customer c2 ON c2.external_user_id = d.accept_user_id
                                  LEFT JOIN tb_wx_customer_group g ON g.chat_id = d.room_id
                      ) o
    </sql>

    <select id="selectCkSessionSensActAlarmRecordList" parameterType="QryActAlarmDto" resultMap="CkSessionSensActAlarmRecordResult">
        <include refid="selectCkSessionSensActAlarmRecordVo"/>
        <where>
        <if test="actTypes != null  and actTypes != ''"> and o.act_type =#{actTypes}</if>
        <if test="ruleName != null  and ruleName != ''"> and o.rule_name  like concat('%', #{ruleName}, '%')</if>
        <if test="sendName != null  and sendName != ''"> and o.send_user_name  like concat('%', #{sendName}, '%')</if>
        <if test="acceptName != null  and acceptName != ''"> and o.accept_user_name  like concat('%', #{acceptName}, '%')</if>
        <if test="beginTime != null  and beginTime != ''">  and <![CDATA[ DATE_FORMAT(o.trigger_time,'%Y-%m-%d')  >= #{beginTime}]]></if>
        <if test="endTime != null  and endTime != ''">  and <![CDATA[ DATE_FORMAT(o.trigger_time,'%Y-%m-%d')  <= #{endTime}]]></if>
        </where>
      </select>
    
    <select id="selectCkSessionSensActAlarmRecordByHotId" parameterType="Long" resultMap="CkSessionSensActAlarmRecordResult">
        <include refid="selectCkSessionSensActAlarmRecordVo"/>
<where>
          o.id = #{id}
</where>
    </select>
        
    <insert id="insertCkSessionSensActAlarmRecord" parameterType="CkSessionSensActAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ck_session_sens_act_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="actType != null and actType != ''">act_type,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="sendUserId != null">send_user_id,</if>
            <if test="acceptUserId != null">accept_user_id,</if>
            <if test="sendUserType != null">send_user_type,</if>
            <if test="acceptUserType != null">accept_user_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="triggerTime != null">trigger_time,</if>
            <if test="msgId != null">msg_id,</if>
            <if test="chatType != null">chat_type,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomConsumeId != null">room_consume_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="actType != null and actType != ''">#{actType},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="sendUserId != null">#{sendUserId},</if>
            <if test="acceptUserId != null">#{acceptUserId},</if>
            <if test="sendUserType != null">#{sendUserType},</if>
            <if test="acceptUserType != null">#{acceptUserType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="triggerTime != null">#{triggerTime},</if>
            <if test="msgId != null">#{msgId},</if>
            <if test="chatType != null">#{chatType},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomConsumeId != null">#{roomConsumeId},</if>
        </trim>
    </insert>

    <update id="updateCkSessionSensActAlarmRecord" parameterType="CkSessionSensActAlarmRecord">
        update ck_session_sens_act_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="actType != null and actType != ''">act_type = #{actType},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="sendUserId != null">send_user_id = #{sendUserId},</if>
            <if test="acceptUserId != null">accept_user_id = #{acceptUserId},</if>
            <if test="sendUserType != null">send_user_type = #{sendUserType},</if>
            <if test="acceptUserType != null">accept_user_type = #{acceptUserType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="triggerTime != null">trigger_time = #{triggerTime},</if>
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="chatType != null">chat_type = #{chatType},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomConsumeId != null">room_consume_id = #{roomConsumeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCkSessionSensActAlarmRecordByHotId" parameterType="Long">
        delete from ck_session_sens_act_alarm_record where id = #{id}
    </delete>

    <delete id="deleteCkSessionSensActAlarmRecordByHotIds" parameterType="String">
        delete from ck_session_sens_act_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="saveSenActAlarmRecord">
        insert into ck_session_sens_act_alarm_record(act_type,rule_id,send_user_id,accept_user_id,send_user_type,accept_user_type,create_time,trigger_time,msg_id,chat_type,room_id,room_consume_id)
        SELECT
            r.act_type,
            r.rule_id,
            cai.from_id AS send_user_id,
            cai.consume_id AS accept_user_id,
            2 AS send_user_type,
            IF(cai.chat_type = 2, 3, (select IF(count(1) > 0, 2, 1) from tb_wx_user where userid = cai.consume_id)) AS accept_user_type,
            CURRENT_TIMESTAMP AS create_time,
            cai.msg_time AS trigger_time,
            cai.msg_id,
            cai.chat_type,
            cai.room_id,
            cai.room_consume_id
        FROM
            wk_chat_archive_info cai
        LEFT JOIN ck_session_sens_check_mapping m ON m.check_user_id = cai.from_id OR m.check_user_id = cai.consume_id
        LEFT JOIN (
            SELECT
                    rule_id,
                    jt.act_type
                FROM
                    ck_session_sens_rule_info,
                    JSON_TABLE ( CONCAT( '["', REPLACE ( act_types, ',', '","' ), '"]' ), '$[*]' COLUMNS (act_type VARCHAR (10) COLLATE utf8mb4_general_ci PATH '$')) AS jt
                WHERE
                    STATUS = '1'
                  AND intercept_type IN ( '2', '3' )) r ON r.rule_id = m.rule_id
        JOIN tb_wx_user u ON u.userid = cai.from_id
        WHERE cai.msg_time >= #{beginTime} AND cai.msg_time <![CDATA[<=]]> #{endTime}
          AND ((cai.msg_type = 5 AND r.act_type = 3) OR (cai.msg_type = 19 AND r.act_type = 4) OR (cai.msg_type = 16 AND r.act_type = 5))
    </insert>
</mapper>