<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSectionRadarMapper">
    <!-- 自定义SQL查询 -->

    <resultMap id="getRadarListMap" type="com.cenker.scrm.pojo.vo.subscr.BuSectionRadarVO">
        <id column="id" property="id"/>
        <result column="radar_id" property="radarId"/>
        <result column="radar_name" property="radarName"/>
        <result column="radar_type" property="radarType"/>
        <result column="radar_del_flag" property="radarDelFlag"/>
        <result column="section_id" property="sectionId"/>
        <result column="summary" property="summary"/>
        <result column="del_flag" property="delFlag"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <association property="tbWxRadarContent" javaType="com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent">
            <result column="content_id" property="id"/>
            <result column="digest" property="digest"/>
            <result column="title" property="title"/>
            <result column="cover" property="cover"/>
            <result column="content" property="content"/>
            <result column="url" property="url"/>
            <result column="show_status" property="showStatus"/>
        </association>
    </resultMap>

    <select id="getBuSectionRadarVOS" resultMap="getRadarListMap">
        SELECT
        r.id,
        r.radar_id,
        i.type AS radar_type,
        i.title AS radar_name,
        i.del_flag AS radar_del_flag,
        r.section_id,
        r.summary,
        r.del_flag,
        r.upload_time,
        r.end_time,
        r.create_by,
        u.nick_name AS create_by_name,
        c.show_status,
        c.title,
        c.digest,
        c.cover,
        c.id as content_id,
        if(c.digest is null or c.digest = '',c.content,c.digest) as content
        FROM bu_section_radar r
        LEFT JOIN sys_user u ON r.create_by = u.user_id
        LEFT JOIN tb_wx_radar_interact i ON r.radar_id = i.id
        LEFT JOIN tb_wx_radar_content c ON r.radar_id = c.radar_id
        <where>
            r.section_id = #{sectionId}
            <if test="name != null and name != ''">
                AND (r.summary LIKE CONCAT('%', #{name}, '%') OR c.title LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="delFlag != null">
                AND r.del_flag = #{delFlag}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND r.upload_time >= #{beginTime, jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null and endTime != ''">
                AND r.upload_time &lt;= #{endTime, jdbcType=TIMESTAMP}
            </if>
            <if test="externalUserId != null and externalUserId != ''">
                AND EXISTS (
                    SELECT 1 FROM bu_subscription s
                    WHERE s.external_user_id = #{externalUserId} and s.section_id = r.section_id
                    AND
                    (s.del_flag = 0 OR (s.del_flag = 1 AND s.cancel_subscr_time >= r.upload_time))
                )
            </if>
            <if test="radarStatus != null and radarStatus != ''">
                AND i.status = #{radarStatus}
            </if>
        </where>
        ORDER BY r.upload_time DESC
    </select>
</mapper>