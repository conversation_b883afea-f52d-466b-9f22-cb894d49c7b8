<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.tag.TbWxAutoTagMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxAutoTag" id="TbWxAutoTag">
        <result property="autoTagId" column="auto_tag_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="order" column="order"/>
        <result property="corpId" column="corp_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tagCondition" column="tag_condition"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <insert id="insertTbWxAutoTag" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxAutoTag">
        insert into tb_wx_auto_tag(
        <if test="autoTagId != null and autoTagId != 0">auto_tag_id,</if>
        <if test="ruleName != null and ruleName != ''">rule_name,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="order != null and order != ''">order,</if>
        <if test="corpId != null and corpId != ''">corp_id,</if>
        <if test="delFlag != null and delFlag != ''">del_flag,</if>
        <if test="tagCondition != null and tagCondition != ''">tag_condition,</if>
        <if test="remark != null and remark != ''">remark,</if>
        create_time,update_time
        )values(
        <if test="autoTagId != null and autoTagId != ''">#{autoTagId},</if>
        <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="order != null and order != ''">#{order},</if>
        <if test="corpId != null and corpId != ''">#{corpId},</if>
        <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        <if test="tagCondition != null and tagCondition != ''">#{tagCondition},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        sysdate(),sysdate()
        )
    </insert>

    <select id="selectDataList" parameterType="com.cenker.scrm.pojo.dto.tag.TagRuleListDto" resultMap="TbWxAutoTag">
        select t.auto_tag_id, t.rule_name, t.create_time,t.status,t.create_by,user.nick_name as create_by_name
        from
        tb_wx_auto_tag t
        LEFT JOIN sys_user user on t.create_by = user.user_id
        where t.del_flag = '0'
        <if test="ruleName != null and ruleName != ''">
            AND t.rule_name like concat('%', #{ruleName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND t.status = #{status}
        </if>
        <if test="createByName != null and createByName != ''">
            AND user.nick_name like concat('%', #{createByName}, '%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and t.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and t.create_by = #{userId}
            </if>
        </if>
        order by t.auto_tag_id desc

    </select>

</mapper>