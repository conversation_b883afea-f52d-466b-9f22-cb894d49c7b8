<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticHotWordMapper">
    <insert id="saveStatisticDateByDay">
        INSERT INTO tb_statistic_hot_word (statistic_date, hot_word, synon_words, staff_trigger_times, staff_trigger_num, customer_trigger_times, customer_trigger_num, create_time, create_by, dept_id, hot_id)
        SELECT
            str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
            ck.hot_word,
            ck.synon_words,
            SUM(CASE WHEN wx.userid is not null THEN 1 ELSE 0 END) as staff_trigger_times,
            COUNT(DISTINCT CASE WHEN wx.userid is not null THEN ck.from_id END) as staff_trigger_num,
            SUM(CASE WHEN wx.userid is null THEN 1 ELSE 0 END) as customer_trigger_times,
            COUNT(DISTINCT CASE WHEN wx.userid is null THEN ck.from_id END) as customer_trigger_num,
            NOW() as create_time,
            hw.create_by,
            hw.dept_id,
            hw.hot_id
        FROM ck_session_hot_word_trigger_record ck
        LEFT JOIN ck_session_hot_word_info hw ON hw.hot_id = ck.rule_id
        LEFT JOIN tb_wx_user wx ON ck.from_id = wx.userid
        WHERE DATE(ck.msg_time) = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY ck.rule_id;

    </insert>

    <select id="summary" resultType="StatisticHotWordSummaryVo">
        SELECT
            COUNT(DISTINCT hot_word) AS hotWordTotal,
            IFNULL(SUM(staff_trigger_times), 0) AS staffTriggerTimes,
            IFNULL(SUM(staff_trigger_num), 0) AS staffTriggerNum,
            IFNULL(SUM(customer_trigger_times), 0) AS customerTriggerTimes,
            IFNULL(SUM(customer_trigger_num), 0) AS customerTriggerNum,
            IFNULL(SUM(staff_trigger_times), 0) + IFNULL(SUM(customer_trigger_times), 0) AS triggerTotal
        FROM
            tb_statistic_hot_word
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and dept_id in
                    <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and create_by = #{userId} and dept_id = #{deptId}
                </if>
            </if>
    </select>

  <select id="graph" resultType="StatisticHotWordSummaryVo">
        SELECT
            statistic_date AS statisticDate,
            COUNT(DISTINCT hot_word) AS hotWordTotal,
            IFNULL(SUM(staff_trigger_times), 0) AS staffTriggerTimes,
            IFNULL(SUM(staff_trigger_num), 0) AS staffTriggerNum,
            IFNULL(SUM(customer_trigger_times), 0) AS customerTriggerTimes,
            IFNULL(SUM(customer_trigger_num), 0) AS customerTriggerNum,
            IFNULL(SUM(staff_trigger_times), 0) + IFNULL(SUM(customer_trigger_times), 0) AS triggerTotal
        FROM
            tb_statistic_hot_word
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
          <!-- 权限控制 -->
          <if test="dataScope == null or dataScope != '1'.toString()">
              <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                  and dept_id in
                  <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
              <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                  and create_by = #{userId} and dept_id = #{deptId}
              </if>
          </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>
</mapper>
