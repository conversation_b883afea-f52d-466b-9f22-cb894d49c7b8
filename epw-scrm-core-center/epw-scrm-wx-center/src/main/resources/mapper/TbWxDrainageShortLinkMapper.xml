<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.drainage.TbWxDrainageShortLinkMapper">
    <select id="selectDrainageShortLinkList" resultType="DrainageShortLinkQueryVo">
        select
               l.id,
               l.link_name,
               l.code_url,
               l.type,
               if(l.expire = 0, '永久有效', if(now() > l.due_date, '已失效', date_format(l.due_date,'%Y-%m-%d'))) due_date,
               (select count(1)
                from tb_wx_drainage_link_click
                where drainage_id = l.id and curdate() = click_date)                     today_cnt,
               (select count(1) from tb_wx_drainage_link_click where drainage_id = l.id) total_cnt,
               (select nick_name from sys_user where user_id = l.create_by)              create_by,
               l.create_time,
               concat(#{shortDomain},l.short_value)short_link
        from tb_wx_drainage_short_link l
        where l.corp_id = #{corpId}
        and l.del_flag = 0
        <if test="linkName != null and linkName != ''">
            and l.link_name like concat('%',#{linkName},'%')
        </if>
        <if test="type != null">
            and l.type = #{type}
        </if>
        order by l.create_time desc
    </select>

    <select id="getInfoByDrainageId" resultType="DrainageShortLinkQuery">
        select
            l.id,
            l.link_name,
            l.type,
            l.code_id,
            l.expire,
            l.due_date,
            l.page_title,
            l.style,
            l.guide_content,
            l.show_base_info,
            l.avatar,
            l.nick_name,
            l.show_corp_info,
            l.corp_name,
            l.corp_avatar,
            l.code_url,
            l.way
        from tb_wx_drainage_short_link l
        where l.id = #{id}
          and l.del_flag = 0
    </select>
</mapper>