<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSubscriptionMenuSubMapper">
    <!-- 自定义SQL查询 -->
    <insert id="insertBatchSomeColumn">
        INSERT INTO bu_subscription_menu_sub (id, menu_id, sub_menu_name, parent_id, sub_menu_sort, intro, del_flag, section_id, create_time, create_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.menuId}, #{item.subMenuName}, #{item.parentId}, #{item.subMenuSort}, #{item.intro}, #{item.delFlag}, #{item.sectionId}, #{item.createTime}, #{item.createBy})
        </foreach>
    </insert>
    <select id="selectMenuSubList" resultType="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuSubVO">
        SELECT
        sub.id,
        sub.menu_id,
        sub.parent_id,
        sub.sub_menu_name,
        sub.section_id,
        sec.section_name,
        sub.sub_menu_sort,
        sub.intro,
        sub.create_time,
        sub.create_by,
        sub.update_time,
        sub.update_by,
        sub.del_flag,
        sub.del_time
        FROM
        bu_subscription_menu_sub sub
        LEFT JOIN
        bu_subscription_section sec ON sub.section_id = sec.id
        WHERE
        sub.menu_id = #{menuId} AND sub.del_flag = 0
        ORDER BY
        sub.parent_id ASC,
        sub.sub_menu_sort ASC
    </select>
</mapper>