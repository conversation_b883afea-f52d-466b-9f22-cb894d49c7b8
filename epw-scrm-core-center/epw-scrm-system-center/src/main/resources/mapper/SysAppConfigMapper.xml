<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.SysAppConfigMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.system.SysAppConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appType" column="app_type" jdbcType="CHAR"/>
            <result property="appName" column="app_name" jdbcType="VARCHAR"/>
            <result property="appConfig" column="app_config" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_type,app_name,
        app_config,create_by,update_by,
        create_time,update_time,
        remark
    </sql>

    <select id="listAppConfig" resultType="com.cenker.scrm.pojo.vo.system.SysAppConfigListVo">
        SELECT
            c.id,
            c.app_type,
            c.app_name,
            u.nick_name AS creator_name,
            c.create_time
        FROM
            sys_app_config c
            LEFT JOIN sys_user u ON  u.user_id = c.create_by
        <where>
            <if test="param.appType!= null and param.appType!= ''">
                c.app_type = #{param.appType}
            </if>
            <if test="param.appName != null and param.appName != ''">
                AND c.app_name like concat('%', #{param.appName}, '%')
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>
</mapper>
