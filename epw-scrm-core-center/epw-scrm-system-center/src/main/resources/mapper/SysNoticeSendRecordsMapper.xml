<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.SysNoticeSendRecordsMapper">
    
    <resultMap type="SysNoticeSendRecords" id="SysNoticeSendRecordsResult">
        <result property="id"    column="id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="userId"    column="user_id"    />
        <result property="noticeMode"    column="notice_mode"    />
        <result property="readStatus"    column="read_status"    />
        <result property="msgId"    column="msg_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delTime"    column="del_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="hasReminder"    column="has_reminder"    />
    </resultMap>

    <sql id="selectSysNoticeSendRecordsVo">
        select id, notice_id, notice_type, user_id, notice_mode, read_status, read_time, msg_id,
            del_flag, del_time, create_by, create_time, update_by, update_time, has_reminder from sys_notice_send_records
    </sql>

    <select id="selectSysNoticeSendRecordsList" parameterType="SysNoticeSendRecords" resultMap="SysNoticeSendRecordsResult">
        <include refid="selectSysNoticeSendRecordsVo"/>
        <where>  
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="noticeMode != null "> and notice_mode = #{noticeMode}</if>
            <if test="readStatus != null "> and read_status = #{readStatus}</if>
            <if test="msgId != null  and msgId != ''"> and msg_id = #{msgId}</if>
            <if test="delTime != null "> and del_time = #{delTime}</if>
        </where>
    </select>
    
    <select id="selectSysNoticeSendRecordsById" parameterType="Long" resultMap="SysNoticeSendRecordsResult">
        <include refid="selectSysNoticeSendRecordsVo"/>
        where id = #{id}
    </select>

    <delete id="deleteSysNoticeSendRecordsById" parameterType="Long">
        delete from sys_notice_send_records where id = #{id}
    </delete>

    <delete id="deleteSysNoticeSendRecordsByIds" parameterType="String">
        delete from sys_notice_send_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="sysNoticeSendRecordsVo" type="com.cenker.scrm.pojo.vo.system.SysNoticeSendRecordsVo">
        <result column="id" property="id"/>
        <result column="notice_id" property="noticeId"/>
        <result column="notice_type" property="noticeType"/>
        <result column="user_id" property="userId"/>
        <result column="notice_mode" property="noticeMode"/>
        <result column="read_status" property="readStatus"/>
        <result column="read_time" property="readTime"/>
        <result column="msg_id" property="msgId"/>
        <result column="del_flag" property="delFlag"/>
        <result column="del_time" property="delTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="notice_title" property="noticeTitle"/>
        <result column="notice_content" property="noticeContent"/>
        <result column="has_reminder" property="hasReminder"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="business_oper" property="businessOper"/>
        <result column="business_json" property="businessJson"/>
    </resultMap>

    <select id="getNoticeRecordList" resultMap="sysNoticeSendRecordsVo">
        select
            nsr.id,
            nsr.notice_id,
            nsr.notice_type,
            nsr.user_id,
            nsr.notice_mode,
            nsr.read_status,
            nsr.read_time,
            nsr.msg_id,
            nsr.del_flag,
            nsr.del_time,
            nsr.create_by,
            nsr.create_time,
            nsr.has_reminder,
            n.notice_content,
            n.notice_title,
            n.business_type,
            n.business_id,
            n.business_oper,
            n.business_json
        from sys_notice_send_records nsr
        left join sys_notice n on n.notice_id = nsr.notice_id
        where nsr.del_flag = 0 and n.del_flag = 0
        <if test="param.id != null">
            and nsr.id = #{param.id}
        </if>
        <if test="param.noticeId != null">
            and nsr.notice_id = #{param.noticeId}
        </if>
        <if test="param.userId != null">
            and nsr.user_id = #{param.userId}
        </if>
        <if test="param.noticeMode != null">
            and nsr.notice_mode = #{param.noticeMode}
        </if>
        <if test="param.noticeType != null">
            and nsr.notice_type = #{param.noticeType}
        </if>
        <if test="param.readStatus != null">
            and nsr.read_status = #{param.readStatus}
        </if>
        <if test="param.beginTime != null and param.beginTime != ''">
            AND nsr.create_time &gt;= #{param.beginTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            AND nsr.create_time &lt;= #{param.endTime}
        </if>
        <if test="param.hasReminder != null and param.hasReminder != ''">
            AND nsr.has_reminder = #{param.hasReminder}
        </if>
        group by nsr.id
        order by nsr.create_time desc
    </select>

    <select id="getNoticeRecordInfoById" resultType="com.cenker.scrm.pojo.vo.system.SysNoticeSendRecordsVo">
        select
            nsr.id,
            nsr.notice_id noticeId,
            nsr.notice_type noticeType,
            nsr.user_id userId,
            nsr.notice_mode noticeMode,
            nsr.read_status readStatus,
            nsr.read_time readTime,
            nsr.msg_id msgId,
            nsr.del_flag delFlag,
            nsr.del_time delTime,
            nsr.create_by createBy,
            nsr.create_time createTime,
            n.notice_content noticeContent,
            n.notice_title noticeTitle
        from sys_notice_send_records nsr
        left join sys_notice n on n.notice_id = nsr.notice_id
        where nsr.id = #{id}
        group by nsr.id
        order by nsr.create_time desc
    </select>
</mapper>