package com.cenker.scrm.controller;

import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysConfig;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.ISysConfigService;
import com.cenker.scrm.util.TokenParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController {
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private TokenParseUtil tokenParseUtil;

    /**
     * 获取参数配置列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(SysConfig config) {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @RequestMapping("/export")
    public List<SysConfig> export(SysConfig config) {
        return configService.selectConfigList(config);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @RequestMapping("/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId) {
        return AjaxResult.success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping("/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey) {
        return AjaxResult.success("操作成功", configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @RequestMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysConfig config, HttpServletRequest request) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return AjaxResult.error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(tokenParseUtil.getLoginUser(request).getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SysConfig config,HttpServletRequest request) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return AjaxResult.error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(tokenParseUtil.getLoginUser(request).getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @RequestMapping("/remove/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds) {
        return toAjax(configService.deleteConfigByIds(configIds));
    }

    /**
     * 清空缓存
     */
    @RequestMapping("/clearCache")
    public AjaxResult clearCache() {
        configService.clearCache();
        return AjaxResult.success();
    }
}
