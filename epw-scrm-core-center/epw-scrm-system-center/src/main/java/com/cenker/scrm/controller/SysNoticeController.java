package com.cenker.scrm.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.enums.SysNoticeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.system.SysNotice;
import com.cenker.scrm.pojo.request.system.SysNoticeRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.system.SysNoticeSendRecordsVo;
import com.cenker.scrm.service.ISysNoticeSendRecordsService;
import com.cenker.scrm.service.ISysNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController {
    @Resource
    private ISysNoticeService noticeService;
    @Resource
    private ISysNoticeSendRecordsService noticeSendRecordsService;

    /**
     * 获取通知公告列表(首页)
     */
    @RequestMapping("/noticeList")
    public AjaxResult noticeList() {
        List<SysNotice> list = noticeService.selectNoticeListTop(null);
        return AjaxResult.success(list);
    }

    /**
     * 获取通知公告列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody SysNoticeRequest request) {
        startPage();
        if(StrUtil.isNotBlank(request.getUserId())){
           List<String> userIds = StrUtil.split(request.getUserId(), StrUtil.COMMA);
           request.setUserIds(userIds);
           request.setUserId(null);
        }
        request.setNoticeType(SysNoticeEnum.NOTICE_TYPE_NOTICE.getCode());
        List<SysNotice> list = noticeService.selectNoticeList(request);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @RequestMapping("/getInfo/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId) {
        return AjaxResult.success(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @RequestMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysNotice notice) {
        // 如果sendTime不为空，则验证是否大于当前时间 ，若buffTime小于等于当前时间，则提示时间不对，当前时间精确到分钟
        if (notice.getSendTime() != null && !notice.getSendTime().after(DateUtil.beginOfMinute(DateUtil.date()))) {
            return AjaxResult.error("定时发布时间不能小于等于当前时间");
        }
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SysNotice notice) {
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     */
    @RequestMapping("/remove/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }


    /**
     * 系统公告+站内信列表
     */
    @RequestMapping("/record/list")
    public TableDataInfo recordList(@RequestBody SysNoticeRequest request){
        startPage();
        request.setNoticeMode(SysNoticeEnum.NOTICE_MODE_ADMIN.getCode());
        List<SysNoticeSendRecordsVo> list = noticeSendRecordsService.recordList(request);
        return getDataTable(list);
    }

    /**
     * 查看消息详情
     */
    @RequestMapping("/record/{id}")
    public AjaxResult recordInfo(@PathVariable("id") Long recordId){
        return AjaxResult.success(noticeSendRecordsService.selectSysNoticeSendRecordsById(recordId));
    }
    /**
     * 查看未读消息
     */
    @RequestMapping("/record/unread")
    public AjaxResult unRead(@RequestBody SysNoticeRequest request){
        log.info("【查看未读消息】 入参:{}",request);
        Date now = new Date();
        JSONObject data = noticeSendRecordsService.unRead(request);
//        List<SysNoticeSendRecordsVo> recordsList = (List<SysNoticeSendRecordsVo>) data.get("recordList");
        log.info("【查看未读消息】 时间点{}之前的所有未读消息标记为已提醒:", DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        noticeSendRecordsService.remind(null, now, request);
        log.info("【查看未读消息】 成功");
        return AjaxResult.success(data);
    }

    @RequestMapping("/record/unreadStatus")
    public AjaxResult unreadStatus(@RequestBody SysNoticeRequest request){
        return AjaxResult.success(noticeSendRecordsService.unreadStatus(request));
    }

    @RequestMapping("/record/setAllRead")
    public AjaxResult setAllRead(@RequestBody SysNoticeRequest request) {
        noticeSendRecordsService.setAllRead(request);
        return AjaxResult.success();
    }

    /**
     * 撤回公告
     */
    @RequestMapping("/revocation")
    public AjaxResult revocation(@RequestBody SysNoticeRequest request){
        return toAjax(noticeService.revocation(request));
    }
    /**
     * 取消公告发布
     */
    @RequestMapping("/cancel")
    public AjaxResult cancelSend(@RequestBody SysNoticeRequest request){
        return toAjax(noticeService.cancelSend(request));
    }
    /**
     * 发布公告
     */
    @RequestMapping("/send")
    public AjaxResult sendNotice(@RequestBody SysNoticeRequest request){
        return toAjax(noticeService.sendNotice(request));
    }
}
