package com.cenker.scrm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.system.NamingRuleListDto;
import com.cenker.scrm.pojo.entity.system.BuNamingRule;
import com.cenker.scrm.pojo.vo.system.NamingRuleVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bu_naming_rule(命名规则表)】的数据库操作Mapper
* @createDate 2024-08-17 23:44:17
* @Entity com.cenker.scrm.BuNamingRule
*/
public interface BuNamingRuleMapper extends BaseMapper<BuNamingRule> {

    List<NamingRuleVo> listByCondition(NamingRuleListDto namingRuleListDto);
}




