package com.cenker.scrm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService extends IService<SysUser> {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName);


    SysUser selectUserByCorpInfo(String corpId, String corpUserId);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    String checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    void checkUserAllowed(SysUser user);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

    /**
     * 校验用户是否有效
     *
     * @param corpId
     * @param corpUserId
     * @return
     */
    int checkUserIsValid(String corpId, String corpUserId);

    /**
     * 修改成员Id
     *
     * @param sysUser
     * @return
     */
    int updateCorpUserId(SysUser sysUser);

    /**
     * 停用企业所有账号权限
     *
     * @param authCorpId
     */
    int stopUserByCorpId(String authCorpId);

    /**
     * 校验用户是否有效
     *
     * @param userName
     * @return
     */
    int checkUserIsValidByName(String userName);

    /**
     * 根据企业id查询所有用户
     * @param corpId
     * @return
     */
    List<SysUser> selectUserByCorpId(String corpId);

    /**
     * 更改用户账号
     * @param sysUser
     */
    void updateUserName(SysUser sysUser);

    int recoverUserByCorpId(String corpId);

    String selectSysUserByCorpUserId(String corpUserId, String corpId);

    /**
     * 是否企业管理员
     */
    Integer checkUserIsCorpAdmin(String corpUserId, String corpId);

    /**
     * 明文企业id和密文userId查询
     */
    SysUser selectUserByOpenUserId(String openUserId, String corpId);

    /**
     * 校验企业账号是否唯一
     */
    String checkCorpUserUnique(SysUser user);

    /**
     * 根据通讯录账号创建系统用户
     * @param tbWxUser
     */
    @Deprecated
    void createSysUserByTbWxUser(TbWxUser tbWxUser);

    /**
     * 根据企微账号开通系统用户
     * @param tbWxUser
     */
    void saveUserByWxUser(TbWxUser tbWxUser);

    /**
     * 根据id停用对应账号
     * @param userId
     */
    void stopUserById(String userId);

    /**
     * 根据domainUserId查询系统用户
     * @param domainAccount
     * @return
     */
    SysUser selectUserByDomainAccount(String domainAccount);

    /**
     * 根据wxUserId查询系统用户
     * @param wxUserId
     * @return
     */
    SysUser selectUserByWxUserId(String wxUserId);


    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectAllocatedList(SysUser user);

    List<SysUser> selectUserNoDeptList();

}
