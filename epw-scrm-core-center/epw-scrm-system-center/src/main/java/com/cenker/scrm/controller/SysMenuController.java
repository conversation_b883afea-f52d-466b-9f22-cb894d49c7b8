package com.cenker.scrm.controller;

import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.TreeSelect;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.ISysMenuService;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
public class SysMenuController extends BaseController {

    private final ISysMenuService menuService;
    private final TokenParseUtil tokenParseUtil;

    /**
     * 获取菜单列表
     */
    @RequestMapping("/list")
    public AjaxResult list(@RequestBody SysMenu menu, HttpServletRequest request) {
        try {
            log.info("【查询菜单列表】入参:{}", menu);
            LoginUser loginUser = tokenParseUtil.getLoginUser(request);
            String userId = loginUser.getUser().getUserId();
            List<SysMenu> menus = menuService.selectMenuList(menu, userId);
            log.info("【查询菜单列表】 菜单个数:{}", menus.size());
            return AjaxResult.success(menus);
        } catch (Exception e) {
            log.error("【查询菜单列表】 异常:{}", e);
            return AjaxResult.error("查询菜单列表失败");
        }
    }

    /**
     * 获取菜单列表
     */
    @RequestMapping("/listForWeCom")
    public AjaxResult listForWeCom(@RequestBody SysMenu menu, HttpServletRequest request) {
        try {
            log.info("【查询菜单列表】入参:{}", menu);
            H5LoginUser loginUser = tokenParseUtil.getLoginUserH5(request);
            String userId = loginUser.getUserId();
            List menus = menuService.selectMenuListByType(menu, userId);
            log.info("【查询菜单列表】 菜单个数:{}", menus.size());
            return AjaxResult.success(menus);
        } catch (Exception e) {
            log.error("【查询菜单列表】 异常:{}", e);
            return AjaxResult.error("查询菜单列表失败");
        }
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @RequestMapping("/{menuId}")
    public AjaxResult getInfo(@PathVariable Long menuId) {
        try {
            log.info("【查询菜单详情】入参:{}", menuId);
            SysMenu menu = menuService.selectMenuById(menuId);
            return AjaxResult.success(menu);
        } catch (Exception e) {
            log.error("【【查询菜单详情】】 异常:{}", e);
            return AjaxResult.error("查询菜单详情失败");
        }
    }

    /**
     * 获取菜单下拉树列表
     */
    @RequestMapping("/treeselect")
    public AjaxResult treeselect(@RequestBody SysMenu menu,HttpServletRequest request) {
        try {
            log.info("【查询菜单下拉树列表】入参:{}", menu);
            LoginUser loginUser = tokenParseUtil.getLoginUser(request);
            String userId = loginUser.getUser().getUserId();
            List<SysMenu> menus = menuService.selectMenuList(menu, userId);
            List<TreeSelect> treeSelects = menuService.buildMenuTreeSelect(menus, menu.isFilterStatus());
            log.info("【查询菜单下拉树列表】 菜单个数:{}", treeSelects.size());
            return AjaxResult.success(treeSelects);
        } catch (Exception e) {
            log.error("【查询菜单下拉树列表】 异常:{}", e);
            return AjaxResult.error("查询菜单下拉树列表失败");
        }
    }

    /**
     * 加载对应角色菜单列表树
     */
    @RequestMapping("/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId, HttpServletRequest request) {
        try {
            String belongTerminal = request.getParameter("belongTerminal");
            LoginUser loginUser = tokenParseUtil.getLoginUser(request);
            log.info("【查询角色菜单数据】查询菜单:roleId={}, belongTerminal={}", roleId, belongTerminal);
            List<SysMenu> menus = menuService.selectMenuList(loginUser.getUser().getUserId(), belongTerminal);
            AjaxResult ajax = AjaxResult.success();
            log.info("【查询角色菜单数据】查询选中菜单ID:roleId={}, belongTerminal={}", roleId, belongTerminal);
            List<String> checkedKeys = menuService.selectMenuListByRoleId(roleId, belongTerminal);
            log.info("【查询角色菜单数据】 选中菜单ID列表:{}", checkedKeys);
            List<TreeSelect> treeSelects = menuService.buildMenuTreeSelect(menus, true);
            ajax.put("checkedKeys", checkedKeys);
            ajax.put("menus", treeSelects);
            return ajax;
        } catch (Exception e) {
            log.error("【查询角色菜单数据】 异常:{}", e);
            return AjaxResult.error("查询角色菜单数据失败");
        }
    }

    /**
     * 新增菜单
     */
    @RequestMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysMenu menu,HttpServletRequest request) {
        try {
            log.info("【新增菜单】入参:{}", menu);
            if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
                return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
            } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame())
                    && !StringUtils.startsWithAny(menu.getPath(), Constants.HTTP, Constants.HTTPS)) {
                return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
            }
            menu.setCreateBy(tokenParseUtil.getLoginUser(request).getUser().getUserId());
            // 默认功能说明图标
            if (StringUtils.isEmpty(menu.getHelpIcon())) {
                menu.setHelpIcon("https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png");
            }
            int rows = menuService.insertMenu(menu);
            log.info("【新增菜单】 新增菜单成功");
            return toAjax(rows);
        } catch (Exception e) {
            log.error("【新增菜单】 异常:{}", e);
            return AjaxResult.error("新增菜单失败");
        }
    }

    /**
     * 修改菜单
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SysMenu menu,HttpServletRequest request) {
        try {
            log.info("【修改菜单】入参:{}", menu);
            if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
                return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
            } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame())
                    && !StringUtils.startsWithAny(menu.getPath(), Constants.HTTP, Constants.HTTPS)) {
                return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
            } else if (menu.getMenuId().equals(menu.getParentId())) {
                return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
            }
            menu.setUpdateBy(tokenParseUtil.getLoginUser(request).getUsername());
            // 默认功能说明图标
            if (StringUtils.isEmpty(menu.getHelpIcon())) {
                menu.setHelpIcon("https://static.chengkeyun.com/scrm/image/dd0a3b31-1bcd-490e-a76b-8699b92625c2.png");
            }
            int rows = menuService.updateMenu(menu);
            log.info("【修改菜单】 修改菜单成功");
            return toAjax(rows);
        } catch (Exception e) {
            log.error("【修改菜单】 异常:{}", e);
            return AjaxResult.error("修改菜单失败");
        }
    }

    /**
     * 删除菜单
     */
    @RequestMapping("/remove/{menuId}")
    public AjaxResult remove(@PathVariable("menuId") Long menuId) {
        try {
            log.info("【删除菜单】入参:{}", menuId);
            if (menuService.hasChildByMenuId(menuId)) {
                return AjaxResult.error("存在子菜单,不允许删除");
            }
            if (menuService.checkMenuExistRole(menuId)) {
                return AjaxResult.error("菜单已分配,不允许删除");
            }
            int rows = menuService.deleteMenuById(menuId);
            log.info("【删除菜单】 删除菜单成功");
            return toAjax(rows);
        } catch (Exception e) {
            log.error("【删除菜单】 异常:{}", e);
            return AjaxResult.error("删除菜单失败");
        }
    }

    @RequestMapping("/getCustomMenuList")
    public AjaxResult getCustomMenuList(@RequestBody SysMenu sysMenu){
        try {
            log.info("【查询功能区菜单列表】入参:{}", sysMenu);
            // remark用作当前登录人id
            List<SysMenu> menus = menuService.selectCustomMenuTreeByUserId(sysMenu);
            return AjaxResult.success(menus);
        } catch (Exception e) {
            log.error("【查询功能区菜单列表】 异常:{}", e);
            return AjaxResult.error("查询功能区菜单列表失败");
        }
    }
}