package com.cenker.scrm.service;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.system.SysNoticeSendRecords;
import com.cenker.scrm.pojo.request.system.SysNoticeRequest;
import com.cenker.scrm.pojo.vo.system.SysNoticeSendRecordsVo;

/**
 * 系统通知发送记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-03
 */
public interface ISysNoticeSendRecordsService extends IService<SysNoticeSendRecords>
{
    /**
     * 查询系统通知发送记录
     * 
     * @param id 系统通知发送记录主键
     * @return 系统通知发送记录
     */
    public SysNoticeSendRecordsVo selectSysNoticeSendRecordsById(Long id);

    /**
     * 查询系统通知发送记录列表
     * 
     * @param sysNoticeSendRecords 系统通知发送记录
     * @return 系统通知发送记录集合
     */
    public List<SysNoticeSendRecords> selectSysNoticeSendRecordsList(SysNoticeSendRecords sysNoticeSendRecords);


    /**
     * 批量删除系统通知发送记录
     * 
     * @param ids 需要删除的系统通知发送记录主键集合
     * @return 结果
     */
    public int deleteSysNoticeSendRecordsByIds(Long[] ids);

    /**
     * 删除系统通知发送记录信息
     * 
     * @param id 系统通知发送记录主键
     * @return 结果
     */
    public int deleteSysNoticeSendRecordsById(Long id);

    List<SysNoticeSendRecordsVo> recordList(SysNoticeRequest request);

    JSONObject unRead(SysNoticeRequest request);

    JSONObject unreadStatus(SysNoticeRequest request);

    void setAllRead(SysNoticeRequest request);

    /**
     * 标记为已提醒
     *
     * @param recordsList
     * @param now
     * @param request
     */
    void remind(List<SysNoticeSendRecordsVo> recordsList, Date now, SysNoticeRequest request);
}
