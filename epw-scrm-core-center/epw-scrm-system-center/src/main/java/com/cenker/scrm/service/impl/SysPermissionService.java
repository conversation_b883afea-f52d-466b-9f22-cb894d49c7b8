package com.cenker.scrm.service.impl;

import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.system.SysRole;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.service.ISysMenuService;
import com.cenker.scrm.service.ISysRoleService;
import com.cenker.scrm.service.ISysUserOnlineService;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionService {
    @Autowired
    private ISysRoleService roleService;
    @Autowired
    private ISysMenuService menuService;
    @Autowired
    private ISysUserOnlineService userOnlineService;
    @Autowired
    private TokenParseUtil tokenParseUtil;
    @Autowired
    private ISysUserService userService;


    /**
     * 获取角色数据权限
     *
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            roles.add("admin");
        } else {
            roles.addAll(roleService.selectRolePermissionByUserId(Long.valueOf(user.getUserId())));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    private Set<String> getMenuPermission(SysUser user) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(Long.valueOf(user.getUserId())));
        }
        return perms;
    }

    @Async
    public void refreshLoginUserPermissions(SysRole role) {
        List<LoginUser> loginUsers = userOnlineService.findLoginUsersByRole(role);
            loginUsers.forEach(loginUser -> {
                if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin()) {
                    loginUser.setPermissions(getMenuPermission(loginUser.getUser()));
                    SysUser user = userService.selectUserByCorpInfo(loginUser.getUser().getCorpId(), loginUser.getUser().getUserName());
                    user.setAdministrator(false);
                    user.setCorpConfigId(loginUser.getUser().getCorpConfigId());
                    if (StringUtils.isNotEmpty(user.getCorpUserId())) {
                        // 如果绑定了账号
                        // 查询是否企业管理员
                        Integer isAdmin = userService.checkUserIsCorpAdmin(user.getCorpUserId(),user.getCorpId());
                        user.setAdministrator(isAdmin != null &&isAdmin == 1);
                    }
                    loginUser.setUser(user);
                    tokenParseUtil.setLoginUser(loginUser);
                }
            });
    }
}
