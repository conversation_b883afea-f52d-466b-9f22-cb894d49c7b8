package com.cenker.scrm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.client.XxlJobClient;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.enums.AppTypeEnum;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.system.*;
import com.cenker.scrm.pojo.entity.system.BuMaterialJob;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobListVo;
import com.cenker.scrm.pojo.vo.system.BuMaterialJobVo;
import com.cenker.scrm.service.IBuMaterialJobService;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.SnowflakeIdUtil;
import com.cenker.scrm.util.TokenParseUtil;
import com.xxl.job.core.biz.entity.XxlJobInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 定时制作任务菜单
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/material/job")
public class BuMaterialJobController extends BaseController {

    private final TokenParseUtil tokenService;
    private final XxlJobClient xxlJobClient;
    private final IBuMaterialJobService materialJobService;

    /**
     * 创建定时制作任务
     * @param param
     * @param request
     * @return
     */
    @PostMapping
    public Result add(@Validated @RequestBody BuMaterialJobAddDto param, HttpServletRequest request) {
        LogUtil.recordOperLog(String.format("创建定时制作任务【%s】", param.getJobName()));
        LoginUser loginUser = tokenService.getLoginUser(request);
        Long jobId = SnowflakeIdUtil.getSnowId();

        BuMaterialJob materialJob = BeanUtil.copyProperties(param, BuMaterialJob.class);
        materialJob.setId(jobId);
        materialJob.setJobStatus(CommonConstants.NUM_1);
        materialJob.setCorpId(loginUser.getTenantId());
        materialJob.setDeptId(loginUser.getDeptId());
        materialJob.setCreateBy(loginUser.getUserId());
        materialJob.setCreateTime(new Date());

        if (AppTypeEnum.MATERIAL_LIBRARY.name().equals(param.getAppType())) {
            // 物料库，不需要创建定时任务
        } else {
            XxlJobInfo xxlJobInfo = buildXxlJobInfo(materialJob);
            xxlJobInfo.setAuthor(loginUser.getNickName());
            xxlJobInfo.setTriggerStatus(1);
            String xxlJobId = xxlJobClient.addJob(xxlJobInfo);
            if (ObjectUtil.isEmpty(xxlJobId)) {
                return Result.error(ErrCodeEnum.SYSTEM_ERROR.getCode(), ErrCodeEnum.SYSTEM_ERROR.getMessage());
            }

            materialJob.setXxlJobId(Integer.parseInt(xxlJobId));
        }
        materialJobService.save(materialJob);
        return Result.success();
    }

    /**
     * 更新定时制作任务
     * @param param
     * @param request
     * @return
     */
    @PutMapping
    public Result update(@Validated @RequestBody BuMaterialJobUpdateDto param, HttpServletRequest request) {
        LogUtil.recordOperLog(String.format("修改定时制作任务【%s】", param.getJobName()));
        LoginUser loginUser = tokenService.getLoginUser(request);

        BuMaterialJob materialJob = materialJobService.getById(param.getId());
        if (ObjectUtil.isNull(materialJob)) {
            throw new CustomException(ErrCodeEnum.DATA_NOT_EXIST);
        }

        BeanUtil.copyProperties(param, materialJob, "id", "createBy", "createTime", "xxlJobId");
        materialJob.setUpdateBy(loginUser.getUserId());
        materialJob.setUpdateTime(new Date());

        XxlJobInfo xxlJobInfo = buildXxlJobInfo(materialJob);
        xxlJobInfo.setId(materialJob.getXxlJobId());
        xxlJobClient.updateJob(xxlJobInfo);

        materialJobService.updateById(materialJob);
        return Result.success();
    }

    /**
     * 删除定时制作任务
     * @param id
     * @return
     */
    @DeleteMapping
    public Result delete(@RequestParam("id") String id) {
        BuMaterialJob materialJob = materialJobService.getById(id);
        if (ObjectUtil.isNull(materialJob)) {
            throw new CustomException(ErrCodeEnum.DATA_NOT_EXIST);
        }

        LogUtil.recordOperLog(String.format("删除定时制作任务【%s】",  materialJob.getJobName()));

        xxlJobClient.removeJobById(materialJob.getXxlJobId());
        materialJobService.removeById(id);
        return Result.success();
    }

    /**
     * 查询定时制作任务详情
     * @param id
     * @return
     */
    @GetMapping
    public Result<BuMaterialJobVo> detail(@RequestParam("id") String id) {
        BuMaterialJobVo materialJob = materialJobService.getJobById(id);
        return Result.data(materialJob);
    }

    /**
     * 获取定时制作任务列表
     * @param param
     * @param request
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(BuMaterialJobListDto param, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        filterCondition(loginUser, param);
        startPage();
        List<BuMaterialJobListVo> list = materialJobService.listByCondition(param);
        return getDataTable(list);
    }

    /**
     * 执行一次定时任务
     * @param {"id", ""}
     * @return
     */
    @PutMapping("/run")
    public Result runJob(@Validated @RequestBody BuMaterialJobRunDto param) {
         BuMaterialJob materialJob = materialJobService.getById(param.getId());
         if (ObjectUtil.isNull(materialJob)) {
             throw new CustomException(ErrCodeEnum.DATA_NOT_EXIST);
         }

        LogUtil.recordOperLog(String.format("执行定时制作任务【%s】", materialJob.getJobName()));

        BuMaterialJobRunDto runParam = new BuMaterialJobRunDto();
        runParam.setId(param.getId());
        runParam.setJobName(materialJob.getJobName());
        String jobParam = JSON.toJSONString(runParam);

        xxlJobClient.run(materialJob.getXxlJobId(), jobParam);
        return Result.success();
    }

    /**
     * 启动/停止定时任务
     * @param param
     * @param request
     * @return
     */
    @PutMapping("/status")
    public Result enable(@Validated @RequestBody BuMaterialJobEnableDto param, HttpServletRequest request) {
        BuMaterialJob materialJob = materialJobService.getById(param.getId());
        if (ObjectUtil.isNull(materialJob)) {
            throw new CustomException(ErrCodeEnum.DATA_NOT_EXIST);
        }

        String jobStatusDesc = CommonConstants.NUM_1.equals(param.getJobStatus()) ? "启动" : "停止";
        LogUtil.recordOperLog(String.format("%s定时制作任务【%s】", jobStatusDesc,materialJob.getJobName()));

        materialJobService.lambdaUpdate()
                .set(BuMaterialJob::getJobStatus, param.getJobStatus())
                .set(BuMaterialJob::getUpdateBy, tokenService.getLoginUser(request).getUserId())
                .set(BuMaterialJob::getUpdateTime, new Date())
                .eq(BuMaterialJob::getId, param.getId()).update();

        if (AppTypeEnum.MATERIAL_LIBRARY.name().equals(materialJob.getAppType())) {
            // 物料库，不需要启动/停止定时任务
        } else {
            // 启动/停止定时任务
            if (CommonConstants.NUM_1.equals(param.getJobStatus())) {
                xxlJobClient.startJobById(materialJob.getXxlJobId());
            } else {
                xxlJobClient.stopJobById(materialJob.getXxlJobId());
            }
        }

        return Result.success();
    }

    /**
     * 构建XxlJobInfo对象
     *
     * @param materialJob
     * @return
     * @throws ParseException
     */
    private XxlJobInfo buildXxlJobInfo(BuMaterialJob materialJob) {
        try {
            String materialScopeDesc = (1 == materialJob.getMaterialScope()) ? "企业智能物料" : "个人智能物料";
            String jobDesc = String.format("%s-定时制作任务【%s】", materialScopeDesc, materialJob.getJobName());
            String cron = this.convertToCron(materialJob.getJobTime());

            BuMaterialJobRunDto runParam = new BuMaterialJobRunDto();
            runParam.setId(String.valueOf(materialJob.getId()));
            runParam.setJobName(materialJob.getJobName());
            String jobParam = JSON.toJSONString(runParam);

            XxlJobInfo xxlJobInfo = new XxlJobInfo();
            xxlJobInfo.setJobDesc(jobDesc);
            xxlJobInfo.setScheduleConf(cron);
            xxlJobInfo.setExecutorParam(jobParam);
            xxlJobInfo.setExecutorHandler(XxlJobContant.AUTO_GENERATE_MATERIAL);
            return xxlJobInfo;
        } catch (ParseException e) {
            log.error("任务时间格式错误！", e);
            throw new CustomException("任务时间格式错误！");
        }
    }


    /**
     * 转换cron表达式
     *
     * @param jobTime 任务时间，格式：HH:mm
     * @return cron表达式
     * @throws ParseException 解析异常
     */
    private String convertToCron(String jobTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        Date date = sdf.parse(jobTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);

        // Cron expression format: "second minute hour day-of-month month day-of-week"
        // Since we only have hour and minute, we set second to 0, day-of-month to *, month to *, and day-of-week to ?
        return String.format("0 %d %d * * ?", minute, hour);
    }

}