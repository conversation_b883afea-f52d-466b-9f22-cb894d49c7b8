package com.cenker.scrm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.system.OperLogListDto;
import com.cenker.scrm.pojo.entity.system.SysOperLog;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.system.SysOperLogVo;
import com.cenker.scrm.service.ISysOperLogService;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController extends BaseController {

    private ISysOperLogService operLogService;

    @RequestMapping("/list")
    public TableDataInfo list(@Valid OperLogListDto params) {
        Date startTime = DateUtil.beginOfDay(DateUtil.parseDate(params.getBeginDate()));
        Date endTime = DateUtil.endOfDay(DateUtil.parseDate(params.getEndDate()));

        Page<SysOperLog> page = operLogService.lambdaQuery()
                .eq(StrUtil.isNotBlank(params.getModuleKey()), SysOperLog::getModuleKey, params.getModuleKey())
                .eq(Objects.nonNull(params.getBusinessType()), SysOperLog::getBusinessType, params.getBusinessType())
                .like(StrUtil.isNotBlank(params.getOperName()), SysOperLog::getOperName, params.getOperName())
                .like(StrUtil.isNotBlank(params.getDeptName()), SysOperLog::getDeptName, params.getDeptName())
                .between(SysOperLog::getOperTime, startTime, endTime)
                .orderByDesc(SysOperLog::getOperId).page(new Page<>(params.getPageNum(), params.getPageSize()));

        List<SysOperLogVo> list = page.getRecords().stream()
                .map(log -> BeanUtil.copyProperties(log, SysOperLogVo.class)).collect(Collectors.toList());

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(page.getTotal());
        return rspData;
    }

    // @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
    @RequestMapping("/export")
    public List<SysOperLogVo> export(@Valid OperLogListDto params) {
        return operLogService.list(params);
    }

    // @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @RequestMapping("/{operIds}")
    public AjaxResult remove(@PathVariable Long[] operIds) {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    // @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @RequestMapping("/clean")
    public AjaxResult clean() {
        operLogService.cleanOperLog();
        return AjaxResult.success();
    }

    @RequestMapping("/insertOperlog")
    public void insertOperlog(@RequestBody SysOperLog sysOperLog){
        operLogService.save(sysOperLog);
    }
}
