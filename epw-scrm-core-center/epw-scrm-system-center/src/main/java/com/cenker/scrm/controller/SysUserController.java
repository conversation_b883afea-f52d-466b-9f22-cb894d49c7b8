package com.cenker.scrm.controller;


import com.cenker.scrm.base.ExceptionCodeEnum;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.corp.WxCorpConfigDto;
import com.cenker.scrm.pojo.entity.system.SysRole;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.*;
import com.cenker.scrm.system.model.UserCategoryVo;
import com.cenker.scrm.system.model.UserPermissionVo;
import com.cenker.scrm.util.SecurityUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
@Slf4j
@AllArgsConstructor
public class SysUserController extends BaseController {

    private final ISysUserService userService;

    private final TokenParseUtil tokenService;
    private ISysRoleService roleService;
    private ISysPostService postService;
    private ISysUserOnlineService userOnlineService;
    private IBuCategoryService categoryService;


    /**
     * 获取用户列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @RequestMapping("/export")
    public List<SysUser> export(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        // ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        // util.exportExcel(list, "user", response);
        return list;
    }

    @RequestMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

/*    @RequestMapping("/importTemplate")
    public AjaxResult importTemplate() {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        return util.importTemplateExcel("用户数据");
    }*/

    /**
     * 根据用户编号获取详细信息
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) String userId) {
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            ajax.put(AjaxResult.DATA_TAG, userService.selectUserById(Long.valueOf(userId)));
            ajax.put("postIds", postService.selectPostListByUserId(Long.valueOf(userId)));
            ajax.put("roleIds", roleService.selectRoleListByUserId(Long.valueOf(userId)));
        }
        return ajax;
    }

    /**
     * 新增用户
     * @param user
     * @return
     */
    @RequestMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        } else if (StringUtils.isNotEmpty(user.getCorpUserId())
                && UserConstants.NOT_UNIQUE.equals(userService.checkCorpUserUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，对应企业账号已存在");
        }

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        user.setCorpId(loginUser.getTenantId());
        user.setCreateBy(loginUser.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     * @param user
     * @param request
     * @return
     */
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody SysUser user, HttpServletRequest request) {
        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        } else if (StringUtils.isNotEmpty(user.getCorpUserId())
                && UserConstants.NOT_UNIQUE.equals(userService.checkCorpUserUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，对应企业账号已存在");
        }
        user.setUpdateBy(tokenService.getLoginUser(request).getUsername());
        // 域账号设置为空
        user.setDomainAccount(Objects.isNull(user.getDomainAccount())? StringUtils.EMPTY : user.getDomainAccount());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     * @param userIds
     * @return
     */
    @RequestMapping("/remove/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     * @param user
     * @param request
     * @return
     */
    @RequestMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user, HttpServletRequest request) {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(tokenService.getLoginUser(request).getUsername());
        // 强退该用户
        userOnlineService.forceLogout(user);
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     * @param user
     * @param request
     * @return
     */
    @RequestMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user, HttpServletRequest request) {
        userService.checkUserAllowed(user);
        user.setUpdateBy(tokenService.getLoginUser(request).getUsername());
        return toAjax(userService.updateUserStatus(user));
    }


    /**********************feign调用***********************************/

    /**
     * 校验用户名称是否唯一
     * @param userName
     * @return
     */
    @RequestMapping("/checkUserNameUnique")
    public String checkUserNameUnique(@RequestParam("userName") String userName) {
        return userService.checkUserNameUnique(userName);
    }

    /**
     * 修改账号用户后缀
     * @param wxCorpConfigDto
     */
    @RequestMapping("/updateUserAccountByConfig")
    public void updateUserAccountByConfig(@RequestBody WxCorpConfigDto wxCorpConfigDto) {
        // 原账号后缀
        String beforeAccountSuffix = wxCorpConfigDto.getBeforeAccountSuffix();
        if (StringUtils.isEmpty(beforeAccountSuffix)) {
            // 避免出现异常问题
            return;
        }
        List<SysUser> sysUsers = userService.selectUserByCorpId(wxCorpConfigDto.getCorpId());
        if (CollectionUtils.isNotEmpty(sysUsers)) {
            for (SysUser sysUser : sysUsers) {
                if (sysUser.getUserName().contains(beforeAccountSuffix)) {
                    sysUser.setUserName(sysUser.getUserName().replace(beforeAccountSuffix, wxCorpConfigDto.getAccountSuffix()));
                    userService.updateUserName(sysUser);
                }
            }
        }

    }

    @RequestMapping("/updateUser")
    public Integer updateUser(@RequestBody SysUser user) {
        return userService.updateUser(user);
    }

    @RequestMapping("/stopUserByCorpId")
    public Integer stopUserByCorpId(@RequestParam("authCorpId") String authCorpId) {
        log.info("开始停止该企业所有用户，{}", authCorpId);
        return userService.stopUserByCorpId(authCorpId);
    }

    @RequestMapping("/recoverUserByCorpId")
    public Integer recoverUserByCorpId(@RequestParam("corpId") String corpId) {
        log.info("开始恢复该企业所有用户，{}", corpId);
        return userService.recoverUserByCorpId(corpId);
    }


    /**
     * 根据userId查询对应的sysUser用户id（企业管理员）
     * @param CorpUserId
     * @param corpId
     * @return
     */
    @RequestMapping("/selectSysUserByCorpUserId")
    public String selectSysUserByCorpUserId(@RequestParam("corpUserId") String CorpUserId, @RequestParam("corpId") String corpId) {
        return userService.selectSysUserByCorpUserId(CorpUserId, corpId);
    }

    /**
     * 设置对应账号的用户角色信息（企业管理员）2022-08-10 新增开启管理员建号功能
     * @param tbWxUser
     */
    @RequestMapping("/setUserRole4CorpAdmin")
    public void setUserRole4CorpAdmin(@RequestBody TbWxUser tbWxUser) {
        userService.saveUserByWxUser(tbWxUser);
        // 先判断是否存在后台账号
        /*String userId = userService.selectSysUserByCorpUserId(tbWxUser.getUserid(), tbWxUser.getCorpId());

        // 若拥有管理后台权限
        if (tbWxUser.getAdministrator()) {

            if (StringUtils.isNotEmpty(userId)) {
                log.info("【处理管理员权限逻辑】存在账号并开启用户权限:{}",tbWxUser);
                SysUser user = new SysUser();
                user.setUserId(userId);
                user.setStatus(UserStatus.OK.getCode());
                user.setDelFlag(UserStatus.OK.getCode());
                user.setUpdateBy(Constants.DEFAULT_USER);
                userService.updateUser(user);

                roleService.selectAndSetEpwAdmin(userId, tbWxUser.getRoleId());
                return;
            }
            log.info("【处理管理员权限逻辑】不存在账号，开始创建账号:{}",tbWxUser);
            userService.createSysUserByTbWxUser(tbWxUser);
            return;
        }
        // 代表关闭管理员
        if (StringUtils.isNotEmpty(userId)) {
            log.info("【处理管理员权限逻辑】存在账号并停用用户权限:{}",tbWxUser);
            // SysRole sysRole = roleService.selectRoleByRoleKey("epw_staff");
            // 清除其管理员角色
            // roleService.selectAndSetEpwAdmin(userId, sysRole.getRoleId());
            // 停用账号
            userService.stopUserById(userId);
        }*/
        // userOnlineService.findLoginUsersByUser(userId);
    }

    /**
     * 通过明文企业id和密文userId查询sysUser账号
     * @param openUserId
     * @param corpId
     * @return
     */
    @RequestMapping("/selectUserByOpenUserId")
    public SysUser selectUserByOpenUserId(@RequestParam("userId") String openUserId, @RequestParam("corpId") String corpId) {
        return userService.selectUserByOpenUserId(openUserId, corpId);
    }

    /**
     * 根据sysUserId查询用户
     * @param userId
     * @return
     */
    @RequestMapping("/selectUserById")
    public SysUser selectUserById(@RequestParam("userId") Long userId) {
        return userService.selectUserById(userId);
    }

    /**
     * 根据域账号查询用户
     * @param domainAccount
     * @return
     */
    @RequestMapping("/selectUserByDomainAccount")
    public SysUser selectUserByDomainAccount(@RequestParam("domainAccount") String domainAccount) {
        return userService.selectUserByDomainAccount(domainAccount);
    }

    /**
     * 根据企微账号查询用户
     * @param wxUserId
     * @return
     */
    @RequestMapping("/selectUserByWxUserId")
    public RemoteResult<SysUser> selectUserByWxUserId(@RequestParam("wxUserId") String wxUserId) {
        SysUser user = userService.selectUserByWxUserId(wxUserId);
        return RemoteResult.data(user);
    }

    /**
     * 根据userId查询用户权限
     * @param userId
     * @return
     */
    @RequestMapping("/getUserPermissionByUserId")
    public RemoteResult<UserPermissionVo> getUserPermissionByUserId(@RequestParam("userId") Long userId) {
        SysUser user = userService.selectUserById(userId);
        if (user == null) {
            return RemoteResult.error(ExceptionCodeEnum.ERROR_BUS.getCode(), "用户不存在");
        }

        UserPermissionVo userPermissionVo = roleService.selectUserPermissionByUserId(user);
        return RemoteResult.data(userPermissionVo);
    }

    /**
     * 根据userId查询用户分组权限
     * @param userId
     * @param categoryType
     * @return
     */
    @RequestMapping("/getUserPermissionCategory")
    public RemoteResult<UserCategoryVo> getUserPermissionCategory(@RequestParam("userId")String userId, @RequestParam("categoryType")String categoryType) {
        UserCategoryVo userCategoryVo = categoryService.getUserPermissionCategory(userId, categoryType);
        return RemoteResult.data(userCategoryVo);
    }


}
