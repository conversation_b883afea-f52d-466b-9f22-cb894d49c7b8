package com.cenker.scrm.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.biz.FileService;
import com.cenker.scrm.constants.FileTypeConstant;
import com.cenker.scrm.enums.OssStoragePathEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.dto.system.ImageCompressDto;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.system.UploadVo;
import com.cenker.scrm.util.TokenParseUtil;
import com.cenker.scrm.util.UUID;
import com.cenker.scrm.util.UrlReplaceUtils;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.qcloud.cos.utils.IOUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/9/7
 * @Description 文件处理
 */
@Slf4j
@RequestMapping("/common")
@RestController
@AllArgsConstructor
public class FileController extends BaseController {

    /**
     * 允许上传的图片类型
     */
    public static final String[] ALLOW_IMAGE_TYPES = { "jpg", "png", "jpeg" };

    private TokenParseUtil tokenService;
    private OssStorageFactory ossStorageFactory;
    private FileService fileService;

    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file, Integer mediaType) throws IOException {
//        String fileExtendName = FileTypeUtils.getFileExtendName(file.getBytes());
        String fileExtendName = FileUtil.getSuffix(file.getOriginalFilename());
        if (mediaType != null && (mediaType == 0 || mediaType == 5) && FileTypeConstant.IMAGE_WEBP_UPPER_CASE.equalsIgnoreCase(fileExtendName)) {
            // 转换图片为png
            return AjaxResult.error("上传图片不支持WEBP格式!");
        }

        String dir = getOssPath(mediaType);
        String uploadUrl = ossStorageFactory.build().upload(file.getInputStream(), file.getOriginalFilename(), dir);

        UploadVo uploadVo = new UploadVo();
        uploadVo.setUploadUrl(uploadUrl);
        uploadVo.setFileSize(file.getSize());

        if (StrUtil.isNotBlank(fileExtendName) && Arrays.asList(ALLOW_IMAGE_TYPES).contains(fileExtendName.toLowerCase())) {
            BufferedImage image = ImgUtil.read(file.getInputStream());
            uploadVo.setWidth(image.getWidth());
            uploadVo.setHeight(image.getHeight());
        }

        AjaxResult ajax = AjaxResult.success(uploadVo);
        // 兼容老版本，以前接口设计有问题
        ajax.put("uploadUrl", uploadUrl);
        return ajax;
    }


    @PostMapping("/upload/pdf")
    public AjaxResult upload(MultipartFile file) throws IOException {
        return getAjaxResult(file, OssStoragePathEnum.PDF_PATH.getPath());
    }

    @PostMapping("/upload/moment")
    public AjaxResult upload(MultipartFile file, String mediaType, HttpServletRequest request) throws IOException {
        if (!file.isEmpty()) {
            String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
            String avatar = ossStorageFactory.build().upload(file.getInputStream(), file.getOriginalFilename(),
                    corpId + SeparatorEnum.SLASH.getSeparator() + OssStoragePathEnum.MOMENT_PATH.getPath());
            //String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
            // 直接上传企微接口校验
            // AjaxResult ajax = fileVerifyFeign.verifyAttachment(avatar,mediaType,corpId);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("uploadUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     *
     * @param file
     * @param newDir  指定目录 如：knowledgebaseai
     * @param request
     * @return
     * @throws IOException
     */
    @PostMapping("/upload/dir")
    public AjaxResult uploadDir(MultipartFile file,String newDir,HttpServletRequest request) throws IOException {
        if (!file.isEmpty()) {
            String corpId = tokenService.getLoginUser(request).getUser().getCorpId();
            String dir = OssStoragePathEnum.COMMON_PATH.getPath();
            if (StringUtils.isNotEmpty(newDir)){
                dir = corpId + SeparatorEnum.SLASH.getSeparator()+OssStoragePathEnum.SCRM_PATH.getPath() + newDir + SeparatorEnum.SLASH.getSeparator();
            }
            String sourceFile = ossStorageFactory.build().upload(file.getInputStream(), file.getOriginalFilename(), dir);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("sourceFile", sourceFile);
            return ajax;
        }
        return AjaxResult.error("上传异常，请联系管理员");
    }

    private AjaxResult getAjaxResult(MultipartFile file, String dir) throws IOException {
        if (!file.isEmpty()) {
            String avatar = ossStorageFactory.build().upload(file.getInputStream(), file.getOriginalFilename(), dir);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("uploadUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    @PostMapping("/download")
    public void download(String url, HttpServletResponse response) throws IOException {
        String[] split = url.split("\\.");
//        COSObjectInputStream cosObjectInputStream = qcloudCosUtils.downloadFile(url);
        url = UrlReplaceUtils.urlReplace(url);
        InputStream cosObjectInputStream = ossStorageFactory.build().download(url);
        // 设置响应类型为html，编码为utf-8，处理相应页面文本显示的乱码
        response.setContentType("application/octet-stream");
        // 设置文件头：最后一个参数是设置下载文件名
        response.setHeader("Content-disposition", "attachment;filename=" + UUID.randomUUID() + "." + split[split.length - 1]);
        try {
            response.getOutputStream().write(IOUtils.toByteArray(cosObjectInputStream));
        } catch (IOException e) {
            log.error("文件下载异常，url:{}", url);
            log.error("错误信息:{}", e);
        } finally {
            cosObjectInputStream.close();
        }
    }

    /**
     * 图片压缩
     * @param imageCompressDto
     * @return
     */
    @PostMapping("/image/compress")
    public AjaxResult compressImage(@RequestBody ImageCompressDto imageCompressDto) {
        if (CollectionUtil.isEmpty(imageCompressDto.getFileUrl())) {
            return AjaxResult.success(Collections.emptyList());
        }

        List<String> urls = fileService.compressImage(imageCompressDto.getFileUrl());
        return AjaxResult.success(urls);
    }

    /**
     * 根据文件类型上传至不同目录
     * @param mediaType
     * @return
     */
    private String getOssPath(Integer mediaType) {
        String dir = OssStoragePathEnum.COMMON_PATH.getPath();
        if (Objects.isNull(mediaType)) {
            return dir;
        }

        // 根据文件类型上传至不同目录
        switch (mediaType) {
            case 0:
                return OssStoragePathEnum.IMAGE_PATH.getPath();
            case 1:
                return OssStoragePathEnum.VOICE_PATH.getPath();
            case 2:
                return OssStoragePathEnum.VIDEO_PATH.getPath();
            case 3:
                return OssStoragePathEnum.FILE_PATH.getPath();
            case 5:
                return OssStoragePathEnum.POST_PATH.getPath();
            default:
                return dir;
        }
    }


}
