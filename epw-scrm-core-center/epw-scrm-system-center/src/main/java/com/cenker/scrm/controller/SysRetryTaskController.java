package com.cenker.scrm.controller;

import cn.hutool.core.date.DateUtil;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.pojo.entity.system.SysRetryTask;
import com.cenker.scrm.service.ISysRetryTaskService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@RequestMapping("/system/retry")
public class SysRetryTaskController {

    private final ISysRetryTaskService sysRetryTaskService;

    @PostMapping()
    public RemoteResult saveRetryTask(@RequestBody SysRetryTask task) {
        task.setDelFlag(CommonConstants.NOT_DELETED);
        task.setCreateTime(DateUtil.date());

        sysRetryTaskService.save(task);
        return RemoteResult.success();
    }
}
