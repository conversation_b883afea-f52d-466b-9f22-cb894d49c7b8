package com.cenker.scrm.controller;


import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.TreeSelect;
import com.cenker.scrm.pojo.entity.system.SysDept;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.ISysDeptService;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Iterator;
import java.util.List;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
@Slf4j
public class SysDeptController extends BaseController {

    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private TokenParseUtil tokenParseUtil;

    /**
     * 获取部门列表
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @RequestMapping("/list")
    public AjaxResult list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return AjaxResult.success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @RequestMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) String deptId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        Iterator<SysDept> it = depts.iterator();
        while (it.hasNext()) {
            SysDept d = (SysDept) it.next();
            if (d.getDeptId().equals(deptId)
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + "")) {
                it.remove();
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @RequestMapping("/{deptId}")
    public AjaxResult getInfo(@PathVariable String deptId) {
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表
     */
    @RequestMapping("/treeSelect")
    public AjaxResult treeSelect(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
    }

    /**
     * 加载对应角色部门列表树
     */
    @RequestMapping("/roleDeptTreeSelect/{roleId}")
    public AjaxResult roleDeptTreeSelect(@PathVariable("roleId") Long roleId) {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.treeSelect());
        return ajax;
    }

    /**
     * 新增部门
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @RequestMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysDept dept, HttpServletRequest request) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(tokenParseUtil.getLoginUser(request).getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @RequestMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SysDept dept,HttpServletRequest request) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(dept.getDeptId())) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())
                && deptService.selectNormalChildrenDeptById(dept.getDeptId()) > 0) {
            return AjaxResult.error("该部门包含未停用的子部门！");
        }
        //dept.setUpdateBy(SecurityUtils.getUsername());
        dept.setUpdateBy(tokenParseUtil.getLoginUser(request).getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    // @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @RequestMapping("/remove/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return AjaxResult.error("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return AjaxResult.error("部门存在用户,不允许删除");
        }
        return toAjax(deptService.deleteDeptById(deptId));
    }


    /*********************************以下为其他服务feign调用****************************************************************/

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @RequestMapping("/selectDeptList")
    public List<SysDept> selectDeptList(SysDept dept){
        return deptService.selectDeptList(dept);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @RequestMapping("/buildDeptTree")
    public List<SysDept> buildDeptTree(List<SysDept> depts){
        return deptService.buildDeptTree(depts);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @RequestMapping("/buildDeptTreeSelect")
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts){
        return deptService.buildDeptTreeSelect(depts);
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @RequestMapping("/selectDeptListByRoleId")
    public List<Integer> selectDeptListByRoleId(@RequestParam("roleId") Long roleId){
        return deptService.selectDeptListByRoleId(roleId);
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @RequestMapping("/selectDeptById")
    public SysDept selectDeptById(@RequestParam("deptId")String deptId){
        return deptService.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @RequestMapping("/selectNormalChildrenDeptById")
    public int selectNormalChildrenDeptById(@RequestParam("deptId")String deptId){
        return deptService.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @RequestMapping("/hasChildByDeptId")
    public boolean hasChildByDeptId(@RequestParam("deptId")Long deptId){
        return deptService.hasChildByDeptId(deptId);
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @RequestMapping("/checkDeptExistUser")
    public boolean checkDeptExistUser(@RequestParam("deptId")Long deptId){
        return deptService.checkDeptExistUser(deptId);
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @RequestMapping("/checkDeptNameUnique")
    public String checkDeptNameUnique(SysDept dept){
        return deptService.checkDeptNameUnique(dept);
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @RequestMapping("/insertDept")
    public int insertDept(SysDept dept){
        return deptService.insertDept(dept);
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @RequestMapping("/updateDept")
    public int updateDept(SysDept dept){
        return deptService.updateDept(dept);
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @RequestMapping("/deleteDeptById")
    public int deleteDeptById(@RequestParam("deptId") Long deptId){
        return deptService.deleteDeptById(deptId);
    }

    /**
     * 更新或者保存商户
     * @param dept  商户
     * @return 结果
     */
    @RequestMapping("/saveOrUpdateDept")
    public void   saveOrUpdateDept(@RequestBody SysDept dept){
         log.info("接收部门信息:{}",dept);
         deptService.saveOrUpdateDept(dept);
    }
}
