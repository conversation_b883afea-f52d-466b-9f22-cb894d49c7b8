package com.cenker.scrm.api.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cenker.scrm.api.util.HttpUtils;
import com.cenker.scrm.common.constant.CommonConstants;
import com.cenker.scrm.common.entity.BaseEntity;
import com.cenker.scrm.common.util.RedisUtils;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.common.validator.ValidatorUtils;
import com.cenker.scrm.common.validator.group.AddGroup;
import com.cenker.scrm.common.validator.group.UpdateGroup;
import com.cenker.scrm.enums.DataScopeEnum;
import com.cenker.scrm.project.constant.ProjectRedisKeyConstants;
import com.cenker.scrm.project.entity.*;
import com.cenker.scrm.project.entity.enums.ProjectSourceTypeEnum;
import com.cenker.scrm.project.entity.enums.ProjectStatusEnum;
import com.cenker.scrm.project.entity.struct.ItemDefaultValueStruct;
import com.cenker.scrm.project.request.OperateProjectItemRequest;
import com.cenker.scrm.project.request.QueryProjectItemRequest;
import com.cenker.scrm.project.request.QueryProjectRequest;
import com.cenker.scrm.project.request.SortProjectItemRequest;
import com.cenker.scrm.project.service.*;
import com.cenker.scrm.project.util.SortUtils;
import com.cenker.scrm.project.vo.*;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.text.JsonUtils;
import com.cenker.scrm.wx.mp.constant.WxMpRedisKeyConstants;
import com.cenker.scrm.wx.mp.request.WxMpQrCodeGenRequest;
import com.cenker.scrm.wx.mp.service.WxMpUserService;
import com.cenker.scrm.wx.mp.vo.WxMpUserVO;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chengkeyun
 * @description : 项目
 * @create : 2020-11-18 18:17
 **/
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/user/project")
public class UserProjectController {

    private final UserProjectService projectService;
    private final UserProjectItemService projectItemService;
    private final UserProjectResultService projectResultService;
    private final SortUtils sortUtils;
    private final UserProjectThemeService userProjectThemeService;
    private final UserProjectSettingService userProjectSettingService;
    private final ProjectTemplateService projectTemplateService;
    private final ProjectTemplateItemService projectTemplateItemService;
    private final WxMpUserService wxMpUserService;
    private final RedisUtils redisUtils;

    @Lazy
    @Autowired
    private WxMpService wxMpService;
    private final StringRedisTemplate stringRedisTemplate;

    @Value("${h5.domain}")
    private String questionnaireModelUrl;




    /**
     * 创建项目
     */
    @PostMapping("/create")
    public Result createProject(@RequestBody UserProjectEntity project) {
        LogUtil.logOperDesc(project.getName());
        try {
            ValidatorUtils.validateEntity(project, AddGroup.class);
        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
        project.setKey(IdUtil.fastSimpleUUID());
        project.setStatus(ProjectStatusEnum.CREATE);
        project.setSourceType(ProjectSourceTypeEnum.BLANK);
        projectService.save(project);
        return Result.success(project.getKey());
    }


    /**
     * 从模板创建项目
     */
    @PostMapping("/use-template/create")
    public Result createProjectByTemplate(@RequestBody ProjectTemplateEntity request) {
        String templateKey = request.getKey();
        ProjectTemplateEntity projectTemplateEntity = projectTemplateService.getByKey(templateKey);
        List<ProjectTemplateItemEntity> projectTemplateItemEntities = projectTemplateItemService.listByTemplateKey(templateKey);
        UserProjectEntity userProjectEntity = new UserProjectEntity();
        BeanUtil.copyProperties(projectTemplateEntity, userProjectEntity, UserProjectEntity.Fields.status);
        userProjectEntity.setSourceType(ProjectSourceTypeEnum.TEMPLATE);
        userProjectEntity.setSourceId(projectTemplateEntity.getId().toString());
        userProjectEntity.setKey(IdUtil.fastSimpleUUID());
        userProjectEntity.setStatus(ProjectStatusEnum.CREATE);
        projectService.save(userProjectEntity);
        List<UserProjectItemEntity> userProjectItemEntityList = JsonUtils.jsonToList(JsonUtils.objToJson(projectTemplateItemEntities), UserProjectItemEntity.class);
        userProjectItemEntityList.forEach(item -> item.setProjectKey(userProjectEntity.getKey()));
        projectItemService.saveBatch(userProjectItemEntityList);
        return Result.success(userProjectEntity.getKey());
    }


    /**
     * 项目另存为为模板
     *
     * @param request
     * @return
     */
    @PostMapping("/template/save")
    public Result saveAsProjectTemplate(@RequestBody UserProjectEntity request) {
        UserProjectEntity projectEntity = projectService.getByKey(request.getKey());
        List<UserProjectItemEntity> itemEntityList = projectItemService.listByProjectKey(request.getKey());
        ProjectTemplateEntity projectTemplateEntity = new ProjectTemplateEntity();
        BeanUtil.copyProperties(projectEntity, projectTemplateEntity, UserProjectEntity.Fields.status);
        projectTemplateEntity.setKey(IdUtil.fastSimpleUUID());
        projectTemplateEntity.setCategoryId(CommonConstants.ConstantNumber.FOUR.longValue());
        projectTemplateService.save(projectTemplateEntity);
        List<ProjectTemplateItemEntity> projectTemplateItemList = JsonUtils.jsonToList(JsonUtils.objToJson(itemEntityList), ProjectTemplateItemEntity.class);
        projectTemplateItemList.forEach(item -> item.setProjectKey(projectTemplateEntity.getKey()));
        projectTemplateItemService.saveBatch(projectTemplateItemList);
        return Result.success(projectTemplateEntity.getKey());
    }


    /**
     * 根据条件查询所有项目
     */
    @GetMapping("/list")
    public Result listProjects(QueryProjectRequest.List request) {
        String dataScope = request.getDataScope();
        List<Integer> permissionDeptIds = request.getPermissionDeptIds();
        String userId = request.getUserId();

        List<UserProjectEntity> entityList = projectService.list(Wrappers.<UserProjectEntity>lambdaQuery()
                .eq(UserProjectEntity::getCorpId, request.getCorpId())
                .eq(UserProjectEntity::getCorpPriId, request.getCorpPriId())
                .eq(ObjectUtil.isNotNull(request.getStatus()), UserProjectEntity::getStatus, request.getStatus())
                .and(dataScope != null && !DataScopeEnum.ALL.getValue().equals(dataScope),
                        i -> i.in(CollectionUtil.isNotEmpty(permissionDeptIds), UserProjectEntity::getDeptId, permissionDeptIds)
                                .eq(CollectionUtil.isEmpty(permissionDeptIds), UserProjectEntity::getCreateBy, userId))
                .orderByDesc(BaseEntity::getUpdateTime)
        );
        return Result.success(entityList);
    }

    /**
     * 查询我的项目分页
     */
    @PostMapping("/page")
    public Result queryMyProjects(@RequestBody QueryProjectRequest.Page request) {
        Page<UserProjectListVO> page = request.toMybatisPage();
        request.setUrl(questionnaireModelUrl);
        projectService.selectProjectList(page, request);
        return Result.success(page);
    }


    /**
     * 查询项目
     */
    @GetMapping("/{key}")
    public Result queryProjectByKey(@PathVariable @NotBlank String key) {
        UserProjectEntity project = projectService.getByKey(key);
        if (Objects.nonNull(project)) {
            project.setUrl(questionnaireModelUrl + key);
        }
        UserProjectListVO vo = new UserProjectListVO();
        BeanUtil.copyProperties(project, vo);
        // 增加返回是否开启自动打标签规则配置
        UserProjectSettingEntity setting = userProjectSettingService.lambdaQuery()
                .select(UserProjectSettingEntity::getEnableAutoLabel, UserProjectSettingEntity::getProjectKey)
                .eq(UserProjectSettingEntity::getProjectKey, key)
                .last("limit 1").one();
        if (setting != null) {
            vo.setEnableAutoLabel(setting.getEnableAutoLabel());
        } else {
            vo.setEnableAutoLabel(false);
        }

        return Result.success(vo);
    }


    /**
     * 发布项目
     */
    @PostMapping("/publish")
    public Result publishProject(@RequestBody UserProjectEntity request) {
        int count = projectItemService
                .count(Wrappers.<UserProjectItemEntity>lambdaQuery().eq(UserProjectItemEntity::getProjectKey, request.getKey()));
        if (count == CommonConstants.ConstantNumber.ZERO) {
            return Result.failed("无有效表单项，无法发布");
        }
        UserProjectEntity entity = projectService.getByKey(request.getKey());
        LogUtil.logOperDesc("发布智能表单：" + entity.getName());

        entity.setStatus(ProjectStatusEnum.RELEASE);
        return Result.success(projectService.updateById(entity));
    }

    /**
     * 停止收集
     *
     * @param request
     */
    @PostMapping("/stop")
    public Result stopProject(@RequestBody UserProjectEntity request) {
        UserProjectEntity entity = projectService.getByKey(request.getKey());
        LogUtil.logOperDesc("停止收集：" + entity.getName());

        entity.setStatus(ProjectStatusEnum.STOP);
        return Result.success(projectService.updateById(entity));
    }

    /**
     * 删除项目
     *
     * @param request
     */
    @PostMapping("/delete")
    public Result deleteProject(@RequestBody UserProjectEntity request) {
        UserProjectEntity project = projectService.getByKey(request.getKey());
        LogUtil.logOperDesc(project.getName());

        boolean del = projectService.update(
                Wrappers.<UserProjectEntity>lambdaUpdate()
                    .set(UserProjectEntity::getDeleted,true)
                    .eq(UserProjectEntity::getKey,request.getKey())
        );
        return Result.success(del);
    }


    /**
     * 查询项目详情
     * 包含项目信息 项目保单项信息 项目主题
     *
     * @param key
     */
    @GetMapping("/details/{key}")
    public Result queryProjectDetails(@PathVariable @NotBlank String key) {
        UserProjectEntity project = projectService.getByKey(key);
        List<UserProjectItemEntity> projectItemList = projectItemService.listByProjectKey(key);
        UserProjectThemeVo themeVo = userProjectThemeService.getUserProjectDetails(key);
        return Result.success(new UserProjectDetailVO(project, projectItemList, themeVo));
    }


    /**
     * 项目更新
     *
     * @param project
     */
    @PostMapping("/update")
    public Result updateProject(@RequestBody UserProjectEntity project) {
        LogUtil.logOperDesc(project.getName());
        try {
            ValidatorUtils.validateEntity(project, AddGroup.class);
        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
        UserProjectEntity oldProject = projectService.getByKey(project.getKey());
        if (ObjectUtil.isNotNull(oldProject)) {
            project.setId(oldProject.getId());
            projectService.updateById(project);
        }
        return Result.success();
    }

    /**
     * 表单项最大Id
     */
    @GetMapping("/item/max-form-id")
    public Result queryProjectMaxFormItemId(@RequestParam @NotBlank String key) {
        UserProjectItemEntity entity = projectItemService.getOne(Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, key).select().orderByDesc(UserProjectItemEntity::getFormItemId).last("limit 1"));
        return Result.success(ObjectUtil.isNotNull(entity) ? entity.getFormItemId() : null);
    }

    /**
     * 项目表单项查询
     */
    @GetMapping("/item/list")
    public Result queryProjectItem(QueryProjectItemRequest request) {
        ValidatorUtils.validateEntity(request);
        List<UserProjectItemEntity> itemEntityList = projectItemService.list(Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, request.getKey())
                .eq(ObjectUtil.isNotNull(request.getDisplayType()), UserProjectItemEntity::getDisplayType, request.getDisplayType())
                .orderByAsc(UserProjectItemEntity::getSort)
        );
        return Result.success(itemEntityList);
    }


    /**
     * 项目表单项创建
     *
     * @param request
     */
    @PostMapping("/item/create")
    public Result createProjectItem(@RequestBody OperateProjectItemRequest request) {
        try {
            ValidatorUtils.validateEntity(request, AddGroup.class);
        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
        UserProjectItemEntity entity = formatProjectItem(request);
        //排序下标计算
        entity.setSort(sortUtils.getInitialSortPosition(
                StrUtil.format(ProjectRedisKeyConstants.PROJECT_ITEM_POS_DELTA, request.getProjectKey())));
        boolean save = projectItemService.save(entity);
        return Result.success(new OperateProjectItemVO(entity.getSort(), entity.getId(), save));
    }


    /**
     * 格式化项目Item的数据
     */
    private UserProjectItemEntity formatProjectItem(OperateProjectItemRequest request) {
        //把Map转换成Bean 在转换成Map 去除不在bean字段列表的多字段
        Object bean = BeanUtil.toBeanIgnoreCase(request.getExpand(), request.getType().getExpandClass(), false);
        UserProjectItemEntity entity = new UserProjectItemEntity();
        BeanUtil.copyProperties(request, entity, UserProjectItemEntity.Fields.defaultValue);
        entity.setExpand(BeanUtil.beanToMap(bean));
        //默认值格式化 1判断是否是Json
        Object defaultValue = request.getDefaultValue();
        if (ObjectUtil.isNotEmpty(defaultValue)) {
            boolean json = JSONUtil.isJson(JsonUtils.objToJson(request.getDefaultValue()));
            if (json) {
                entity.setDefaultValue(new ItemDefaultValueStruct(true, JsonUtils.objToJson(request.getDefaultValue())));
            }
        }
        entity.setDefaultValue(new ItemDefaultValueStruct(false, defaultValue));
        return entity;
    }


    /**
     * 表单项更新
     *
     * @param request
     */
    @PostMapping("/item/update")
    public Result updateProjectItem(@RequestBody OperateProjectItemRequest request) {
        try {
            ValidatorUtils.validateEntity(request, UpdateGroup.class);
        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
        boolean update = projectItemService.update(formatProjectItem(request), Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, request.getProjectKey())
                .eq(UserProjectItemEntity::getFormItemId, request.getFormItemId()));
        return Result.success(update);
    }


    /**
     * 表单项删除
     */
    @PostMapping("/item/delete")
    public Result deleteProjectItem(@RequestBody OperateProjectItemRequest request) {
        try {
            ValidatorUtils.validateEntity(request, OperateProjectItemRequest.DeleteGroup.class);
        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
        boolean delete = projectItemService.remove(Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, request.getProjectKey())
                .eq(UserProjectItemEntity::getFormItemId, request.getFormItemId())
                .eq(UserProjectItemEntity::getType, request.getType()));
        return Result.success(delete);
    }

    /**
     * 表单项排序
     *
     * @param request
     */
    @PostMapping("/item/sort")
    public Result sortProjectItem(@RequestBody SortProjectItemRequest request) {
        ValidatorUtils.validateEntity(request);
        if (ObjectUtil.isNull(request.getAfterPosition())
                && ObjectUtil.isNull(request.getBeforePosition())) {
            return Result.success();
        }
        UserProjectItemEntity itemEntity = projectItemService.getOne(Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, request.getProjectKey())
                .eq(UserProjectItemEntity::getFormItemId, request.getFormItemId()));
        Long sort = sortUtils.calcSortPosition(request.getBeforePosition(), request.getAfterPosition());
        itemEntity.setSort(sort);
        boolean b = projectItemService.updateById(itemEntity);
        return Result.success(new OperateProjectItemVO(itemEntity.getSort(), itemEntity.getId(), b));
    }


    /**
     * 项目主题保存
     *
     * @param themeEntity
     */
    @PostMapping("/theme/save")
    public Result saveProjectTheme(@RequestBody UserProjectThemeEntity themeEntity) {
        ValidatorUtils.validateEntity(themeEntity);
        UserProjectThemeEntity entity = userProjectThemeService
                .getOne(Wrappers.<UserProjectThemeEntity>lambdaQuery().eq(UserProjectThemeEntity::getProjectKey, themeEntity.getProjectKey()));
        if (ObjectUtil.isNotNull(entity)) {
            themeEntity.setId(entity.getId());
        }
        return Result.success(userProjectThemeService.saveOrUpdate(themeEntity));
    }


    /**
     * 项目主题查询
     *
     * @param projectKey
     */
    @GetMapping("/theme/{key}")
    public Result queryThemeByKey(@PathVariable("key") String projectKey) {
        UserProjectThemeEntity entity = userProjectThemeService
                .getOne(Wrappers.<UserProjectThemeEntity>lambdaQuery().eq(UserProjectThemeEntity::getProjectKey, projectKey));
        return Result.success(entity);
    }

    /**
     * 项目设置保存
     *
     * @param settingEntity
     */
    @PostMapping("/setting/save")
    public Result saveProjectSetting(@RequestBody UserProjectSettingEntity settingEntity) {
        ValidatorUtils.validateEntity(settingEntity);
        UserProjectSettingEntity entity = userProjectSettingService
                .getOne(Wrappers.<UserProjectSettingEntity>lambdaQuery().eq(UserProjectSettingEntity::getProjectKey, settingEntity.getProjectKey()));
        if (ObjectUtil.isNotNull(entity)) {
            settingEntity.setId(entity.getId());
        }
        userProjectSettingService.saveOrUpdate(settingEntity);
        if (null == settingEntity.getTimedQuantitativeQuantity()) {
            userProjectSettingService.update(
                    Wrappers.<UserProjectSettingEntity>lambdaUpdate()
                    .set(UserProjectSettingEntity::getTimedQuantitativeQuantity,null)
                    .eq(UserProjectSettingEntity::getProjectKey,settingEntity.getProjectKey())
            );
        }

        UserProjectSettingEntity one = userProjectSettingService
                .getOne(Wrappers.<UserProjectSettingEntity>lambdaQuery().eq(UserProjectSettingEntity::getProjectKey, settingEntity.getProjectKey()));

        UserProjectEntity byKey = projectService.getByKey(one.getProjectKey());
        one.setUserProjectName(byKey.getName());

        stringRedisTemplate.delete("pvq:project:setting:" + one.getProjectKey());
        stringRedisTemplate.opsForValue().set("pvq:project:setting:" + one.getProjectKey(), JSONUtil.toJsonStr(one));
        return Result.success();
    }


    /**
     * 项目设置查询
     *
     * @param projectKey
     */
    @GetMapping("/setting/{key}")
    public Result querySettingByKey(@PathVariable("key") String projectKey, @RequestParam(value = "filterTagRule", required = false) boolean filterTagRule) {
        log.info("start 查询智能表单设置信息 projectKey:{}", projectKey);
        UserProjectSettingEntity entity = userProjectSettingService
                .getOne(Wrappers.<UserProjectSettingEntity>lambdaQuery().eq(UserProjectSettingEntity::getProjectKey, projectKey));
        if (filterTagRule) {
            entity.setTagRule(null);
        } else {
            // 处理tagRule ，获取最新的条件
            if (Objects.nonNull(entity)
                    && entity.getEnableAutoLabel()
                    && StrUtil.isNotEmpty(entity.getTagRule())) {
                List<UserProjectItemEntity> itemList = projectItemService.listByProjectKey(projectKey);
                userProjectSettingService.dealTagRule(itemList, entity);
            }
        }

        log.info("end 查询智能表单设置信息 projectKey:{}", projectKey);
        return Result.success(entity);
    }


    /**
     * 当前设置的状态
     */
    @GetMapping("/setting-status")
    public Result querySettingStatus(@RequestParam("projectKey") String projectKey, @RequestParam(value = "wxOpenId",required = false) String wxOpenId, HttpServletRequest request) {
        projectKey = projectKey.split(",")[0];
        return userProjectSettingService.getUserProjectSettingStatus(projectKey, HttpUtils.getIpAddr(request), wxOpenId);
    }


    /**
     * 填写微信通知二维码
     */
    @GetMapping("/wx/notify-qrcode")
    public Result getWxNotifyQrCode(@RequestParam("key") String projectKey) throws WxErrorException {
        String loginSceneStr = JsonUtils.objToJson(new WxMpQrCodeGenRequest(WxMpQrCodeGenRequest.QrCodeType.SUB_NOTIFY, projectKey));
        //5分钟有效
        WxMpQrCodeTicket ticket = wxMpService.getQrcodeService().qrCodeCreateTmpTicket(loginSceneStr, 10 * 60);
        String subNotifyQrcodeUrl = wxMpService.getQrcodeService().qrCodePictureUrl(ticket.getTicket());
        return Result.success(subNotifyQrcodeUrl);
    }


    /**
     * 填写微信通知二维码
     */
    @PostMapping("/wx/delete/notify-user")
    public Result deleteWxNotifyQrCode(@RequestParam("key") String key, @RequestParam("openId") String openId) {
        redisUtils.setRemove(StrUtil.format(WxMpRedisKeyConstants.WX_MP_SUB_NOTIFY, key), openId);
        return Result.success();
    }

    /**
     * 获取项目微信通知用户
     */
    @GetMapping("/wx/notify-user")
    public Result getWxNotifyUser(@RequestParam("key") String projectKey, @RequestParam(required = false) String openIdStr) {
        Set<Object> subNotifyUsers = null;
        if (StrUtil.isNotBlank(openIdStr)) {
            subNotifyUsers = Sets.newHashSet(StrUtil.splitTrim(openIdStr, ";"));
        } else {
            subNotifyUsers = redisUtils.setMembers(StrUtil.format(WxMpRedisKeyConstants.WX_MP_SUB_NOTIFY, projectKey));
        }
        return Result.success(wxMpUserService.listWxMpUserByOpenId(subNotifyUsers)
                .stream().map(item -> new WxMpUserVO(item.getNickname(), item.getHeadImgUrl(), item.getOpenId())).collect(Collectors.toList()));
    }

    /**
     * 回收站项目分页
     */
    @GetMapping("/recycle/page")
    public Result queryRecycleProjects(@RequestBody QueryProjectRequest.Page request) {
        Page page = projectService.page(request.toMybatisPage(),
                Wrappers.<UserProjectEntity>lambdaQuery().eq(UserProjectEntity::getCorpId, request.getCorpId())
                        .eq(UserProjectEntity::getCorpPriId, request.getCorpPriId())
                        .eq(UserProjectEntity::getDeleted, true)
                        .orderByDesc(BaseEntity::getUpdateTime));
        List<UserProjectEntity> records = page.getRecords();
        List<RecycleProjectVO> projectVOList = records.stream().map(item -> {
            int count = projectResultService.count(Wrappers.<UserProjectResultEntity>lambdaQuery().eq(UserProjectResultEntity::getProjectKey, item.getKey()));
            return new RecycleProjectVO(item.getKey(), count, item.getName(), item.getCreateTime(), item.getUpdateTime());
        }).collect(Collectors.toList());
        page.setRecords(projectVOList);
        return Result.success(page);
    }

    /**
     * 从回收站中恢复项目
     */
    @PostMapping("/recycle/restore")
    public Result restoreRecycleProject(@RequestBody UserProjectEntity request) {
        boolean flag = projectService.update(
                new UserProjectEntity() {{
                    setDeleted(Boolean.FALSE);
                }},
                Wrappers.<UserProjectEntity>lambdaQuery().eq(UserProjectEntity::getKey, request.getKey()));
        return Result.success(flag);
    }

    /**
     * 从回收站中删除项目
     */
    @PostMapping("/recycle/delete")
    public Result deleteRecycleProject(@RequestBody UserProjectEntity projectEntity) {
        boolean remove = projectService.remove(Wrappers.<UserProjectEntity>lambdaQuery()
                .eq(UserProjectEntity::getCorpId, projectEntity.getCorpId())
                .eq(UserProjectEntity::getCorpPriId, projectEntity.getCorpPriId())
                .eq(UserProjectEntity::getKey, projectEntity.getKey()));
        if (remove) {
            userProjectThemeService.remove(Wrappers.<UserProjectThemeEntity>lambdaQuery()
                    .eq(UserProjectThemeEntity::getProjectKey, projectEntity.getKey()));
            userProjectSettingService.remove(Wrappers.<UserProjectSettingEntity>lambdaQuery()
                    .eq(UserProjectSettingEntity::getProjectKey, projectEntity.getKey()));
        }
        return Result.success(remove);
    }


}