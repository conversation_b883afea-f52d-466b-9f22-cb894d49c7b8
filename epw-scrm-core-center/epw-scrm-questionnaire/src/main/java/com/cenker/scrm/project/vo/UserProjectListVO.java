package com.cenker.scrm.project.vo;


import cn.hutool.core.date.DatePattern;
import com.cenker.scrm.project.entity.UserProjectItemEntity;
import com.cenker.scrm.project.entity.enums.ProjectSourceTypeEnum;
import com.cenker.scrm.project.entity.enums.ProjectStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/14
 * @Description
 */
@Data
public class UserProjectListVO {
    private Long id;
    /**
     * 项目code
     */
    private String key;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目描述
     */
    private String describe;
    /**
     * 项目来源
     */
    private ProjectSourceTypeEnum sourceType;

    /**
     * 来源ID
     */
    private String sourceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业唯一标识
     */
    private Long corpPriId;

    /***
     * 状态
     */
    private ProjectStatusEnum status;
    /**
     * 项目类型
     */
    private Integer type;
    /**
     * 是否客户画像表单
     */
    private Boolean customerPortraitProject;
    /**
     * 项目地址
     */
    private String url;

    private Boolean deleted;

    private Long createBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    private Long updateBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
    /**
     * 部门id
     */
    private Integer deptId;

    private String cover;

    /**
     * 浏览量
     */
    private Long viewCount;

    /**
     * 有效回收量
     */
    private Long completeCount;
    /**
     * 是否开启自动打标签
     */
    private Boolean enableAutoLabel;
    /**
     * 表单项
     */
    private List<UserProjectItemEntity> projectItems;
}
