package com.cenker.scrm.api.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.cenker.scrm.api.web.client.OauthFeign;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.google.common.collect.Maps;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR> chengkeyun
 * @description : 微信网页开发
 * @create : 2020-12-02 13:40
 **/
@RestController
@RequestMapping("/wx/jsapi/")
public class WxJsApiController {
    @Lazy
    @Autowired
    private WxMpService wxService;
    @Autowired
    private OauthFeign oauthFeign;

    /**
     * 用户授权url
     *
     * @return hcah
     */
    @GetMapping("/authorization/url")
    public Result getAuthorizationUrl(@RequestParam @NotBlank String url) {
        // 前端授权
        url = url.split(",")[0];
        String appId = wxService.getWxMpConfigStorage().getAppId();
        String authorizationUrl = StrUtil.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid={}&redirect_uri={}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect", appId, URLUtil.encode(url));

        // 后端授权
//        url = url.split(",")[0];
//        String redirect_uri = questionnaireModelServerUrl + "api/customer/wx/callback/h5";
//        String appId = wxService.getWxMpConfigStorage().getAppId();
//        String authorizationUrl =
//                "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appId
//                        + "&redirect_uri=" + redirect_uri + "?from=" + Base64.getEncoder().encodeToString(url.getBytes())
//                        + "&response_type=code&scope=snsapi_userinfo&state=chengkeyun#wechat_redirect";

        return Result.success(authorizationUrl);
    }


    /**
     * 根据code获取用户信息
     *
     * @param code
     * @return
     */
    @GetMapping("/authorization/user/info")
    public Result<WxOAuth2UserInfo> greetUser(@RequestParam @NotBlank String code) throws WxErrorException {
        code = code.split(",")[0];
        WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
        WxOAuth2UserInfo userInfo = wxService.getOAuth2Service().getUserInfo(accessToken, null);
        return Result.success(userInfo);
    }


    /**
     * 签名
     *
     * @param url 1
     * @return
     * @throws WxErrorException
     */
    @GetMapping("/signature")
    public Result getSignature(@RequestParam String url) throws WxErrorException {
        WxJsapiSignature signature = wxService.createJsapiSignature(url);
        return Result.success(signature);
    }

    @GetMapping("/getAnonymityToken")
    public Result getSignature(){
        // 登录
        Map<String, Object> result = Maps.newHashMap();
        MpWxUser mpWxUser = new MpWxUser();
        H5LoginUser h5LoginUser = new H5LoginUser();
        h5LoginUser.setMpWxUser(mpWxUser);
        String token = oauthFeign.getToken(h5LoginUser);
        result.put("token", token);
        return Result.success(result);
    }

}
