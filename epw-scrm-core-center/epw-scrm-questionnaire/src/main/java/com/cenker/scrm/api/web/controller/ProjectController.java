package com.cenker.scrm.api.web.controller;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.common.mybatis.wrapper.JsonWrappers;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.project.entity.ProjectThemeEntity;
import com.cenker.scrm.project.request.QueryProThemeRequest;
import com.cenker.scrm.project.service.ProjectThemeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> chengkeyun
 * @description :
 * @create : 2020-11-24 10:13
 **/

@RestController
@RequestMapping("/project")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectThemeService projectThemeService;

    @GetMapping("/theme/list")
    public Result queryThemes(QueryProThemeRequest request) {
        List<ProjectThemeEntity> list = projectThemeService.list(JsonWrappers.<ProjectThemeEntity>jsonLambdaQuery()
                .jsonConcat(StrUtil.isNotBlank(request.getColor()), ProjectThemeEntity.Fields.color, StrUtil.EMPTY, request.getColor())
                .jsonConcat(StrUtil.isNotBlank(request.getStyle()), ProjectThemeEntity.Fields.style, StrUtil.EMPTY, request.getStyle()));
        return Result.success(list);
    }
}
