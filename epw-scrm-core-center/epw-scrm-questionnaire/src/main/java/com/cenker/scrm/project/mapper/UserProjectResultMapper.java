package com.cenker.scrm.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.project.entity.UserProjectResultEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 项目表单项(ProjectResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-11-23 14:09:21
 */
public interface UserProjectResultMapper extends BaseMapper<UserProjectResultEntity> {
    /**
     * 查询账户名
     * @param userId
     * @return
     */
    String selectUserNameByUserId(@Param("userId") String userId);
}