package com.cenker.scrm.api.web.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.common.util.RedisUtils;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.project.constant.ProjectRedisKeyConstants;
import com.cenker.scrm.project.entity.UserProjectResultEntity;
import com.cenker.scrm.project.service.ProjectDashboardService;
import com.cenker.scrm.project.service.UserProjectResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * <AUTHOR> chengkeyun
 * @description :
 * @create :  2020/12/30 16:47
 **/

@RestController
@RequiredArgsConstructor
@RequestMapping("/user/project/report")
public class ProjectDashboardController {

    private final RedisUtils redisUtils;
    private final UserProjectResultService userProjectResultService;
    private final ProjectDashboardService projectDashboardService;

    /**
     * 项目收集信息
     */
    @GetMapping("/stats")
    public Result projectReportStats(String projectKey) {
        //浏览量
        Long viewCount = redisUtils.hmSize(StrUtil.format(ProjectRedisKeyConstants.PROJECT_VIEW_IP_LIST, projectKey));
        //平均完成时间
        Map<String, Object> resultMap = userProjectResultService.getMap(Wrappers.<UserProjectResultEntity>query().select(" IFNULL(ROUND(AVG(complete_time),0),0) as avgCompleteTime, count(1) as completeCount").eq("project_key", projectKey));
        resultMap.put("viewCount", viewCount);
        return Result.success(resultMap);
    }


    /**
     * 项目收集情况 按周查看
     */
    @GetMapping("/situation")
    public Result projectReportSituation(String projectKey) {
        return Result.success(projectDashboardService.projectReportSituation(projectKey));
    }


    /**
     * 项目收集位置情况
     */
    @GetMapping("/position")
    public Result projectReportPosition(String projectKey) {
        return Result.success(projectDashboardService.projectReportPosition(projectKey));
    }


    /**
     * 项目收集设备
     */
    @GetMapping("/device")
    public Result projectReportDevice(String projectKey) {
        return Result.success(projectDashboardService.projectReportDevice(projectKey));
    }


    /**
     * 项目收集来源
     */
    @GetMapping("/source")
    public Result projectReportSource(String projectKey) {
        return Result.success(projectDashboardService.projectReportSource(projectKey));
    }

    /**
     * 数据分析
     */
    @GetMapping("/analysis")
    public Result projectReportAnalysis(String projectKey) {
        return Result.success(projectDashboardService.projectReportAnalysis(projectKey));
    }
}
