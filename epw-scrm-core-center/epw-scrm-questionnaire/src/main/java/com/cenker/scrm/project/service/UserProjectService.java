package com.cenker.scrm.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.project.entity.UserProjectEntity;
import com.cenker.scrm.project.request.QueryProjectRequest;
import com.cenker.scrm.project.vo.UserProjectListVO;

/**
 * 项目表(Project)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-18 18:16:18
 */
public interface UserProjectService extends IService<UserProjectEntity> {


    /**
     * 根据key获取
     *
     * @param key
     * @return
     */
    UserProjectEntity getByKey(final String key);

    /**
     * 列表数据
     * @param page
     * @param request
     * @return
     */
    IPage<UserProjectListVO> selectProjectList(Page<UserProjectListVO> page, QueryProjectRequest.Page request);
}