package com.cenker.scrm.project.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.common.constant.CommonConstants;
import com.cenker.scrm.common.entity.BaseEntity;
import com.cenker.scrm.common.util.AddressUtils;
import com.cenker.scrm.common.util.AsyncProcessUtils;
import com.cenker.scrm.common.util.RedisUtils;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.enums.TagSource;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.manager.QuestionMqSendMessageManager;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.TagRuleDto;
import com.cenker.scrm.pojo.dto.message.QryAndListDTO;
import com.cenker.scrm.pojo.dto.message.QryOrListDTO;
import com.cenker.scrm.pojo.entity.enums.OperRelEnum;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.exception.BaseException;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.project.entity.UserProjectEntity;
import com.cenker.scrm.project.entity.UserProjectItemEntity;
import com.cenker.scrm.project.entity.UserProjectResultEntity;
import com.cenker.scrm.project.entity.UserProjectSettingEntity;
import com.cenker.scrm.project.entity.enums.ProjectItemTypeEnum;
import com.cenker.scrm.project.entity.struct.UploadResultStruct;
import com.cenker.scrm.project.mapper.UserProjectMapper;
import com.cenker.scrm.project.mapper.UserProjectResultMapper;
import com.cenker.scrm.project.mapper.UserProjectSettingMapper;
import com.cenker.scrm.project.request.QueryProjectResultRequest;
import com.cenker.scrm.project.service.UserProjectItemService;
import com.cenker.scrm.project.service.UserProjectResultService;
import com.cenker.scrm.project.vo.ExportProjectResultVO;
import com.cenker.scrm.wx.WxExtCustomerFeign;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.cky.common.storage.util.StorageUtils;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.cenker.scrm.project.constant.ProjectRedisKeyConstants.PROJECT_RESULT_NUMBER;

/**
 * 项目表单项(ProjectResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-23 14:09:22
 */
@Slf4j
@Service("projectResultService")
@RequiredArgsConstructor
public class UserProjectResultServiceImpl extends ServiceImpl<UserProjectResultMapper, UserProjectResultEntity> implements UserProjectResultService {

    private final UserProjectItemService userProjectItemService;
    private final RedisUtils redisUtils;
    private final OssStorageFactory ossStorageFactory;
    private final UserProjectSettingMapper userProjectSettingMapper;
    private final QuestionMqSendMessageManager questionMqSendMessageManager;
    private final WxExtCustomerFeign wxExtCustomerFeign;
    private final UserProjectMapper userProjectMapper;
    /**
     * 需要处理类型
     */
    private final Set<ProjectItemTypeEnum> needProcessItemTypeSet =
            Sets.newHashSet(ProjectItemTypeEnum.SELECT, ProjectItemTypeEnum.RADIO, ProjectItemTypeEnum.CHECKBOX, ProjectItemTypeEnum.CASCADER);


    @Override
    public void saveProjectResult(UserProjectResultEntity entity) {
        String projectKey = entity.getProjectKey();
        entity.setSerialNumber(redisUtils.incr(StrUtil.format(PROJECT_RESULT_NUMBER, projectKey), CommonConstants.ConstantNumber.ONE));
        entity.setSubmitAddress(AddressUtils.getRealAddressByIP(entity.getSubmitRequestIp()));
        this.save(entity);

        this.markTag(entity);
    }

    /**
     * 根据表单设置的打标签规则，自动会提交表单的客户打上标签
     * @param entity
     */
    private void markTag(UserProjectResultEntity entity) {
        log.info("【客户提交表单】开始为客户添加标签！projectKey={}, unionId={}", entity.getProjectKey(), entity.getWxUnionId());
        UserProjectSettingEntity userProjectSetting = userProjectSettingMapper.selectOne(Wrappers.<UserProjectSettingEntity>lambdaQuery().eq(UserProjectSettingEntity::getProjectKey, entity.getProjectKey()));
        if (Objects.isNull(userProjectSetting)
                || !userProjectSetting.getEnableAutoLabel()
                || StrUtil.isBlank(userProjectSetting.getTagRule())
                || StrUtil.isBlank(entity.getWxUnionId())) {
            log.warn("【客户提交表单】没有开启自动打标签！projectKey={}, unionId={}", entity.getProjectKey(), entity.getWxUnionId());
            return;
        }

        RemoteResult<TbWxExtCustomer> wxExtCustomerRemoteResult = wxExtCustomerFeign.getCustomerByUnionId(entity.getWxUnionId());
        if (!wxExtCustomerRemoteResult.isSuccess() || wxExtCustomerRemoteResult.getData() == null) {
            log.warn("【客户提交表单】获取客户信息失败！projectKey={}, unionId={}", entity.getProjectKey(), entity.getWxUnionId());
            return;
        }

        TbWxExtCustomer wxExtCustomer = wxExtCustomerRemoteResult.getData();

        try {
            UserProjectEntity userProject = userProjectMapper.selectOne(Wrappers.<UserProjectEntity>lambdaQuery().eq(UserProjectEntity::getKey, entity.getProjectKey()));

            List<TagVO> tagList = new ArrayList<>();
            List<TagRuleDto> tagRuleList = JSON.parseArray(userProjectSetting.getTagRule(), TagRuleDto.class);
            for (TagRuleDto tagRule : tagRuleList) {
                QryOrListDTO condition = JSON.parseObject(tagRule.getCondition(), QryOrListDTO.class);

                // 如果满足条件，则给该客户打上标签
                if (CollectionUtil.isNotEmpty(tagRule.getTagList()) && matchCondition(condition, entity.getProcessData(), entity.getOriginalData())) {
                    tagList.addAll(tagRule.getTagList());
                }
            }
            log.info("【客户提交表单】为客户添加标签！projectKey={}, unionId={}, 可以添加标签数量：{}", entity.getProjectKey(), entity.getWxUnionId(), tagList.size());
            if (CollectionUtil.isNotEmpty(tagList)) {
                String userName = this.baseMapper.selectUserNameByUserId(userProject.getUserId());
                CustomerAddTagMsgDto customerAddTagMsgDto = CustomerAddTagMsgDto.builder()
                        .eventTypeEnum(TrackEventTypeEnum.AUTO_TAGGING_SMART_MATERIAL)
                        .corpId(entity.getCorpId())
                        .externalUserIds(Collections.singletonList(wxExtCustomer.getExternalUserId()))
                        .tagList(tagList)
                        .tagSource(TagSource.SMART_FORM.name())
                        .fromCallback(false)
                        .userId(userName)
                        .name(userProject.getName())
                        .isRetry(false).build();
                questionMqSendMessageManager.sendCustomerAddTagMessage(customerAddTagMsgDto);
                log.info("【客户提交表单】发送客户添加标签MQ消息成功！projectKey={}, unionId={}",
                        entity.getProjectKey(), entity.getWxUnionId());
            }
        } catch (Exception e) {
            log.error("【客户提交表单】为客户添加标签失败！projectKey={}, unionId={}", entity.getProjectKey(), entity.getWxUnionId(), e);
        }
    }

    @Override
    public Page listByQueryConditions(QueryProjectResultRequest request) {
        LambdaQueryWrapper<UserProjectResultEntity> lambdaQueryWrapper = Wrappers.<UserProjectResultEntity>lambdaQuery()
                .eq(UserProjectResultEntity::getProjectKey, request.getProjectKey())
                .le(ObjectUtil.isNotNull(request.getEndDateTime()), UserProjectResultEntity::getCreateTime, request.getEndDateTime())
                .ge(ObjectUtil.isNotNull(request.getBeginDateTime()), UserProjectResultEntity::getCreateTime, request.getBeginDateTime())
                .orderByDesc(BaseEntity::getCreateTime);
        if (ObjectUtil.isNotNull(request.getExtParamsMap())) {
            request.getExtParamsMap().keySet().forEach(item -> {
                String comparison = MapUtil.getStr(request.getExtComparisonsMap(), item);
                QueryProjectResultRequest.QueryComparison queryComparison = QueryProjectResultRequest.QueryComparison.get(comparison);
                Object value = request.getExtParamsMap().get(item);
                if (queryComparison == QueryProjectResultRequest.QueryComparison.LIKE) {
                    value = "'%" + value + "%'";
                }
                lambdaQueryWrapper.apply(StrUtil.format("original_data ->'$.{}' {} {} ", item, queryComparison.getKey(), value));
            });
        }
        Page page = this.page(request.toMybatisPage(), lambdaQueryWrapper);

        // 3.1.4 微信昵称/头像统一取tb_wx_ext_customer表数据
        List<UserProjectResultEntity> lstEntity = page.getRecords();
        List<String> lstWxUnionIds = lstEntity.stream().map(UserProjectResultEntity::getWxUnionId).distinct().collect(Collectors.toList());
        Map<String, TbWxExtCustomer> mapTbWxExtCustomer = new HashMap<>();
        for (String wxUnionId : lstWxUnionIds) {
            RemoteResult<TbWxExtCustomer> wxExtCustomerRemoteResult = wxExtCustomerFeign.getCustomerByUnionId(wxUnionId);
            if (wxExtCustomerRemoteResult.isSuccess() && null != wxExtCustomerRemoteResult.getData()) {
                mapTbWxExtCustomer.put(wxUnionId, wxExtCustomerRemoteResult.getData());
            }
        }

        for (UserProjectResultEntity userProjectResultEntity : lstEntity) {
            if (mapTbWxExtCustomer.containsKey(userProjectResultEntity.getWxUnionId())) {
                TbWxExtCustomer customer = mapTbWxExtCustomer.get(userProjectResultEntity.getWxUnionId());
                userProjectResultEntity.setWxOpenName(customer.getName());
                userProjectResultEntity.setWxHeadImgUrl(customer.getAvatar());
            }
        }

        return page;
    }

    @Override
    public ExportProjectResultVO exportProjectResult(QueryProjectResultRequest request) {
        // 问题列表
        String projectKey = request.getProjectKey();
        List<UserProjectItemEntity> userProjectItemEntityList = userProjectItemService.listByProjectKey(projectKey);
        // excel 标题列
        List<ExportProjectResultVO.ExcelHeader> titleList = userProjectItemEntityList.stream()
                .map(item -> new ExportProjectResultVO.ExcelHeader(item.getFormItemId().toString(), item.getLabel()))
                .collect(Collectors.toList());
        // 结果
        List<UserProjectResultEntity> resultEntityList = this.list(Wrappers.<UserProjectResultEntity>lambdaQuery()
                .eq(UserProjectResultEntity::getProjectKey, request.getProjectKey())
                .le(ObjectUtil.isNotNull(request.getEndDateTime()), UserProjectResultEntity::getCreateTime, request.getEndDateTime())
                .ge(ObjectUtil.isNotNull(request.getBeginDateTime()), UserProjectResultEntity::getCreateTime, request.getBeginDateTime())
                .orderByDesc(BaseEntity::getCreateTime));
        if (CollectionUtil.isEmpty(resultEntityList)) {
            throw new BaseException("此表单无有效反馈，不能导出");
        }
        // 导出会使用第一行数据作为标题 第一行未填写的数据需要填充默认值 不然导出会存在列丢失
        AtomicReference<Boolean> isFillRow = new AtomicReference<>(false);
        List<Map<String, Object>> resultList = resultEntityList.stream().map(item -> {
            Map<String, Object> processData = item.getProcessData();

            if (!isFillRow.get()) {
                titleList.stream()
                        .map(ExportProjectResultVO.ExcelHeader::getFieldKey).collect(Collectors.toList()).forEach(key -> {
                            if (!processData.containsKey(key)){
                                processData.put(key, StrUtil.EMPTY);
                            }
                        });
                isFillRow.set(true);
            }

            Iterator<String> iterator = processData.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                List<String> titleStrList = titleList.stream()
                        .map(ExportProjectResultVO.ExcelHeader::getFieldKey).collect(Collectors.toList());
                // 不存在导出列的数据移除掉 避免多于字段导致excel格式错乱
                if (!titleStrList.contains(key)) {
                    iterator.remove();
                }
            }

            processData.put(UserProjectResultEntity.Fields.wxOpenName,item.getWxOpenName());
            processData.put(BaseEntity.Fields.createTime, DateUtil.formatDateTime(item.getCreateTime()));
            processData.put(UserProjectResultEntity.Fields.submitAddress, item.getSubmitAddress());
            return processData;
        }).collect(Collectors.toList());

        List<ExportProjectResultVO.ExcelHeader> allHeaderList = new ArrayList<>();
        allHeaderList.addAll(ExportProjectResultVO.DEFAULT_HEADER_NAME);
        allHeaderList.addAll(titleList);
        return new ExportProjectResultVO(allHeaderList, resultList);
    }

    /**
     * 下载项目结果中的附件
     *
     * @param request
     * @return
     */
    @Override
    public Result downloadProjectResultFile(QueryProjectResultRequest request) {
        List<UserProjectItemEntity> userProjectItemEntityList = userProjectItemService.list(Wrappers.<UserProjectItemEntity>lambdaQuery()
                .eq(UserProjectItemEntity::getProjectKey, request.getProjectKey())
                .eq(UserProjectItemEntity::getType, ProjectItemTypeEnum.UPLOAD));
        String filed = "field";
        // 临时下载文件位置
        ApplicationHome home = new ApplicationHome(getClass());
        File path = home.getSource();
        String uuid = IdUtil.fastSimpleUUID();
        StringBuffer downloadPath = new StringBuffer(path.getParentFile().toString()).append(File.separator).append(uuid).append(File.separator);
        System.out.println(downloadPath);
        //结果
        List<UserProjectResultEntity> resultEntityList = this.list(Wrappers.<UserProjectResultEntity>lambdaQuery()
                .eq(UserProjectResultEntity::getProjectKey, request.getProjectKey())
                .le(ObjectUtil.isNotNull(request.getEndDateTime()), UserProjectResultEntity::getCreateTime, request.getEndDateTime())
                .ge(ObjectUtil.isNotNull(request.getBeginDateTime()), UserProjectResultEntity::getCreateTime, request.getBeginDateTime())
                .orderByDesc(BaseEntity::getCreateTime));
        if (CollectionUtil.isEmpty(resultEntityList) || CollectionUtil.isEmpty(userProjectItemEntityList)) {
            return Result.failed("暂无收集附件，无法下载");
        }

        ThreadUtil.execAsync(() -> {
            try {
                resultEntityList.forEach(result -> {
                    int index = 0;
                    userProjectItemEntityList.forEach(item -> {
                        StringBuffer tempDownloadPath = new StringBuffer(downloadPath).append(item.getFormItemId());
                        UploadResultStruct uploadResult = MapUtil.get(result.getProcessData(), filed + item.getFormItemId(), UploadResultStruct.class);
                        if (ObjectUtil.isNotNull(uploadResult) && CollectionUtil.isNotEmpty(uploadResult.getFiles())) {
                            uploadResult.getFiles().forEach(uFile -> {
                                if (StrUtil.isNotBlank(uFile.getUrl())) {
                                    File downFile = FileUtil.file(new StringBuffer(tempDownloadPath).append(File.separator)
                                            .append(result.getId()).append(CharUtil.DASHED).append(uFile.getFileName()).toString());
                                    HttpUtil.downloadFile(uFile.getUrl(), downFile);
                                }
                            });
                        }
                    });
                    AsyncProcessUtils.setProcess(uuid, ++index / resultEntityList.size() + 1);
                });
                // 压缩上传oss
                File zip = ZipUtil.zip(downloadPath.toString());
                String downloadUrl = ossStorageFactory.build().upload(new FileInputStream(zip), StorageUtils.generateFileName("download", ".zip"));
                AsyncProcessUtils.setProcess(uuid, downloadUrl);
                //删除临时文件
                FileUtil.del(zip);
                FileUtil.del(downloadPath.toString());
            } catch (Exception e) {
                log.error("download file", e);
            }
        });
        return Result.success(uuid);
    }

    /**
     * 判断是否满足条件
     *
     * @param orList       自动打标签规则
     * @param resultData   表单结果数据
     * @param originalData
     * @return
     */
    private boolean matchCondition(QryOrListDTO orList, Map<String, Object> resultData, Map<String, Object> originalData) {
        if(orList == null || CollectionUtil.isEmpty(orList.getOrList())){
            return false;
        }

        boolean isMatched = false;

        //外层或条件
        for (QryAndListDTO qryDto : orList.getOrList()) {
            if (CollectionUtil.isEmpty(qryDto.getAndList())) {
                continue;
            }

            List<QryAndListDTO.QryWhereVo> andList = qryDto.getAndList();

            //or条件里面的and查询条件列表
            for (QryAndListDTO.QryWhereVo whereVo : andList) {
                if (StrUtil.isBlank(whereVo.getConditionValue()) || StrUtil.isBlank(whereVo.getOperRelId())) {
                    continue;
                }

                // 且条件，只要一个条件不满足,则退出判断
                if (!matchCondition(whereVo.getConditionKey(), whereVo.getOperRelId(), whereVo.getConditionValue(), whereVo.getNewConditionValue(), resultData, originalData)) {
                    isMatched = false;
                    break;
                }

                // 且条件，必须所有条件都满足，才算匹配成功，只有一个条件满足，需继续判断
                isMatched = true;
            }

            // or条件，只要有一个条件满足,则退出判断
            if (isMatched) {
                break;
            }
        }

        return isMatched;
    }

    /**
     * 判断条件是否满足
     *
     * @param conditionKey      题目id
     * @param operRelId         条件关系
     * @param conditionValue    条件值，多选题的label值
     * @param newConditionValue 新条件值，多选题的编码值
     * @param resultData        表单结果数据
     * @param originalData     表单结果数据，但对于选择题，保存的是其编码值
     * @return
     */
    private boolean matchCondition(String conditionKey, String operRelId, String conditionValue, String newConditionValue, Map<String, Object> resultData, Map<String, Object> originalData) {
        // 如果题目答案为空，直接判定不满足条件
        String resultValue = (String)resultData.get("field" + conditionKey);
        String originalValue = null;
        Object originalObj = originalData.get("field" + conditionKey);
        if (originalObj instanceof List) {
            List originalList = (List<String>)originalObj;
            originalValue = (String) originalList.stream().map(String::valueOf).collect(Collectors.joining(","));
        } else {
            originalValue = String.valueOf(originalObj);
        }
        if (StrUtil.isBlank(resultValue) && StrUtil.isBlankIfStr(originalValue)) {
            return false;
        }
        // newConditionValue为空，表示是历史规则，使用label作为条件值
        String finalResultValue = StrUtil.isBlank(newConditionValue)? resultValue : originalValue;
        String finalConditionValue = StrUtil.isBlank(newConditionValue)? conditionValue : newConditionValue;

        if(OperRelEnum.OPER_REL_IN.getValue().equalsIgnoreCase(operRelId)) {
            return in(finalResultValue, finalConditionValue);
        } else if (OperRelEnum.OPER_REL_NOTIN.getValue().equalsIgnoreCase(operRelId)) {
            return notIn(finalResultValue, finalConditionValue);
        } else if (OperRelEnum.OPER_REL_LIKE.getValue().equalsIgnoreCase(operRelId)) {
            return like(finalResultValue, finalConditionValue);
        } else if (OperRelEnum.OPER_REL_NOTLIKE.getValue().equalsIgnoreCase(operRelId)) {
            return notLike(finalResultValue, finalConditionValue);
        } else {
            return false;
        }
    }

    /**
     * 判断是否匹配
     * @param resultValue
     * @param conditionValue
     * @return
     */
    private boolean like(String resultValue, String conditionValue) {
        return StrUtil.contains(resultValue, conditionValue);
    }

    /**
     * 判断是否不匹配
     * @param resultValue
     * @param conditionValue
     * @return
     */
    private boolean notLike(String resultValue, String conditionValue) {
        return !StrUtil.contains(resultValue, conditionValue);
    }

    /**
     * 判断是否包含
     * @param resultValue
     * @param conditionValue
     * @return
     */
    private boolean in(String resultValue, String conditionValue) {
        List<String> resultValueList = StrUtil.splitTrim(resultValue, ",");
        List<String> conditionValueList = StrUtil.splitTrim(conditionValue, ",");

        return CollectionUtil.isNotEmpty(resultValueList) && CollectionUtil.containsAny(resultValueList, conditionValueList);
    }

    /**
     * 判断是否不包含
     * @param resultValue
     * @param conditionValue
     * @return
     */
    private boolean notIn(String resultValue, String conditionValue) {
        List<String> resultValueList = StrUtil.splitTrim(resultValue, ",");
        List<String> conditionValueList = StrUtil.splitTrim(conditionValue, ",");

        return CollectionUtil.isEmpty(resultValueList) || !CollectionUtil.containsAny(resultValueList, conditionValueList);
    }
}